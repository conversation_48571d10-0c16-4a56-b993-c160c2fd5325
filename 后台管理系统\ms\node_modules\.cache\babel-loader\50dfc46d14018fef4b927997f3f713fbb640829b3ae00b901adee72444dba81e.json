{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createBlock as _createBlock, Fragment as _Fragment, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, resolveDirective as _resolveDirective, withDirectives as _withDirectives, renderList as _renderList } from \"vue\";\nconst _hoisted_1 = {\n  class: \"user-list-container\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  class: \"pagination-container\"\n};\nconst _hoisted_4 = {\n  class: \"teacher-option\"\n};\nconst _hoisted_5 = {\n  class: \"teacher-dept\"\n};\nconst _hoisted_6 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_Upload = _resolveComponent(\"Upload\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 搜索和操作区域 \"), _createVNode(_component_el_card, {\n    class: \"search-card\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      inline: true,\n      model: $setup.searchForm,\n      class: \"search-form\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"用户名\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.searchForm.username,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.searchForm.username = $event),\n          placeholder: \"请输入用户名\",\n          clearable: \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"手机号\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.searchForm.phone,\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.searchForm.phone = $event),\n          placeholder: \"请输入手机号\",\n          clearable: \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, null, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: $setup.handleSearch\n        }, {\n          default: _withCtx(() => _cache[14] || (_cache[14] = [_createTextVNode(\"查询\")])),\n          _: 1 /* STABLE */,\n          __: [14]\n        }), _createVNode(_component_el_button, {\n          onClick: $setup.resetForm\n        }, {\n          default: _withCtx(() => _cache[15] || (_cache[15] = [_createTextVNode(\"重置\")])),\n          _: 1 /* STABLE */,\n          __: [15]\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 表格区域 \"), _createVNode(_component_el_card, {\n    class: \"table-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[20] || (_cache[20] = _createElementVNode(\"span\", null, \"用户列表\", -1 /* CACHED */)), _createElementVNode(\"div\", null, [_createVNode(_component_el_button, {\n      type: \"success\",\n      onClick: _ctx.downloadTemplate\n    }, {\n      default: _withCtx(() => _cache[16] || (_cache[16] = [_createTextVNode(\"下载模板\")])),\n      _: 1 /* STABLE */,\n      __: [16]\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      type: \"danger\",\n      onClick: _ctx.openImportDialog\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Upload)]),\n        _: 1 /* STABLE */\n      }), _cache[17] || (_cache[17] = _createTextVNode(\" 一键导入 \"))]),\n      _: 1 /* STABLE */,\n      __: [17]\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.handleAdd\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Plus\"])]),\n        _: 1 /* STABLE */\n      }), _cache[18] || (_cache[18] = _createTextVNode(\" 新增用户 \"))]),\n      _: 1 /* STABLE */,\n      __: [18]\n    }), _createVNode(_component_el_button, {\n      onClick: $setup.refreshTable\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Refresh\"])]),\n        _: 1 /* STABLE */\n      }), _cache[19] || (_cache[19] = _createTextVNode(\" 刷新 \"))]),\n      _: 1 /* STABLE */,\n      __: [19]\n    })])])]),\n    default: _withCtx(() => [_withDirectives((_openBlock(), _createBlock(_component_el_table, {\n      data: $setup.userList,\n      onSelectionChange: $setup.handleSelectionChange,\n      style: {\n        \"width\": \"100%\"\n      },\n      border: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        width: \"55\",\n        align: \"center\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"id\",\n        label: \"ID\",\n        width: \"80\",\n        align: \"center\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"username\",\n        label: \"用户名\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"name\",\n        label: \"姓名\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"phone\",\n        label: \"手机号\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"email\",\n        label: \"邮箱\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"role\",\n        label: \"角色\",\n        width: \"100\"\n      }, {\n        default: _withCtx(scope => [scope.row.role === 'admin' ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 0,\n          type: \"danger\"\n        }, {\n          default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\"管理员\")])),\n          _: 1 /* STABLE */,\n          __: [21]\n        })) : scope.row.role === 'teacher' ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 1,\n          type: \"warning\"\n        }, {\n          default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\"教师\")])),\n          _: 1 /* STABLE */,\n          __: [22]\n        })) : scope.row.role === 'teaching_admin' ? (_openBlock(), _createElementBlock(_Fragment, {\n          key: 2\n        }, [_createCommentVNode(\" 教学管理员、带教老师、教研室主任、教研室副主任、教学秘书 \"), _createVNode(_component_el_tag, {\n          type: \"warning\"\n        }, {\n          default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\"教学管理员\")])),\n          _: 1 /* STABLE */,\n          __: [23]\n        })], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : scope.row.role === 'teaching_teacher' ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 3,\n          type: \"warning\"\n        }, {\n          default: _withCtx(() => _cache[24] || (_cache[24] = [_createTextVNode(\"带教老师\")])),\n          _: 1 /* STABLE */,\n          __: [24]\n        })) : scope.row.role === 'department_head' ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 4,\n          type: \"warning\"\n        }, {\n          default: _withCtx(() => _cache[25] || (_cache[25] = [_createTextVNode(\"教研室主任\")])),\n          _: 1 /* STABLE */,\n          __: [25]\n        })) : scope.row.role === 'department_deputy_head' ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 5,\n          type: \"warning\"\n        }, {\n          default: _withCtx(() => _cache[26] || (_cache[26] = [_createTextVNode(\"教研室副主任\")])),\n          _: 1 /* STABLE */,\n          __: [26]\n        })) : scope.row.role === 'teaching_secretary' ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 6,\n          type: \"warning\"\n        }, {\n          default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\"教学秘书\")])),\n          _: 1 /* STABLE */,\n          __: [27]\n        })) : (_openBlock(), _createBlock(_component_el_tag, {\n          key: 7,\n          type: \"info\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString(scope.row.role), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */))]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"created_at\",\n        label: \"创建时间\",\n        width: \"160\"\n      }, {\n        default: _withCtx(scope => [_createTextVNode(_toDisplayString($setup.formatDate(scope.row.created_at)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        fixed: \"right\",\n        label: \"操作\",\n        width: \"180\",\n        align: \"center\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_button, {\n          size: \"small\",\n          onClick: $event => $setup.handleEdit(scope.row)\n        }, {\n          default: _withCtx(() => _cache[28] || (_cache[28] = [_createTextVNode(\"编辑\")])),\n          _: 2 /* DYNAMIC */,\n          __: [28]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n          size: \"small\",\n          type: \"danger\",\n          onClick: $event => $setup.handleDelete(scope.row)\n        }, {\n          default: _withCtx(() => _cache[29] || (_cache[29] = [_createTextVNode(\"删除\")])),\n          _: 2 /* DYNAMIC */,\n          __: [29]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"data\"])), [[_directive_loading, $setup.loading]]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_pagination, {\n      \"current-page\": $setup.currentPage,\n      \"onUpdate:currentPage\": _cache[2] || (_cache[2] = $event => $setup.currentPage = $event),\n      \"page-size\": $setup.pageSize,\n      \"onUpdate:pageSize\": _cache[3] || (_cache[3] = $event => $setup.pageSize = $event),\n      \"page-sizes\": [10, 20, 50, 100],\n      total: $setup.total,\n      layout: \"total, sizes, prev, pager, next, jumper\",\n      onSizeChange: $setup.handleSizeChange,\n      onCurrentChange: $setup.handleCurrentChange\n    }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\"])])]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 用户表单对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.dialogVisible,\n    \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $setup.dialogVisible = $event),\n    title: $setup.dialogType === 'add' ? '新增用户' : '编辑用户',\n    width: \"600px\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_button, {\n      onClick: _cache[12] || (_cache[12] = $event => $setup.dialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[31] || (_cache[31] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [31]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.submitForm\n    }, {\n      default: _withCtx(() => _cache[32] || (_cache[32] = [_createTextVNode(\"确定\")])),\n      _: 1 /* STABLE */,\n      __: [32]\n    })])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.userForm,\n      rules: $setup.userFormRules,\n      ref: \"userFormRef\",\n      \"label-width\": \"100px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"用户名\",\n        prop: \"username\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userForm.username,\n          \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.userForm.username = $event),\n          placeholder: \"请输入用户名\",\n          disabled: $setup.dialogType === 'edit'\n        }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"姓名\",\n        prop: \"name\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userForm.name,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.userForm.name = $event),\n          placeholder: \"请输入姓名\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), $setup.dialogType === 'add' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 0,\n        label: \"密码\",\n        prop: \"password\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userForm.password,\n          \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.userForm.password = $event),\n          type: \"password\",\n          placeholder: \"请输入密码\",\n          \"show-password\": \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_form_item, {\n        label: \"手机号\",\n        prop: \"phone\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userForm.phone,\n          \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.userForm.phone = $event),\n          placeholder: \"请输入手机号\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"邮箱\",\n        prop: \"email\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userForm.email,\n          \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.userForm.email = $event),\n          placeholder: \"请输入邮箱\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), $setup.dialogType === 'add' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 1,\n        label: \"角色\",\n        prop: \"role\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.userForm.role,\n          \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $setup.userForm.role = $event),\n          placeholder: \"请选择角色\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_option, {\n            label: \"管理员\",\n            value: \"admin\"\n          }), _createVNode(_component_el_option, {\n            label: \"教师\",\n            value: \"teacher\"\n          }), _createVNode(_component_el_option, {\n            label: \"教学管理员\",\n            value: \"teaching_admin\"\n          }), _createVNode(_component_el_option, {\n            label: \"带教老师\",\n            value: \"teaching_teacher\"\n          }), _createVNode(_component_el_option, {\n            label: \"教研室主任\",\n            value: \"department_head\"\n          }), _createVNode(_component_el_option, {\n            label: \"教研室副主任\",\n            value: \"department_deputy_head\"\n          }), _createVNode(_component_el_option, {\n            label: \"教学秘书\",\n            value: \"teaching_secretary\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 当角色是教师时，显示教师选择器 \"), $setup.userForm.role === 'teacher' && $setup.dialogType === 'add' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 2,\n        label: \"关联教师\",\n        prop: \"teacher_id\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.userForm.teacher_id,\n          \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $setup.userForm.teacher_id = $event),\n          placeholder: \"请选择关联的教师\",\n          filterable: \"\",\n          loading: $setup.teacherListLoading\n        }, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.teacherList, item => {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: item.id,\n              label: `${item.name} - ${item.department}`,\n              value: item.id\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"span\", null, _toDisplayString(item.name), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_5, _toDisplayString(item.department), 1 /* TEXT */)])]),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\", \"loading\"]), _cache[30] || (_cache[30] = _createElementVNode(\"div\", {\n          class: \"form-tip\"\n        }, \"将用户账号关联到现有教师\", -1 /* CACHED */))]),\n        _: 1 /* STABLE */,\n        __: [30]\n      })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 当角色不是教师时，隐藏教师ID字段 \"), $setup.userForm.role !== 'teacher' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 3,\n        label: \"教师ID\",\n        prop: \"teacher_id\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userForm.teacher_id,\n          \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $setup.userForm.teacher_id = $event),\n          placeholder: \"请输入教师ID（选填）\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"title\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createVNode", "_component_el_card", "_component_el_form", "inline", "model", "$setup", "searchForm", "_component_el_form_item", "label", "_component_el_input", "username", "$event", "placeholder", "clearable", "phone", "_component_el_button", "type", "onClick", "handleSearch", "_cache", "resetForm", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_ctx", "downloadTemplate", "openImportDialog", "_component_el_icon", "_component_Upload", "handleAdd", "refreshTable", "_createBlock", "_component_el_table", "data", "userList", "onSelectionChange", "handleSelectionChange", "style", "border", "_component_el_table_column", "width", "align", "prop", "default", "scope", "row", "role", "_component_el_tag", "_Fragment", "key", "formatDate", "created_at", "fixed", "size", "handleEdit", "handleDelete", "loading", "_hoisted_3", "_component_el_pagination", "currentPage", "pageSize", "total", "layout", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "_component_el_dialog", "dialogVisible", "title", "dialogType", "footer", "_hoisted_6", "submitForm", "userForm", "rules", "userFormRules", "ref", "disabled", "name", "password", "email", "_component_el_select", "_component_el_option", "value", "teacher_id", "filterable", "teacherListL<PERSON>ding", "_renderList", "teacherList", "item", "id", "department", "_hoisted_4", "_toDisplayString", "_hoisted_5"], "sources": ["D:\\admin\\202506\\督导系统\\后台管理系统\\ms\\src\\views\\users\\UserList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"user-list-container\">\r\n    <!-- 搜索和操作区域 -->\r\n    <el-card class=\"search-card\">\r\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\r\n        <el-form-item label=\"用户名\">\r\n          <el-input v-model=\"searchForm.username\" placeholder=\"请输入用户名\" clearable></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\">\r\n          <el-input v-model=\"searchForm.phone\" placeholder=\"请输入手机号\" clearable></el-input>\r\n        </el-form-item>\r\n      \r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n          <el-button @click=\"resetForm\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-card>\r\n    \r\n    <!-- 表格区域 -->\r\n    <el-card class=\"table-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span>用户列表</span>\r\n          <div>\r\n            <el-button type=\"success\" @click=\"downloadTemplate\">下载模板</el-button>\r\n            <el-button type=\"danger\" @click=\"openImportDialog\">\r\n              <el-icon><Upload /></el-icon> 一键导入\r\n            </el-button>\r\n            <el-button type=\"primary\" @click=\"handleAdd\">\r\n              <el-icon><Plus /></el-icon> 新增用户\r\n            </el-button>\r\n            <el-button @click=\"refreshTable\">\r\n              <el-icon><Refresh /></el-icon> 刷新\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n      \r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"userList\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        style=\"width: 100%\"\r\n        border\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column prop=\"id\" label=\"ID\" width=\"80\" align=\"center\" />\r\n        <el-table-column prop=\"username\" label=\"用户名\" />\r\n        <el-table-column prop=\"name\" label=\"姓名\" />\r\n        <el-table-column prop=\"phone\" label=\"手机号\" />\r\n        <el-table-column prop=\"email\" label=\"邮箱\" />\r\n        <el-table-column prop=\"role\" label=\"角色\" width=\"100\">\r\n          <template #default=\"scope\">\r\n            <el-tag v-if=\"scope.row.role === 'admin'\" type=\"danger\">管理员</el-tag>\r\n            <el-tag v-else-if=\"scope.row.role === 'teacher'\" type=\"warning\">教师</el-tag>\r\n            <!-- 教学管理员、带教老师、教研室主任、教研室副主任、教学秘书 -->\r\n             <el-tag v-else-if=\"scope.row.role === 'teaching_admin'\" type=\"warning\">教学管理员</el-tag>\r\n             <el-tag v-else-if=\"scope.row.role === 'teaching_teacher'\" type=\"warning\">带教老师</el-tag>\r\n             <el-tag v-else-if=\"scope.row.role === 'department_head'\" type=\"warning\">教研室主任</el-tag>\r\n             <el-tag v-else-if=\"scope.row.role === 'department_deputy_head'\" type=\"warning\">教研室副主任</el-tag>\r\n             <el-tag v-else-if=\"scope.row.role === 'teaching_secretary'\" type=\"warning\">教学秘书</el-tag>\r\n            <el-tag v-else type=\"info\">{{ scope.row.role }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n       \r\n        \r\n       \r\n       \r\n        <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"160\">\r\n          <template #default=\"scope\">\r\n            {{ formatDate(scope.row.created_at) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"180\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <el-button size=\"small\" @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n            <el-button \r\n              size=\"small\" \r\n              type=\"danger\" \r\n              @click=\"handleDelete(scope.row)\"\r\n            >删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <!-- 分页 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          v-model:current-page=\"currentPage\"\r\n          v-model:page-size=\"pageSize\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          :total=\"total\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 用户表单对话框 -->\r\n    <el-dialog\r\n      v-model=\"dialogVisible\"\r\n      :title=\"dialogType === 'add' ? '新增用户' : '编辑用户'\"\r\n      width=\"600px\"\r\n    >\r\n      <el-form\r\n        :model=\"userForm\"\r\n        :rules=\"userFormRules\"\r\n        ref=\"userFormRef\"\r\n        label-width=\"100px\"\r\n      >\r\n        <el-form-item label=\"用户名\" prop=\"username\">\r\n          <el-input v-model=\"userForm.username\" placeholder=\"请输入用户名\" :disabled=\"dialogType === 'edit'\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"姓名\" prop=\"name\">\r\n          <el-input v-model=\"userForm.name\" placeholder=\"请输入姓名\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"dialogType === 'add'\" label=\"密码\" prop=\"password\">\r\n          <el-input v-model=\"userForm.password\" type=\"password\" placeholder=\"请输入密码\" show-password></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\" prop=\"phone\">\r\n          <el-input v-model=\"userForm.phone\" placeholder=\"请输入手机号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱\" prop=\"email\" >\r\n          <el-input v-model=\"userForm.email\" placeholder=\"请输入邮箱\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"角色\" prop=\"role\" v-if=\"dialogType === 'add'\">\r\n          <el-select v-model=\"userForm.role\" placeholder=\"请选择角色\">\r\n            <el-option label=\"管理员\" value=\"admin\"></el-option>\r\n            <el-option label=\"教师\" value=\"teacher\"></el-option>\r\n            <el-option label=\"教学管理员\" value=\"teaching_admin\"></el-option>\r\n            <el-option label=\"带教老师\" value=\"teaching_teacher\"></el-option>\r\n            <el-option label=\"教研室主任\" value=\"department_head\"></el-option>\r\n            <el-option label=\"教研室副主任\" value=\"department_deputy_head\"></el-option>\r\n            <el-option label=\"教学秘书\" value=\"teaching_secretary\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 当角色是教师时，显示教师选择器 -->\r\n        <el-form-item v-if=\"userForm.role === 'teacher' && dialogType === 'add'\" label=\"关联教师\" prop=\"teacher_id\">\r\n          <el-select \r\n            v-model=\"userForm.teacher_id\" \r\n            placeholder=\"请选择关联的教师\" \r\n            filterable\r\n            :loading=\"teacherListLoading\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in teacherList\"\r\n              :key=\"item.id\"\r\n              :label=\"`${item.name} - ${item.department}`\"\r\n              :value=\"item.id\"\r\n            >\r\n              <div class=\"teacher-option\">\r\n                <span>{{ item.name }}</span>\r\n                <span class=\"teacher-dept\">{{ item.department }}</span>\r\n              </div>\r\n            </el-option>\r\n          </el-select>\r\n          <div class=\"form-tip\">将用户账号关联到现有教师</div>\r\n        </el-form-item>\r\n\r\n        <!-- 当角色不是教师时，隐藏教师ID字段 -->\r\n        <el-form-item v-if=\"userForm.role !== 'teacher'\" label=\"教师ID\" prop=\"teacher_id\">\r\n          <el-input v-model=\"userForm.teacher_id\" placeholder=\"请输入教师ID（选填）\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted, watch } from 'vue'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { Plus, Delete, Refresh } from '@element-plus/icons-vue'\r\nimport userService from '@/services/userService'\r\nimport teacherService from '@/services/teacherService'\r\n\r\nconst loading = ref(false)\r\nconst teacherListLoading = ref(false)\r\nconst currentPage = ref(1)\r\nconst pageSize = ref(10)\r\nconst total = ref(0)\r\nconst multipleSelection = ref([])\r\nconst dialogVisible = ref(false)\r\nconst dialogType = ref('add') // 'add' or 'edit'\r\nconst userFormRef = ref(null)\r\nconst teacherList = ref([]) // 教师列表\r\n\r\n// 搜索表单\r\nconst searchForm = reactive({\r\n  username: '',\r\n  phone: '',\r\n  status: ''\r\n})\r\n\r\n// 用户表单\r\nconst userForm = reactive({\r\n  id: '',\r\n  username: '',\r\n  name: '',\r\n  password: '',\r\n  phone: '',\r\n  email: '',\r\n  role: 'teacher',\r\n  teacher_id: '',\r\n  status: 1\r\n})\r\n\r\n// 获取教师列表\r\nconst fetchTeacherList = async () => {\r\n  teacherListLoading.value = true\r\n  try {\r\n    const response = await teacherService.getTeachers()\r\n    teacherList.value = response.data.data\r\n  } catch (error) {\r\n    console.error('获取教师列表失败:', error)\r\n    ElMessage.error('获取教师列表失败')\r\n  } finally {\r\n    teacherListLoading.value = false\r\n  }\r\n}\r\n\r\n// 当角色选择为教师时，加载教师列表\r\nwatch(() => userForm.role, (newRole) => {\r\n  if (newRole === 'teacher' && teacherList.value.length === 0) {\r\n    fetchTeacherList()\r\n  }\r\n})\r\n\r\n// 当对话框打开时，如果角色是教师且教师列表为空，则获取教师列表\r\nwatch(() => dialogVisible.value, (newVal) => {\r\n  if (newVal && userForm.role === 'teacher' && teacherList.value.length === 0) {\r\n    fetchTeacherList()\r\n  }\r\n})\r\n\r\n// 表单校验规则\r\nconst userFormRules = {\r\n  username: [\r\n    { required: true, message: '请输入用户名', trigger: 'blur' },\r\n    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  name: [\r\n    { required: true, message: '请输入姓名', trigger: 'blur' }\r\n  ],\r\n  password: [\r\n    { required: true, message: '请输入密码', trigger: 'blur' },\r\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  phone: [\r\n    { required: true, message: '请输入手机号', trigger: 'blur' },\r\n    { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\r\n  ],\r\n  email: [\r\n    { required: true, message: '请输入邮箱', trigger: 'blur' },\r\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\r\n  ],\r\n  role: [\r\n    { required: true, message: '请选择角色', trigger: 'change' }\r\n  ],\r\n  teacher_id: [\r\n    { \r\n      validator: (rule, value, callback) => {\r\n        if (userForm.role === 'teacher' && !value) {\r\n          callback(new Error('请选择关联的教师'));\r\n        } else {\r\n          callback();\r\n        }\r\n      }, \r\n      trigger: 'change' \r\n    }\r\n  ]\r\n}\r\n\r\n// 用户数据\r\nconst userList = ref([])\r\n\r\nonMounted(() => {\r\n  fetchData()\r\n})\r\n\r\n// 格式化日期\r\nconst formatDate = (dateString) => {\r\n  if (!dateString) return ''\r\n  const date = new Date(dateString)\r\n  const year = date.getFullYear()\r\n  const month = String(date.getMonth() + 1).padStart(2, '0')\r\n  const day = String(date.getDate()).padStart(2, '0')\r\n  const hours = String(date.getHours()).padStart(2, '0')\r\n  const minutes = String(date.getMinutes()).padStart(2, '0')\r\n  return `${year}-${month}-${day} ${hours}:${minutes}`\r\n}\r\n\r\n// 获取数据\r\nconst fetchData = async () => {\r\n  loading.value = true\r\n  try {\r\n    const response = await userService.getUsers({\r\n      page: currentPage.value,\r\n      limit: pageSize.value,\r\n      username: searchForm.username || undefined,\r\n      phone: searchForm.phone || undefined,\r\n      status: searchForm.status || undefined\r\n    })\r\n    userList.value = response.data.data\r\n    total.value = response.data.count || 0\r\n  } catch (error) {\r\n    console.error('获取用户列表失败:', error)\r\n    ElMessage.error('获取用户列表失败')\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\n// 查询\r\nconst handleSearch = () => {\r\n  currentPage.value = 1\r\n  fetchData()\r\n}\r\n\r\n// 重置表单\r\nconst resetForm = () => {\r\n  Object.keys(searchForm).forEach(key => {\r\n    searchForm[key] = ''\r\n  })\r\n  handleSearch()\r\n}\r\n\r\n// 刷新表格\r\nconst refreshTable = () => {\r\n  fetchData()\r\n}\r\n\r\n// 多选变化\r\nconst handleSelectionChange = (selection) => {\r\n  multipleSelection.value = selection\r\n}\r\n\r\n// 新增用户\r\nconst handleAdd = () => {\r\n  dialogType.value = 'add'\r\n  resetUserForm()\r\n  dialogVisible.value = true\r\n}\r\n\r\n// 编辑用户\r\nconst handleEdit = (row) => {\r\n  dialogType.value = 'edit'\r\n  resetUserForm()\r\n  Object.keys(userForm).forEach(key => {\r\n    if (key !== 'password') {\r\n      userForm[key] = row[key]\r\n    }\r\n  })\r\n  dialogVisible.value = true\r\n}\r\n\r\n// 重置用户表单\r\nconst resetUserForm = () => {\r\n  if (userFormRef.value) {\r\n    userFormRef.value.resetFields()\r\n  }\r\n  Object.assign(userForm, {\r\n    id: '',\r\n    username: '',\r\n    name: '',\r\n    password: '',\r\n    phone: '',\r\n    email: '',\r\n    role: 'teacher',\r\n    teacher_id: '',\r\n    status: 1\r\n  })\r\n}\r\n\r\n// 提交表单\r\nconst submitForm = async () => {\r\n  if (!userFormRef.value) return\r\n  \r\n  await userFormRef.value.validate(async (valid) => {\r\n    if (valid) {\r\n      try {\r\n        if (dialogType.value === 'add') {\r\n          // 新增用户\r\n          if (userForm.role !== 'teacher') {\r\n           delete userForm.teacher_id\r\n          }\r\n          await userService.createUser(userForm)\r\n          ElMessage.success('新增用户成功')\r\n        } else {\r\n          // 编辑用户\r\n          await userService.updateUser(userForm.id, userForm)\r\n          ElMessage.success('编辑用户成功')\r\n        }\r\n        dialogVisible.value = false\r\n        fetchData()\r\n      } catch (error) {\r\n        console.error('保存用户失败:', error)\r\n        ElMessage.error('保存用户失败: ' + (error.response?.data?.message || error.message))\r\n      }\r\n    } else {\r\n      return false\r\n    }\r\n  })\r\n}\r\n\r\n// 删除用户\r\nconst handleDelete = (row) => {\r\n  ElMessageBox.confirm(`确定要删除用户 ${row.username} 吗?`, '警告', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    try {\r\n      await userService.deleteUser(row.id)\r\n      ElMessage.success(`用户 ${row.username} 已删除`)\r\n      fetchData()\r\n    } catch (error) {\r\n      console.error('删除用户失败:', error)\r\n      ElMessage.error('删除用户失败: ' + (error.response?.data?.message || error.message))\r\n    }\r\n  }).catch(() => {})\r\n}\r\n\r\n// 批量删除\r\nconst handleBatchDelete = () => {\r\n  if (multipleSelection.value.length === 0) {\r\n    ElMessage.warning('请至少选择一条记录')\r\n    return\r\n  }\r\n  \r\n  const names = multipleSelection.value.map(item => item.username).join('、')\r\n  const ids = multipleSelection.value.map(item => item.id)\r\n  \r\n  ElMessageBox.confirm(`确定要删除选中的 ${multipleSelection.value.length} 条记录吗?`, '警告', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    try {\r\n      await userService.batchDeleteUsers(ids)\r\n      ElMessage.success('批量删除成功')\r\n      fetchData()\r\n    } catch (error) {\r\n      console.error('批量删除失败:', error)\r\n      ElMessage.error('批量删除失败: ' + (error.response?.data?.message || error.message))\r\n    }\r\n  }).catch(() => {})\r\n}\r\n\r\n// 修改状态\r\nconst handleStatusChange = async (val, row) => {\r\n  try {\r\n    await userService.updateUserStatus(row.id, val);\r\n    const status = val === 1 ? '启用' : '禁用';\r\n    ElMessage.success(`已${status}用户 ${row.username}`);\r\n  } catch (error) {\r\n    console.error('修改状态失败:', error);\r\n    ElMessage.error('修改状态失败: ' + (error.response?.data?.message || error.message));\r\n    // 回滚状态\r\n    row.status = val === 1 ? 0 : 1;\r\n  }\r\n};\r\n\r\n// 分页大小变化\r\nconst handleSizeChange = (size) => {\r\n  pageSize.value = size\r\n  fetchData()\r\n}\r\n\r\n// 页码变化\r\nconst handleCurrentChange = (page) => {\r\n  currentPage.value = page\r\n  fetchData()\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.user-list-container {\r\n  padding: 10px;\r\n}\r\n\r\n.search-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.teacher-option {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.teacher-dept {\r\n  color: #909399;\r\n  font-size: 0.9em;\r\n}\r\n\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 5px;\r\n}\r\n</style> "], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EAqBrBA,KAAK,EAAC;AAAa;;EAiErBA,KAAK,EAAC;AAAsB;;EAkEpBA,KAAK,EAAC;AAAgB;;EAEnBA,KAAK,EAAC;AAAc;;EAa7BA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;uBAvKhCC,mBAAA,CA6KM,OA7KNC,UA6KM,GA5KJC,mBAAA,aAAgB,EAChBC,YAAA,CAcUC,kBAAA;IAdDL,KAAK,EAAC;EAAa;sBAC1B,MAYU,CAZVI,YAAA,CAYUE,kBAAA;MAZAC,MAAM,EAAE,IAAI;MAAGC,KAAK,EAAEC,MAAA,CAAAC,UAAU;MAAEV,KAAK,EAAC;;wBAChD,MAEe,CAFfI,YAAA,CAEeO,uBAAA;QAFDC,KAAK,EAAC;MAAK;0BACvB,MAAkF,CAAlFR,YAAA,CAAkFS,mBAAA;sBAA/DJ,MAAA,CAAAC,UAAU,CAACI,QAAQ;qEAAnBL,MAAA,CAAAC,UAAU,CAACI,QAAQ,GAAAC,MAAA;UAAEC,WAAW,EAAC,QAAQ;UAACC,SAAS,EAAT;;;UAE/Db,YAAA,CAEeO,uBAAA;QAFDC,KAAK,EAAC;MAAK;0BACvB,MAA+E,CAA/ER,YAAA,CAA+ES,mBAAA;sBAA5DJ,MAAA,CAAAC,UAAU,CAACQ,KAAK;qEAAhBT,MAAA,CAAAC,UAAU,CAACQ,KAAK,GAAAH,MAAA;UAAEC,WAAW,EAAC,QAAQ;UAACC,SAAS,EAAT;;;UAG5Db,YAAA,CAGeO,uBAAA;0BAFb,MAA8D,CAA9DP,YAAA,CAA8De,oBAAA;UAAnDC,IAAI,EAAC,SAAS;UAAEC,OAAK,EAAEZ,MAAA,CAAAa;;4BAAc,MAAEC,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;YAClDnB,YAAA,CAA4Ce,oBAAA;UAAhCE,OAAK,EAAEZ,MAAA,CAAAe;QAAS;4BAAE,MAAED,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;;;;;MAKtCpB,mBAAA,UAAa,EACbC,YAAA,CA8EUC,kBAAA;IA9EDL,KAAK,EAAC;EAAY;IACdyB,MAAM,EAAAC,QAAA,CACf,MAcM,CAdNC,mBAAA,CAcM,OAdNC,UAcM,G,4BAbJD,mBAAA,CAAiB,cAAX,MAAI,qBACVA,mBAAA,CAWM,cAVJvB,YAAA,CAAoEe,oBAAA;MAAzDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEQ,IAAA,CAAAC;;wBAAkB,MAAIP,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;oCACxDnB,YAAA,CAEYe,oBAAA;MAFDC,IAAI,EAAC,QAAQ;MAAEC,OAAK,EAAEQ,IAAA,CAAAE;;wBAC/B,MAA6B,CAA7B3B,YAAA,CAA6B4B,kBAAA;0BAApB,MAAU,CAAV5B,YAAA,CAAU6B,iBAAA,E;;uDAAU,QAC/B,G;;;oCACA7B,YAAA,CAEYe,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEZ,MAAA,CAAAyB;;wBAChC,MAA2B,CAA3B9B,YAAA,CAA2B4B,kBAAA;0BAAlB,MAAQ,CAAR5B,YAAA,CAAQK,MAAA,U;;uDAAU,QAC7B,G;;;QACAL,YAAA,CAEYe,oBAAA;MAFAE,OAAK,EAAEZ,MAAA,CAAA0B;IAAY;wBAC7B,MAA8B,CAA9B/B,YAAA,CAA8B4B,kBAAA;0BAArB,MAAW,CAAX5B,YAAA,CAAWK,MAAA,a;;uDAAU,MAChC,G;;;;sBAKN,MA6CW,C,+BA7CX2B,YAAA,CA6CWC,mBAAA;MA3CRC,IAAI,EAAE7B,MAAA,CAAA8B,QAAQ;MACdC,iBAAgB,EAAE/B,MAAA,CAAAgC,qBAAqB;MACxCC,KAAmB,EAAnB;QAAA;MAAA,CAAmB;MACnBC,MAAM,EAAN;;wBAEA,MAA8D,CAA9DvC,YAAA,CAA8DwC,0BAAA;QAA7CxB,IAAI,EAAC,WAAW;QAACyB,KAAK,EAAC,IAAI;QAACC,KAAK,EAAC;UACnD1C,YAAA,CAAkEwC,0BAAA;QAAjDG,IAAI,EAAC,IAAI;QAACnC,KAAK,EAAC,IAAI;QAACiC,KAAK,EAAC,IAAI;QAACC,KAAK,EAAC;UACvD1C,YAAA,CAA+CwC,0BAAA;QAA9BG,IAAI,EAAC,UAAU;QAACnC,KAAK,EAAC;UACvCR,YAAA,CAA0CwC,0BAAA;QAAzBG,IAAI,EAAC,MAAM;QAACnC,KAAK,EAAC;UACnCR,YAAA,CAA4CwC,0BAAA;QAA3BG,IAAI,EAAC,OAAO;QAACnC,KAAK,EAAC;UACpCR,YAAA,CAA2CwC,0BAAA;QAA1BG,IAAI,EAAC,OAAO;QAACnC,KAAK,EAAC;UACpCR,YAAA,CAYkBwC,0BAAA;QAZDG,IAAI,EAAC,MAAM;QAACnC,KAAK,EAAC,IAAI;QAACiC,KAAK,EAAC;;QACjCG,OAAO,EAAAtB,QAAA,CACoDuB,KAD7C,KACTA,KAAK,CAACC,GAAG,CAACC,IAAI,gB,cAA5Bf,YAAA,CAAoEgB,iBAAA;;UAA1BhC,IAAI,EAAC;;4BAAS,MAAGG,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;cACxC0B,KAAK,CAACC,GAAG,CAACC,IAAI,kB,cAAjCf,YAAA,CAA2EgB,iBAAA;;UAA1BhC,IAAI,EAAC;;4BAAU,MAAEG,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;cAE9C0B,KAAK,CAACC,GAAG,CAACC,IAAI,yB,cAAjClD,mBAAA,CAAqFoD,SAAA;UAAAC,GAAA;QAAA,IADtFnD,mBAAA,kCAAqC,EACpCC,YAAA,CAAqFgD,iBAAA;UAA7BhC,IAAI,EAAC;QAAS;4BAAC,MAAKG,MAAA,SAAAA,MAAA,Q,iBAAL,OAAK,E;;;+DACzD0B,KAAK,CAACC,GAAG,CAACC,IAAI,2B,cAAjCf,YAAA,CAAsFgB,iBAAA;;UAA5BhC,IAAI,EAAC;;4BAAU,MAAIG,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;cAC1D0B,KAAK,CAACC,GAAG,CAACC,IAAI,0B,cAAjCf,YAAA,CAAsFgB,iBAAA;;UAA7BhC,IAAI,EAAC;;4BAAU,MAAKG,MAAA,SAAAA,MAAA,Q,iBAAL,OAAK,E;;;cAC1D0B,KAAK,CAACC,GAAG,CAACC,IAAI,iC,cAAjCf,YAAA,CAA8FgB,iBAAA;;UAA9BhC,IAAI,EAAC;;4BAAU,MAAMG,MAAA,SAAAA,MAAA,Q,iBAAN,QAAM,E;;;cAClE0B,KAAK,CAACC,GAAG,CAACC,IAAI,6B,cAAjCf,YAAA,CAAwFgB,iBAAA;;UAA5BhC,IAAI,EAAC;;4BAAU,MAAIG,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;6BAChFa,YAAA,CAAwDgB,iBAAA;;UAAzChC,IAAI,EAAC;;4BAAO,MAAoB,C,kCAAjB6B,KAAK,CAACC,GAAG,CAACC,IAAI,iB;;;;UAOhD/C,YAAA,CAIkBwC,0BAAA;QAJDG,IAAI,EAAC,YAAY;QAACnC,KAAK,EAAC,MAAM;QAACiC,KAAK,EAAC;;QACzCG,OAAO,EAAAtB,QAAA,CACsBuB,KADf,K,kCACpBxC,MAAA,CAAA8C,UAAU,CAACN,KAAK,CAACC,GAAG,CAACM,UAAU,kB;;UAGtCpD,YAAA,CASkBwC,0BAAA;QATDa,KAAK,EAAC,OAAO;QAAC7C,KAAK,EAAC,IAAI;QAACiC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;;QAC/CE,OAAO,EAAAtB,QAAA,CACqDuB,KAD9C,KACvB7C,YAAA,CAAqEe,oBAAA;UAA1DuC,IAAI,EAAC,OAAO;UAAErC,OAAK,EAAAN,MAAA,IAAEN,MAAA,CAAAkD,UAAU,CAACV,KAAK,CAACC,GAAG;;4BAAG,MAAE3B,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;0DACzDnB,YAAA,CAIee,oBAAA;UAHbuC,IAAI,EAAC,OAAO;UACZtC,IAAI,EAAC,QAAQ;UACZC,OAAK,EAAAN,MAAA,IAAEN,MAAA,CAAAmD,YAAY,CAACX,KAAK,CAACC,GAAG;;4BAC/B,MAAE3B,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;;;wDAzCId,MAAA,CAAAoD,OAAO,E,GA+CpBlC,mBAAA,CAUM,OAVNmC,UAUM,GATJ1D,YAAA,CAQE2D,wBAAA;MAPQ,cAAY,EAAEtD,MAAA,CAAAuD,WAAW;kEAAXvD,MAAA,CAAAuD,WAAW,GAAAjD,MAAA;MACzB,WAAS,EAAEN,MAAA,CAAAwD,QAAQ;+DAARxD,MAAA,CAAAwD,QAAQ,GAAAlD,MAAA;MAC1B,YAAU,EAAE,iBAAiB;MAC7BmD,KAAK,EAAEzD,MAAA,CAAAyD,KAAK;MACbC,MAAM,EAAC,yCAAyC;MAC/CC,YAAW,EAAE3D,MAAA,CAAA4D,gBAAgB;MAC7BC,eAAc,EAAE7D,MAAA,CAAA8D;;;MAKvBpE,mBAAA,aAAgB,EAChBC,YAAA,CAwEYoE,oBAAA;gBAvED/D,MAAA,CAAAgE,aAAa;iEAAbhE,MAAA,CAAAgE,aAAa,GAAA1D,MAAA;IACrB2D,KAAK,EAAEjE,MAAA,CAAAkE,UAAU;IAClB9B,KAAK,EAAC;;IA+DK+B,MAAM,EAAAlD,QAAA,CACf,MAGM,CAHNC,mBAAA,CAGM,OAHNkD,UAGM,GAFJzE,YAAA,CAAwDe,oBAAA;MAA5CE,OAAK,EAAAE,MAAA,SAAAA,MAAA,OAAAR,MAAA,IAAEN,MAAA,CAAAgE,aAAa;;wBAAU,MAAElD,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC5CnB,YAAA,CAA4De,oBAAA;MAAjDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEZ,MAAA,CAAAqE;;wBAAY,MAAEvD,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;sBAhEpD,MA4DU,CA5DVnB,YAAA,CA4DUE,kBAAA;MA3DPE,KAAK,EAAEC,MAAA,CAAAsE,QAAQ;MACfC,KAAK,EAAEvE,MAAA,CAAAwE,aAAa;MACrBC,GAAG,EAAC,aAAa;MACjB,aAAW,EAAC;;wBAEZ,MAEe,CAFf9E,YAAA,CAEeO,uBAAA;QAFDC,KAAK,EAAC,KAAK;QAACmC,IAAI,EAAC;;0BAC7B,MAAwG,CAAxG3C,YAAA,CAAwGS,mBAAA;sBAArFJ,MAAA,CAAAsE,QAAQ,CAACjE,QAAQ;qEAAjBL,MAAA,CAAAsE,QAAQ,CAACjE,QAAQ,GAAAC,MAAA;UAAEC,WAAW,EAAC,QAAQ;UAAEmE,QAAQ,EAAE1E,MAAA,CAAAkE,UAAU;;;UAElFvE,YAAA,CAEeO,uBAAA;QAFDC,KAAK,EAAC,IAAI;QAACmC,IAAI,EAAC;;0BAC5B,MAAiE,CAAjE3C,YAAA,CAAiES,mBAAA;sBAA9CJ,MAAA,CAAAsE,QAAQ,CAACK,IAAI;qEAAb3E,MAAA,CAAAsE,QAAQ,CAACK,IAAI,GAAArE,MAAA;UAAEC,WAAW,EAAC;;;UAE5BP,MAAA,CAAAkE,UAAU,c,cAA9BvC,YAAA,CAEezB,uBAAA;;QAF2BC,KAAK,EAAC,IAAI;QAACmC,IAAI,EAAC;;0BACxD,MAAmG,CAAnG3C,YAAA,CAAmGS,mBAAA;sBAAhFJ,MAAA,CAAAsE,QAAQ,CAACM,QAAQ;qEAAjB5E,MAAA,CAAAsE,QAAQ,CAACM,QAAQ,GAAAtE,MAAA;UAAEK,IAAI,EAAC,UAAU;UAACJ,WAAW,EAAC,OAAO;UAAC,eAAa,EAAb;;;+CAE5EZ,YAAA,CAEeO,uBAAA;QAFDC,KAAK,EAAC,KAAK;QAACmC,IAAI,EAAC;;0BAC7B,MAAmE,CAAnE3C,YAAA,CAAmES,mBAAA;sBAAhDJ,MAAA,CAAAsE,QAAQ,CAAC7D,KAAK;qEAAdT,MAAA,CAAAsE,QAAQ,CAAC7D,KAAK,GAAAH,MAAA;UAAEC,WAAW,EAAC;;;UAEjDZ,YAAA,CAEeO,uBAAA;QAFDC,KAAK,EAAC,IAAI;QAACmC,IAAI,EAAC;;0BAC5B,MAAkE,CAAlE3C,YAAA,CAAkES,mBAAA;sBAA/CJ,MAAA,CAAAsE,QAAQ,CAACO,KAAK;qEAAd7E,MAAA,CAAAsE,QAAQ,CAACO,KAAK,GAAAvE,MAAA;UAAEC,WAAW,EAAC;;;UAENP,MAAA,CAAAkE,UAAU,c,cAArDvC,YAAA,CAUezB,uBAAA;;QAVDC,KAAK,EAAC,IAAI;QAACmC,IAAI,EAAC;;0BAC5B,MAQY,CARZ3C,YAAA,CAQYmF,oBAAA;sBARQ9E,MAAA,CAAAsE,QAAQ,CAAC5B,IAAI;qEAAb1C,MAAA,CAAAsE,QAAQ,CAAC5B,IAAI,GAAApC,MAAA;UAAEC,WAAW,EAAC;;4BAC7C,MAAiD,CAAjDZ,YAAA,CAAiDoF,oBAAA;YAAtC5E,KAAK,EAAC,KAAK;YAAC6E,KAAK,EAAC;cAC7BrF,YAAA,CAAkDoF,oBAAA;YAAvC5E,KAAK,EAAC,IAAI;YAAC6E,KAAK,EAAC;cAC5BrF,YAAA,CAA4DoF,oBAAA;YAAjD5E,KAAK,EAAC,OAAO;YAAC6E,KAAK,EAAC;cAC/BrF,YAAA,CAA6DoF,oBAAA;YAAlD5E,KAAK,EAAC,MAAM;YAAC6E,KAAK,EAAC;cAC9BrF,YAAA,CAA6DoF,oBAAA;YAAlD5E,KAAK,EAAC,OAAO;YAAC6E,KAAK,EAAC;cAC/BrF,YAAA,CAAqEoF,oBAAA;YAA1D5E,KAAK,EAAC,QAAQ;YAAC6E,KAAK,EAAC;cAChCrF,YAAA,CAA+DoF,oBAAA;YAApD5E,KAAK,EAAC,MAAM;YAAC6E,KAAK,EAAC;;;;;+CAIlCtF,mBAAA,qBAAwB,EACJM,MAAA,CAAAsE,QAAQ,CAAC5B,IAAI,kBAAkB1C,MAAA,CAAAkE,UAAU,c,cAA7DvC,YAAA,CAoBezB,uBAAA;;QApB0DC,KAAK,EAAC,MAAM;QAACmC,IAAI,EAAC;;0BACzF,MAiBY,CAjBZ3C,YAAA,CAiBYmF,oBAAA;sBAhBD9E,MAAA,CAAAsE,QAAQ,CAACW,UAAU;uEAAnBjF,MAAA,CAAAsE,QAAQ,CAACW,UAAU,GAAA3E,MAAA;UAC5BC,WAAW,EAAC,UAAU;UACtB2E,UAAU,EAAV,EAAU;UACT9B,OAAO,EAAEpD,MAAA,CAAAmF;;4BAGR,MAA2B,E,kBAD7B3F,mBAAA,CAUYoD,SAAA,QAAAwC,WAAA,CATKpF,MAAA,CAAAqF,WAAW,EAAnBC,IAAI;iCADb3D,YAAA,CAUYoD,oBAAA;cARTlC,GAAG,EAAEyC,IAAI,CAACC,EAAE;cACZpF,KAAK,KAAKmF,IAAI,CAACX,IAAI,MAAMW,IAAI,CAACE,UAAU;cACxCR,KAAK,EAAEM,IAAI,CAACC;;gCAEb,MAGM,CAHNrE,mBAAA,CAGM,OAHNuE,UAGM,GAFJvE,mBAAA,CAA4B,cAAAwE,gBAAA,CAAnBJ,IAAI,CAACX,IAAI,kBAClBzD,mBAAA,CAAuD,QAAvDyE,UAAuD,EAAAD,gBAAA,CAAzBJ,IAAI,CAACE,UAAU,iB;;;;;kFAInDtE,mBAAA,CAAwC;UAAnC3B,KAAK,EAAC;QAAU,GAAC,cAAY,oB;;;+CAGpCG,mBAAA,uBAA0B,EACNM,MAAA,CAAAsE,QAAQ,CAAC5B,IAAI,kB,cAAjCf,YAAA,CAEezB,uBAAA;;QAFkCC,KAAK,EAAC,MAAM;QAACmC,IAAI,EAAC;;0BACjE,MAA6E,CAA7E3C,YAAA,CAA6ES,mBAAA;sBAA1DJ,MAAA,CAAAsE,QAAQ,CAACW,UAAU;uEAAnBjF,MAAA,CAAAsE,QAAQ,CAACW,UAAU,GAAA3E,MAAA;UAAEC,WAAW,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}