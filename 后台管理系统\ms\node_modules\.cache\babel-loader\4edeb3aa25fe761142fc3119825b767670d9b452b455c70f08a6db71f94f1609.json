{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"add-evaluation-container\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  class: \"title\"\n};\nconst _hoisted_4 = [\"src\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_input_number = _resolveComponent(\"el-input-number\");\n  const _component_el_radio = _resolveComponent(\"el-radio\");\n  const _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  const _component_Plus = _resolveComponent(\"Plus\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_upload = _resolveComponent(\"el-upload\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, {\n    class: \"box-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"span\", _hoisted_3, _toDisplayString($setup.isEdit ? '编辑督导评价' : '添加督导评价'), 1 /* TEXT */), _createVNode(_component_el_button, {\n      onClick: $setup.goBack\n    }, {\n      default: _withCtx(() => _cache[13] || (_cache[13] = [_createTextVNode(\"返回列表\")])),\n      _: 1 /* STABLE */,\n      __: [13]\n    }, 8 /* PROPS */, [\"onClick\"])])]),\n    default: _withCtx(() => [_withDirectives((_openBlock(), _createElementBlock(\"div\", null, [_createVNode(_component_el_form, {\n      ref: \"evaluationFormRef\",\n      model: $setup.formData,\n      rules: $setup.formRules,\n      \"label-width\": \"120px\",\n      \"label-position\": \"right\"\n    }, {\n      default: _withCtx(() => [_createCommentVNode(\" 督导教研室 \"), _createVNode(_component_el_form_item, {\n        label: \"督导教研室\",\n        prop: \"supervising_department\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.formData.supervising_department,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.formData.supervising_department = $event),\n          placeholder: \"请输入督导教研室\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 病例/主题 \"), _createVNode(_component_el_form_item, {\n        label: \"病例/主题\",\n        prop: \"case_topic\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.formData.case_topic,\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.formData.case_topic = $event),\n          placeholder: \"请输入病例或主题\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 教学活动形式 \"), _createVNode(_component_el_form_item, {\n        label: \"教学活动形式\",\n        prop: \"teaching_form\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.formData.teaching_form,\n          \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.formData.teaching_form = $event),\n          placeholder: \"例如：小讲课、教学查房等\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 带教老师 \"), _createVNode(_component_el_form_item, {\n        label: \"带教老师\",\n        prop: \"teacher_id\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.formData.teacher_id,\n          \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.formData.teacher_id = $event),\n          placeholder: \"请选择带教老师\",\n          filterable: \"\",\n          loading: $setup.teacherLoading\n        }, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.teacherOptions, item => {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: item.id,\n              label: `${item.name || '未命名'} (${item.department || '无部门'})`,\n              value: item.id\n            }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\", \"loading\"])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 带教老师职称 \"), _createVNode(_component_el_form_item, {\n        label: \"带教老师职称\",\n        prop: \"teacher_title\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.formData.teacher_title,\n          \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.formData.teacher_title = $event),\n          placeholder: \"请输入带教老师职称\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 学员姓名 \"), _createVNode(_component_el_form_item, {\n        label: \"学员姓名\",\n        prop: \"student_name\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.formData.student_name,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.formData.student_name = $event),\n          placeholder: \"请输入学员姓名\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 学员类别 \"), _createVNode(_component_el_form_item, {\n        label: \"学员类别\",\n        prop: \"student_type\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.formData.student_type,\n          \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.formData.student_type = $event),\n          placeholder: \"请选择学员类别\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_option, {\n            label: \"实习生\",\n            value: \"实习生\"\n          }), _createVNode(_component_el_option, {\n            label: \"进修生\",\n            value: \"进修生\"\n          }), _createVNode(_component_el_option, {\n            label: \"低年资轮转\",\n            value: \"低年资轮转\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 平均分 \"), _createVNode(_component_el_form_item, {\n        label: \"评分\",\n        prop: \"average_score\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input_number, {\n          modelValue: $setup.formData.average_score,\n          \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.formData.average_score = $event),\n          min: 0,\n          max: 100,\n          precision: 1,\n          step: 1,\n          \"controls-position\": \"right\"\n        }, null, 8 /* PROPS */, [\"modelValue\"]), _cache[14] || (_cache[14] = _createElementVNode(\"span\", {\n          class: \"score-hint\"\n        }, \"（0-100分）\", -1 /* CACHED */))]),\n        _: 1 /* STABLE */,\n        __: [14]\n      }), _createCommentVNode(\" 亮点 \"), _createVNode(_component_el_form_item, {\n        label: \"亮点\",\n        prop: \"highlights\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.formData.highlights,\n          \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.formData.highlights = $event),\n          type: \"textarea\",\n          rows: 3,\n          placeholder: \"请输入教学亮点\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 不足 \"), _createVNode(_component_el_form_item, {\n        label: \"不足\",\n        prop: \"shortcomings\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.formData.shortcomings,\n          \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $setup.formData.shortcomings = $event),\n          type: \"textarea\",\n          rows: 3,\n          placeholder: \"请输入教学不足之处\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 改进建议 \"), _createVNode(_component_el_form_item, {\n        label: \"改进建议\",\n        prop: \"improvement_suggestions\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.formData.improvement_suggestions,\n          \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $setup.formData.improvement_suggestions = $event),\n          type: \"textarea\",\n          rows: 3,\n          placeholder: \"请输入改进建议\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 能力认定 \"), _createVNode(_component_el_form_item, {\n        label: \"能力认定\",\n        prop: \"competency_approved\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_radio_group, {\n          modelValue: $setup.formData.competency_approved,\n          \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $setup.formData.competency_approved = $event)\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_radio, {\n            label: 1\n          }, {\n            default: _withCtx(() => _cache[15] || (_cache[15] = [_createTextVNode(\"同意\")])),\n            _: 1 /* STABLE */,\n            __: [15]\n          }), _createVNode(_component_el_radio, {\n            label: 0\n          }, {\n            default: _withCtx(() => _cache[16] || (_cache[16] = [_createTextVNode(\"不同意\")])),\n            _: 1 /* STABLE */,\n            __: [16]\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 当次督导人员 \"), _createVNode(_component_el_form_item, {\n        label: \"当次督导人员\",\n        prop: \"supervisor_name\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.formData.supervisor_name,\n          \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $setup.formData.supervisor_name = $event),\n          placeholder: \"请输入当次督导人员姓名\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 照片上传 \"), _createVNode(_component_el_form_item, {\n        label: \"照片上传\",\n        prop: \"photo\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_upload, {\n          class: \"photo-uploader\",\n          action: $setup.uploadUrl,\n          headers: $setup.uploadHeaders,\n          \"show-file-list\": false,\n          \"on-success\": $setup.handlePhotoSuccess,\n          \"on-error\": $setup.handlePhotoError,\n          \"before-upload\": $setup.beforePhotoUpload,\n          accept: \"image/*\"\n        }, {\n          default: _withCtx(() => [$setup.formData.photo ? (_openBlock(), _createElementBlock(\"img\", {\n            key: 0,\n            src: $setup.getPhotoUrl($setup.formData.photo),\n            class: \"photo\"\n          }, null, 8 /* PROPS */, _hoisted_4)) : (_openBlock(), _createBlock(_component_el_icon, {\n            key: 1,\n            class: \"photo-uploader-icon\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_Plus)]),\n            _: 1 /* STABLE */\n          }))]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"action\", \"headers\", \"on-success\", \"on-error\", \"before-upload\"]), _cache[17] || (_cache[17] = _createElementVNode(\"div\", {\n          class: \"upload-tip\"\n        }, \"支持 jpg、png 格式，文件大小不超过 5MB\", -1 /* CACHED */))]),\n        _: 1 /* STABLE */,\n        __: [17]\n      }), _createCommentVNode(\" 操作按钮 \"), _createVNode(_component_el_form_item, null, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: $setup.submitForm\n        }, {\n          default: _withCtx(() => _cache[18] || (_cache[18] = [_createTextVNode(\"提交\")])),\n          _: 1 /* STABLE */,\n          __: [18]\n        }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n          onClick: $setup.resetForm\n        }, {\n          default: _withCtx(() => _cache[19] || (_cache[19] = [_createTextVNode(\"重置\")])),\n          _: 1 /* STABLE */,\n          __: [19]\n        }, 8 /* PROPS */, [\"onClick\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\", \"rules\"])])), [[_directive_loading, $setup.loading]])]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "$setup", "isEdit", "_component_el_button", "onClick", "goBack", "_cache", "_component_el_form", "ref", "model", "formData", "rules", "formRules", "_createCommentVNode", "_component_el_form_item", "label", "prop", "_component_el_input", "supervising_department", "$event", "placeholder", "case_topic", "teaching_form", "_component_el_select", "teacher_id", "filterable", "loading", "teacherLoading", "_Fragment", "_renderList", "teacherOptions", "item", "_createBlock", "_component_el_option", "key", "id", "name", "department", "value", "teacher_title", "student_name", "student_type", "_component_el_input_number", "average_score", "min", "max", "precision", "step", "highlights", "type", "rows", "shortcomings", "improvement_suggestions", "_component_el_radio_group", "competency_approved", "_component_el_radio", "supervisor_name", "_component_el_upload", "action", "uploadUrl", "headers", "uploadHeaders", "handlePhotoSuccess", "handlePhotoError", "beforePhotoUpload", "accept", "photo", "src", "getPhotoUrl", "_component_el_icon", "_component_Plus", "submitForm", "resetForm"], "sources": ["D:\\admin\\202506\\督导系统\\后台管理系统\\ms\\src\\views\\evaluations\\AddEvaluation.vue"], "sourcesContent": ["<template>\r\n  <div class=\"add-evaluation-container\">\r\n    <el-card class=\"box-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span class=\"title\">{{ isEdit ? '编辑督导评价' : '添加督导评价' }}</span>\r\n          <el-button @click=\"goBack\">返回列表</el-button>\r\n        </div>\r\n      </template>\r\n\r\n      <div v-loading=\"loading\">\r\n        <el-form\r\n          ref=\"evaluationFormRef\"\r\n          :model=\"formData\"\r\n          :rules=\"formRules\"\r\n          label-width=\"120px\"\r\n          label-position=\"right\"\r\n        >\r\n          <!-- 督导教研室 -->\r\n          <el-form-item label=\"督导教研室\" prop=\"supervising_department\">\r\n            <el-input v-model=\"formData.supervising_department\" placeholder=\"请输入督导教研室\" />\r\n          </el-form-item>\r\n\r\n          <!-- 病例/主题 -->\r\n          <el-form-item label=\"病例/主题\" prop=\"case_topic\">\r\n            <el-input v-model=\"formData.case_topic\" placeholder=\"请输入病例或主题\" />\r\n          </el-form-item>\r\n\r\n          <!-- 教学活动形式 -->\r\n          <el-form-item label=\"教学活动形式\" prop=\"teaching_form\">\r\n            <el-input v-model=\"formData.teaching_form\" placeholder=\"例如：小讲课、教学查房等\" />\r\n          </el-form-item>\r\n\r\n          <!-- 带教老师 -->\r\n          <el-form-item label=\"带教老师\" prop=\"teacher_id\">\r\n            <el-select \r\n              v-model=\"formData.teacher_id\" \r\n              placeholder=\"请选择带教老师\" \r\n              filterable \r\n              :loading=\"teacherLoading\"\r\n            >\r\n              <el-option \r\n                v-for=\"item in teacherOptions\" \r\n                :key=\"item.id\" \r\n                :label=\"`${item.name || '未命名'} (${item.department || '无部门'})`\" \r\n                :value=\"item.id\" \r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n\r\n          <!-- 带教老师职称 -->\r\n          <el-form-item label=\"带教老师职称\" prop=\"teacher_title\">\r\n            <el-input v-model=\"formData.teacher_title\" placeholder=\"请输入带教老师职称\" />\r\n          </el-form-item>\r\n\r\n          <!-- 学员姓名 -->\r\n          <el-form-item label=\"学员姓名\" prop=\"student_name\">\r\n            <el-input v-model=\"formData.student_name\" placeholder=\"请输入学员姓名\" />\r\n          </el-form-item>\r\n\r\n          <!-- 学员类别 -->\r\n          <el-form-item label=\"学员类别\" prop=\"student_type\">\r\n            <el-select v-model=\"formData.student_type\" placeholder=\"请选择学员类别\">\r\n              <el-option label=\"实习生\" value=\"实习生\" />\r\n              <el-option label=\"进修生\" value=\"进修生\" />\r\n              <el-option label=\"低年资轮转\" value=\"低年资轮转\" />\r\n            </el-select>\r\n          </el-form-item>\r\n\r\n          <!-- 平均分 -->\r\n          <el-form-item label=\"评分\" prop=\"average_score\">\r\n            <el-input-number \r\n              v-model=\"formData.average_score\" \r\n              :min=\"0\" \r\n              :max=\"100\" \r\n              :precision=\"1\" \r\n              :step=\"1\" \r\n              controls-position=\"right\" \r\n            />\r\n            <span class=\"score-hint\">（0-100分）</span>\r\n          </el-form-item>\r\n\r\n          <!-- 亮点 -->\r\n          <el-form-item label=\"亮点\" prop=\"highlights\">\r\n            <el-input \r\n              v-model=\"formData.highlights\" \r\n              type=\"textarea\" \r\n              :rows=\"3\" \r\n              placeholder=\"请输入教学亮点\" \r\n            />\r\n          </el-form-item>\r\n\r\n          <!-- 不足 -->\r\n          <el-form-item label=\"不足\" prop=\"shortcomings\">\r\n            <el-input \r\n              v-model=\"formData.shortcomings\" \r\n              type=\"textarea\" \r\n              :rows=\"3\" \r\n              placeholder=\"请输入教学不足之处\" \r\n            />\r\n          </el-form-item>\r\n\r\n          <!-- 改进建议 -->\r\n          <el-form-item label=\"改进建议\" prop=\"improvement_suggestions\">\r\n            <el-input \r\n              v-model=\"formData.improvement_suggestions\" \r\n              type=\"textarea\" \r\n              :rows=\"3\" \r\n              placeholder=\"请输入改进建议\" \r\n            />\r\n          </el-form-item>\r\n\r\n          <!-- 能力认定 -->\r\n          <el-form-item label=\"能力认定\" prop=\"competency_approved\">\r\n            <el-radio-group v-model=\"formData.competency_approved\">\r\n              <el-radio :label=\"1\">同意</el-radio>\r\n              <el-radio :label=\"0\">不同意</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n\r\n          <!-- 当次督导人员 -->\r\n          <el-form-item label=\"当次督导人员\" prop=\"supervisor_name\">\r\n            <el-input v-model=\"formData.supervisor_name\" placeholder=\"请输入当次督导人员姓名\" />\r\n          </el-form-item>\r\n\r\n          <!-- 照片上传 -->\r\n          <el-form-item label=\"照片上传\" prop=\"photo\">\r\n            <el-upload\r\n              class=\"photo-uploader\"\r\n              :action=\"uploadUrl\"\r\n              :headers=\"uploadHeaders\"\r\n              :show-file-list=\"false\"\r\n              :on-success=\"handlePhotoSuccess\"\r\n              :on-error=\"handlePhotoError\"\r\n              :before-upload=\"beforePhotoUpload\"\r\n              accept=\"image/*\"\r\n            >\r\n              <img v-if=\"formData.photo\" :src=\"getPhotoUrl(formData.photo)\" class=\"photo\" />\r\n              <el-icon v-else class=\"photo-uploader-icon\"><Plus /></el-icon>\r\n            </el-upload>\r\n            <div class=\"upload-tip\">支持 jpg、png 格式，文件大小不超过 5MB</div>\r\n          </el-form-item>\r\n\r\n          <!-- 操作按钮 -->\r\n          <el-form-item>\r\n            <el-button type=\"primary\" @click=\"submitForm\">提交</el-button>\r\n            <el-button @click=\"resetForm\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, computed, onMounted, watch } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nimport { Plus } from '@element-plus/icons-vue'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'AddEvaluation',\r\n  components: {\r\n    Plus\r\n  },\r\n  setup() {\r\n    const route = useRoute()\r\n    const router = useRouter()\r\n    const evaluationFormRef = ref(null)\r\n    let token = localStorage.getItem('token')\r\n    if (token) {\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`\r\n    }\r\n    \r\n    // 上传相关配置\r\n    const uploadUrl = 'http://localhost:3000/api/upload/photo'\r\n    const uploadHeaders = {\r\n      'Authorization': `Bearer ${token}`\r\n    }\r\n    \r\n    // 基础数据\r\n    const loading = ref(false)\r\n    const teacherLoading = ref(false)\r\n    const teacherOptions = ref([])\r\n    const allTeachers = ref([])\r\n    \r\n    // 是否为编辑模式\r\n    const evaluationId = route.query.id\r\n    const isEdit = computed(() => !!evaluationId)\r\n    \r\n    // 表单数据\r\n    const formData = reactive({\r\n      supervising_department: '',\r\n      case_topic: '',\r\n      teaching_form: '',\r\n      teacher_id: '',\r\n      teacher_title: '',\r\n      student_name: '',\r\n      student_type: '实习生',\r\n      average_score: 7.0,\r\n      highlights: '',\r\n      shortcomings: '',\r\n      improvement_suggestions: '',\r\n      competency_approved: 1,\r\n      supervisor_name: '',\r\n      photo: '',\r\n      evaluator_id: 1\r\n    })\r\n    \r\n    // 表单验证规则\r\n    const formRules = {\r\n      supervising_department: [\r\n        { required: true, message: '请输入督导教研室', trigger: 'blur' }\r\n      ],\r\n      case_topic: [\r\n        { required: true, message: '请输入病例/主题', trigger: 'blur' }\r\n      ],\r\n      teaching_form: [\r\n        { required: true, message: '请输入教学活动形式', trigger: 'blur' }\r\n      ],\r\n      teacher_id: [\r\n        { required: true, message: '请选择带教老师', trigger: 'change' }\r\n      ],\r\n      teacher_title: [\r\n        { required: true, message: '请输入带教老师职称', trigger: 'blur' }\r\n      ],\r\n      student_name: [\r\n        { required: true, message: '请输入学员姓名', trigger: 'blur' }\r\n      ],\r\n      student_type: [\r\n        { required: true, message: '请选择学员类别', trigger: 'change' }\r\n      ],\r\n      average_score: [\r\n        { required: true, message: '请输入平均分', trigger: 'change' }\r\n      ],\r\n      supervisor_name: [\r\n        { required: true, message: '请输入当次督导人员', trigger: 'blur' }\r\n      ]\r\n    }\r\n\r\n    // 照片上传相关方法\r\n    const handlePhotoSuccess = (response) => {\r\n      if (response.success) {\r\n        formData.photo = response.data.path\r\n        ElMessage.success('照片上传成功')\r\n      } else {\r\n        ElMessage.error('照片上传失败')\r\n      }\r\n    }\r\n\r\n    const handlePhotoError = () => {\r\n      ElMessage.error('照片上传失败')\r\n    }\r\n\r\n    const beforePhotoUpload = (file) => {\r\n      const isImage = file.type.startsWith('image/')\r\n      const isLt5M = file.size / 1024 / 1024 < 5\r\n\r\n      if (!isImage) {\r\n        ElMessage.error('只能上传图片文件!')\r\n        return false\r\n      }\r\n      if (!isLt5M) {\r\n        ElMessage.error('图片大小不能超过 5MB!')\r\n        return false\r\n      }\r\n      return true\r\n    }\r\n\r\n    const getPhotoUrl = (path) => {\r\n      if (!path) return ''\r\n      if (path.startsWith('http')) return path\r\n      return `http://localhost:3000${path}`\r\n    }\r\n\r\n    // 监听教师ID变化自动填充职称\r\n    watch(() => formData.teacher_id, (newValue) => {\r\n      if (newValue) {\r\n        const selectedTeacher = allTeachers.value.find(teacher => teacher.id === newValue)\r\n        if (selectedTeacher && selectedTeacher.title) {\r\n          formData.teacher_title = selectedTeacher.title\r\n        }\r\n      }\r\n    })\r\n    \r\n    // 获取教师列表\r\n    const fetchTeachers = async () => {\r\n      teacherLoading.value = true\r\n      try {\r\n        const response = await axios.get('http://localhost:3000/api/teachers')\r\n        if (response.data && response.data.data) {\r\n          allTeachers.value = response.data.data\r\n          teacherOptions.value = response.data.data\r\n        } else {\r\n          teacherOptions.value = []\r\n          console.error('获取教师列表返回格式有误')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取教师列表失败:', error)\r\n        ElMessage.error('获取教师列表失败')\r\n      } finally {\r\n        teacherLoading.value = false\r\n      }\r\n    }\r\n    \r\n    // 获取评价详情\r\n    const fetchEvaluationDetail = async () => {\r\n      loading.value = true\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/evaluations/${evaluationId}`)\r\n        const data = response.data.data\r\n        \r\n        // 填充表单数据\r\n        Object.keys(formData).forEach(key => {\r\n          if (key in data) {\r\n            formData[key] = data[key]\r\n          }\r\n        })\r\n      } catch (error) {\r\n        console.error('获取评价详情失败:', error)\r\n        ElMessage.error('获取评价详情失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 提交表单\r\n    const submitForm = async () => {\r\n      if (!evaluationFormRef.value) return\r\n      \r\n      await evaluationFormRef.value.validate(async (valid) => {\r\n        if (valid) {\r\n          loading.value = true\r\n          try {\r\n            if (isEdit.value) {\r\n              // 编辑模式\r\n              await axios.put(`http://localhost:3000/api/evaluations/${evaluationId}`, formData)\r\n              ElMessage.success('督导评价更新成功')\r\n            } else {\r\n              // 添加模式\r\n              await axios.post('http://localhost:3000/api/evaluations', formData)\r\n              ElMessage.success('督导评价添加成功')\r\n            }\r\n            \r\n            // 跳转回列表页\r\n            router.push('/evaluations/list')\r\n          } catch (error) {\r\n            console.error('操作失败:', error)\r\n            ElMessage.error('操作失败')\r\n          } finally {\r\n            loading.value = false\r\n          }\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    }\r\n    \r\n    // 重置表单\r\n    const resetForm = () => {\r\n      if (evaluationFormRef.value) {\r\n        evaluationFormRef.value.resetFields()\r\n      }\r\n    }\r\n    \r\n    // 返回列表\r\n    const goBack = () => {\r\n      router.push('/evaluations/list')\r\n    }\r\n    \r\n    // 生命周期钩子\r\n    onMounted(async () => {\r\n      // 初始化教师下拉列表\r\n      await fetchTeachers()\r\n      \r\n      // 如果是编辑模式，加载评价数据\r\n      if (isEdit.value) {\r\n        await fetchEvaluationDetail()\r\n      }\r\n    })\r\n    \r\n    return {\r\n      loading,\r\n      teacherLoading,\r\n      teacherOptions,\r\n      evaluationFormRef,\r\n      formData,\r\n      formRules,\r\n      isEdit,\r\n      uploadUrl,\r\n      uploadHeaders,\r\n      submitForm,\r\n      resetForm,\r\n      goBack,\r\n      handlePhotoSuccess,\r\n      handlePhotoError,\r\n      beforePhotoUpload,\r\n      getPhotoUrl\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.add-evaluation-container {\r\n  padding: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.score-hint {\r\n  margin-left: 10px;\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.photo-uploader .photo {\r\n  width: 178px;\r\n  height: 178px;\r\n  display: block;\r\n  object-fit: cover;\r\n  border-radius: 6px;\r\n}\r\n\r\n.photo-uploader .el-upload {\r\n  border: 1px dashed var(--el-border-color);\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n  transition: var(--el-transition-duration-fast);\r\n}\r\n\r\n.photo-uploader .el-upload:hover {\r\n  border-color: var(--el-color-primary);\r\n}\r\n\r\n.photo-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  width: 178px;\r\n  height: 178px;\r\n  text-align: center;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.upload-tip {\r\n  margin-top: 8px;\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n</style> \r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA0B;;EAG1BA,KAAK,EAAC;AAAa;;EAChBA,KAAK,EAAC;AAAO;;;;;;;;;;;;;;;;;uBAJ3BC,mBAAA,CAsJM,OAtJNC,UAsJM,GArJJC,YAAA,CAoJUC,kBAAA;IApJDJ,KAAK,EAAC;EAAU;IACZK,MAAM,EAAAC,QAAA,CACf,MAGM,CAHNC,mBAAA,CAGM,OAHNC,UAGM,GAFJD,mBAAA,CAA6D,QAA7DE,UAA6D,EAAAC,gBAAA,CAAtCC,MAAA,CAAAC,MAAM,wCAC7BT,YAAA,CAA2CU,oBAAA;MAA/BC,OAAK,EAAEH,MAAA,CAAAI;IAAM;wBAAE,MAAIC,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;;sBAInC,MA2IM,C,+BA3INf,mBAAA,CA2IM,cA1IJE,YAAA,CAyIUc,kBAAA;MAxIRC,GAAG,EAAC,mBAAmB;MACtBC,KAAK,EAAER,MAAA,CAAAS,QAAQ;MACfC,KAAK,EAAEV,MAAA,CAAAW,SAAS;MACjB,aAAW,EAAC,OAAO;MACnB,gBAAc,EAAC;;wBAEf,MAAc,CAAdC,mBAAA,WAAc,EACdpB,YAAA,CAEeqB,uBAAA;QAFDC,KAAK,EAAC,OAAO;QAACC,IAAI,EAAC;;0BAC/B,MAA6E,CAA7EvB,YAAA,CAA6EwB,mBAAA;sBAA1DhB,MAAA,CAAAS,QAAQ,CAACQ,sBAAsB;qEAA/BjB,MAAA,CAAAS,QAAQ,CAACQ,sBAAsB,GAAAC,MAAA;UAAEC,WAAW,EAAC;;;UAGlEP,mBAAA,WAAc,EACdpB,YAAA,CAEeqB,uBAAA;QAFDC,KAAK,EAAC,OAAO;QAACC,IAAI,EAAC;;0BAC/B,MAAiE,CAAjEvB,YAAA,CAAiEwB,mBAAA;sBAA9ChB,MAAA,CAAAS,QAAQ,CAACW,UAAU;qEAAnBpB,MAAA,CAAAS,QAAQ,CAACW,UAAU,GAAAF,MAAA;UAAEC,WAAW,EAAC;;;UAGtDP,mBAAA,YAAe,EACfpB,YAAA,CAEeqB,uBAAA;QAFDC,KAAK,EAAC,QAAQ;QAACC,IAAI,EAAC;;0BAChC,MAAwE,CAAxEvB,YAAA,CAAwEwB,mBAAA;sBAArDhB,MAAA,CAAAS,QAAQ,CAACY,aAAa;qEAAtBrB,MAAA,CAAAS,QAAQ,CAACY,aAAa,GAAAH,MAAA;UAAEC,WAAW,EAAC;;;UAGzDP,mBAAA,UAAa,EACbpB,YAAA,CAceqB,uBAAA;QAdDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAYY,CAZZvB,YAAA,CAYY8B,oBAAA;sBAXDtB,MAAA,CAAAS,QAAQ,CAACc,UAAU;qEAAnBvB,MAAA,CAAAS,QAAQ,CAACc,UAAU,GAAAL,MAAA;UAC5BC,WAAW,EAAC,SAAS;UACrBK,UAAU,EAAV,EAAU;UACTC,OAAO,EAAEzB,MAAA,CAAA0B;;4BAGR,MAA8B,E,kBADhCpC,mBAAA,CAKEqC,SAAA,QAAAC,WAAA,CAJe5B,MAAA,CAAA6B,cAAc,EAAtBC,IAAI;iCADbC,YAAA,CAKEC,oBAAA;cAHCC,GAAG,EAAEH,IAAI,CAACI,EAAE;cACZpB,KAAK,KAAKgB,IAAI,CAACK,IAAI,cAAcL,IAAI,CAACM,UAAU;cAChDC,KAAK,EAAEP,IAAI,CAACI;;;;;;UAKnBtB,mBAAA,YAAe,EACfpB,YAAA,CAEeqB,uBAAA;QAFDC,KAAK,EAAC,QAAQ;QAACC,IAAI,EAAC;;0BAChC,MAAqE,CAArEvB,YAAA,CAAqEwB,mBAAA;sBAAlDhB,MAAA,CAAAS,QAAQ,CAAC6B,aAAa;qEAAtBtC,MAAA,CAAAS,QAAQ,CAAC6B,aAAa,GAAApB,MAAA;UAAEC,WAAW,EAAC;;;UAGzDP,mBAAA,UAAa,EACbpB,YAAA,CAEeqB,uBAAA;QAFDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAAkE,CAAlEvB,YAAA,CAAkEwB,mBAAA;sBAA/ChB,MAAA,CAAAS,QAAQ,CAAC8B,YAAY;qEAArBvC,MAAA,CAAAS,QAAQ,CAAC8B,YAAY,GAAArB,MAAA;UAAEC,WAAW,EAAC;;;UAGxDP,mBAAA,UAAa,EACbpB,YAAA,CAMeqB,uBAAA;QANDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAIY,CAJZvB,YAAA,CAIY8B,oBAAA;sBAJQtB,MAAA,CAAAS,QAAQ,CAAC+B,YAAY;qEAArBxC,MAAA,CAAAS,QAAQ,CAAC+B,YAAY,GAAAtB,MAAA;UAAEC,WAAW,EAAC;;4BACrD,MAAqC,CAArC3B,YAAA,CAAqCwC,oBAAA;YAA1BlB,KAAK,EAAC,KAAK;YAACuB,KAAK,EAAC;cAC7B7C,YAAA,CAAqCwC,oBAAA;YAA1BlB,KAAK,EAAC,KAAK;YAACuB,KAAK,EAAC;cAC7B7C,YAAA,CAAyCwC,oBAAA;YAA9BlB,KAAK,EAAC,OAAO;YAACuB,KAAK,EAAC;;;;;UAInCzB,mBAAA,SAAY,EACZpB,YAAA,CAUeqB,uBAAA;QAVDC,KAAK,EAAC,IAAI;QAACC,IAAI,EAAC;;0BAC5B,MAOE,CAPFvB,YAAA,CAOEiD,0BAAA;sBANSzC,MAAA,CAAAS,QAAQ,CAACiC,aAAa;qEAAtB1C,MAAA,CAAAS,QAAQ,CAACiC,aAAa,GAAAxB,MAAA;UAC9ByB,GAAG,EAAE,CAAC;UACNC,GAAG,EAAE,GAAG;UACRC,SAAS,EAAE,CAAC;UACZC,IAAI,EAAE,CAAC;UACR,mBAAiB,EAAC;6EAEpBlD,mBAAA,CAAwC;UAAlCP,KAAK,EAAC;QAAY,GAAC,UAAQ,oB;;;UAGnCuB,mBAAA,QAAW,EACXpB,YAAA,CAOeqB,uBAAA;QAPDC,KAAK,EAAC,IAAI;QAACC,IAAI,EAAC;;0BAC5B,MAKE,CALFvB,YAAA,CAKEwB,mBAAA;sBAJShB,MAAA,CAAAS,QAAQ,CAACsC,UAAU;qEAAnB/C,MAAA,CAAAS,QAAQ,CAACsC,UAAU,GAAA7B,MAAA;UAC5B8B,IAAI,EAAC,UAAU;UACdC,IAAI,EAAE,CAAC;UACR9B,WAAW,EAAC;;;UAIhBP,mBAAA,QAAW,EACXpB,YAAA,CAOeqB,uBAAA;QAPDC,KAAK,EAAC,IAAI;QAACC,IAAI,EAAC;;0BAC5B,MAKE,CALFvB,YAAA,CAKEwB,mBAAA;sBAJShB,MAAA,CAAAS,QAAQ,CAACyC,YAAY;qEAArBlD,MAAA,CAAAS,QAAQ,CAACyC,YAAY,GAAAhC,MAAA;UAC9B8B,IAAI,EAAC,UAAU;UACdC,IAAI,EAAE,CAAC;UACR9B,WAAW,EAAC;;;UAIhBP,mBAAA,UAAa,EACbpB,YAAA,CAOeqB,uBAAA;QAPDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAKE,CALFvB,YAAA,CAKEwB,mBAAA;sBAJShB,MAAA,CAAAS,QAAQ,CAAC0C,uBAAuB;uEAAhCnD,MAAA,CAAAS,QAAQ,CAAC0C,uBAAuB,GAAAjC,MAAA;UACzC8B,IAAI,EAAC,UAAU;UACdC,IAAI,EAAE,CAAC;UACR9B,WAAW,EAAC;;;UAIhBP,mBAAA,UAAa,EACbpB,YAAA,CAKeqB,uBAAA;QALDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAGiB,CAHjBvB,YAAA,CAGiB4D,yBAAA;sBAHQpD,MAAA,CAAAS,QAAQ,CAAC4C,mBAAmB;uEAA5BrD,MAAA,CAAAS,QAAQ,CAAC4C,mBAAmB,GAAAnC,MAAA;;4BACnD,MAAkC,CAAlC1B,YAAA,CAAkC8D,mBAAA;YAAvBxC,KAAK,EAAE;UAAC;8BAAE,MAAET,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;cACvBb,YAAA,CAAmC8D,mBAAA;YAAxBxC,KAAK,EAAE;UAAC;8BAAE,MAAGT,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;;;;;UAI5BO,mBAAA,YAAe,EACfpB,YAAA,CAEeqB,uBAAA;QAFDC,KAAK,EAAC,QAAQ;QAACC,IAAI,EAAC;;0BAChC,MAAyE,CAAzEvB,YAAA,CAAyEwB,mBAAA;sBAAtDhB,MAAA,CAAAS,QAAQ,CAAC8C,eAAe;uEAAxBvD,MAAA,CAAAS,QAAQ,CAAC8C,eAAe,GAAArC,MAAA;UAAEC,WAAW,EAAC;;;UAG3DP,mBAAA,UAAa,EACbpB,YAAA,CAeeqB,uBAAA;QAfDC,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC;;0BAC9B,MAYY,CAZZvB,YAAA,CAYYgE,oBAAA;UAXVnE,KAAK,EAAC,gBAAgB;UACrBoE,MAAM,EAAEzD,MAAA,CAAA0D,SAAS;UACjBC,OAAO,EAAE3D,MAAA,CAAA4D,aAAa;UACtB,gBAAc,EAAE,KAAK;UACrB,YAAU,EAAE5D,MAAA,CAAA6D,kBAAkB;UAC9B,UAAQ,EAAE7D,MAAA,CAAA8D,gBAAgB;UAC1B,eAAa,EAAE9D,MAAA,CAAA+D,iBAAiB;UACjCC,MAAM,EAAC;;4BATW,MAG1B,CAQmBhE,MAAA,CAAAS,QAAQ,CAACwD,KAAK,I,cAAzB3E,mBAAA,CAA8E;;YAAlD4E,GAAG,EAAElE,MAAA,CAAAmE,WAAW,CAACnE,MAAA,CAAAS,QAAQ,CAACwD,KAAK;YAAG5E,KAAK,EAAC;gEACpE0C,YAAA,CAA8DqC,kBAAA;;YAA9C/E,KAAK,EAAC;;8BAAsB,MAAQ,CAARG,YAAA,CAAQ6E,eAAA,E;;;;yHAEtDzE,mBAAA,CAAuD;UAAlDP,KAAK,EAAC;QAAY,GAAC,2BAAyB,oB;;;UAGnDuB,mBAAA,UAAa,EACbpB,YAAA,CAGeqB,uBAAA;0BAFb,MAA4D,CAA5DrB,YAAA,CAA4DU,oBAAA;UAAjD8C,IAAI,EAAC,SAAS;UAAE7C,OAAK,EAAEH,MAAA,CAAAsE;;4BAAY,MAAEjE,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;wCAChDb,YAAA,CAA4CU,oBAAA;UAAhCC,OAAK,EAAEH,MAAA,CAAAuE;QAAS;4BAAE,MAAElE,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;;;oEAxItBL,MAAA,CAAAyB,OAAO,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}