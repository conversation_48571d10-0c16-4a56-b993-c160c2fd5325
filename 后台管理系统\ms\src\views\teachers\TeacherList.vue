<template>
  <div class="teacher-list-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="title">教师管理</span>
          <div>
            <el-button type="success" @click="downloadTemplate">下载模板</el-button>
            <el-button type="danger" @click="openImportDialog">一键导入</el-button>
            <el-button type="primary" @click="openDialog()">添加教师</el-button>
          </div>
          
        </div>
      </template>

      <!-- 搜索区域 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="姓名">
          <el-input v-model="searchForm.name" placeholder="教师姓名" clearable />
        </el-form-item>
        <el-form-item label="科室">
          <el-input v-model="searchForm.department" placeholder="所属科室" clearable />
        </el-form-item>
        <el-form-item label="在聘状态">
          <el-select v-model="searchForm.is_employed" placeholder="是否在聘" clearable style="width: 120px">
            <el-option label="在聘" :value="1" />
            <el-option label="不在聘" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="teacherList"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID"  />
        <el-table-column prop="name" label="姓名"  />
        <el-table-column prop="gender" label="性别" />
        <el-table-column prop="department" label="科室" />
        <el-table-column prop="school" label="学校" />
        <el-table-column prop="major" label="专业" />
        <el-table-column prop="education" label="学历" />
        <el-table-column label="在聘状态" >
          <template #default="scope">
            <el-tag :type="scope.row.is_employed ? 'success' : 'danger'">
              {{ scope.row.is_employed ? '在聘' : '不在聘' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="employment_period" label="聘期" min-width="120" />
        <el-table-column label="照片" width="80">
          <template #default="scope">
            <el-image
              v-if="scope.row.photo"
              :src="`${baseUrl}${scope.row.photo}`"
              :preview-src-list="[`${baseUrl}${scope.row.photo}`]"
              fit="cover"
              style="width: 50px; height: 50px"
            />
            <el-avatar v-else :size="50" icon="UserFilled" />
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="200">
          <template #default="scope">
            <el-button size="small" @click="viewDetails(scope.row.id)">详情</el-button>
            <el-button size="small" type="primary" @click="openDialog(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 教师表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="formData.id ? '编辑教师' : '添加教师'"
      width="50%"
      destroy-on-close
    >
      <el-form
        ref="teacherFormRef"
        :model="formData"
        :rules="formRules"
        label-width="80px"
        label-position="right"
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="formData.name" placeholder="请输入姓名" />
        </el-form-item>
        
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="formData.gender">
            <el-radio label="男">男</el-radio>
            <el-radio label="女">女</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="科室" prop="department">
          <el-input v-model="formData.department" placeholder="请输入科室" />
        </el-form-item>
        
        <el-form-item label="学校" prop="school">
          <el-input v-model="formData.school" placeholder="请输入学校" />
        </el-form-item>
        
        <el-form-item label="专业" prop="major">
          <el-input v-model="formData.major" placeholder="请输入专业" />
        </el-form-item>
        
        <el-form-item label="学历" prop="education">
          <el-select v-model="formData.education" placeholder="请选择学历" style="width: 100%">
            <el-option label="博士" value="博士" />
            <el-option label="硕士" value="硕士" />
            <el-option label="本科" value="本科" />
            <el-option label="专科" value="专科" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="在聘状态" prop="is_employed">
          <el-switch
            v-model="formData.is_employed"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        
        <el-form-item label="聘期" prop="employment_period" v-if="formData.is_employed === 1">
          <el-input v-model="formData.employment_period" placeholder="例如：2023年6月-2026年5月" />
        </el-form-item>
        
        <el-form-item label="联系方式" prop="phone">
          <el-input v-model="formData.phone" placeholder="请输入联系方式" />
        </el-form-item>
        
        <el-form-item label="照片" prop="photo">
          <el-upload
            class="avatar-uploader"
            :action="`${baseUrl}/api/teachers/upload/photo`"
            :headers="uploadHeaders"
            name="photo"
            :show-file-list="false"
            :before-upload="beforePhotoUpload"
            :on-success="handlePhotoSuccess"
            :on-error="handlePhotoError"
          >
            <img v-if="photoPreview" :src="photoPreview" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">点击上传照片，JPG/PNG格式，小于2MB</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- Excel导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="批量导入教师"
      width="50%"
      destroy-on-close
    >
      <div class="import-content">
        <el-alert
          title="导入说明"
          type="info"
          :closable="false"
          style="margin-bottom: 20px"
        >
          <template #default>
            <div>
              <p>1. 请先下载Excel模板，按照模板格式填写教师信息</p>
              <p>2. 必填字段：姓名、性别、科室、学校、专业、学历</p>
              <p>3. 性别只能填写"男"或"女"</p>
              <p>4. 是否在聘可填写：是/否、true/false、1/0</p>
              <p>5. 支持.xlsx和.xls格式，文件大小不超过10MB</p>
            </div>
          </template>
        </el-alert>

        <el-upload
          ref="uploadRef"
          class="upload-demo"
          :action="`${baseUrl}/api/teachers/import/excel`"
          :headers="uploadHeaders"
          :before-upload="beforeExcelUpload"
          :on-success="handleImportSuccess"
          :on-error="handleImportError"
          :on-change="handleFileChange"
          :file-list="fileList"
          :auto-upload="false"
          accept=".xlsx,.xls"
          :limit="1"
        >
          <el-button type="primary">选择Excel文件</el-button>
          <template #tip>
            <div class="el-upload__tip">
              只能上传xlsx/xls文件，且不超过10MB
            </div>
          </template>
        </el-upload>

        <!-- 导入结果显示 -->
        <div v-if="importResult" class="import-result" style="margin-top: 20px">
          <el-alert
            :title="`导入完成！成功 ${importResult.success} 条，失败 ${importResult.failed} 条`"
            :type="importResult.failed > 0 ? 'warning' : 'success'"
            :closable="false"
          />

          <!-- 失败记录详情 -->
          <div v-if="importResult.failedRecords && importResult.failedRecords.length > 0" style="margin-top: 15px">
            <el-collapse>
              <el-collapse-item title="查看失败记录" name="failed">
                <el-table :data="importResult.failedRecords" border size="small">
                  <el-table-column prop="row" label="行号" width="80" />
                  <el-table-column prop="error" label="错误原因" />
                </el-table>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleUpload" :loading="uploading">开始导入</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'
import { Plus } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

export default {
  name: 'TeacherList',
  components: { Plus },
  setup() {
    // 路由器
    const router = useRouter()
    let token = localStorage.getItem('token')
    if (token) {
      axios.defaults.headers['Authorization'] = `Bearer ${token}`
    }
    console.log(axios.defaults.headers['Authorization'])
    // API基础URL
    const baseUrl = 'http://localhost:3000'
    
    // 上传头像的headers
    const uploadHeaders = {
      // 如果需要认证可以在这里添加
    }
    
    // 基础数据
    const loading = ref(false)
    const dialogVisible = ref(false)
    const teacherFormRef = ref(null)
    const teacherList = ref([])
    const total = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(10)
    const photoPreview = ref('')

    // 导入相关数据
    const importDialogVisible = ref(false)
    const uploadRef = ref(null)
    const uploading = ref(false)
    const fileList = ref([])
    const importResult = ref(null)
    
    // 搜索表单
    const searchForm = reactive({
      name: '',
      department: '',
      is_employed: ''
    })
    
    // 表单数据
    const formData = reactive({
      id: '',
      name: '',
      gender: '男',
      department: '',
      school: '',
      major: '',
      education: '',
      is_employed: 1,
      employment_period: '',
      phone: '',
      photo: ''
    })
    
    // 表单校验规则
    const formRules = reactive({
      name: [
        { required: true, message: '请输入姓名', trigger: 'blur' },
        { min: 2, max: 10, message: '长度在 2 到 10 个字符', trigger: 'blur' }
      ],
      gender: [
        { required: true, message: '请选择性别', trigger: 'change' }
      ],
      department: [
        { required: true, message: '请输入科室', trigger: 'blur' }
      ],
      school: [
        { required: true, message: '请输入学校', trigger: 'blur' }
      ],
      major: [
        { required: true, message: '请输入专业', trigger: 'blur' }
      ],
      education: [
        { required: true, message: '请选择学历', trigger: 'change' }
      ]
    })
    
    // 监听表单数据变化，更新照片预览
    watch(() => formData.photo, (newVal) => {
      if (newVal) {
        if (newVal.startsWith('http')) {
          photoPreview.value = newVal
        } else {
          photoPreview.value = `${baseUrl}${newVal}`
        }
      } else {
        photoPreview.value = ''
      }
    }, { immediate: true })
    
    // 生命周期钩子
    onMounted(() => {
      fetchTeachers()
    })
    
    // 获取教师列表
    const fetchTeachers = async () => {
      loading.value = true
      try {
        // 构建查询参数
        const params = new URLSearchParams()
        if (searchForm.name) params.append('name', searchForm.name)
        if (searchForm.department) params.append('department', searchForm.department)
        if (searchForm.is_employed !== '') params.append('is_employed', searchForm.is_employed)
        params.append('page', currentPage.value)
        params.append('limit', pageSize.value)
        
        const response = await axios.get(`${baseUrl}/api/teachers`, { params })
        teacherList.value = response.data.data
        total.value = response.data.count
      } catch (error) {
        console.error('获取教师列表失败:', error)
        ElMessage.error('获取教师列表失败')
      } finally {
        loading.value = false
      }
    }
    
    // 搜索操作
    const handleSearch = () => {
      currentPage.value = 1
      fetchTeachers()
    }
    
    // 重置搜索
    const resetSearch = () => {
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
      })
      currentPage.value = 1
      fetchTeachers()
    }
    
    // 分页操作
    const handleSizeChange = (val) => {
      pageSize.value = val
      fetchTeachers()
    }
    
    const handleCurrentChange = (val) => {
      currentPage.value = val
      fetchTeachers()
    }
    
    // 打开对话框
    const openDialog = (row) => {
      if (row) {
        // 编辑模式
        Object.keys(formData).forEach(key => {
          formData[key] = row[key]
        })
        // 更新照片预览
        if (row.photo) {
          photoPreview.value = `${baseUrl}${row.photo}`
        }
      } else {
        // 新增模式
        Object.keys(formData).forEach(key => {
          formData[key] = key === 'gender' ? '男' : 
                          key === 'is_employed' ? 1 : ''
        })
        photoPreview.value = ''
      }
      dialogVisible.value = true
    }
    
    // 照片上传前验证
    const beforePhotoUpload = (file) => {
      const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isJPGOrPNG) {
        ElMessage.error('上传头像图片只能是 JPG 或 PNG 格式!')
        return false
      }
      
      if (!isLt2M) {
        ElMessage.error('上传头像图片大小不能超过 2MB!')
        return false
      }
      
      // 创建临时预览
      photoPreview.value = URL.createObjectURL(file)
      return true
    }
    
    // 照片上传成功回调
    const handlePhotoSuccess = (response) => {
      console.log('照片上传成功响应:', response)
      if (response.success) {
        formData.photo = response.path
        console.log('设置照片路径:', formData.photo)
        ElMessage.success('照片上传成功')
      } else {
        ElMessage.error(response.message || '照片上传失败')
      }
    }
 
    // 照片上传失败回调
    const handlePhotoError = (err) => {
      console.error('照片上传失败:', err)
      ElMessage.error('照片上传失败，请检查网络连接')
    }
    
    // 提交表单
    const submitForm = async () => {
      if (!teacherFormRef.value) return
      
      await teacherFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            console.log('提交表单数据:', formData)
            
            // 创建表单数据对象
            const formDataToSubmit = new FormData()
            
            // 添加所有字段到FormData
            Object.keys(formData).forEach(key => {
              if (formData[key] !== null && formData[key] !== undefined && formData[key] !== '') {
                formDataToSubmit.append(key, formData[key])
              }
            })
            
            const config = {
              headers: {
                'Content-Type': 'multipart/form-data'
              }
            }
            
            if (formData.id) {
              // 编辑
              await axios.put(`${baseUrl}/api/teachers/${formData.id}`, formDataToSubmit, config)
              ElMessage.success('教师信息更新成功')
            } else {
              // 新增
              await axios.post(`${baseUrl}/api/teachers`, formDataToSubmit, config)
              ElMessage.success('教师添加成功')
            }
            
            dialogVisible.value = false
            fetchTeachers()
          } catch (error) {
            console.error('操作失败:', error)
            ElMessage.error(error.response?.data?.message || '操作失败')
          }
        } else {
          return false
        }
      })
    }
    
    // 删除教师
    const handleDelete = (row) => {
      ElMessageBox.confirm(
        `确定要删除教师 ${row.name} 吗?`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(async () => {
          try {
            await axios.delete(`${baseUrl}/api/teachers/${row.id}`)
            ElMessage.success('删除成功')
            // 如果当前页只有一条数据且不是第一页，删除后跳转到上一页
            if (teacherList.value.length === 1 && currentPage.value > 1) {
              currentPage.value--
            }
            fetchTeachers()
          } catch (error) {
            console.error('删除失败:', error)
            ElMessage.error(error.response?.data?.message || '删除失败')
          }
        })
        .catch(() => {
          ElMessage.info('已取消删除')
        })
    }
    
    // 查看详情
    const viewDetails = (id) => {
      // 跳转到详情页面
      router.push(`/teachers/detail/${id}`)
    }

    // 下载模板
    const downloadTemplate = async () => {
      try {
        const response = await axios.get(`${baseUrl}/api/teachers/import/template`, {
          responseType: 'blob'
        })

        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', 'teacher_import_template.xlsx')
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        ElMessage.success('模板下载成功')
      } catch (error) {
        console.error('下载模板失败:', error)
        ElMessage.error('下载模板失败')
      }
    }

    // 打开导入对话框
    const openImportDialog = () => {
      importDialogVisible.value = true
      fileList.value = []
      importResult.value = null
    }

    // 文件选择变化处理
    const handleFileChange = (file, fileListParam) => {
      console.log('文件选择变化:', file, fileListParam)
      fileList.value = fileListParam
    }

    // Excel文件上传前验证
    const beforeExcelUpload = (file) => {
      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                     file.type === 'application/vnd.ms-excel'
      const isLt10M = file.size / 1024 / 1024 < 10

      if (!isExcel) {
        ElMessage.error('只能上传Excel文件!')
        return false
      }

      if (!isLt10M) {
        ElMessage.error('文件大小不能超过10MB!')
        return false
      }

      return true
    }

    // 手动上传
    const handleUpload = () => {
      if (fileList.value.length === 0) {
        ElMessage.warning('请先选择要上传的文件')
        return
      }

      uploading.value = true
      uploadRef.value.submit()
    }

    // 导入成功回调
    const handleImportSuccess = (response) => {
      uploading.value = false
      console.log('导入成功响应:', response)

      if (response.success) {
        importResult.value = response.data
        ElMessage.success(response.message)
        // 刷新教师列表
        fetchTeachers()
      } else {
        ElMessage.error(response.message || '导入失败')
      }
    }

    // 导入失败回调
    const handleImportError = (error) => {
      uploading.value = false
      console.error('导入失败:', error)
      ElMessage.error('导入失败，请检查文件格式和网络连接')
    }
    
    return {
      baseUrl,
      uploadHeaders,
      loading,
      teacherList,
      searchForm,
      formData,
      formRules,
      dialogVisible,
      teacherFormRef,
      currentPage,
      pageSize,
      total,
      photoPreview,
      // 导入相关
      importDialogVisible,
      uploadRef,
      uploading,
      fileList,
      importResult,
      // 方法
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      openDialog,
      submitForm,
      beforePhotoUpload,
      handlePhotoSuccess,
      handlePhotoError,
      handleDelete,
      viewDetails,
      downloadTemplate,
      openImportDialog,
      handleFileChange,
      beforeExcelUpload,
      handleUpload,
      handleImportSuccess,
      handleImportError
    }
  }
}
</script>

<style scoped>
.teacher-list-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.avatar-uploader {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}

.upload-tip {
  font-size: 12px;
  color: #606266;
  margin-top: 5px;
}

.import-content {
  padding: 10px 0;
}

.import-result {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #f8f9fa;
}

.upload-demo {
  text-align: center;
  padding: 20px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
}

.upload-demo:hover {
  border-color: #409EFF;
}
</style> 