const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const authController = require('../controllers/authController');

// 保护所有用户管理路由 - 需要管理员权限
router.use(authController.protect);
router.use(authController.authorize('admin'));

// 获取所有用户
router.get('/', userController.getAllUsers);

// 创建用户
router.post('/', userController.createUser);

// 批量删除用户
router.delete('/batch', userController.batchDeleteUsers);

// 获取单个用户
router.get('/:id', userController.getUser);

// 更新用户
router.put('/:id', userController.updateUser);

// 更新用户状态
router.put('/:id/status', userController.updateUserStatus);

// 删除用户
router.delete('/:id', userController.deleteUser);

module.exports = router; 