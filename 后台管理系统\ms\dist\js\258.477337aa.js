"use strict";(self["webpackChunkms"]=self["webpackChunkms"]||[]).push([[258],{2258:function(e,a,t){t.r(a),t.d(a,{default:function(){return $}});var l=t(6768),r=t(4232);const o={class:"training-list-container"},n={class:"card-header"},i={class:"filter-container"},s={class:"filter-item"},u={class:"filter-buttons"},d={key:0,class:"empty-data"},c={key:0,class:"file-actions"},p={key:1},g={key:2,class:"pagination-container"},m={class:"dialog-footer"};function f(e,a,t,f,_,b){const h=(0,l.g2)("el-button"),k=(0,l.g2)("el-option"),y=(0,l.g2)("el-select"),v=(0,l.g2)("el-table-column"),F=(0,l.g2)("el-tag"),w=(0,l.g2)("Document"),C=(0,l.g2)("el-icon"),D=(0,l.g2)("el-table"),V=(0,l.g2)("el-pagination"),R=(0,l.g2)("el-card"),z=(0,l.g2)("el-input"),S=(0,l.g2)("el-form-item"),$=(0,l.g2)("Upload"),U=(0,l.g2)("el-upload"),K=(0,l.g2)("el-form"),L=(0,l.g2)("el-dialog"),W=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",o,[(0,l.bF)(R,{class:"box-card"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",n,[a[10]||(a[10]=(0,l.Lk)("span",{class:"title"},"专项培训",-1)),f.isAdmin?((0,l.uX)(),(0,l.Wv)(h,{key:0,type:"primary",onClick:a[0]||(a[0]=e=>f.openDialog())},{default:(0,l.k6)(()=>a[9]||(a[9]=[(0,l.eW)("添加培训课程")])),_:1,__:[9]})):(0,l.Q3)("",!0)])]),default:(0,l.k6)(()=>[(0,l.Lk)("div",i,[(0,l.Lk)("div",s,[a[11]||(a[11]=(0,l.Lk)("span",{class:"filter-label"},"课程类型",-1)),(0,l.bF)(y,{modelValue:f.filterForm.course_type,"onUpdate:modelValue":a[1]||(a[1]=e=>f.filterForm.course_type=e),placeholder:"选择课程类型",clearable:"",class:"filter-select"},{default:(0,l.k6)(()=>[(0,l.bF)(k,{label:"小讲课",value:"小讲课"}),(0,l.bF)(k,{label:"教学病例讨论",value:"教学病例讨论"}),(0,l.bF)(k,{label:"教学查房",value:"教学查房"}),(0,l.bF)(k,{label:"其他",value:"其他"})]),_:1},8,["modelValue"])]),(0,l.Lk)("div",u,[(0,l.bF)(h,{type:"primary",onClick:f.handleFilter},{default:(0,l.k6)(()=>a[12]||(a[12]=[(0,l.eW)("筛选")])),_:1,__:[12]},8,["onClick"]),(0,l.bF)(h,{onClick:f.resetFilter},{default:(0,l.k6)(()=>a[13]||(a[13]=[(0,l.eW)("重置")])),_:1,__:[13]},8,["onClick"])])]),(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",null,[0!==f.trainingCourses.length||f.loading?((0,l.uX)(),(0,l.Wv)(D,{key:1,data:f.trainingCourses,style:{width:"100%"},border:""},{default:(0,l.k6)(()=>[(0,l.bF)(v,{prop:"title",label:"课程标题","min-width":"180","show-overflow-tooltip":""}),(0,l.bF)(v,{prop:"course_type",label:"课程类型",width:"150",align:"center"},{default:(0,l.k6)(e=>[(0,l.bF)(F,{type:f.getTagType(e.row.course_type)},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(e.row.course_type),1)]),_:2},1032,["type"])]),_:1}),(0,l.bF)(v,{prop:"description",label:"课程描述","min-width":"200","show-overflow-tooltip":""}),(0,l.bF)(v,{prop:"created_at",label:"创建时间",width:"120",align:"center"},{default:(0,l.k6)(e=>[(0,l.eW)((0,r.v_)(f.formatDate(e.row.created_at)),1)]),_:1}),(0,l.bF)(v,{label:"课程资料",width:"220",align:"center"},{default:(0,l.k6)(e=>[e.row.material_path?((0,l.uX)(),(0,l.CE)("div",c,[(0,l.bF)(h,{type:"primary",link:"",onClick:a=>f.previewFile(e.row),title:"预览文件"},{default:(0,l.k6)(()=>[(0,l.bF)(C,null,{default:(0,l.k6)(()=>[(0,l.bF)(w)]),_:1}),(0,l.eW)(" "+(0,r.v_)(e.row.original_filename),1)]),_:2},1032,["onClick"])])):((0,l.uX)(),(0,l.CE)("span",p,"无资料"))]),_:1}),(0,l.bF)(v,{label:"操作",width:"150",align:"center",fixed:"right"},{default:(0,l.k6)(e=>[(0,l.bF)(h,{type:"primary",link:"",size:"small",onClick:a=>f.openDialog(e.row)},{default:(0,l.k6)(()=>a[14]||(a[14]=[(0,l.eW)("编辑")])),_:2,__:[14]},1032,["onClick"]),(0,l.bF)(h,{type:"danger",link:"",size:"small",onClick:a=>f.handleDelete(e.row)},{default:(0,l.k6)(()=>a[15]||(a[15]=[(0,l.eW)("删除")])),_:2,__:[15]},1032,["onClick"])]),_:1})]),_:1},8,["data"])):((0,l.uX)(),(0,l.CE)("div",d," 暂无培训课程，请添加新课程 ")),f.trainingCourses.length>0?((0,l.uX)(),(0,l.CE)("div",g,[(0,l.bF)(V,{"current-page":f.currentPage,"onUpdate:currentPage":a[2]||(a[2]=e=>f.currentPage=e),"page-size":f.pageSize,"onUpdate:pageSize":a[3]||(a[3]=e=>f.pageSize=e),"page-sizes":[10,20,30,50],layout:"total, sizes, prev, pager, next, jumper",total:f.total,onSizeChange:f.handleSizeChange,onCurrentChange:f.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])):(0,l.Q3)("",!0)])),[[W,f.loading]])]),_:1}),(0,l.bF)(L,{modelValue:f.dialogVisible,"onUpdate:modelValue":a[8]||(a[8]=e=>f.dialogVisible=e),title:f.formData.id?"编辑培训课程":"添加培训课程",width:"50%","destroy-on-close":""},{footer:(0,l.k6)(()=>[(0,l.Lk)("span",m,[(0,l.bF)(h,{onClick:a[7]||(a[7]=e=>f.dialogVisible=!1)},{default:(0,l.k6)(()=>a[18]||(a[18]=[(0,l.eW)("取消")])),_:1,__:[18]}),(0,l.bF)(h,{type:"primary",onClick:f.submitForm,loading:f.submitting},{default:(0,l.k6)(()=>a[19]||(a[19]=[(0,l.eW)("确定")])),_:1,__:[19]},8,["onClick","loading"])])]),default:(0,l.k6)(()=>[(0,l.bF)(K,{ref:"courseFormRef",model:f.formData,rules:f.formRules,"label-width":"100px","label-position":"right"},{default:(0,l.k6)(()=>[(0,l.bF)(S,{label:"课程标题",prop:"title"},{default:(0,l.k6)(()=>[(0,l.bF)(z,{modelValue:f.formData.title,"onUpdate:modelValue":a[4]||(a[4]=e=>f.formData.title=e),placeholder:"请输入课程标题"},null,8,["modelValue"])]),_:1}),(0,l.bF)(S,{label:"课程类型",prop:"course_type"},{default:(0,l.k6)(()=>[(0,l.bF)(y,{modelValue:f.formData.course_type,"onUpdate:modelValue":a[5]||(a[5]=e=>f.formData.course_type=e),placeholder:"选择课程类型",style:{width:"100%"}},{default:(0,l.k6)(()=>[(0,l.bF)(k,{label:"小讲课",value:"小讲课"}),(0,l.bF)(k,{label:"教学病例讨论",value:"教学病例讨论"}),(0,l.bF)(k,{label:"教学查房",value:"教学查房"}),(0,l.bF)(k,{label:"其他",value:"其他"})]),_:1},8,["modelValue"])]),_:1}),(0,l.bF)(S,{label:"课程描述",prop:"description"},{default:(0,l.k6)(()=>[(0,l.bF)(z,{modelValue:f.formData.description,"onUpdate:modelValue":a[6]||(a[6]=e=>f.formData.description=e),type:"textarea",rows:"3",placeholder:"请输入课程描述"},null,8,["modelValue"])]),_:1}),(0,l.bF)(S,{label:"课件/视频"},{default:(0,l.k6)(()=>[(0,l.bF)(U,{class:"material-uploader",drag:"",action:"#","auto-upload":!1,"on-change":f.handleFileChange,"on-remove":f.handleRemove,"before-upload":f.beforeMaterialUpload,"file-list":f.fileList,limit:1},{tip:(0,l.k6)(()=>a[16]||(a[16]=[(0,l.Lk)("div",{class:"el-upload__tip"}," 支持各种文档、PPT、视频等格式，文件大小不超过100MB ",-1)])),default:(0,l.k6)(()=>[(0,l.bF)(C,{class:"el-icon--upload"},{default:(0,l.k6)(()=>[(0,l.bF)($)]),_:1}),a[17]||(a[17]=(0,l.Lk)("div",{class:"el-upload__text"},[(0,l.eW)(" 拖拽文件到此处或 "),(0,l.Lk)("em",null,"点击上传")],-1))]),_:1,__:[17]},8,["on-change","on-remove","before-upload","file-list"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])}t(8111),t(5207);var _=t(144),b=t(1219),h=t(2933),k=t(7477),y=t(653),v=t(4373);const F=async(e={})=>{try{const a=await v.A.get(`${y.H}/trainings`,{params:e});return a.data}catch(a){throw console.error("获取培训课程列表失败:",a),a}},w=async(e,a={})=>{try{const t=await v.A.get(`${y.H}/trainings/type/${encodeURIComponent(e)}`,{params:a});return t.data}catch(t){throw console.error(`获取${e}类型培训课程失败:`,t),t}},C=async(e,a)=>{try{const t=new FormData;t.append("title",e.title),t.append("description",e.description||""),t.append("course_type",e.course_type),a&&t.append("material",a);const l=await v.A.post(`${y.H}/trainings`,t,{headers:{"Content-Type":"multipart/form-data"}});return l.data}catch(t){throw console.error("创建培训课程失败:",t),t}},D=async(e,a,t)=>{try{const l=new FormData;l.append("title",a.title),l.append("description",a.description||""),l.append("course_type",a.course_type),t?l.append("material",t):a.material_path&&a.original_filename&&(l.append("material_path",a.material_path),l.append("original_filename",a.original_filename));const r=await v.A.put(`${y.H}/trainings/${e}`,l,{headers:{"Content-Type":"multipart/form-data"}});return r.data}catch(l){throw console.error("更新培训课程失败:",l),l}},V=async e=>{try{const a=await v.A.delete(`${y.H}/trainings/${e}`);return a.data}catch(a){throw console.error("删除培训课程失败:",a),a}};var R={name:"TrainingList",components:{Document:k.Document,Upload:k.Upload,More:k.More,Download:k.Download},setup(){const e=(0,_.KR)(!1),a=(0,_.KR)(!1),t=(0,_.KR)([]),r=(0,_.KR)(!1),o=(0,_.KR)(null),n=(0,_.KR)(0),i=(0,_.KR)(1),s=(0,_.KR)(10),u=(0,_.KR)([]),d=(0,_.KR)(null);let c=localStorage.getItem("userInfo")?JSON.parse(localStorage.getItem("userInfo")):"";const p=(0,_.KR)("admin"==c?.role),g=(0,_.Kh)({course_type:""}),m=(0,_.Kh)({id:"",title:"",course_type:"",description:"",material_path:"",original_filename:""}),f={title:[{required:!0,message:"请输入课程标题",trigger:"blur"},{min:2,max:100,message:"长度在 2 到 100 个字符",trigger:"blur"}],course_type:[{required:!0,message:"请选择课程类型",trigger:"change"}]};(0,l.sV)(()=>{k()});const k=async()=>{e.value=!0;try{let e;const a={page:i.value,limit:s.value};e=g.course_type?await w(g.course_type,a):await F(a),t.value=e.data,n.value=e.count}catch(a){console.error("获取培训课程失败:",a),b.nk.error("获取培训课程失败")}finally{e.value=!1}},y=()=>{i.value=1,k()},v=()=>{g.course_type="",y()},R=e=>{s.value=e,k()},z=e=>{i.value=e,k()},S=e=>{const a=e.size/1024/1024<100;return a||b.nk.error("上传文件大小不能超过 100MB!"),a},$=e=>{d.value=e.raw},U=()=>{m.material_path="",m.original_filename="",u.value=[],d.value=null},K=e=>{e?(Object.keys(m).forEach(a=>{m[a]=e[a]}),u.value=[],e.material_path&&e.original_filename&&(u.value=[{name:decodeURIComponent(e.original_filename),url:`http://localhost:3000${e.material_path}`}])):(Object.keys(m).forEach(e=>{m[e]=""}),u.value=[],d.value=null),r.value=!0},L=async()=>{o.value&&await o.value.validate(async e=>{if(!e)return!1;a.value=!0;try{m.id?(await D(m.id,m,d.value),b.nk.success("课程更新成功")):(await C(m,d.value),b.nk.success("课程添加成功")),r.value=!1,k()}catch(t){console.error("操作失败:",t),b.nk.error("操作失败: "+(t.response?.data?.message||t.message))}finally{a.value=!1}})},W=e=>{h.s.confirm(`确定要删除培训课程 "${e.title}" 吗?`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await V(e.id),b.nk.success("删除成功"),k()}catch(a){console.error("删除失败:",a),b.nk.error("删除失败")}}).catch(()=>{b.nk.info("已取消删除")})},T=(e,a)=>{"edit"===e?K(a):"delete"===e&&W(a)},x=e=>{switch(e){case"小讲课":return"success";case"教学病例讨论":return"warning";case"教学查房":return"danger";default:return"info"}},A=e=>{if(!e)return"-";const a=new Date(e);return`${a.getFullYear()}-${(a.getMonth()+1).toString().padStart(2,"0")}-${a.getDate().toString().padStart(2,"0")}`},E=e=>{window.open(`http://localhost:3000${e.material_path}`,"_blank")};return{loading:e,submitting:a,trainingCourses:t,dialogVisible:r,courseFormRef:o,formData:m,formRules:f,filterForm:g,fileList:u,uploadFile:d,currentPage:i,pageSize:s,total:n,isAdmin:p,handleFilter:y,resetFilter:v,handleSizeChange:R,handleCurrentChange:z,openDialog:K,submitForm:L,beforeMaterialUpload:S,handleFileChange:$,handleRemove:U,handleCommand:T,handleDelete:W,getTagType:x,formatDate:A,previewFile:E}}},z=t(1241);const S=(0,z.A)(R,[["render",f],["__scopeId","data-v-54ccfd7e"]]);var $=S}}]);
//# sourceMappingURL=258.477337aa.js.map