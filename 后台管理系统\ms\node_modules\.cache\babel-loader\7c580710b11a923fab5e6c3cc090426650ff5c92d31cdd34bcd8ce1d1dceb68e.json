{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport axios from 'axios';\nexport default {\n  name: 'EvaluationList',\n  setup() {\n    const router = useRouter();\n\n    // 基础数据\n    const loading = ref(false);\n    const evaluationList = ref([]);\n    const total = ref(0);\n    const currentPage = ref(1);\n    const pageSize = ref(10);\n    let token = localStorage.getItem('token');\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    }\n    // 是否为管理员或督导\n    const isAdmin = computed(() => {\n      // 这里可以根据实际的用户角色判断\n      // 简单起见，这里暂时返回 true\n      return true;\n    });\n\n    // 搜索表单\n    const searchForm = reactive({\n      teacherName: '',\n      department: '',\n      competencyApproved: ''\n    });\n\n    // 生命周期钩子\n    onMounted(() => {\n      fetchEvaluations();\n    });\n\n    // 获取评价列表\n    const fetchEvaluations = async () => {\n      loading.value = true;\n      try {\n        // 构建查询参数\n        const params = {\n          page: currentPage.value,\n          limit: pageSize.value\n        };\n\n        // 添加搜索条件\n        if (searchForm.teacherName) {\n          params.teacherName = searchForm.teacherName;\n        }\n        if (searchForm.department) {\n          params.department = searchForm.department;\n        }\n        if (searchForm.competencyApproved !== '') {\n          params.competencyApproved = searchForm.competencyApproved;\n        }\n        const response = await axios.get('http://localhost:3000/api/evaluations', {\n          params\n        });\n        evaluationList.value = response.data.data;\n        total.value = response.data.count;\n      } catch (error) {\n        console.error('获取督导评价列表失败:', error);\n        ElMessage.error('获取督导评价列表失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 搜索操作\n    const handleSearch = () => {\n      currentPage.value = 1;\n      fetchEvaluations();\n    };\n\n    // 重置搜索\n    const resetSearch = () => {\n      Object.keys(searchForm).forEach(key => {\n        searchForm[key] = '';\n      });\n      currentPage.value = 1;\n      fetchEvaluations();\n    };\n\n    // 分页操作\n    const handleSizeChange = val => {\n      pageSize.value = val;\n      fetchEvaluations();\n    };\n    const handleCurrentChange = val => {\n      currentPage.value = val;\n      fetchEvaluations();\n    };\n\n    // 查看详情\n    const viewDetails = id => {\n      router.push(`/evaluations/detail/${id}`);\n    };\n\n    // 添加评价\n    const goToAddEvaluation = () => {\n      router.push('/evaluations/add');\n    };\n\n    // 编辑评价\n    const editEvaluation = id => {\n      router.push(`/evaluations/add?id=${id}`);\n    };\n\n    // 删除评价\n    const handleDelete = row => {\n      ElMessageBox.confirm(`确定要删除\"${row.teacher_name}\"的督导评价记录吗?`, '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async () => {\n        try {\n          await axios.delete(`http://localhost:3000/api/evaluations/${row.id}`);\n          ElMessage.success('删除成功');\n          fetchEvaluations();\n        } catch (error) {\n          console.error('删除失败:', error);\n          ElMessage.error('删除失败');\n        }\n      }).catch(() => {\n        ElMessage.info('已取消删除');\n      });\n    };\n\n    // 格式化日期\n    const formatDate = dateString => {\n      if (!dateString) return '-';\n      const date = new Date(dateString);\n      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;\n    };\n    return {\n      loading,\n      evaluationList,\n      searchForm,\n      currentPage,\n      pageSize,\n      total,\n      isAdmin,\n      handleSearch,\n      resetSearch,\n      handleSizeChange,\n      handleCurrentChange,\n      viewDetails,\n      goToAddEvaluation,\n      editEvaluation,\n      handleDelete,\n      formatDate\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "useRouter", "ElMessage", "ElMessageBox", "axios", "name", "setup", "router", "loading", "evaluationList", "total", "currentPage", "pageSize", "token", "localStorage", "getItem", "defaults", "headers", "common", "isAdmin", "searchForm", "<PERSON><PERSON><PERSON>", "department", "competencyApproved", "fetchEvaluations", "value", "params", "page", "limit", "response", "get", "data", "count", "error", "console", "handleSearch", "resetSearch", "Object", "keys", "for<PERSON>ach", "key", "handleSizeChange", "val", "handleCurrentChange", "viewDetails", "id", "push", "goToAddEvaluation", "editEvaluation", "handleDelete", "row", "confirm", "teacher_name", "confirmButtonText", "cancelButtonText", "type", "then", "delete", "success", "catch", "info", "formatDate", "dateString", "date", "Date", "getFullYear", "getMonth", "toString", "padStart", "getDate"], "sources": ["D:\\admin\\202506\\督导系统\\后台管理系统\\ms\\src\\views\\evaluations\\EvaluationList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"evaluation-list-container\">\r\n    <el-card class=\"box-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span class=\"title\">教学活动督导评价</span>\r\n          <el-button type=\"primary\" @click=\"goToAddEvaluation\">添加评价</el-button>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- 搜索区域 -->\r\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\r\n        <el-form-item label=\"教师姓名\">\r\n          <el-input v-model=\"searchForm.teacherName\" placeholder=\"教师姓名\" clearable />\r\n        </el-form-item>\r\n       \r\n       \r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n          <el-button @click=\"resetSearch\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <!-- 表格区域 -->\r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"evaluationList\"\r\n        border\r\n        style=\"width: 100%\"\r\n      >\r\n        <el-table-column type=\"index\" width=\"50\" label=\"#\" />\r\n        <el-table-column prop=\"teacher_name\" label=\"教师姓名\" width=\"100\" />\r\n        <el-table-column prop=\"supervising_department\" label=\"督导教研室\" width=\"120\" />\r\n        <el-table-column prop=\"case_topic\" label=\"病例/主题\" show-overflow-tooltip />\r\n        <el-table-column prop=\"teaching_form\" label=\"教学活动形式\" width=\"120\" />\r\n        <el-table-column prop=\"student_name\" label=\"学员姓名\" width=\"100\" />\r\n        <el-table-column prop=\"student_type\" label=\"学员类别\" width=\"120\" />\r\n        <el-table-column prop=\"average_score\" label=\"平均分\" width=\"80\" />\r\n        <el-table-column label=\"能力认定\" width=\"100\">\r\n          <template #default=\"scope\">\r\n            <el-tag :type=\"scope.row.competency_approved ? 'success' : 'danger'\">\r\n              {{ scope.row.competency_approved ? '同意' : '不同意' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"evaluation_date\" label=\"评价时间\" width=\"180\">\r\n          <template #default=\"scope\">\r\n            {{ formatDate(scope.row.evaluation_date) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"evaluator_name\" label=\"评估人\" width=\"100\" />\r\n        <el-table-column label=\"操作\" width=\"180\" fixed=\"right\">\r\n          <template #default=\"scope\">\r\n            <el-button size=\"small\" @click=\"viewDetails(scope.row.id)\">详情</el-button>\r\n            <el-button size=\"small\" type=\"primary\" @click=\"editEvaluation(scope.row.id)\" v-if=\"isAdmin\">编辑</el-button>\r\n            <el-button size=\"small\" type=\"danger\" @click=\"handleDelete(scope.row)\" v-if=\"isAdmin\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          v-model:current-page=\"currentPage\"\r\n          v-model:page-size=\"pageSize\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n        />\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, computed, onMounted } from 'vue'\r\nimport { useRouter } from 'vue-router'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'EvaluationList',\r\n  setup() {\r\n    const router = useRouter()\r\n    \r\n    // 基础数据\r\n    const loading = ref(false)\r\n    const evaluationList = ref([])\r\n    const total = ref(0)\r\n    const currentPage = ref(1)\r\n    const pageSize = ref(10)\r\n      let token = localStorage.getItem('token')\r\n    if (token) {\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`\r\n    }\r\n    // 是否为管理员或督导\r\n    const isAdmin = computed(() => {\r\n      // 这里可以根据实际的用户角色判断\r\n      // 简单起见，这里暂时返回 true\r\n      return true\r\n    })\r\n    \r\n    // 搜索表单\r\n    const searchForm = reactive({\r\n      teacherName: '',\r\n      department: '',\r\n      competencyApproved: ''\r\n    })\r\n    \r\n    // 生命周期钩子\r\n    onMounted(() => {\r\n      fetchEvaluations()\r\n    })\r\n    \r\n    // 获取评价列表\r\n    const fetchEvaluations = async () => {\r\n      loading.value = true\r\n      try {\r\n        // 构建查询参数\r\n        const params = {\r\n          page: currentPage.value,\r\n          limit: pageSize.value\r\n        }\r\n        \r\n        // 添加搜索条件\r\n        if (searchForm.teacherName) {\r\n          params.teacherName = searchForm.teacherName\r\n        }\r\n        if (searchForm.department) {\r\n          params.department = searchForm.department\r\n        }\r\n        if (searchForm.competencyApproved !== '') {\r\n          params.competencyApproved = searchForm.competencyApproved\r\n        }\r\n        \r\n        const response = await axios.get('http://localhost:3000/api/evaluations', { params })\r\n        evaluationList.value = response.data.data\r\n        total.value = response.data.count\r\n      } catch (error) {\r\n        console.error('获取督导评价列表失败:', error)\r\n        ElMessage.error('获取督导评价列表失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 搜索操作\r\n    const handleSearch = () => {\r\n      currentPage.value = 1\r\n      fetchEvaluations()\r\n    }\r\n    \r\n    // 重置搜索\r\n    const resetSearch = () => {\r\n      Object.keys(searchForm).forEach(key => {\r\n        searchForm[key] = ''\r\n      })\r\n      currentPage.value = 1\r\n      fetchEvaluations()\r\n    }\r\n    \r\n    // 分页操作\r\n    const handleSizeChange = (val) => {\r\n      pageSize.value = val\r\n      fetchEvaluations()\r\n    }\r\n    \r\n    const handleCurrentChange = (val) => {\r\n      currentPage.value = val\r\n      fetchEvaluations()\r\n    }\r\n    \r\n    // 查看详情\r\n    const viewDetails = (id) => {\r\n      router.push(`/evaluations/detail/${id}`)\r\n    }\r\n    \r\n    // 添加评价\r\n    const goToAddEvaluation = () => {\r\n      router.push('/evaluations/add')\r\n    }\r\n    \r\n    // 编辑评价\r\n    const editEvaluation = (id) => {\r\n      router.push(`/evaluations/add?id=${id}`)\r\n    }\r\n    \r\n    // 删除评价\r\n    const handleDelete = (row) => {\r\n      ElMessageBox.confirm(\r\n        `确定要删除\"${row.teacher_name}\"的督导评价记录吗?`,\r\n        '警告',\r\n        {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n        }\r\n      )\r\n        .then(async () => {\r\n          try {\r\n            await axios.delete(`http://localhost:3000/api/evaluations/${row.id}`)\r\n            ElMessage.success('删除成功')\r\n            fetchEvaluations()\r\n          } catch (error) {\r\n            console.error('删除失败:', error)\r\n            ElMessage.error('删除失败')\r\n          }\r\n        })\r\n        .catch(() => {\r\n          ElMessage.info('已取消删除')\r\n        })\r\n    }\r\n    \r\n    // 格式化日期\r\n    const formatDate = (dateString) => {\r\n      if (!dateString) return '-'\r\n      const date = new Date(dateString)\r\n      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`\r\n    }\r\n    \r\n    return {\r\n      loading,\r\n      evaluationList,\r\n      searchForm,\r\n      currentPage,\r\n      pageSize,\r\n      total,\r\n      isAdmin,\r\n      handleSearch,\r\n      resetSearch,\r\n      handleSizeChange,\r\n      handleCurrentChange,\r\n      viewDetails,\r\n      goToAddEvaluation,\r\n      editEvaluation,\r\n      handleDelete,\r\n      formatDate\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.evaluation-list-container {\r\n  padding: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.search-form {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n</style> "], "mappings": ";;;AA6EA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAI;AACvD,SAASC,SAAQ,QAAS,YAAW;AACrC,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,OAAOC,KAAI,MAAO,OAAM;AAExB,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,KAAKA,CAAA,EAAG;IACN,MAAMC,MAAK,GAAIN,SAAS,CAAC;;IAEzB;IACA,MAAMO,OAAM,GAAIX,GAAG,CAAC,KAAK;IACzB,MAAMY,cAAa,GAAIZ,GAAG,CAAC,EAAE;IAC7B,MAAMa,KAAI,GAAIb,GAAG,CAAC,CAAC;IACnB,MAAMc,WAAU,GAAId,GAAG,CAAC,CAAC;IACzB,MAAMe,QAAO,GAAIf,GAAG,CAAC,EAAE;IACrB,IAAIgB,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,OAAO;IAC1C,IAAIF,KAAK,EAAE;MACTT,KAAK,CAACY,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,IAAI,UAAUL,KAAK,EAAC;IACnE;IACA;IACA,MAAMM,OAAM,GAAIpB,QAAQ,CAAC,MAAM;MAC7B;MACA;MACA,OAAO,IAAG;IACZ,CAAC;;IAED;IACA,MAAMqB,UAAS,GAAItB,QAAQ,CAAC;MAC1BuB,WAAW,EAAE,EAAE;MACfC,UAAU,EAAE,EAAE;MACdC,kBAAkB,EAAE;IACtB,CAAC;;IAED;IACAvB,SAAS,CAAC,MAAM;MACdwB,gBAAgB,CAAC;IACnB,CAAC;;IAED;IACA,MAAMA,gBAAe,GAAI,MAAAA,CAAA,KAAY;MACnChB,OAAO,CAACiB,KAAI,GAAI,IAAG;MACnB,IAAI;QACF;QACA,MAAMC,MAAK,GAAI;UACbC,IAAI,EAAEhB,WAAW,CAACc,KAAK;UACvBG,KAAK,EAAEhB,QAAQ,CAACa;QAClB;;QAEA;QACA,IAAIL,UAAU,CAACC,WAAW,EAAE;UAC1BK,MAAM,CAACL,WAAU,GAAID,UAAU,CAACC,WAAU;QAC5C;QACA,IAAID,UAAU,CAACE,UAAU,EAAE;UACzBI,MAAM,CAACJ,UAAS,GAAIF,UAAU,CAACE,UAAS;QAC1C;QACA,IAAIF,UAAU,CAACG,kBAAiB,KAAM,EAAE,EAAE;UACxCG,MAAM,CAACH,kBAAiB,GAAIH,UAAU,CAACG,kBAAiB;QAC1D;QAEA,MAAMM,QAAO,GAAI,MAAMzB,KAAK,CAAC0B,GAAG,CAAC,uCAAuC,EAAE;UAAEJ;QAAO,CAAC;QACpFjB,cAAc,CAACgB,KAAI,GAAII,QAAQ,CAACE,IAAI,CAACA,IAAG;QACxCrB,KAAK,CAACe,KAAI,GAAII,QAAQ,CAACE,IAAI,CAACC,KAAI;MAClC,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK;QAClC/B,SAAS,CAAC+B,KAAK,CAAC,YAAY;MAC9B,UAAU;QACRzB,OAAO,CAACiB,KAAI,GAAI,KAAI;MACtB;IACF;;IAEA;IACA,MAAMU,YAAW,GAAIA,CAAA,KAAM;MACzBxB,WAAW,CAACc,KAAI,GAAI;MACpBD,gBAAgB,CAAC;IACnB;;IAEA;IACA,MAAMY,WAAU,GAAIA,CAAA,KAAM;MACxBC,MAAM,CAACC,IAAI,CAAClB,UAAU,CAAC,CAACmB,OAAO,CAACC,GAAE,IAAK;QACrCpB,UAAU,CAACoB,GAAG,IAAI,EAAC;MACrB,CAAC;MACD7B,WAAW,CAACc,KAAI,GAAI;MACpBD,gBAAgB,CAAC;IACnB;;IAEA;IACA,MAAMiB,gBAAe,GAAKC,GAAG,IAAK;MAChC9B,QAAQ,CAACa,KAAI,GAAIiB,GAAE;MACnBlB,gBAAgB,CAAC;IACnB;IAEA,MAAMmB,mBAAkB,GAAKD,GAAG,IAAK;MACnC/B,WAAW,CAACc,KAAI,GAAIiB,GAAE;MACtBlB,gBAAgB,CAAC;IACnB;;IAEA;IACA,MAAMoB,WAAU,GAAKC,EAAE,IAAK;MAC1BtC,MAAM,CAACuC,IAAI,CAAC,uBAAuBD,EAAE,EAAE;IACzC;;IAEA;IACA,MAAME,iBAAgB,GAAIA,CAAA,KAAM;MAC9BxC,MAAM,CAACuC,IAAI,CAAC,kBAAkB;IAChC;;IAEA;IACA,MAAME,cAAa,GAAKH,EAAE,IAAK;MAC7BtC,MAAM,CAACuC,IAAI,CAAC,uBAAuBD,EAAE,EAAE;IACzC;;IAEA;IACA,MAAMI,YAAW,GAAKC,GAAG,IAAK;MAC5B/C,YAAY,CAACgD,OAAO,CAClB,SAASD,GAAG,CAACE,YAAY,YAAY,EACrC,IAAI,EACJ;QACEC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CACF,EACGC,IAAI,CAAC,YAAY;QAChB,IAAI;UACF,MAAMpD,KAAK,CAACqD,MAAM,CAAC,yCAAyCP,GAAG,CAACL,EAAE,EAAE;UACpE3C,SAAS,CAACwD,OAAO,CAAC,MAAM;UACxBlC,gBAAgB,CAAC;QACnB,EAAE,OAAOS,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;UAC5B/B,SAAS,CAAC+B,KAAK,CAAC,MAAM;QACxB;MACF,CAAC,EACA0B,KAAK,CAAC,MAAM;QACXzD,SAAS,CAAC0D,IAAI,CAAC,OAAO;MACxB,CAAC;IACL;;IAEA;IACA,MAAMC,UAAS,GAAKC,UAAU,IAAK;MACjC,IAAI,CAACA,UAAU,EAAE,OAAO,GAAE;MAC1B,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU;MAChC,OAAO,GAAGC,IAAI,CAACE,WAAW,CAAC,CAAC,IAAI,CAACF,IAAI,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIL,IAAI,CAACM,OAAO,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC;IAClI;IAEA,OAAO;MACL5D,OAAO;MACPC,cAAc;MACdW,UAAU;MACVT,WAAW;MACXC,QAAQ;MACRF,KAAK;MACLS,OAAO;MACPgB,YAAY;MACZC,WAAW;MACXK,gBAAgB;MAChBE,mBAAmB;MACnBC,WAAW;MACXG,iBAAiB;MACjBC,cAAc;MACdC,YAAY;MACZY;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}