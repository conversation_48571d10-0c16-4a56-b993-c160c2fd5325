"use strict";(self["webpackChunkms"]=self["webpackChunkms"]||[]).push([[636],{4636:function(a,e,t){t.r(e),t.d(e,{default:function(){return R}});var l=t(6768),s=t(4232);const c={class:"competency-detail-container"},i={class:"card-header"},o={class:"teacher-info-card"},r={class:"teacher-avatar"},n={key:0},d={class:"certification-info"},u={class:"certification-status"},v={class:"certification-note"},p={class:"certification-progress"},k={class:"progress-stats"},_={class:"stat-item"},b={class:"stat-value"},f={class:"stat-item"},h={class:"stat-value"},g={class:"stat-item"},m={class:"stat-value"},y={class:"certification-rules"},F={key:1,class:"no-data"},L={key:0,class:"no-data"};function w(a,e,t,w,D,C){const W=(0,l.g2)("el-button"),$=(0,l.g2)("el-image"),E=(0,l.g2)("el-avatar"),X=(0,l.g2)("el-descriptions-item"),K=(0,l.g2)("el-tag"),R=(0,l.g2)("el-descriptions"),A=(0,l.g2)("el-col"),S=(0,l.g2)("el-alert"),z=(0,l.g2)("el-progress"),N=(0,l.g2)("el-divider"),x=(0,l.g2)("el-card"),B=(0,l.g2)("el-table-column"),j=(0,l.g2)("el-table"),q=(0,l.g2)("el-row"),I=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",c,[(0,l.bF)(x,{class:"box-card"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",i,[e[1]||(e[1]=(0,l.Lk)("span",{class:"title"},"能力认定详情",-1)),(0,l.Lk)("div",null,[(0,l.bF)(W,{onClick:w.goBack},{default:(0,l.k6)(()=>e[0]||(e[0]=[(0,l.eW)("返回列表")])),_:1,__:[0]},8,["onClick"])])])]),default:(0,l.k6)(()=>[(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",null,[(0,l.bF)(q,{gutter:20},{default:(0,l.k6)(()=>[(0,l.bF)(A,{span:6},{default:(0,l.k6)(()=>[(0,l.Lk)("div",o,[(0,l.Lk)("div",r,[w.teacherData.photo?((0,l.uX)(),(0,l.Wv)($,{key:0,src:`http://localhost:3000${w.teacherData.photo}`,fit:"cover",class:"avatar-image","preview-src-list":[`http://localhost:3000${w.teacherData.photo}`]},null,8,["src","preview-src-list"])):((0,l.uX)(),(0,l.Wv)(E,{key:1,size:120,icon:"UserFilled"}))]),(0,l.bF)(R,{title:"教师信息",direction:"vertical",column:1,border:""},{default:(0,l.k6)(()=>[(0,l.bF)(X,{label:"姓名"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(w.teacherData.name),1)]),_:1}),(0,l.bF)(X,{label:"性别"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(w.teacherData.gender),1)]),_:1}),(0,l.bF)(X,{label:"科室"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(w.teacherData.department),1)]),_:1}),(0,l.bF)(X,{label:"学校"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(w.teacherData.school),1)]),_:1}),(0,l.bF)(X,{label:"专业"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(w.teacherData.major),1)]),_:1}),(0,l.bF)(X,{label:"学历"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(w.teacherData.education),1)]),_:1}),(0,l.bF)(X,{label:"在聘状态"},{default:(0,l.k6)(()=>[(0,l.bF)(K,{type:w.teacherData.is_employed?"success":"danger"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(w.teacherData.is_employed?"在聘":"不在聘"),1)]),_:1},8,["type"])]),_:1})]),_:1})])]),_:1}),(0,l.bF)(A,{span:18},{default:(0,l.k6)(()=>[(0,l.bF)(x,{class:"certification-status-card",shadow:"hover"},{header:(0,l.k6)(()=>e[2]||(e[2]=[(0,l.Lk)("div",{class:"certification-header"},[(0,l.Lk)("span",null,"认定状态")],-1)])),default:(0,l.k6)(()=>[w.competencyData?((0,l.uX)(),(0,l.CE)("div",n,[(0,l.Lk)("div",d,[(0,l.Lk)("div",u,[(0,l.bF)(K,{type:w.competencyData.is_certified?"success":"info",size:"large"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(w.competencyData.is_certified?"已认证":"未认证"),1)]),_:1},8,["type"]),(0,l.Lk)("div",v,[(0,l.bF)(S,{title:w.getCertificationNote(),type:w.competencyData.is_certified?"success":"info",closable:!1,"show-icon":""},null,8,["title","type"])])]),(0,l.Lk)("div",p,[(0,l.bF)(z,{percentage:w.competencyData.approval_rate||0,status:w.competencyData.is_certified?"success":"","stroke-width":20,format:w.percentFormat},null,8,["percentage","status","format"]),(0,l.Lk)("div",k,[(0,l.Lk)("div",_,[(0,l.Lk)("div",b,(0,s.v_)(w.competencyData.total_evaluations),1),e[3]||(e[3]=(0,l.Lk)("div",{class:"stat-label"},"评价总数",-1))]),(0,l.Lk)("div",f,[(0,l.Lk)("div",h,(0,s.v_)(w.competencyData.approved_count),1),e[4]||(e[4]=(0,l.Lk)("div",{class:"stat-label"},"认可数",-1))]),(0,l.Lk)("div",g,[(0,l.Lk)("div",m,(0,s.v_)(w.competencyData.approval_rate)+"%",1),e[5]||(e[5]=(0,l.Lk)("div",{class:"stat-label"},"认可率",-1))])])])]),(0,l.Lk)("div",y,[(0,l.bF)(N,{"content-position":"left"},{default:(0,l.k6)(()=>e[6]||(e[6]=[(0,l.eW)("认定规则说明")])),_:1,__:[6]}),e[7]||(e[7]=(0,l.Lk)("div",{class:"rules-content"},[(0,l.Lk)("p",null,"1. 需要至少参加3次督导评价才能被认定资格"),(0,l.Lk)("p",null,"2. 督导评价的认可率需达到80%以上才能获得认证"),(0,l.Lk)("p",null,"3. 每次评价中，督导老师需要对教师的教学能力进行认可评定"),(0,l.Lk)("p",null,"4. 认证状态会随着新的评价自动更新")],-1))])])):((0,l.uX)(),(0,l.CE)("div",F," 该教师暂无能力认定数据 "))]),_:1}),(0,l.bF)(x,{class:"evaluations-card",shadow:"hover"},{header:(0,l.k6)(()=>e[8]||(e[8]=[(0,l.Lk)("div",{class:"evaluations-header"},[(0,l.Lk)("span",null,"评价记录")],-1)])),default:(0,l.k6)(()=>[(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",null,[(0,l.bF)(j,{data:w.evaluations,border:"",style:{width:"100%"}},{default:(0,l.k6)(()=>[(0,l.bF)(B,{type:"index",width:"50",label:"#"}),(0,l.bF)(B,{prop:"supervising_department",label:"督导教研室",width:"120"}),(0,l.bF)(B,{prop:"case_topic",label:"病例/主题","show-overflow-tooltip":""}),(0,l.bF)(B,{prop:"teaching_form",label:"教学活动形式",width:"120"}),(0,l.bF)(B,{prop:"average_score",label:"平均分",width:"80"}),(0,l.bF)(B,{label:"能力认定",width:"100"},{default:(0,l.k6)(a=>[(0,l.bF)(K,{type:a.row.competency_approved?"success":"danger"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(a.row.competency_approved?"同意":"不同意"),1)]),_:2},1032,["type"])]),_:1}),(0,l.bF)(B,{prop:"evaluation_date",label:"评价时间",width:"180"},{default:(0,l.k6)(a=>[(0,l.eW)((0,s.v_)(w.formatDate(a.row.evaluation_date)),1)]),_:1}),(0,l.bF)(B,{prop:"evaluator_name",label:"评估人",width:"100"}),(0,l.bF)(B,{label:"操作",width:"100"},{default:(0,l.k6)(a=>[(0,l.bF)(W,{size:"small",onClick:e=>w.viewEvaluation(a.row.id)},{default:(0,l.k6)(()=>e[9]||(e[9]=[(0,l.eW)("详情")])),_:2,__:[9]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),0!==w.evaluations.length||w.evaluationsLoading?(0,l.Q3)("",!0):((0,l.uX)(),(0,l.CE)("div",L," 暂无评价记录 "))])),[[I,w.evaluationsLoading]])]),_:1})]),_:1})]),_:1})])),[[I,w.loading]])]),_:1})])}t(4114);var D=t(144),C=t(1387),W=t(1219),$=t(4373),E={name:"CompetencyDetail",setup(){const a=(0,C.lq)(),e=(0,C.rd)(),t=a.params.id,s=(0,D.KR)(!1),c=(0,D.KR)(!1),i=(0,D.KR)({}),o=(0,D.KR)(null),r=(0,D.KR)([]);(0,l.sV)(()=>{n(),d(),u()});const n=async()=>{s.value=!0;try{const a=await $.A.get(`http://localhost:3000/api/teachers/${t}`);i.value=a.data.data}catch(a){console.error("获取教师信息失败:",a),W.nk.error("获取教师信息失败")}finally{s.value=!1}},d=async()=>{try{const a=await $.A.get(`http://localhost:3000/api/evaluations/competency/teacher/${t}`);o.value=a.data.data}catch(a){console.error("获取能力认证状态失败:",a)}},u=async()=>{c.value=!0;try{const a=await $.A.get(`http://localhost:3000/api/evaluations/teacher/${t}`);r.value=a.data.data}catch(a){console.error("获取评价列表失败:",a),W.nk.error("获取评价列表失败")}finally{c.value=!1}},v=()=>o.value?o.value.is_certified?`该教师已通过能力认定，认可率为 ${o.value.approval_rate}%`:o.value.total_evaluations<3?`评价次数不足，至少需要3次评价才能进行认定（当前: ${o.value.total_evaluations}次）`:`认可率不足，需要80%以上才能获得认定（当前: ${o.value.approval_rate}%）`:"",p=a=>`${a}%`,k=a=>{if(!a)return"-";const e=new Date(a);return`${e.getFullYear()}-${(e.getMonth()+1).toString().padStart(2,"0")}-${e.getDate().toString().padStart(2,"0")}`},_=()=>{e.push("/competency/list")},b=a=>{e.push(`/evaluations/detail/${a}`)};return{loading:s,evaluationsLoading:c,teacherData:i,competencyData:o,evaluations:r,goBack:_,viewEvaluation:b,getCertificationNote:v,percentFormat:p,formatDate:k}}},X=t(1241);const K=(0,X.A)(E,[["render",w],["__scopeId","data-v-04ec8c68"]]);var R=K}}]);
//# sourceMappingURL=636.1f1a582a.js.map