<template>
  <div class="evaluation-detail-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="title">督导评价详情</span>
          <div>
            <el-button @click="goBack">返回列表</el-button>
            <el-button type="primary" @click="editEvaluation" v-if="isAdmin">编辑</el-button>
          </div>
        </div>
      </template>

      <div v-loading="loading">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-descriptions title="评价基本信息" :column="3" border>
              <el-descriptions-item label="督导教研室">{{ evaluationData.supervising_department }}</el-descriptions-item>
              <el-descriptions-item label="评价日期">{{ formatDate(evaluationData.evaluation_date) }}</el-descriptions-item>
              <el-descriptions-item label="评估人">{{ evaluationData.evaluator_name }}</el-descriptions-item>
            </el-descriptions>
            
            <el-divider />
            
            <el-descriptions title="教学活动信息" :column="3" border>
              <el-descriptions-item label="病例/主题" :span="3">{{ evaluationData.case_topic }}</el-descriptions-item>
              <el-descriptions-item label="教学活动形式">{{ evaluationData.teaching_form }}</el-descriptions-item>
              <el-descriptions-item label="带教老师" :span="2">
                {{ evaluationData.teacher_name }} ({{ evaluationData.teacher_title }})
              </el-descriptions-item>
              <el-descriptions-item label="学员姓名">{{ evaluationData.student_name }}</el-descriptions-item>
              <el-descriptions-item label="学员类别" :span="2">{{ evaluationData.student_type }}</el-descriptions-item>
            </el-descriptions>
            
            <el-divider />
            
            <el-descriptions title="评价内容" :column="1" border>
              <el-descriptions-item label="平均分">
                <el-rate
                  v-model="score"
                  :max="10"
                  show-score
                  disabled
                  score-template="{value}"
                />
              </el-descriptions-item>
              
              <el-descriptions-item label="亮点">
                <div class="description-content">
                  {{ evaluationData.highlights || '无' }}
                </div>
              </el-descriptions-item>
              
              <el-descriptions-item label="不足">
                <div class="description-content">
                  {{ evaluationData.shortcomings || '无' }}
                </div>
              </el-descriptions-item>
              
              <el-descriptions-item label="改进建议">
                <div class="description-content">
                  {{ evaluationData.improvement_suggestions || '无' }}
                </div>
              </el-descriptions-item>
              
              <el-descriptions-item label="能力认定">
                <el-tag :type="evaluationData.competency_approved ? 'success' : 'danger'" size="large">
                  {{ evaluationData.competency_approved ? '同意' : '不同意' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>
        
        <el-divider />
        
        <!-- 教师信息卡片 -->
        <el-card class="teacher-info-card" shadow="hover" v-if="teacherData.id">
          <template #header>
            <div class="teacher-card-header">
              <span>带教老师信息</span>
              <el-button type="text" @click="viewTeacherDetail">查看详情</el-button>
            </div>
          </template>
          
          <el-row :gutter="20">
            <el-col :span="4">
              <div class="teacher-avatar">
                <el-image
                  v-if="teacherData.photo"
                  :src="`http://127.0.0.1:3000${teacherData.photo}`"
                  fit="cover"
                  class="avatar-image"
                  :preview-src-list="[`http://127.0.0.1:3000${teacherData.photo}`]"
                />
                <el-avatar v-else :size="100" icon="UserFilled" />
              </div>
            </el-col>
            <el-col :span="20">
              <el-descriptions :column="3" border>
                <el-descriptions-item label="姓名">{{ teacherData.name }}</el-descriptions-item>
                <el-descriptions-item label="性别">{{ teacherData.gender }}</el-descriptions-item>
                <el-descriptions-item label="科室">{{ teacherData.department }}</el-descriptions-item>
                <el-descriptions-item label="学校">{{ teacherData.school }}</el-descriptions-item>
                <el-descriptions-item label="专业">{{ teacherData.major }}</el-descriptions-item>
                <el-descriptions-item label="学历">{{ teacherData.education }}</el-descriptions-item>
              </el-descriptions>
              
              <div class="teacher-stats" v-if="competencyData">
                <div class="teacher-stats-item">
                  <div class="stats-label">评价总数:</div>
                  <div class="stats-value">{{ competencyData.total_evaluations }}</div>
                </div>
                <div class="teacher-stats-item">
                  <div class="stats-label">认可数:</div>
                  <div class="stats-value">{{ competencyData.approved_count }}</div>
                </div>
                <div class="teacher-stats-item">
                  <div class="stats-label">认可率:</div>
                  <div class="stats-value">{{ competencyData.approval_rate }}%</div>
                </div>
                <div class="teacher-stats-item">
                  <div class="stats-label">认证状态:</div>
                  <div class="stats-value">
                    <el-tag :type="competencyData.is_certified ? 'success' : 'info'">
                      {{ competencyData.is_certified ? '已认证' : '未认证' }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import axios from 'axios'

export default {
  name: 'EvaluationDetail',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const evaluationId = route.params.id
    
    // 基础数据
    const loading = ref(false)
    const evaluationData = ref({})
    const teacherData = ref({})
    const competencyData = ref(null)
    
    // 是否为管理员或督导
    const isAdmin = computed(() => {
      // 这里可以根据实际的用户角色判断
      // 简单起见，这里暂时返回 true
      return true
    })
    
    // 评分
    const score = computed(() => {
      return evaluationData.value.average_score || 0
    })
    
    // 生命周期钩子
    onMounted(() => {
      fetchEvaluationDetail()
    })
    
    // 获取评价详情
    const fetchEvaluationDetail = async () => {
      loading.value = true
      try {
        const response = await axios.get(`http://127.0.0.1:3000/api/evaluations/${evaluationId}`)
        evaluationData.value = response.data.data
        
        // 如果有教师ID，获取教师信息
        if (evaluationData.value.teacher_id) {
          await fetchTeacherData(evaluationData.value.teacher_id)
          await fetchCompetencyStatus(evaluationData.value.teacher_id)
        }
      } catch (error) {
        console.error('获取评价详情失败:', error)
        ElMessage.error('获取评价详情失败')
      } finally {
        loading.value = false
      }
    }
    
    // 获取教师信息
    const fetchTeacherData = async (teacherId) => {
      try {
        const response = await axios.get(`http://127.0.0.1:3000/api/teachers/${teacherId}`)
        teacherData.value = response.data.data
      } catch (error) {
        console.error('获取教师信息失败:', error)
      }
    }
    
    // 获取能力认证状态
    const fetchCompetencyStatus = async (teacherId) => {
      try {
        const response = await axios.get(`http://127.0.0.1:3000/api/evaluations/competency/teacher/${teacherId}`)
        competencyData.value = response.data.data
      } catch (error) {
        console.error('获取能力认证状态失败:', error)
      }
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
    }
    
    // 返回列表
    const goBack = () => {
      router.push('/evaluations/list')
    }
    
    // 编辑评价
    const editEvaluation = () => {
      router.push(`/evaluations/add?id=${evaluationId}`)
    }
    
    // 查看教师详情
    const viewTeacherDetail = () => {
      if (teacherData.value.id) {
        router.push(`/teachers/detail/${teacherData.value.id}`)
      }
    }
    
    return {
      loading,
      evaluationData,
      teacherData,
      competencyData,
      score,
      isAdmin,
      formatDate,
      goBack,
      editEvaluation,
      viewTeacherDetail
    }
  }
}
</script>

<style scoped>
.evaluation-detail-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.description-content {
  white-space: pre-line;
  padding: 10px;
  background-color: #f7f7f7;
  border-radius: 4px;
  min-height: 50px;
}

.teacher-info-card {
  margin-top: 20px;
}

.teacher-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.teacher-avatar {
  display: flex;
  justify-content: center;
}

.avatar-image {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #eee;
}

.teacher-stats {
  display: flex;
  margin-top: 15px;
  background-color: #f7f7f7;
  padding: 10px;
  border-radius: 4px;
}

.teacher-stats-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stats-label {
  font-size: 12px;
  color: #606266;
  margin-bottom: 5px;
}

.stats-value {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}
</style> 