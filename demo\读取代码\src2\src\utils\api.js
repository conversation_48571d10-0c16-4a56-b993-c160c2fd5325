import axios from 'axios'

// API基础URL
export const API_URL = 'http://127.0.0.1:3000/api'

// 配置axios默认值
const api = axios.create({
  baseURL: 'http://127.0.0.1:3000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 添加认证头
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 处理常见错误
api.interceptors.response.use(
  response => {
    return response
  },
  error => {
    const { response } = error
    if (response) {
      // 未授权，清除token并重定向到登录页
      if (response.status === 401) {
        localStorage.removeItem('token')
        localStorage.removeItem('userId')
        localStorage.removeItem('userRole')
        window.location.href = '/login'
      }
    }
    return Promise.reject(error)
  }
)

export default api 