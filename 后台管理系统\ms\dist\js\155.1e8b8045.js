"use strict";(self["webpackChunkms"]=self["webpackChunkms"]||[]).push([[155],{3155:function(e,a,l){l.r(a),l.d(a,{default:function(){return f}});var t=l(6768),o=l(4232);const r={class:"add-evaluation-container"},u={class:"card-header"},d={class:"title"};function n(e,a,l,n,i,s){const m=(0,t.g2)("el-button"),c=(0,t.g2)("el-input"),p=(0,t.g2)("el-form-item"),_=(0,t.g2)("el-option"),g=(0,t.g2)("el-select"),f=(0,t.g2)("el-input-number"),b=(0,t.g2)("el-radio"),h=(0,t.g2)("el-radio-group"),v=(0,t.g2)("el-form"),k=(0,t.g2)("el-card"),F=(0,t.gN)("loading");return(0,t.uX)(),(0,t.CE)("div",r,[(0,t.bF)(k,{class:"box-card"},{header:(0,t.k6)(()=>[(0,t.Lk)("div",u,[(0,t.Lk)("span",d,(0,o.v_)(n.isEdit?"编辑督导评价":"添加督导评价"),1),(0,t.bF)(m,{onClick:n.goBack},{default:(0,t.k6)(()=>a[12]||(a[12]=[(0,t.eW)("返回列表")])),_:1,__:[12]},8,["onClick"])])]),default:(0,t.k6)(()=>[(0,t.bo)(((0,t.uX)(),(0,t.CE)("div",null,[(0,t.bF)(v,{ref:"evaluationFormRef",model:n.formData,rules:n.formRules,"label-width":"120px","label-position":"right"},{default:(0,t.k6)(()=>[(0,t.bF)(p,{label:"督导教研室",prop:"supervising_department"},{default:(0,t.k6)(()=>[(0,t.bF)(c,{modelValue:n.formData.supervising_department,"onUpdate:modelValue":a[0]||(a[0]=e=>n.formData.supervising_department=e),placeholder:"请输入督导教研室"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"病例/主题",prop:"case_topic"},{default:(0,t.k6)(()=>[(0,t.bF)(c,{modelValue:n.formData.case_topic,"onUpdate:modelValue":a[1]||(a[1]=e=>n.formData.case_topic=e),placeholder:"请输入病例或主题"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"教学活动形式",prop:"teaching_form"},{default:(0,t.k6)(()=>[(0,t.bF)(c,{modelValue:n.formData.teaching_form,"onUpdate:modelValue":a[2]||(a[2]=e=>n.formData.teaching_form=e),placeholder:"例如：小讲课、教学查房等"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"带教老师",prop:"teacher_id"},{default:(0,t.k6)(()=>[(0,t.bF)(g,{modelValue:n.formData.teacher_id,"onUpdate:modelValue":a[3]||(a[3]=e=>n.formData.teacher_id=e),placeholder:"请选择带教老师",filterable:"",loading:n.teacherLoading},{default:(0,t.k6)(()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(n.teacherOptions,e=>((0,t.uX)(),(0,t.Wv)(_,{key:e.id,label:`${e.name||"未命名"} (${e.department||"无部门"})`,value:e.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),(0,t.bF)(p,{label:"带教老师职称",prop:"teacher_title"},{default:(0,t.k6)(()=>[(0,t.bF)(c,{modelValue:n.formData.teacher_title,"onUpdate:modelValue":a[4]||(a[4]=e=>n.formData.teacher_title=e),placeholder:"请输入带教老师职称"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"学员姓名",prop:"student_name"},{default:(0,t.k6)(()=>[(0,t.bF)(c,{modelValue:n.formData.student_name,"onUpdate:modelValue":a[5]||(a[5]=e=>n.formData.student_name=e),placeholder:"请输入学员姓名"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"学员类别",prop:"student_type"},{default:(0,t.k6)(()=>[(0,t.bF)(g,{modelValue:n.formData.student_type,"onUpdate:modelValue":a[6]||(a[6]=e=>n.formData.student_type=e),placeholder:"请选择学员类别"},{default:(0,t.k6)(()=>[(0,t.bF)(_,{label:"实习生",value:"实习生"}),(0,t.bF)(_,{label:"进修生",value:"进修生"}),(0,t.bF)(_,{label:"低年资轮转",value:"低年资轮转"})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"评分",prop:"average_score"},{default:(0,t.k6)(()=>[(0,t.bF)(f,{modelValue:n.formData.average_score,"onUpdate:modelValue":a[7]||(a[7]=e=>n.formData.average_score=e),min:0,max:100,precision:1,step:1,"controls-position":"right"},null,8,["modelValue"]),a[13]||(a[13]=(0,t.Lk)("span",{class:"score-hint"},"（0-100分）",-1))]),_:1,__:[13]}),(0,t.bF)(p,{label:"亮点",prop:"highlights"},{default:(0,t.k6)(()=>[(0,t.bF)(c,{modelValue:n.formData.highlights,"onUpdate:modelValue":a[8]||(a[8]=e=>n.formData.highlights=e),type:"textarea",rows:3,placeholder:"请输入教学亮点"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"不足",prop:"shortcomings"},{default:(0,t.k6)(()=>[(0,t.bF)(c,{modelValue:n.formData.shortcomings,"onUpdate:modelValue":a[9]||(a[9]=e=>n.formData.shortcomings=e),type:"textarea",rows:3,placeholder:"请输入教学不足之处"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"改进建议",prop:"improvement_suggestions"},{default:(0,t.k6)(()=>[(0,t.bF)(c,{modelValue:n.formData.improvement_suggestions,"onUpdate:modelValue":a[10]||(a[10]=e=>n.formData.improvement_suggestions=e),type:"textarea",rows:3,placeholder:"请输入改进建议"},null,8,["modelValue"])]),_:1}),(0,t.bF)(p,{label:"能力认定",prop:"competency_approved"},{default:(0,t.k6)(()=>[(0,t.bF)(h,{modelValue:n.formData.competency_approved,"onUpdate:modelValue":a[11]||(a[11]=e=>n.formData.competency_approved=e)},{default:(0,t.k6)(()=>[(0,t.bF)(b,{label:1},{default:(0,t.k6)(()=>a[14]||(a[14]=[(0,t.eW)("同意")])),_:1,__:[14]}),(0,t.bF)(b,{label:0},{default:(0,t.k6)(()=>a[15]||(a[15]=[(0,t.eW)("不同意")])),_:1,__:[15]})]),_:1},8,["modelValue"])]),_:1}),(0,t.bF)(p,null,{default:(0,t.k6)(()=>[(0,t.bF)(m,{type:"primary",onClick:n.submitForm},{default:(0,t.k6)(()=>a[16]||(a[16]=[(0,t.eW)("提交")])),_:1,__:[16]},8,["onClick"]),(0,t.bF)(m,{onClick:n.resetForm},{default:(0,t.k6)(()=>a[17]||(a[17]=[(0,t.eW)("重置")])),_:1,__:[17]},8,["onClick"])]),_:1})]),_:1},8,["model","rules"])])),[[F,n.loading]])]),_:1})])}l(4114),l(8111),l(116),l(5207);var i=l(144),s=l(1387),m=l(1219),c=l(4373),p={name:"AddEvaluation",setup(){const e=(0,s.lq)(),a=(0,s.rd)(),l=(0,i.KR)(null),o=(0,i.KR)(!1),r=(0,i.KR)(!1),u=(0,i.KR)([]),d=(0,i.KR)([]),n=e.query.id,p=(0,t.EW)(()=>!!n),_=(0,i.Kh)({supervising_department:"",case_topic:"",teaching_form:"",teacher_id:"",teacher_title:"",student_name:"",student_type:"实习生",average_score:7,highlights:"",shortcomings:"",improvement_suggestions:"",competency_approved:1,evaluator_id:1}),g={supervising_department:[{required:!0,message:"请输入督导教研室",trigger:"blur"}],case_topic:[{required:!0,message:"请输入病例/主题",trigger:"blur"}],teaching_form:[{required:!0,message:"请输入教学活动形式",trigger:"blur"}],teacher_id:[{required:!0,message:"请选择带教老师",trigger:"change"}],teacher_title:[{required:!0,message:"请输入带教老师职称",trigger:"blur"}],student_name:[{required:!0,message:"请输入学员姓名",trigger:"blur"}],student_type:[{required:!0,message:"请选择学员类别",trigger:"change"}],average_score:[{required:!0,message:"请输入平均分",trigger:"change"}]};(0,t.wB)(()=>_.teacher_id,e=>{if(e){const a=d.value.find(a=>a.id===e);a&&a.title&&(_.teacher_title=a.title)}});const f=async()=>{r.value=!0;try{const e=await c.A.get("http://localhost:3000/api/teachers");e.data&&e.data.data?(d.value=e.data.data,u.value=e.data.data):(u.value=[],console.error("获取教师列表返回格式有误"))}catch(e){console.error("获取教师列表失败:",e),m.nk.error("获取教师列表失败")}finally{r.value=!1}},b=async()=>{o.value=!0;try{const e=await c.A.get(`http://localhost:3000/api/evaluations/${n}`),a=e.data.data;Object.keys(_).forEach(e=>{e in a&&(_[e]=a[e])})}catch(e){console.error("获取评价详情失败:",e),m.nk.error("获取评价详情失败")}finally{o.value=!1}},h=async()=>{l.value&&await l.value.validate(async e=>{if(!e)return!1;o.value=!0;try{p.value?(await c.A.put(`http://localhost:3000/api/evaluations/${n}`,_),m.nk.success("督导评价更新成功")):(await c.A.post("http://localhost:3000/api/evaluations",_),m.nk.success("督导评价添加成功")),a.push("/evaluations/list")}catch(l){console.error("操作失败:",l),m.nk.error("操作失败")}finally{o.value=!1}})},v=()=>{l.value&&l.value.resetFields()},k=()=>{a.push("/evaluations/list")};return(0,t.sV)(async()=>{await f(),p.value&&await b()}),{loading:o,teacherLoading:r,teacherOptions:u,evaluationFormRef:l,formData:_,formRules:g,isEdit:p,submitForm:h,resetForm:v,goBack:k}}},_=l(1241);const g=(0,_.A)(p,[["render",n],["__scopeId","data-v-84544d94"]]);var f=g}}]);
//# sourceMappingURL=155.1e8b8045.js.map