<template>
  <div class="evaluation-list-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="title">教学活动督导评价</span>
          <el-button type="primary" @click="goToAddEvaluation">添加评价</el-button>
        </div>
      </template>

      <!-- 搜索区域 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="教师姓名">
          <el-input v-model="searchForm.teacherName" placeholder="教师姓名" clearable />
        </el-form-item>
       
       
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="evaluationList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" width="50" label="#" />
        <el-table-column prop="teacher_name" label="教师姓名" width="100" />
        <el-table-column prop="supervising_department" label="督导教研室" width="120" />
        <el-table-column prop="case_topic" label="病例/主题" show-overflow-tooltip />
        <el-table-column prop="teaching_form" label="教学活动形式" width="120" />
        <el-table-column prop="student_name" label="学员姓名" width="100" />
        <el-table-column prop="student_type" label="学员类别" width="120" />
        <el-table-column prop="average_score" label="平均分" width="80" />
        <el-table-column label="能力认定" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.competency_approved ? 'success' : 'danger'">
              {{ scope.row.competency_approved ? '同意' : '不同意' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="evaluation_date" label="评价时间" width="180">
          <template #default="scope">
            {{ formatDate(scope.row.evaluation_date) }}
          </template>
        </el-table-column>
        <el-table-column prop="evaluator_name" label="评估人" width="100" />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="viewDetails(scope.row.id)">详情</el-button>
            <el-button size="small" type="primary" @click="editEvaluation(scope.row.id)" v-if="isAdmin">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)" v-if="isAdmin">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'

export default {
  name: 'EvaluationList',
  setup() {
    const router = useRouter()
    
    // 基础数据
    const loading = ref(false)
    const evaluationList = ref([])
    const total = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(10)
      let token = localStorage.getItem('token')
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
    }
    // 是否为管理员或督导
    const isAdmin = computed(() => {
      // 这里可以根据实际的用户角色判断
      // 简单起见，这里暂时返回 true
      return true
    })
    
    // 搜索表单
    const searchForm = reactive({
      teacherName: '',
      department: '',
      competencyApproved: ''
    })
    
    // 生命周期钩子
    onMounted(() => {
      fetchEvaluations()
    })
    
    // 获取评价列表
    const fetchEvaluations = async () => {
      loading.value = true
      try {
        // 构建查询参数
        const params = {
          page: currentPage.value,
          limit: pageSize.value
        }
        
        // 添加搜索条件
        if (searchForm.teacherName) {
          params.teacherName = searchForm.teacherName
        }
        if (searchForm.department) {
          params.department = searchForm.department
        }
        if (searchForm.competencyApproved !== '') {
          params.competencyApproved = searchForm.competencyApproved
        }
        
        const response = await axios.get('http://localhost:3000/api/evaluations', { params })
        evaluationList.value = response.data.data
        total.value = response.data.count
      } catch (error) {
        console.error('获取督导评价列表失败:', error)
        ElMessage.error('获取督导评价列表失败')
      } finally {
        loading.value = false
      }
    }
    
    // 搜索操作
    const handleSearch = () => {
      currentPage.value = 1
      fetchEvaluations()
    }
    
    // 重置搜索
    const resetSearch = () => {
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
      })
      currentPage.value = 1
      fetchEvaluations()
    }
    
    // 分页操作
    const handleSizeChange = (val) => {
      pageSize.value = val
      fetchEvaluations()
    }
    
    const handleCurrentChange = (val) => {
      currentPage.value = val
      fetchEvaluations()
    }
    
    // 查看详情
    const viewDetails = (id) => {
      router.push(`/evaluations/detail/${id}`)
    }
    
    // 添加评价
    const goToAddEvaluation = () => {
      router.push('/evaluations/add')
    }
    
    // 编辑评价
    const editEvaluation = (id) => {
      router.push(`/evaluations/add?id=${id}`)
    }
    
    // 删除评价
    const handleDelete = (row) => {
      ElMessageBox.confirm(
        `确定要删除"${row.teacher_name}"的督导评价记录吗?`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(async () => {
          try {
            await axios.delete(`http://localhost:3000/api/evaluations/${row.id}`)
            ElMessage.success('删除成功')
            fetchEvaluations()
          } catch (error) {
            console.error('删除失败:', error)
            ElMessage.error('删除失败')
          }
        })
        .catch(() => {
          ElMessage.info('已取消删除')
        })
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
    }
    
    return {
      loading,
      evaluationList,
      searchForm,
      currentPage,
      pageSize,
      total,
      isAdmin,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      viewDetails,
      goToAddEvaluation,
      editEvaluation,
      handleDelete,
      formatDate
    }
  }
}
</script>

<style scoped>
.evaluation-list-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style> 