import api from '@/utils/api';

const teacherService = {
  /**
   * 获取教师列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getTeachers(params) {
    return api.get('/api/teachers', { params });
  },

  /**
   * 获取单个教师
   * @param {number} id - 教师ID
   * @returns {Promise}
   */
  getTeacher(id) {
    return api.get(`/teachers/${id}`);
  },

  /**
   * 创建教师
   * @param {Object} teacherData - 教师数据
   * @returns {Promise}
   */
  createTeacher(teacherData) {
    const formData = new FormData();
    
    // 添加基本教师信息到表单
    Object.keys(teacherData).forEach(key => {
      if (key === 'photo' && teacherData[key] instanceof File) {
        formData.append('photo', teacherData[key]);
      } else if (teacherData[key] !== null && teacherData[key] !== undefined) {
        formData.append(key, teacherData[key]);
      }
    });
    
    return api.post('/teachers', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  /**
   * 更新教师
   * @param {number} id - 教师ID
   * @param {Object} teacherData - 教师数据
   * @returns {Promise}
   */
  updateTeacher(id, teacherData) {
    const formData = new FormData();
    
    // 添加基本教师信息到表单
    Object.keys(teacherData).forEach(key => {
      if (key === 'photo' && teacherData[key] instanceof File) {
        formData.append('photo', teacherData[key]);
      } else if (teacherData[key] !== null && teacherData[key] !== undefined) {
        formData.append(key, teacherData[key]);
      }
    });
    
    return api.put(`/teachers/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  /**
   * 删除教师
   * @param {number} id - 教师ID
   * @returns {Promise}
   */
  deleteTeacher(id) {
    return api.delete(`/teachers/${id}`);
  },

  /**
   * 获取教师督导评价
   * @param {number} teacherId - 教师ID
   * @returns {Promise}
   */
  getTeacherEvaluations(teacherId) {
    return api.get(`/evaluations/teacher/${teacherId}`);
  },
  
  /**
   * 获取教师能力认定状态
   * @param {number} teacherId - 教师ID
   * @returns {Promise}
   */
  getTeacherCompetency(teacherId) {
    return api.get(`/evaluations/competency/teacher/${teacherId}`);
  }
};

export default teacherService; 