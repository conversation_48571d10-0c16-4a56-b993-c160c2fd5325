<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<script>
import axios from 'axios'
import { onMounted } from 'vue'

export default {
  name: 'App',
  setup() {
    onMounted(() => {
      // 初始化检查 - 确保学生ID存在
      checkStudentId()
    })
  }
}

// 检查并尝试获取学生ID
function checkStudentId() {
  const userId = localStorage.getItem('userId')
  const userRole = localStorage.getItem('userRole')
  
  if (userRole === 'student' && !localStorage.getItem('studentId') && userId) {
    console.log('尝试获取学生ID...')
    
    // 获取API URL
    const API_URL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api'
    
    // 获取token
    const token = localStorage.getItem('token')
    if (!token) {
      console.error('未找到登录令牌，无法获取学生ID')
      return
    }
    
    // 设置请求头
    const headers = {
      'Authorization': `Bearer ${token}`
    }
    
    // 发送请求获取学生ID
    axios.get(`${API_URL}/students/by-user/${userId}`, { headers })
      .then(response => {
        if (response.data.success && response.data.data) {
          localStorage.setItem('studentId', response.data.data.id)
          console.log('成功获取并保存学生ID:', response.data.data.id)
        } else {
          console.error('无法获取学生ID')
        }
      })
      .catch(error => {
        console.error('获取学生ID失败:', error)
      })
  }
}
</script>

<style>
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
}

#app {
  height: 100%;
}

.el-main {
  padding: 0 !important;
}

/* 覆盖Element Plus默认样式，减少边距 */
.el-card__body {
  padding: 10px !important;
}

.el-form--inline .el-form-item {
  margin-right: 10px;
  margin-bottom: 10px;
}

/* 设置表格最大高度，避免出现滚动条 */
.el-table {
  max-height: calc(100vh - 300px) !important;
  overflow: hidden !important;
}

/* 确保表格内容适应容器 */
.el-table__body-wrapper {
  overflow: hidden !important;
}

/* 表格卡片样式 */
.table-card {
  overflow: hidden !important;
}

/* 表格卡片内容区域 */
.table-card .el-card__body {
  overflow: hidden !important;
}
</style>
