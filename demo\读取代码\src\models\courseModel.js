const { pool } = require('../config/db');

class Course {
  // 创建新课程
  static async create(courseData) {
    try {
      const [result] = await pool.query(
        `INSERT INTO courses (title, description, material_path, original_filename) 
         VALUES (?, ?, ?, ?)`,
        [
          courseData.title, 
          courseData.description, 
          courseData.material_path, 
          courseData.original_filename
        ]
      );
      return result.insertId;
    } catch (error) {
      console.error('创建课程失败:', error);
      throw error;
    }
  }

  // 获取所有课程
  static async findAll() {
    try {
      const [rows] = await pool.query('SELECT * FROM courses');
      return rows;
    } catch (error) {
      console.error('获取课程列表失败:', error);
      throw error;
    }
  }

  // 根据ID获取课程
  static async findById(id) {
    try {
      const [rows] = await pool.query('SELECT * FROM courses WHERE id = ?', [id]);
      return rows[0];
    } catch (error) {
      console.error('获取课程信息失败:', error);
      throw error;
    }
  }

  // 更新课程信息
  static async update(id, courseData) {
    try {
      const [result] = await pool.query(
        `UPDATE courses 
         SET title = ?, 
             description = ?, 
             material_path = ?, 
             original_filename = ? 
         WHERE id = ?`,
        [
          courseData.title, 
          courseData.description, 
          courseData.material_path,
          courseData.original_filename, 
          id
        ]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新课程信息失败:', error);
      throw error;
    }
  }

  // 删除课程
  static async delete(id) {
    try {
      const [result] = await pool.query('DELETE FROM courses WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除课程失败:', error);
      throw error;
    }
  }

  // 搜索课程
  static async search(keyword) {
    try {
      const [rows] = await pool.query(
        `SELECT * FROM courses WHERE title LIKE ? OR description LIKE ?`,
        [`%${keyword}%`, `%${keyword}%`]
      );
      return rows;
    } catch (error) {
      console.error('搜索课程失败:', error);
      throw error;
    }
  }
}

module.exports = Course; 