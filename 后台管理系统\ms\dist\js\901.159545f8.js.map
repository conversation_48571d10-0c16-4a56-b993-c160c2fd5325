{"version": 3, "file": "js/901.159545f8.js", "mappings": "iLACOA,MAAM,+B,GAGAA,MAAM,e,GA4CEA,MAAM,uB,GAMNA,MAAM,uB,GAMNA,MAAM,uB,GAmBVA,MAAM,uB,GAQJA,MAAM,kB,SAqBNA,MAAM,iB,GACJA,MAAM,sB,GAEJA,MAAM,e,GAERA,MAAM,sB,GAEJA,MAAM,e,GAERA,MAAM,sB,GAEJA,MAAM,e,GAERA,MAAM,sB,GAEJA,MAAM,e,kVA1H3BC,EAAAA,EAAAA,IAsIM,MAtINC,EAsIM,EArIJC,EAAAA,EAAAA,IAoIUC,EAAA,CApIDJ,MAAM,YAAU,CACZK,QAAMC,EAAAA,EAAAA,IACf,IAMM,EANNC,EAAAA,EAAAA,IAMM,MANNC,EAMM,C,aALJD,EAAAA,EAAAA,IAAiC,QAA3BP,MAAM,SAAQ,UAAM,KAC1BO,EAAAA,EAAAA,IAGM,aAFJJ,EAAAA,EAAAA,IAA2CM,EAAA,CAA/BC,QAAOC,EAAAC,QAAM,C,iBAAE,IAAIC,EAAA,KAAAA,EAAA,K,QAAJ,W,2BAC6BF,EAAAG,U,WAAxDC,EAAAA,EAAAA,IAA+EN,EAAA,C,MAApEO,KAAK,UAAWN,QAAOC,EAAAM,gB,kBAA+B,IAAEJ,EAAA,KAAAA,EAAA,K,QAAF,S,mEAKvE,IAwHM,E,qBAxHNZ,EAAAA,EAAAA,IAwHM,aAvHJE,EAAAA,EAAAA,IA0DSe,EAAA,CA1DAC,OAAQ,IAAE,C,iBACjB,IAwDS,EAxDThB,EAAAA,EAAAA,IAwDSiB,EAAA,CAxDAC,KAAM,IAAE,C,iBACf,IAIkB,EAJlBlB,EAAAA,EAAAA,IAIkBmB,EAAA,CAJDC,MAAM,SAAUC,OAAQ,EAAGC,OAAA,I,kBAC1C,IAAsG,EAAtGtB,EAAAA,EAAAA,IAAsGuB,EAAA,CAAhFC,MAAM,SAAO,C,iBAAC,IAA2C,E,iBAAxChB,EAAAiB,eAAeC,wBAAsB,K,OAC5E1B,EAAAA,EAAAA,IAA0GuB,EAAA,CAApFC,MAAM,QAAM,C,iBAAC,IAAgD,E,iBAA7ChB,EAAAmB,WAAWnB,EAAAiB,eAAeG,kBAAe,K,OAC/E5B,EAAAA,EAAAA,IAA4FuB,EAAA,CAAtEC,MAAM,OAAK,C,iBAAC,IAAmC,E,iBAAhChB,EAAAiB,eAAeI,gBAAc,K,eAGpE7B,EAAAA,EAAAA,IAAc8B,IAEd9B,EAAAA,EAAAA,IAQkBmB,EAAA,CARDC,MAAM,SAAUC,OAAQ,EAAGC,OAAA,I,kBAC1C,IAAoG,EAApGtB,EAAAA,EAAAA,IAAoGuB,EAAA,CAA9EC,MAAM,QAASN,KAAM,G,kBAAG,IAA+B,E,iBAA5BV,EAAAiB,eAAeM,YAAU,K,OAC1E/B,EAAAA,EAAAA,IAA8FuB,EAAA,CAAxEC,MAAM,UAAQ,C,iBAAC,IAAkC,E,iBAA/BhB,EAAAiB,eAAeO,eAAa,K,OACpEhC,EAAAA,EAAAA,IAEuBuB,EAAA,CAFDC,MAAM,OAAQN,KAAM,G,kBACxC,IAAiC,E,iBAA9BV,EAAAiB,eAAeQ,cAAe,MAAEC,EAAAA,EAAAA,IAAG1B,EAAAiB,eAAeU,eAAgB,KACvE,K,OACAnC,EAAAA,EAAAA,IAA2FuB,EAAA,CAArEC,MAAM,QAAM,C,iBAAC,IAAiC,E,iBAA9BhB,EAAAiB,eAAeW,cAAY,K,OACjEpC,EAAAA,EAAAA,IAAqGuB,EAAA,CAA/EC,MAAM,OAAQN,KAAM,G,kBAAG,IAAiC,E,iBAA9BV,EAAAiB,eAAeY,cAAY,K,eAG7ErC,EAAAA,EAAAA,IAAc8B,IAEd9B,EAAAA,EAAAA,IAkCkBmB,EAAA,CAlCDC,MAAM,OAAQC,OAAQ,EAAGC,OAAA,I,kBACxC,IAQuB,EARvBtB,EAAAA,EAAAA,IAQuBuB,EAAA,CARDC,MAAM,OAAK,C,iBAC/B,IAME,EANFxB,EAAAA,EAAAA,IAMEsC,EAAA,C,WALS9B,EAAA+B,M,qCAAA/B,EAAA+B,MAAKC,GACbC,IAAK,GACN,gBACAC,SAAA,GACA,iBAAe,W,gCAInB1C,EAAAA,EAAAA,IAIuBuB,EAAA,CAJDC,MAAM,MAAI,C,iBAC9B,IAEM,EAFNpB,EAAAA,EAAAA,IAEM,MAFNuC,GAEMT,EAAAA,EAAAA,IADD1B,EAAAiB,eAAemB,YAAc,KAAJ,K,OAIhC5C,EAAAA,EAAAA,IAIuBuB,EAAA,CAJDC,MAAM,MAAI,C,iBAC9B,IAEM,EAFNpB,EAAAA,EAAAA,IAEM,MAFNyC,GAEMX,EAAAA,EAAAA,IADD1B,EAAAiB,eAAeqB,cAAgB,KAAJ,K,OAIlC9C,EAAAA,EAAAA,IAIuBuB,EAAA,CAJDC,MAAM,QAAM,C,iBAChC,IAEM,EAFNpB,EAAAA,EAAAA,IAEM,MAFN2C,GAEMb,EAAAA,EAAAA,IADD1B,EAAAiB,eAAeuB,yBAA2B,KAAJ,K,OAI7ChD,EAAAA,EAAAA,IAIuBuB,EAAA,CAJDC,MAAM,QAAM,C,iBAChC,IAES,EAFTxB,EAAAA,EAAAA,IAESiD,EAAA,CAFApC,KAAML,EAAAiB,eAAeyB,oBAAsB,UAAY,SAAUC,KAAK,S,kBAC7E,IAAuD,E,iBAApD3C,EAAAiB,eAAeyB,oBAAsB,KAAO,OAAV,K,kDAO/ClD,EAAAA,EAAAA,IAAc8B,GAG0CtB,EAAA4C,YAAYC,K,WAApEzC,EAAAA,EAAAA,IAuDUX,EAAA,C,MAvDDJ,MAAM,oBAAoByD,OAAO,S,CAC7BpD,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNC,EAAAA,EAAAA,IAGM,MAHNmD,EAGM,C,aAFJnD,EAAAA,EAAAA,IAAmB,YAAb,UAAM,KACZJ,EAAAA,EAAAA,IAAkEM,EAAA,CAAvDO,KAAK,OAAQN,QAAOC,EAAAgD,mB,kBAAmB,IAAI9C,EAAA,KAAAA,EAAA,K,QAAJ,W,gDAItD,IA8CS,EA9CTV,EAAAA,EAAAA,IA8CSe,EAAA,CA9CAC,OAAQ,IAAE,C,iBACjB,IAWS,EAXThB,EAAAA,EAAAA,IAWSiB,EAAA,CAXAC,KAAM,GAAC,C,iBACd,IASM,EATNd,EAAAA,EAAAA,IASM,MATNqD,EASM,CAPIjD,EAAA4C,YAAYM,Q,WADpB9C,EAAAA,EAAAA,IAME+C,EAAA,C,MAJCC,IAAG,0BAA4BpD,EAAA4C,YAAYM,QAC5CG,IAAI,QACJhE,MAAM,eACL,mBAAgB,2BAA6BW,EAAA4C,YAAYM,U,iDAE5D9C,EAAAA,EAAAA,IAAkDkD,EAAA,C,MAA/BX,KAAM,IAAKY,KAAK,oB,OAGvC/D,EAAAA,EAAAA,IAgCSiB,EAAA,CAhCAC,KAAM,IAAE,C,iBACf,IAOkB,EAPlBlB,EAAAA,EAAAA,IAOkBmB,EAAA,CAPAE,OAAQ,EAAGC,OAAA,I,kBAC3B,IAA8E,EAA9EtB,EAAAA,EAAAA,IAA8EuB,EAAA,CAAxDC,MAAM,MAAI,C,iBAAC,IAAsB,E,iBAAnBhB,EAAA4C,YAAYY,MAAI,K,OACpDhE,EAAAA,EAAAA,IAAgFuB,EAAA,CAA1DC,MAAM,MAAI,C,iBAAC,IAAwB,E,iBAArBhB,EAAA4C,YAAYa,QAAM,K,OACtDjE,EAAAA,EAAAA,IAAoFuB,EAAA,CAA9DC,MAAM,MAAI,C,iBAAC,IAA4B,E,iBAAzBhB,EAAA4C,YAAYc,YAAU,K,OAC1DlE,EAAAA,EAAAA,IAAgFuB,EAAA,CAA1DC,MAAM,MAAI,C,iBAAC,IAAwB,E,iBAArBhB,EAAA4C,YAAYe,QAAM,K,OACtDnE,EAAAA,EAAAA,IAA+EuB,EAAA,CAAzDC,MAAM,MAAI,C,iBAAC,IAAuB,E,iBAApBhB,EAAA4C,YAAYgB,OAAK,K,OACrDpE,EAAAA,EAAAA,IAAmFuB,EAAA,CAA7DC,MAAM,MAAI,C,iBAAC,IAA2B,E,iBAAxBhB,EAAA4C,YAAYiB,WAAS,K,cAG1B7D,EAAA8D,iB,WAAjCxE,EAAAA,EAAAA,IAqBM,MArBNyE,EAqBM,EApBJnE,EAAAA,EAAAA,IAGM,MAHNoE,EAGM,C,aAFJpE,EAAAA,EAAAA,IAAoC,OAA/BP,MAAM,eAAc,SAAK,KAC9BO,EAAAA,EAAAA,IAAqE,MAArEqE,GAAqEvC,EAAAA,EAAAA,IAAzC1B,EAAA8D,eAAeI,mBAAiB,MAE9DtE,EAAAA,EAAAA,IAGM,MAHNuE,EAGM,C,aAFJvE,EAAAA,EAAAA,IAAmC,OAA9BP,MAAM,eAAc,QAAI,KAC7BO,EAAAA,EAAAA,IAAkE,MAAlEwE,GAAkE1C,EAAAA,EAAAA,IAAtC1B,EAAA8D,eAAeO,gBAAc,MAE3DzE,EAAAA,EAAAA,IAGM,MAHN0E,EAGM,C,aAFJ1E,EAAAA,EAAAA,IAAmC,OAA9BP,MAAM,eAAc,QAAI,KAC7BO,EAAAA,EAAAA,IAAkE,MAAlE2E,GAAkE7C,EAAAA,EAAAA,IAAtC1B,EAAA8D,eAAeU,eAAgB,IAAC,MAE9D5E,EAAAA,EAAAA,IAOM,MAPN6E,EAOM,C,aANJ7E,EAAAA,EAAAA,IAAoC,OAA/BP,MAAM,eAAc,SAAK,KAC9BO,EAAAA,EAAAA,IAIM,MAJN8E,EAIM,EAHJlF,EAAAA,EAAAA,IAESiD,EAAA,CAFApC,KAAML,EAAA8D,eAAea,aAAe,UAAY,Q,kBACvD,IAAiD,E,iBAA9C3E,EAAA8D,eAAea,aAAe,MAAQ,OAAX,K,wFAhH9B3E,EAAA4E,a,2DAmItB,GACEpB,KAAM,mBACNqB,KAAAA,GACE,MAAMC,GAAQC,EAAAA,EAAAA,MACRC,GAASC,EAAAA,EAAAA,MACTC,EAAeJ,EAAMK,OAAOtC,GAG5B+B,GAAUQ,EAAAA,EAAAA,KAAI,GACdnE,GAAiBmE,EAAAA,EAAAA,IAAI,CAAC,GACtBxC,GAAcwC,EAAAA,EAAAA,IAAI,CAAC,GACnBtB,GAAiBsB,EAAAA,EAAAA,IAAI,MAGrBjF,GAAUkF,EAAAA,EAAAA,IAAS,KAGhB,GAIHtD,GAAQsD,EAAAA,EAAAA,IAAS,IACdpE,EAAeqE,MAAMC,eAAiB,IAI/CC,EAAAA,EAAAA,IAAU,KACRC,MAIF,MAAMA,EAAwBC,UAC5Bd,EAAQU,OAAQ,EAChB,IACE,MAAMK,QAAiBC,EAAAA,EAAMC,IAAI,2CAA2CX,KAC5EjE,EAAeqE,MAAQK,EAASG,KAAKA,KAGjC7E,EAAeqE,MAAMS,mBACjBC,EAAiB/E,EAAeqE,MAAMS,kBACtCE,EAAsBhF,EAAeqE,MAAMS,YAErD,CAAE,MAAOG,GACPC,QAAQD,MAAM,YAAaA,GAC3BE,EAAAA,GAAUF,MAAM,WAClB,CAAE,QACAtB,EAAQU,OAAQ,CAClB,GAIIU,EAAmBN,UACvB,IACE,MAAMC,QAAiBC,EAAAA,EAAMC,IAAI,wCAAwCQ,KACzEzD,EAAY0C,MAAQK,EAASG,KAAKA,IACpC,CAAE,MAAOI,GACPC,QAAQD,MAAM,YAAaA,EAC7B,GAIID,EAAwBP,UAC5B,IACE,MAAMC,QAAiBC,EAAAA,EAAMC,IAAI,8DAA8DQ,KAC/FvC,EAAewB,MAAQK,EAASG,KAAKA,IACvC,CAAE,MAAOI,GACPC,QAAQD,MAAM,cAAeA,EAC/B,GAII/E,EAAcmF,IAClB,IAAKA,EAAY,MAAO,IACxB,MAAMC,EAAO,IAAIC,KAAKF,GACtB,MAAO,GAAGC,EAAKE,kBAAkBF,EAAKG,WAAa,GAAGC,WAAWC,SAAS,EAAG,QAAQL,EAAKM,UAAUF,WAAWC,SAAS,EAAG,QAIvH3G,EAASA,KACb+E,EAAO8B,KAAK,sBAIRxG,EAAiBA,KACrB0E,EAAO8B,KAAK,uBAAuB5B,MAI/BlC,EAAoBA,KACpBJ,EAAY0C,MAAMzC,IACpBmC,EAAO8B,KAAK,oBAAoBlE,EAAY0C,MAAMzC,OAItD,MAAO,CACL+B,UACA3D,iBACA2B,cACAkB,iBACA/B,QACA5B,UACAgB,aACAlB,SACAK,iBACA0C,oBAEJ,G,UCnPF,MAAM+D,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://ms/./src/views/evaluations/EvaluationDetail.vue", "webpack://ms/./src/views/evaluations/EvaluationDetail.vue?f6c9"], "sourcesContent": ["<template>\r\n  <div class=\"evaluation-detail-container\">\r\n    <el-card class=\"box-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span class=\"title\">督导评价详情</span>\r\n          <div>\r\n            <el-button @click=\"goBack\">返回列表</el-button>\r\n            <el-button type=\"primary\" @click=\"editEvaluation\" v-if=\"isAdmin\">编辑</el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <div v-loading=\"loading\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-descriptions title=\"评价基本信息\" :column=\"3\" border>\r\n              <el-descriptions-item label=\"督导教研室\">{{ evaluationData.supervising_department }}</el-descriptions-item>\r\n              <el-descriptions-item label=\"评价日期\">{{ formatDate(evaluationData.evaluation_date) }}</el-descriptions-item>\r\n              <el-descriptions-item label=\"评估人\">{{ evaluationData.evaluator_name }}</el-descriptions-item>\r\n            </el-descriptions>\r\n            \r\n            <el-divider />\r\n            \r\n            <el-descriptions title=\"教学活动信息\" :column=\"3\" border>\r\n              <el-descriptions-item label=\"病例/主题\" :span=\"3\">{{ evaluationData.case_topic }}</el-descriptions-item>\r\n              <el-descriptions-item label=\"教学活动形式\">{{ evaluationData.teaching_form }}</el-descriptions-item>\r\n              <el-descriptions-item label=\"带教老师\" :span=\"2\">\r\n                {{ evaluationData.teacher_name }} ({{ evaluationData.teacher_title }})\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"学员姓名\">{{ evaluationData.student_name }}</el-descriptions-item>\r\n              <el-descriptions-item label=\"学员类别\" :span=\"2\">{{ evaluationData.student_type }}</el-descriptions-item>\r\n            </el-descriptions>\r\n            \r\n            <el-divider />\r\n            \r\n            <el-descriptions title=\"评价内容\" :column=\"1\" border>\r\n              <el-descriptions-item label=\"平均分\">\r\n                <el-rate\r\n                  v-model=\"score\"\r\n                  :max=\"10\"\r\n                  show-score\r\n                  disabled\r\n                  score-template=\"{value}\"\r\n                />\r\n              </el-descriptions-item>\r\n              \r\n              <el-descriptions-item label=\"亮点\">\r\n                <div class=\"description-content\">\r\n                  {{ evaluationData.highlights || '无' }}\r\n                </div>\r\n              </el-descriptions-item>\r\n              \r\n              <el-descriptions-item label=\"不足\">\r\n                <div class=\"description-content\">\r\n                  {{ evaluationData.shortcomings || '无' }}\r\n                </div>\r\n              </el-descriptions-item>\r\n              \r\n              <el-descriptions-item label=\"改进建议\">\r\n                <div class=\"description-content\">\r\n                  {{ evaluationData.improvement_suggestions || '无' }}\r\n                </div>\r\n              </el-descriptions-item>\r\n              \r\n              <el-descriptions-item label=\"能力认定\">\r\n                <el-tag :type=\"evaluationData.competency_approved ? 'success' : 'danger'\" size=\"large\">\r\n                  {{ evaluationData.competency_approved ? '同意' : '不同意' }}\r\n                </el-tag>\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-divider />\r\n        \r\n        <!-- 教师信息卡片 -->\r\n        <el-card class=\"teacher-info-card\" shadow=\"hover\" v-if=\"teacherData.id\">\r\n          <template #header>\r\n            <div class=\"teacher-card-header\">\r\n              <span>带教老师信息</span>\r\n              <el-button type=\"text\" @click=\"viewTeacherDetail\">查看详情</el-button>\r\n            </div>\r\n          </template>\r\n          \r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"4\">\r\n              <div class=\"teacher-avatar\">\r\n                <el-image\r\n                  v-if=\"teacherData.photo\"\r\n                  :src=\"`http://localhost:3000${teacherData.photo}`\"\r\n                  fit=\"cover\"\r\n                  class=\"avatar-image\"\r\n                  :preview-src-list=\"[`http://localhost:3000${teacherData.photo}`]\"\r\n                />\r\n                <el-avatar v-else :size=\"100\" icon=\"UserFilled\" />\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"20\">\r\n              <el-descriptions :column=\"3\" border>\r\n                <el-descriptions-item label=\"姓名\">{{ teacherData.name }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"性别\">{{ teacherData.gender }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"科室\">{{ teacherData.department }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"学校\">{{ teacherData.school }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"专业\">{{ teacherData.major }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"学历\">{{ teacherData.education }}</el-descriptions-item>\r\n              </el-descriptions>\r\n              \r\n              <div class=\"teacher-stats\" v-if=\"competencyData\">\r\n                <div class=\"teacher-stats-item\">\r\n                  <div class=\"stats-label\">评价总数:</div>\r\n                  <div class=\"stats-value\">{{ competencyData.total_evaluations }}</div>\r\n                </div>\r\n                <div class=\"teacher-stats-item\">\r\n                  <div class=\"stats-label\">认可数:</div>\r\n                  <div class=\"stats-value\">{{ competencyData.approved_count }}</div>\r\n                </div>\r\n                <div class=\"teacher-stats-item\">\r\n                  <div class=\"stats-label\">认可率:</div>\r\n                  <div class=\"stats-value\">{{ competencyData.approval_rate }}%</div>\r\n                </div>\r\n                <div class=\"teacher-stats-item\">\r\n                  <div class=\"stats-label\">认证状态:</div>\r\n                  <div class=\"stats-value\">\r\n                    <el-tag :type=\"competencyData.is_certified ? 'success' : 'info'\">\r\n                      {{ competencyData.is_certified ? '已认证' : '未认证' }}\r\n                    </el-tag>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, computed, onMounted } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'EvaluationDetail',\r\n  setup() {\r\n    const route = useRoute()\r\n    const router = useRouter()\r\n    const evaluationId = route.params.id\r\n    \r\n    // 基础数据\r\n    const loading = ref(false)\r\n    const evaluationData = ref({})\r\n    const teacherData = ref({})\r\n    const competencyData = ref(null)\r\n    \r\n    // 是否为管理员或督导\r\n    const isAdmin = computed(() => {\r\n      // 这里可以根据实际的用户角色判断\r\n      // 简单起见，这里暂时返回 true\r\n      return true\r\n    })\r\n    \r\n    // 评分\r\n    const score = computed(() => {\r\n      return evaluationData.value.average_score || 0\r\n    })\r\n    \r\n    // 生命周期钩子\r\n    onMounted(() => {\r\n      fetchEvaluationDetail()\r\n    })\r\n    \r\n    // 获取评价详情\r\n    const fetchEvaluationDetail = async () => {\r\n      loading.value = true\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/evaluations/${evaluationId}`)\r\n        evaluationData.value = response.data.data\r\n        \r\n        // 如果有教师ID，获取教师信息\r\n        if (evaluationData.value.teacher_id) {\r\n          await fetchTeacherData(evaluationData.value.teacher_id)\r\n          await fetchCompetencyStatus(evaluationData.value.teacher_id)\r\n        }\r\n      } catch (error) {\r\n        console.error('获取评价详情失败:', error)\r\n        ElMessage.error('获取评价详情失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 获取教师信息\r\n    const fetchTeacherData = async (teacherId) => {\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/teachers/${teacherId}`)\r\n        teacherData.value = response.data.data\r\n      } catch (error) {\r\n        console.error('获取教师信息失败:', error)\r\n      }\r\n    }\r\n    \r\n    // 获取能力认证状态\r\n    const fetchCompetencyStatus = async (teacherId) => {\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/evaluations/competency/teacher/${teacherId}`)\r\n        competencyData.value = response.data.data\r\n      } catch (error) {\r\n        console.error('获取能力认证状态失败:', error)\r\n      }\r\n    }\r\n    \r\n    // 格式化日期\r\n    const formatDate = (dateString) => {\r\n      if (!dateString) return '-'\r\n      const date = new Date(dateString)\r\n      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`\r\n    }\r\n    \r\n    // 返回列表\r\n    const goBack = () => {\r\n      router.push('/evaluations/list')\r\n    }\r\n    \r\n    // 编辑评价\r\n    const editEvaluation = () => {\r\n      router.push(`/evaluations/add?id=${evaluationId}`)\r\n    }\r\n    \r\n    // 查看教师详情\r\n    const viewTeacherDetail = () => {\r\n      if (teacherData.value.id) {\r\n        router.push(`/teachers/detail/${teacherData.value.id}`)\r\n      }\r\n    }\r\n    \r\n    return {\r\n      loading,\r\n      evaluationData,\r\n      teacherData,\r\n      competencyData,\r\n      score,\r\n      isAdmin,\r\n      formatDate,\r\n      goBack,\r\n      editEvaluation,\r\n      viewTeacherDetail\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.evaluation-detail-container {\r\n  padding: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.description-content {\r\n  white-space: pre-line;\r\n  padding: 10px;\r\n  background-color: #f7f7f7;\r\n  border-radius: 4px;\r\n  min-height: 50px;\r\n}\r\n\r\n.teacher-info-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.teacher-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.teacher-avatar {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.avatar-image {\r\n  width: 100px;\r\n  height: 100px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 2px solid #eee;\r\n}\r\n\r\n.teacher-stats {\r\n  display: flex;\r\n  margin-top: 15px;\r\n  background-color: #f7f7f7;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.teacher-stats-item {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.stats-label {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stats-value {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n</style> ", "import { render } from \"./EvaluationDetail.vue?vue&type=template&id=0e8fc6dd&scoped=true\"\nimport script from \"./EvaluationDetail.vue?vue&type=script&lang=js\"\nexport * from \"./EvaluationDetail.vue?vue&type=script&lang=js\"\n\nimport \"./EvaluationDetail.vue?vue&type=style&index=0&id=0e8fc6dd&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-0e8fc6dd\"]])\n\nexport default __exports__"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_component_el_button", "onClick", "$setup", "goBack", "_cache", "isAdmin", "_createBlock", "type", "editEvaluation", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_descriptions", "title", "column", "border", "_component_el_descriptions_item", "label", "evaluationData", "supervising_department", "formatDate", "evaluation_date", "evaluator_name", "_component_el_divider", "case_topic", "teaching_form", "teacher_name", "_toDisplayString", "teacher_title", "student_name", "student_type", "_component_el_rate", "score", "$event", "max", "disabled", "_hoisted_3", "highlights", "_hoisted_4", "shortcomings", "_hoisted_5", "improvement_suggestions", "_component_el_tag", "competency_approved", "size", "teacher<PERSON><PERSON>", "id", "shadow", "_hoisted_6", "viewTeacherDetail", "_hoisted_7", "photo", "_component_el_image", "src", "fit", "_component_el_avatar", "icon", "name", "gender", "department", "school", "major", "education", "competencyData", "_hoisted_8", "_hoisted_9", "_hoisted_10", "total_evaluations", "_hoisted_11", "_hoisted_12", "approved_count", "_hoisted_13", "_hoisted_14", "approval_rate", "_hoisted_15", "_hoisted_16", "is_certified", "loading", "setup", "route", "useRoute", "router", "useRouter", "evaluationId", "params", "ref", "computed", "value", "average_score", "onMounted", "fetchEvaluationDetail", "async", "response", "axios", "get", "data", "teacher_id", "fetchTeacherData", "fetchCompetencyStatus", "error", "console", "ElMessage", "teacherId", "dateString", "date", "Date", "getFullYear", "getMonth", "toString", "padStart", "getDate", "push", "__exports__", "render"], "sourceRoot": ""}