"use strict";(self["webpackChunkms"]=self["webpackChunkms"]||[]).push([[296],{6296:function(e,a,t){t.r(a),t.d(a,{default:function(){return A}});var l=t(6768),s=t(4232);const r={class:"exam-results-container"},n={class:"card-header"},o={key:0,class:"exam-info"},u={class:"results-stats"},i={class:"stat-card"},c={class:"stat-value"},d={class:"stat-card"},p={class:"stat-value"},k={class:"stat-card"},_={class:"stat-value"},v={class:"stat-card"},b={class:"stat-value"},m={class:"pagination-container"},g={key:0,class:"result-detail"},f={class:"answer-item-title"},h={class:"question-score"},F={class:"question-content"},w={key:0,class:"options-list"},C={class:"option-label"},y={class:"option-content"},x={key:0,class:"option-mark correct-mark"},W={key:1,class:"option-mark wrong-mark"},L={key:1,class:"true-false-answer"},S={class:"answer-row"},D={class:"answer-value"},R={class:"answer-row"},E={class:"answer-value"},X={key:2,class:"question-explanation"},V={class:"explanation-content"};function P(e,a,t,P,z,$){const K=(0,l.g2)("el-button"),T=(0,l.g2)("el-descriptions-item"),q=(0,l.g2)("el-tag"),O=(0,l.g2)("el-descriptions"),U=(0,l.g2)("el-col"),M=(0,l.g2)("el-row"),A=(0,l.g2)("el-input"),Q=(0,l.g2)("el-form-item"),I=(0,l.g2)("el-option"),j=(0,l.g2)("el-select"),B=(0,l.g2)("el-form"),H=(0,l.g2)("el-empty"),N=(0,l.g2)("el-table-column"),Y=(0,l.g2)("el-table"),G=(0,l.g2)("el-pagination"),J=(0,l.g2)("el-card"),Z=(0,l.g2)("el-divider"),ee=(0,l.g2)("Check"),ae=(0,l.g2)("el-icon"),te=(0,l.g2)("Close"),le=(0,l.g2)("el-collapse-item"),se=(0,l.g2)("el-collapse"),re=(0,l.g2)("el-dialog"),ne=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",r,[(0,l.bF)(J,{class:"box-card"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",n,[a[8]||(a[8]=(0,l.Lk)("span",{class:"title"},"考试成绩",-1)),(0,l.Lk)("div",null,[(0,l.bF)(K,{onClick:P.goBack},{default:(0,l.k6)(()=>a[6]||(a[6]=[(0,l.eW)("返回考试列表")])),_:1,__:[6]},8,["onClick"]),(0,l.bF)(K,{type:"primary",onClick:P.exportResults},{default:(0,l.k6)(()=>a[7]||(a[7]=[(0,l.eW)("导出成绩")])),_:1,__:[7]},8,["onClick"])])])]),default:(0,l.k6)(()=>[P.examData?((0,l.uX)(),(0,l.CE)("div",o,[(0,l.bF)(O,{title:"考试信息",column:3,border:""},{default:(0,l.k6)(()=>[(0,l.bF)(T,{label:"考试名称"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(P.examData.title),1)]),_:1}),(0,l.bF)(T,{label:"考试时长"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(P.examData.duration)+"分钟",1)]),_:1}),(0,l.bF)(T,{label:"总分"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(P.examData.total_score)+"分",1)]),_:1}),(0,l.bF)(T,{label:"及格分数"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(P.examData.pass_score)+"分",1)]),_:1}),(0,l.bF)(T,{label:"考试状态"},{default:(0,l.k6)(()=>[(0,l.bF)(q,{type:P.getExamStatusType(P.examData.status)},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(P.getExamStatusText(P.examData.status)),1)]),_:1},8,["type"])]),_:1}),(0,l.bF)(T,{label:"创建时间"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(P.formatDate(P.examData.created_at)),1)]),_:1})]),_:1})])):(0,l.Q3)("",!0),(0,l.Lk)("div",u,[(0,l.bF)(M,{gutter:20},{default:(0,l.k6)(()=>[(0,l.bF)(U,{span:6},{default:(0,l.k6)(()=>[(0,l.Lk)("div",i,[(0,l.Lk)("div",c,(0,s.v_)(P.totalParticipants),1),a[9]||(a[9]=(0,l.Lk)("div",{class:"stat-label"},"参考人数",-1))])]),_:1}),(0,l.bF)(U,{span:6},{default:(0,l.k6)(()=>[(0,l.Lk)("div",d,[(0,l.Lk)("div",p,(0,s.v_)(P.passCount),1),a[10]||(a[10]=(0,l.Lk)("div",{class:"stat-label"},"通过人数",-1))])]),_:1}),(0,l.bF)(U,{span:6},{default:(0,l.k6)(()=>[(0,l.Lk)("div",k,[(0,l.Lk)("div",_,(0,s.v_)(P.passRate)+"%",1),a[11]||(a[11]=(0,l.Lk)("div",{class:"stat-label"},"通过率",-1))])]),_:1}),(0,l.bF)(U,{span:6},{default:(0,l.k6)(()=>[(0,l.Lk)("div",v,[(0,l.Lk)("div",b,(0,s.v_)(P.averageScore),1),a[12]||(a[12]=(0,l.Lk)("div",{class:"stat-label"},"平均分",-1))])]),_:1})]),_:1})]),(0,l.bF)(B,{inline:!0,model:P.searchForm,class:"search-form"},{default:(0,l.k6)(()=>[(0,l.bF)(Q,{label:"姓名"},{default:(0,l.k6)(()=>[(0,l.bF)(A,{modelValue:P.searchForm.name,"onUpdate:modelValue":a[0]||(a[0]=e=>P.searchForm.name=e),placeholder:"教师姓名",clearable:""},null,8,["modelValue"])]),_:1}),(0,l.bF)(Q,{label:"科室"},{default:(0,l.k6)(()=>[(0,l.bF)(A,{modelValue:P.searchForm.department,"onUpdate:modelValue":a[1]||(a[1]=e=>P.searchForm.department=e),placeholder:"所属科室",clearable:""},null,8,["modelValue"])]),_:1}),(0,l.bF)(Q,{label:"成绩状态"},{default:(0,l.k6)(()=>[(0,l.bF)(j,{modelValue:P.searchForm.status,"onUpdate:modelValue":a[2]||(a[2]=e=>P.searchForm.status=e),placeholder:"成绩状态",clearable:""},{default:(0,l.k6)(()=>[(0,l.bF)(I,{label:"通过",value:"pass"}),(0,l.bF)(I,{label:"不通过",value:"fail"})]),_:1},8,["modelValue"])]),_:1}),(0,l.bF)(Q,null,{default:(0,l.k6)(()=>[(0,l.bF)(K,{type:"primary",onClick:P.handleSearch},{default:(0,l.k6)(()=>a[13]||(a[13]=[(0,l.eW)("查询")])),_:1,__:[13]},8,["onClick"]),(0,l.bF)(K,{onClick:P.resetSearch},{default:(0,l.k6)(()=>a[14]||(a[14]=[(0,l.eW)("重置")])),_:1,__:[14]},8,["onClick"])]),_:1})]),_:1},8,["model"]),(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",null,[0===P.results.length?((0,l.uX)(),(0,l.Wv)(H,{key:0,description:"暂无考试成绩"})):((0,l.uX)(),(0,l.Wv)(Y,{key:1,data:P.results,border:"",style:{width:"100%"}},{default:(0,l.k6)(()=>[(0,l.bF)(N,{type:"index",width:"50",label:"#"}),(0,l.bF)(N,{prop:"teacher_name",label:"姓名",width:"100"}),(0,l.bF)(N,{prop:"department",label:"科室",width:"120"}),(0,l.bF)(N,{prop:"score",label:"分数",width:"80"},{default:(0,l.k6)(e=>[(0,l.Lk)("span",{class:(0,s.C4)({"pass-score":P.isPass(e.row),"fail-score":!P.isPass(e.row)})},(0,s.v_)(e.row.score),3)]),_:1}),(0,l.bF)(N,{label:"状态",width:"100"},{default:(0,l.k6)(e=>[(0,l.bF)(q,{type:P.isPass(e.row)?"success":"danger"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(P.isPass(e.row)?"通过":"不通过"),1)]),_:2},1032,["type"])]),_:1}),(0,l.bF)(N,{prop:"start_time",label:"开始时间",width:"180"},{default:(0,l.k6)(e=>[(0,l.eW)((0,s.v_)(P.formatDateTime(e.row.start_time)),1)]),_:1}),(0,l.bF)(N,{prop:"end_time",label:"结束时间",width:"180"},{default:(0,l.k6)(e=>[(0,l.eW)((0,s.v_)(P.formatDateTime(e.row.end_time)),1)]),_:1}),(0,l.bF)(N,{prop:"duration",label:"用时",width:"120"},{default:(0,l.k6)(e=>[(0,l.eW)((0,s.v_)(P.formatDuration(e.row.start_time,e.row.end_time)),1)]),_:1}),(0,l.bF)(N,{label:"操作",width:"120",fixed:"right"},{default:(0,l.k6)(e=>[(0,l.bF)(K,{size:"small",onClick:a=>P.viewDetail(e.row)},{default:(0,l.k6)(()=>a[15]||(a[15]=[(0,l.eW)("查看详情")])),_:2,__:[15]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),(0,l.Lk)("div",m,[(0,l.bF)(G,{"current-page":P.currentPage,"onUpdate:currentPage":a[3]||(a[3]=e=>P.currentPage=e),"page-size":P.pageSize,"onUpdate:pageSize":a[4]||(a[4]=e=>P.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:P.total,onSizeChange:P.handleSizeChange,onCurrentChange:P.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])])),[[ne,P.loading]])]),_:1}),(0,l.bF)(re,{modelValue:P.detailDialogVisible,"onUpdate:modelValue":a[5]||(a[5]=e=>P.detailDialogVisible=e),title:"成绩详情",width:"800px"},{default:(0,l.k6)(()=>[P.currentResult?((0,l.uX)(),(0,l.CE)("div",g,[(0,l.bF)(O,{title:"考生信息",column:3,border:""},{default:(0,l.k6)(()=>[(0,l.bF)(T,{label:"姓名"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(P.currentResult.teacher_name),1)]),_:1}),(0,l.bF)(T,{label:"科室"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(P.currentResult.department),1)]),_:1}),(0,l.bF)(T,{label:"分数"},{default:(0,l.k6)(()=>[(0,l.Lk)("span",{class:(0,s.C4)({"pass-score":P.isPass(P.currentResult),"fail-score":!P.isPass(P.currentResult)})},(0,s.v_)(P.currentResult.score),3)]),_:1}),(0,l.bF)(T,{label:"状态"},{default:(0,l.k6)(()=>[(0,l.bF)(q,{type:P.isPass(P.currentResult)?"success":"danger"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(P.isPass(P.currentResult)?"通过":"不通过"),1)]),_:1},8,["type"])]),_:1}),(0,l.bF)(T,{label:"开始时间"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(P.formatDateTime(P.currentResult.start_time)),1)]),_:1}),(0,l.bF)(T,{label:"结束时间"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(P.formatDateTime(P.currentResult.end_time)),1)]),_:1})]),_:1}),(0,l.bF)(Z,{"content-position":"center"},{default:(0,l.k6)(()=>a[16]||(a[16]=[(0,l.eW)("答题详情")])),_:1,__:[16]}),P.currentResult.answers&&P.currentResult.answers.length>0?((0,l.uX)(),(0,l.Wv)(se,{key:0},{default:(0,l.k6)(()=>[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(P.currentResult.answers,(e,t)=>((0,l.uX)(),(0,l.Wv)(le,{key:t,name:t},{title:(0,l.k6)(()=>[(0,l.Lk)("div",f,[(0,l.Lk)("span",null,"第 "+(0,s.v_)(t+1)+" 题",1),(0,l.bF)(q,{type:e.is_correct?"success":"danger",size:"small"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(e.is_correct?"正确":"错误"),1)]),_:2},1032,["type"]),(0,l.Lk)("span",h,(0,s.v_)(e.score)+" 分",1)])]),default:(0,l.k6)(()=>[(0,l.Lk)("div",F,(0,s.v_)(e.question_content),1),"true_false"!==e.question_type?((0,l.uX)(),(0,l.CE)("div",w,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(e.options,(a,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,class:(0,s.C4)(["option-item",{"selected-option":P.isOptionSelected(e,a),"correct-option":a.is_correct,"wrong-option":P.isWrongOption(e,a)}])},[(0,l.Lk)("div",C,(0,s.v_)(String.fromCharCode(65+t)),1),(0,l.Lk)("div",y,(0,s.v_)(a.content),1),a.is_correct?((0,l.uX)(),(0,l.CE)("div",x,[(0,l.bF)(ae,null,{default:(0,l.k6)(()=>[(0,l.bF)(ee)]),_:1})])):P.isOptionSelected(e,a)?((0,l.uX)(),(0,l.CE)("div",W,[(0,l.bF)(ae,null,{default:(0,l.k6)(()=>[(0,l.bF)(te)]),_:1})])):(0,l.Q3)("",!0)],2))),128))])):((0,l.uX)(),(0,l.CE)("div",L,[(0,l.Lk)("div",S,[a[17]||(a[17]=(0,l.Lk)("div",{class:"answer-label"},"正确答案：",-1)),(0,l.Lk)("div",D,[(0,l.bF)(q,{type:e.correct_answer?"success":"danger"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(e.correct_answer?"正确":"错误"),1)]),_:2},1032,["type"])])]),(0,l.Lk)("div",R,[a[18]||(a[18]=(0,l.Lk)("div",{class:"answer-label"},"考生答案：",-1)),(0,l.Lk)("div",E,[(0,l.bF)(q,{type:e.user_answer===e.correct_answer?"success":"danger"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(e.user_answer?"正确":"错误"),1)]),_:2},1032,["type"])])])])),e.explanation?((0,l.uX)(),(0,l.CE)("div",X,[a[19]||(a[19]=(0,l.Lk)("div",{class:"explanation-label"},"解析：",-1)),(0,l.Lk)("div",V,(0,s.v_)(e.explanation),1)])):(0,l.Q3)("",!0)]),_:2},1032,["name"]))),128))]),_:1})):((0,l.uX)(),(0,l.Wv)(H,{key:1,description:"暂无答题详情"}))])):(0,l.Q3)("",!0)]),_:1},8,["modelValue"])])}t(4114),t(8111),t(2489),t(5207),t(8237);var z=t(144),$=t(1387),K=t(1219),T=t(7477),q=t(4373),O={name:"ExamResults",components:{Check:T.Check,Close:T.Close},setup(){const e=(0,$.lq)(),a=(0,$.rd)(),t=e.params.id,s=(0,z.KR)(!1),r=(0,z.KR)(null),n=(0,z.KR)([]),o=(0,z.KR)(0),u=(0,z.KR)(1),i=(0,z.KR)(10),c=(0,z.KR)(!1),d=(0,z.KR)(null),p=(0,z.Kh)({name:"",department:"",status:""}),k=(0,l.EW)(()=>n.value.length),_=(0,l.EW)(()=>n.value.filter(e=>F(e)).length),v=(0,l.EW)(()=>0===k.value?0:Math.round(_.value/k.value*100)),b=(0,l.EW)(()=>{if(0===k.value)return 0;const e=n.value.reduce((e,a)=>e+a.score,0);return(e/k.value).toFixed(1)});(0,l.sV)(()=>{m(),g()});const m=async()=>{try{const e=await q.A.get(`http://localhost:3000/api/exams/${t}`);r.value=e.data.data}catch(e){console.error("获取考试信息失败:",e),K.nk.error("获取考试信息失败")}},g=async()=>{s.value=!0;try{const e=await q.A.get(`http://localhost:3000/api/exams/${t}/results`);n.value=e.data.data,o.value=e.data.count}catch(e){console.error("获取考试成绩失败:",e),K.nk.error("获取考试成绩失败")}finally{s.value=!1}},f=e=>{const a={draft:"草稿",published:"已发布",in_progress:"进行中",completed:"已结束"};return a[e]||"未知状态"},h=e=>{const a={draft:"info",published:"success",in_progress:"warning",completed:"danger"};return a[e]||"info"},F=e=>!(!r.value||!e)&&e.score>=r.value.pass_score,w=e=>{if(!e)return"-";const a=new Date(e);return`${a.getFullYear()}-${(a.getMonth()+1).toString().padStart(2,"0")}-${a.getDate().toString().padStart(2,"0")}`},C=e=>{if(!e)return"-";const a=new Date(e);return`${w(e)} ${a.getHours().toString().padStart(2,"0")}:${a.getMinutes().toString().padStart(2,"0")}`},y=(e,a)=>{if(!e||!a)return"-";const t=new Date(e),l=new Date(a),s=l-t,r=Math.floor(s/6e4),n=Math.floor(s%6e4/1e3);return`${r}分${n}秒`},x=()=>{a.push("/exams/list")},W=()=>{u.value=1,g()},L=()=>{Object.keys(p).forEach(e=>{p[e]=""}),u.value=1,g()},S=e=>{i.value=e,g()},D=e=>{u.value=e,g()},R=async e=>{try{const a=await q.A.get(`http://localhost:3000/api/exams/results/${e.id}`);d.value=a.data.data,c.value=!0}catch(a){console.error("获取成绩详情失败:",a),K.nk.error("获取成绩详情失败")}},E=()=>{window.open(`http://localhost:3000/api/exams/${t}/results/export`,"_blank")},X=(e,a)=>!!e.user_answers&&e.user_answers.includes(a.id),V=(e,a)=>X(e,a)&&!a.is_correct;return{loading:s,examData:r,results:n,total:o,currentPage:u,pageSize:i,searchForm:p,detailDialogVisible:c,currentResult:d,totalParticipants:k,passCount:_,passRate:v,averageScore:b,getExamStatusText:f,getExamStatusType:h,isPass:F,formatDate:w,formatDateTime:C,formatDuration:y,goBack:x,handleSearch:W,resetSearch:L,handleSizeChange:S,handleCurrentChange:D,viewDetail:R,exportResults:E,isOptionSelected:X,isWrongOption:V}}},U=t(1241);const M=(0,U.A)(O,[["render",P],["__scopeId","data-v-6993119e"]]);var A=M}}]);
//# sourceMappingURL=296.04e512cf.js.map