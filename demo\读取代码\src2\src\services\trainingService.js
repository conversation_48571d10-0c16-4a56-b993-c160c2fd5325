import axios from 'axios';
import { API_URL } from '../utils/api';

/**
 * 获取所有培训课程
 * @param {Object} params - 请求参数
 * @returns {Promise} - 请求Promise
 */
export const getAllTrainings = async (params = {}) => {
  try {
    const response = await axios.get(`${API_URL}/trainings`, { params });
    return response.data;
  } catch (error) {
    console.error('获取培训课程列表失败:', error);
    throw error;
  }
};

/**
 * 按类型获取培训课程
 * @param {string} type - 课程类型
 * @param {Object} params - 请求参数
 * @returns {Promise} - 请求Promise
 */
export const getTrainingsByType = async (type, params = {}) => {
  try {
    const response = await axios.get(`${API_URL}/trainings/type/${encodeURIComponent(type)}`, { params });
    return response.data;
  } catch (error) {
    console.error(`获取${type}类型培训课程失败:`, error);
    throw error;
  }
};

/**
 * 获取单个培训课程详情
 * @param {string} id - 课程ID
 * @returns {Promise} - 请求Promise
 */
export const getTrainingById = async (id) => {
  try {
    const response = await axios.get(`${API_URL}/trainings/${id}`);
    return response.data;
  } catch (error) {
    console.error('获取培训课程详情失败:', error);
    throw error;
  }
};

/**
 * 创建新的培训课程
 * @param {Object} trainingData - 培训课程数据
 * @param {File} file - 课程文件
 * @returns {Promise} - 请求Promise
 */
export const createTraining = async (trainingData, file) => {
  try {
    const formData = new FormData();
    
    // 添加基本课程数据
    formData.append('title', trainingData.title);
    formData.append('description', trainingData.description || '');
    formData.append('course_type', trainingData.course_type);
    
    // 添加文件（如果有）
    if (file) {
      formData.append('material', file);
    }
    
    const response = await axios.post(`${API_URL}/trainings`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('创建培训课程失败:', error);
    throw error;
  }
};

/**
 * 更新培训课程
 * @param {string} id - 课程ID
 * @param {Object} trainingData - 更新的课程数据
 * @param {File} file - 课程文件（可选）
 * @returns {Promise} - 请求Promise
 */
export const updateTraining = async (id, trainingData, file) => {
  try {
    const formData = new FormData();
    
    // 添加基本课程数据
    formData.append('title', trainingData.title);
    formData.append('description', trainingData.description || '');
    formData.append('course_type', trainingData.course_type);
    
    // 添加文件（如果有）
    if (file) {
      formData.append('material', file);
    }
    // 如果没有新文件但有旧文件信息，则传递旧文件信息
    else if (trainingData.material_path && trainingData.original_filename) {
      formData.append('material_path', trainingData.material_path);
      formData.append('original_filename', trainingData.original_filename);
    }
    
    const response = await axios.put(`${API_URL}/trainings/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('更新培训课程失败:', error);
    throw error;
  }
};

/**
 * 删除培训课程
 * @param {string} id - 课程ID
 * @returns {Promise} - 请求Promise
 */
export const deleteTraining = async (id) => {
  try {
    const response = await axios.delete(`${API_URL}/trainings/${id}`);
    return response.data;
  } catch (error) {
    console.error('删除培训课程失败:', error);
    throw error;
  }
};

/**
 * 上传课程材料
 * @param {File} file - 文件对象
 * @returns {Promise} - 请求Promise
 */
export const uploadCourseMaterial = async (file) => {
  try {
    const formData = new FormData();
    formData.append('material', file);
    
    const response = await axios.post(`${API_URL}/upload/course`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('上传课程材料失败:', error);
    throw error;
  }
}; 