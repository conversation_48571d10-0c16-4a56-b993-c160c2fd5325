{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport api from '@/utils/api';\nconst teacherService = {\n  /**\r\n   * 获取教师列表\r\n   * @param {Object} params - 查询参数\r\n   * @returns {Promise}\r\n   */\n  getTeachers(params) {\n    return api.get('/api/teachers', {\n      params\n    });\n  },\n  /**\r\n   * 获取单个教师\r\n   * @param {number} id - 教师ID\r\n   * @returns {Promise}\r\n   */\n  getTeacher(id) {\n    return api.get(`/teachers/${id}`);\n  },\n  /**\r\n   * 创建教师\r\n   * @param {Object} teacherData - 教师数据\r\n   * @returns {Promise}\r\n   */\n  createTeacher(teacherData) {\n    const formData = new FormData();\n\n    // 添加基本教师信息到表单\n    Object.keys(teacherData).forEach(key => {\n      if (key === 'photo' && teacherData[key] instanceof File) {\n        formData.append('photo', teacherData[key]);\n      } else if (teacherData[key] !== null && teacherData[key] !== undefined) {\n        formData.append(key, teacherData[key]);\n      }\n    });\n    return api.post('/teachers', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n  },\n  /**\r\n   * 更新教师\r\n   * @param {number} id - 教师ID\r\n   * @param {Object} teacherData - 教师数据\r\n   * @returns {Promise}\r\n   */\n  updateTeacher(id, teacherData) {\n    const formData = new FormData();\n\n    // 添加基本教师信息到表单\n    Object.keys(teacherData).forEach(key => {\n      if (key === 'photo' && teacherData[key] instanceof File) {\n        formData.append('photo', teacherData[key]);\n      } else if (teacherData[key] !== null && teacherData[key] !== undefined) {\n        formData.append(key, teacherData[key]);\n      }\n    });\n    return api.put(`/teachers/${id}`, formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n  },\n  /**\r\n   * 删除教师\r\n   * @param {number} id - 教师ID\r\n   * @returns {Promise}\r\n   */\n  deleteTeacher(id) {\n    return api.delete(`/teachers/${id}`);\n  },\n  /**\r\n   * 获取教师督导评价\r\n   * @param {number} teacherId - 教师ID\r\n   * @returns {Promise}\r\n   */\n  getTeacherEvaluations(teacherId) {\n    return api.get(`/evaluations/teacher/${teacherId}`);\n  },\n  /**\r\n   * 获取教师能力认定状态\r\n   * @param {number} teacherId - 教师ID\r\n   * @returns {Promise}\r\n   */\n  getTeacherCompetency(teacherId) {\n    return api.get(`/evaluations/competency/teacher/${teacherId}`);\n  },\n  /**\r\n   * 下载教师导入模板\r\n   * @returns {Promise}\r\n   */\n  downloadImportTemplate() {\n    return api.get('/api/teachers/import/template', {\n      responseType: 'blob'\n    });\n  },\n  /**\r\n   * 批量导入教师\r\n   * @param {File} file - Excel文件\r\n   * @returns {Promise}\r\n   */\n  importTeachers(file) {\n    const formData = new FormData();\n    formData.append('excel', file);\n    return api.post('/api/teachers/import/excel', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n  }\n};\nexport default teacherService;", "map": {"version": 3, "names": ["api", "teacherService", "getTeachers", "params", "get", "<PERSON><PERSON><PERSON>er", "id", "create<PERSON><PERSON>er", "teacher<PERSON><PERSON>", "formData", "FormData", "Object", "keys", "for<PERSON>ach", "key", "File", "append", "undefined", "post", "headers", "update<PERSON><PERSON>er", "put", "deleteTeacher", "delete", "getTeacherEvaluations", "teacherId", "getTeacherCompetency", "downloadImportTemplate", "responseType", "importTeachers", "file"], "sources": ["D:/admin/202506/督导系统/后台管理系统/ms/src/services/teacherService.js"], "sourcesContent": ["import api from '@/utils/api';\r\n\r\nconst teacherService = {\r\n  /**\r\n   * 获取教师列表\r\n   * @param {Object} params - 查询参数\r\n   * @returns {Promise}\r\n   */\r\n  getTeachers(params) {\r\n    return api.get('/api/teachers', { params });\r\n  },\r\n\r\n  /**\r\n   * 获取单个教师\r\n   * @param {number} id - 教师ID\r\n   * @returns {Promise}\r\n   */\r\n  getTeacher(id) {\r\n    return api.get(`/teachers/${id}`);\r\n  },\r\n\r\n  /**\r\n   * 创建教师\r\n   * @param {Object} teacherData - 教师数据\r\n   * @returns {Promise}\r\n   */\r\n  createTeacher(teacherData) {\r\n    const formData = new FormData();\r\n    \r\n    // 添加基本教师信息到表单\r\n    Object.keys(teacherData).forEach(key => {\r\n      if (key === 'photo' && teacherData[key] instanceof File) {\r\n        formData.append('photo', teacherData[key]);\r\n      } else if (teacherData[key] !== null && teacherData[key] !== undefined) {\r\n        formData.append(key, teacherData[key]);\r\n      }\r\n    });\r\n    \r\n    return api.post('/teachers', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data'\r\n      }\r\n    });\r\n  },\r\n\r\n  /**\r\n   * 更新教师\r\n   * @param {number} id - 教师ID\r\n   * @param {Object} teacherData - 教师数据\r\n   * @returns {Promise}\r\n   */\r\n  updateTeacher(id, teacherData) {\r\n    const formData = new FormData();\r\n    \r\n    // 添加基本教师信息到表单\r\n    Object.keys(teacherData).forEach(key => {\r\n      if (key === 'photo' && teacherData[key] instanceof File) {\r\n        formData.append('photo', teacherData[key]);\r\n      } else if (teacherData[key] !== null && teacherData[key] !== undefined) {\r\n        formData.append(key, teacherData[key]);\r\n      }\r\n    });\r\n    \r\n    return api.put(`/teachers/${id}`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data'\r\n      }\r\n    });\r\n  },\r\n\r\n  /**\r\n   * 删除教师\r\n   * @param {number} id - 教师ID\r\n   * @returns {Promise}\r\n   */\r\n  deleteTeacher(id) {\r\n    return api.delete(`/teachers/${id}`);\r\n  },\r\n\r\n  /**\r\n   * 获取教师督导评价\r\n   * @param {number} teacherId - 教师ID\r\n   * @returns {Promise}\r\n   */\r\n  getTeacherEvaluations(teacherId) {\r\n    return api.get(`/evaluations/teacher/${teacherId}`);\r\n  },\r\n  \r\n  /**\r\n   * 获取教师能力认定状态\r\n   * @param {number} teacherId - 教师ID\r\n   * @returns {Promise}\r\n   */\r\n  getTeacherCompetency(teacherId) {\r\n    return api.get(`/evaluations/competency/teacher/${teacherId}`);\r\n  },\r\n\r\n  /**\r\n   * 下载教师导入模板\r\n   * @returns {Promise}\r\n   */\r\n  downloadImportTemplate() {\r\n    return api.get('/api/teachers/import/template', {\r\n      responseType: 'blob'\r\n    });\r\n  },\r\n\r\n  /**\r\n   * 批量导入教师\r\n   * @param {File} file - Excel文件\r\n   * @returns {Promise}\r\n   */\r\n  importTeachers(file) {\r\n    const formData = new FormData();\r\n    formData.append('excel', file);\r\n\r\n    return api.post('/api/teachers/import/excel', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data'\r\n      }\r\n    });\r\n  }\r\n};\r\n\r\nexport default teacherService; "], "mappings": ";;AAAA,OAAOA,GAAG,MAAM,aAAa;AAE7B,MAAMC,cAAc,GAAG;EACrB;AACF;AACA;AACA;AACA;EACEC,WAAWA,CAACC,MAAM,EAAE;IAClB,OAAOH,GAAG,CAACI,GAAG,CAAC,eAAe,EAAE;MAAED;IAAO,CAAC,CAAC;EAC7C,CAAC;EAED;AACF;AACA;AACA;AACA;EACEE,UAAUA,CAACC,EAAE,EAAE;IACb,OAAON,GAAG,CAACI,GAAG,CAAC,aAAaE,EAAE,EAAE,CAAC;EACnC,CAAC;EAED;AACF;AACA;AACA;AACA;EACEC,aAAaA,CAACC,WAAW,EAAE;IACzB,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;IAE/B;IACAC,MAAM,CAACC,IAAI,CAACJ,WAAW,CAAC,CAACK,OAAO,CAACC,GAAG,IAAI;MACtC,IAAIA,GAAG,KAAK,OAAO,IAAIN,WAAW,CAACM,GAAG,CAAC,YAAYC,IAAI,EAAE;QACvDN,QAAQ,CAACO,MAAM,CAAC,OAAO,EAAER,WAAW,CAACM,GAAG,CAAC,CAAC;MAC5C,CAAC,MAAM,IAAIN,WAAW,CAACM,GAAG,CAAC,KAAK,IAAI,IAAIN,WAAW,CAACM,GAAG,CAAC,KAAKG,SAAS,EAAE;QACtER,QAAQ,CAACO,MAAM,CAACF,GAAG,EAAEN,WAAW,CAACM,GAAG,CAAC,CAAC;MACxC;IACF,CAAC,CAAC;IAEF,OAAOd,GAAG,CAACkB,IAAI,CAAC,WAAW,EAAET,QAAQ,EAAE;MACrCU,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEC,aAAaA,CAACd,EAAE,EAAEE,WAAW,EAAE;IAC7B,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;IAE/B;IACAC,MAAM,CAACC,IAAI,CAACJ,WAAW,CAAC,CAACK,OAAO,CAACC,GAAG,IAAI;MACtC,IAAIA,GAAG,KAAK,OAAO,IAAIN,WAAW,CAACM,GAAG,CAAC,YAAYC,IAAI,EAAE;QACvDN,QAAQ,CAACO,MAAM,CAAC,OAAO,EAAER,WAAW,CAACM,GAAG,CAAC,CAAC;MAC5C,CAAC,MAAM,IAAIN,WAAW,CAACM,GAAG,CAAC,KAAK,IAAI,IAAIN,WAAW,CAACM,GAAG,CAAC,KAAKG,SAAS,EAAE;QACtER,QAAQ,CAACO,MAAM,CAACF,GAAG,EAAEN,WAAW,CAACM,GAAG,CAAC,CAAC;MACxC;IACF,CAAC,CAAC;IAEF,OAAOd,GAAG,CAACqB,GAAG,CAAC,aAAaf,EAAE,EAAE,EAAEG,QAAQ,EAAE;MAC1CU,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEG,aAAaA,CAAChB,EAAE,EAAE;IAChB,OAAON,GAAG,CAACuB,MAAM,CAAC,aAAajB,EAAE,EAAE,CAAC;EACtC,CAAC;EAED;AACF;AACA;AACA;AACA;EACEkB,qBAAqBA,CAACC,SAAS,EAAE;IAC/B,OAAOzB,GAAG,CAACI,GAAG,CAAC,wBAAwBqB,SAAS,EAAE,CAAC;EACrD,CAAC;EAED;AACF;AACA;AACA;AACA;EACEC,oBAAoBA,CAACD,SAAS,EAAE;IAC9B,OAAOzB,GAAG,CAACI,GAAG,CAAC,mCAAmCqB,SAAS,EAAE,CAAC;EAChE,CAAC;EAED;AACF;AACA;AACA;EACEE,sBAAsBA,CAAA,EAAG;IACvB,OAAO3B,GAAG,CAACI,GAAG,CAAC,+BAA+B,EAAE;MAC9CwB,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEC,cAAcA,CAACC,IAAI,EAAE;IACnB,MAAMrB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACO,MAAM,CAAC,OAAO,EAAEc,IAAI,CAAC;IAE9B,OAAO9B,GAAG,CAACkB,IAAI,CAAC,4BAA4B,EAAET,QAAQ,EAAE;MACtDU,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AAED,eAAelB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}