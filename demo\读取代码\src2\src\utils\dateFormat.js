/**
 * 格式化日期时间
 * @param {string|Date} date - 要格式化的日期
 * @param {string} format - 格式化模式，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  console.log(date)
  if (!date) return '';
  
  const d = typeof date === 'string' ? new Date(date) : new Date(date);
  console.log(d)
  if (isNaN(d.getTime())) return '';
  
  const o = {
    'M+': d.getMonth() + 1, // 月份
    'D+': d.getDate(), // 日
    'H+': d.getHours(), // 小时
    'm+': d.getMinutes(), // 分
    's+': d.getSeconds(), // 秒
    'q+': Math.floor((d.getMonth() + 3) / 3), // 季度
    'S': d.getMilliseconds() // 毫秒
  };
  
  // 替换年份
  if (/(Y+)/.test(format)) {
    format = format.replace(RegExp.$1, (d.getFullYear() + '').substring(4 - RegExp.$1.length));
  }
  
  // 替换其他时间单位
  for (let k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substring(('' + o[k]).length)
      );
    }
  }
  console.log(format)
  return format;
}

/**
 * 相对时间格式化（如：3分钟前，2小时前，昨天，等）
 * @param {string|Date} date - 要格式化的日期
 * @returns {string} 格式化后的相对时间字符串
 */
export function formatRelativeTime(date) {
  if (!date) return '';
  
  const d = typeof date === 'string' ? new Date(date.replace(/-/g, '/')) : new Date(date);
  
  if (isNaN(d.getTime())) return '';
  
  const now = new Date();
  const diff = now.getTime() - d.getTime(); // 时间差（毫秒）
  
  // 转换为秒
  const seconds = Math.floor(diff / 1000);
  
  if (seconds < 60) {
    return '刚刚';
  } else if (seconds < 3600) { // 小于1小时
    return Math.floor(seconds / 60) + '分钟前';
  } else if (seconds < 86400) { // 小于1天
    return Math.floor(seconds / 3600) + '小时前';
  } else if (seconds < 172800) { // 小于2天
    return '昨天';
  } else if (seconds < 2592000) { // 小于30天
    return Math.floor(seconds / 86400) + '天前';
  } else if (seconds < 31536000) { // 小于1年
    return Math.floor(seconds / 2592000) + '个月前';
  } else {
    return Math.floor(seconds / 31536000) + '年前';
  }
} 