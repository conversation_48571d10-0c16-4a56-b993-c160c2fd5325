{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nimport { ref, reactive, computed, onMounted, watch } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport axios from 'axios';\nimport { Plus } from '@element-plus/icons-vue';\nimport { useRouter } from 'vue-router';\nexport default {\n  name: 'TeacherList',\n  components: {\n    Plus\n  },\n  setup() {\n    // 路由器\n    const router = useRouter();\n    let token = localStorage.getItem('token');\n    if (token) {\n      axios.defaults.headers['Authorization'] = `Bearer ${token}`;\n    }\n    console.log(axios.defaults.headers['Authorization']);\n    // API基础URL\n    const baseUrl = 'http://localhost:3000';\n\n    // 上传头像的headers\n    const uploadHeaders = {\n      // 如果需要认证可以在这里添加\n    };\n\n    // 基础数据\n    const loading = ref(false);\n    const dialogVisible = ref(false);\n    const teacherFormRef = ref(null);\n    const teacherList = ref([]);\n    const total = ref(0);\n    const currentPage = ref(1);\n    const pageSize = ref(10);\n    const photoPreview = ref('');\n\n    // 导入相关数据\n    const importDialogVisible = ref(false);\n    const uploadRef = ref(null);\n    const uploading = ref(false);\n    const fileList = ref([]);\n    const importResult = ref(null);\n\n    // 搜索表单\n    const searchForm = reactive({\n      name: '',\n      department: '',\n      is_employed: ''\n    });\n\n    // 表单数据\n    const formData = reactive({\n      id: '',\n      name: '',\n      gender: '男',\n      department: '',\n      school: '',\n      major: '',\n      education: '',\n      is_employed: 1,\n      employment_period: '',\n      phone: '',\n      photo: ''\n    });\n\n    // 表单校验规则\n    const formRules = reactive({\n      name: [{\n        required: true,\n        message: '请输入姓名',\n        trigger: 'blur'\n      }, {\n        min: 2,\n        max: 10,\n        message: '长度在 2 到 10 个字符',\n        trigger: 'blur'\n      }],\n      gender: [{\n        required: true,\n        message: '请选择性别',\n        trigger: 'change'\n      }],\n      department: [{\n        required: true,\n        message: '请输入科室',\n        trigger: 'blur'\n      }],\n      school: [{\n        required: true,\n        message: '请输入学校',\n        trigger: 'blur'\n      }],\n      major: [{\n        required: true,\n        message: '请输入专业',\n        trigger: 'blur'\n      }],\n      education: [{\n        required: true,\n        message: '请选择学历',\n        trigger: 'change'\n      }]\n    });\n\n    // 监听表单数据变化，更新照片预览\n    watch(() => formData.photo, newVal => {\n      if (newVal) {\n        if (newVal.startsWith('http')) {\n          photoPreview.value = newVal;\n        } else {\n          photoPreview.value = `${baseUrl}${newVal}`;\n        }\n      } else {\n        photoPreview.value = '';\n      }\n    }, {\n      immediate: true\n    });\n\n    // 生命周期钩子\n    onMounted(() => {\n      fetchTeachers();\n    });\n\n    // 获取教师列表\n    const fetchTeachers = async () => {\n      loading.value = true;\n      try {\n        // 构建查询参数\n        const params = new URLSearchParams();\n        if (searchForm.name) params.append('name', searchForm.name);\n        if (searchForm.department) params.append('department', searchForm.department);\n        if (searchForm.is_employed !== '') params.append('is_employed', searchForm.is_employed);\n        params.append('page', currentPage.value);\n        params.append('limit', pageSize.value);\n        const response = await axios.get(`${baseUrl}/api/teachers`, {\n          params\n        });\n        teacherList.value = response.data.data;\n        total.value = response.data.count;\n      } catch (error) {\n        console.error('获取教师列表失败:', error);\n        ElMessage.error('获取教师列表失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 搜索操作\n    const handleSearch = () => {\n      currentPage.value = 1;\n      fetchTeachers();\n    };\n\n    // 重置搜索\n    const resetSearch = () => {\n      Object.keys(searchForm).forEach(key => {\n        searchForm[key] = '';\n      });\n      currentPage.value = 1;\n      fetchTeachers();\n    };\n\n    // 分页操作\n    const handleSizeChange = val => {\n      pageSize.value = val;\n      fetchTeachers();\n    };\n    const handleCurrentChange = val => {\n      currentPage.value = val;\n      fetchTeachers();\n    };\n\n    // 打开对话框\n    const openDialog = row => {\n      if (row) {\n        // 编辑模式\n        Object.keys(formData).forEach(key => {\n          formData[key] = row[key];\n        });\n        // 更新照片预览\n        if (row.photo) {\n          photoPreview.value = `${baseUrl}${row.photo}`;\n        }\n      } else {\n        // 新增模式\n        Object.keys(formData).forEach(key => {\n          formData[key] = key === 'gender' ? '男' : key === 'is_employed' ? 1 : '';\n        });\n        photoPreview.value = '';\n      }\n      dialogVisible.value = true;\n    };\n\n    // 照片上传前验证\n    const beforePhotoUpload = file => {\n      const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/png';\n      const isLt2M = file.size / 1024 / 1024 < 2;\n      if (!isJPGOrPNG) {\n        ElMessage.error('上传头像图片只能是 JPG 或 PNG 格式!');\n        return false;\n      }\n      if (!isLt2M) {\n        ElMessage.error('上传头像图片大小不能超过 2MB!');\n        return false;\n      }\n\n      // 创建临时预览\n      photoPreview.value = URL.createObjectURL(file);\n      return true;\n    };\n\n    // 照片上传成功回调\n    const handlePhotoSuccess = response => {\n      console.log('照片上传成功响应:', response);\n      if (response.success) {\n        formData.photo = response.path;\n        console.log('设置照片路径:', formData.photo);\n        ElMessage.success('照片上传成功');\n      } else {\n        ElMessage.error(response.message || '照片上传失败');\n      }\n    };\n\n    // 照片上传失败回调\n    const handlePhotoError = err => {\n      console.error('照片上传失败:', err);\n      ElMessage.error('照片上传失败，请检查网络连接');\n    };\n\n    // 提交表单\n    const submitForm = async () => {\n      if (!teacherFormRef.value) return;\n      await teacherFormRef.value.validate(async valid => {\n        if (valid) {\n          try {\n            console.log('提交表单数据:', formData);\n\n            // 创建表单数据对象\n            const formDataToSubmit = new FormData();\n\n            // 添加所有字段到FormData\n            Object.keys(formData).forEach(key => {\n              if (formData[key] !== null && formData[key] !== undefined && formData[key] !== '') {\n                formDataToSubmit.append(key, formData[key]);\n              }\n            });\n            const config = {\n              headers: {\n                'Content-Type': 'multipart/form-data'\n              }\n            };\n            if (formData.id) {\n              // 编辑\n              await axios.put(`${baseUrl}/api/teachers/${formData.id}`, formDataToSubmit, config);\n              ElMessage.success('教师信息更新成功');\n            } else {\n              // 新增\n              await axios.post(`${baseUrl}/api/teachers`, formDataToSubmit, config);\n              ElMessage.success('教师添加成功');\n            }\n            dialogVisible.value = false;\n            fetchTeachers();\n          } catch (error) {\n            console.error('操作失败:', error);\n            ElMessage.error(error.response?.data?.message || '操作失败');\n          }\n        } else {\n          return false;\n        }\n      });\n    };\n\n    // 删除教师\n    const handleDelete = row => {\n      ElMessageBox.confirm(`确定要删除教师 ${row.name} 吗?`, '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async () => {\n        try {\n          await axios.delete(`${baseUrl}/api/teachers/${row.id}`);\n          ElMessage.success('删除成功');\n          // 如果当前页只有一条数据且不是第一页，删除后跳转到上一页\n          if (teacherList.value.length === 1 && currentPage.value > 1) {\n            currentPage.value--;\n          }\n          fetchTeachers();\n        } catch (error) {\n          console.error('删除失败:', error);\n          ElMessage.error(error.response?.data?.message || '删除失败');\n        }\n      }).catch(() => {\n        ElMessage.info('已取消删除');\n      });\n    };\n\n    // 查看详情\n    const viewDetails = id => {\n      // 跳转到详情页面\n      router.push(`/teachers/detail/${id}`);\n    };\n\n    // 下载模板\n    const downloadTemplate = async () => {\n      try {\n        const response = await axios.get(`${baseUrl}/api/teachers/import/template`, {\n          responseType: 'blob'\n        });\n\n        // 创建下载链接\n        const url = window.URL.createObjectURL(new Blob([response.data]));\n        const link = document.createElement('a');\n        link.href = url;\n        link.setAttribute('download', 'teacher_import_template.xlsx');\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n        ElMessage.success('模板下载成功');\n      } catch (error) {\n        console.error('下载模板失败:', error);\n        ElMessage.error('下载模板失败');\n      }\n    };\n\n    // 打开导入对话框\n    const openImportDialog = () => {\n      importDialogVisible.value = true;\n      fileList.value = [];\n      importResult.value = null;\n    };\n\n    // Excel文件上传前验证\n    const beforeExcelUpload = file => {\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'application/vnd.ms-excel';\n      const isLt10M = file.size / 1024 / 1024 < 10;\n      if (!isExcel) {\n        ElMessage.error('只能上传Excel文件!');\n        return false;\n      }\n      if (!isLt10M) {\n        ElMessage.error('文件大小不能超过10MB!');\n        return false;\n      }\n      return true;\n    };\n\n    // 手动上传\n    const handleUpload = () => {\n      if (fileList.value.length === 0) {\n        ElMessage.warning('请先选择要上传的文件');\n        return;\n      }\n      uploading.value = true;\n      uploadRef.value.submit();\n    };\n\n    // 导入成功回调\n    const handleImportSuccess = response => {\n      uploading.value = false;\n      console.log('导入成功响应:', response);\n      if (response.success) {\n        importResult.value = response.data;\n        ElMessage.success(response.message);\n        // 刷新教师列表\n        fetchTeachers();\n      } else {\n        ElMessage.error(response.message || '导入失败');\n      }\n    };\n\n    // 导入失败回调\n    const handleImportError = error => {\n      uploading.value = false;\n      console.error('导入失败:', error);\n      ElMessage.error('导入失败，请检查文件格式和网络连接');\n    };\n    return {\n      baseUrl,\n      uploadHeaders,\n      loading,\n      teacherList,\n      searchForm,\n      formData,\n      formRules,\n      dialogVisible,\n      teacherFormRef,\n      currentPage,\n      pageSize,\n      total,\n      photoPreview,\n      // 导入相关\n      importDialogVisible,\n      uploadRef,\n      uploading,\n      fileList,\n      importResult,\n      // 方法\n      handleSearch,\n      resetSearch,\n      handleSizeChange,\n      handleCurrentChange,\n      openDialog,\n      submitForm,\n      beforePhotoUpload,\n      handlePhotoSuccess,\n      handlePhotoError,\n      handleDelete,\n      viewDetails,\n      downloadTemplate,\n      openImportDialog,\n      beforeExcelUpload,\n      handleUpload,\n      handleImportSuccess,\n      handleImportError\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "watch", "ElMessage", "ElMessageBox", "axios", "Plus", "useRouter", "name", "components", "setup", "router", "token", "localStorage", "getItem", "defaults", "headers", "console", "log", "baseUrl", "uploadHeaders", "loading", "dialogVisible", "teacherFormRef", "teacherList", "total", "currentPage", "pageSize", "photoPreview", "importDialogVisible", "uploadRef", "uploading", "fileList", "importResult", "searchForm", "department", "is_employed", "formData", "id", "gender", "school", "major", "education", "employment_period", "phone", "photo", "formRules", "required", "message", "trigger", "min", "max", "newVal", "startsWith", "value", "immediate", "fetchTeachers", "params", "URLSearchParams", "append", "response", "get", "data", "count", "error", "handleSearch", "resetSearch", "Object", "keys", "for<PERSON>ach", "key", "handleSizeChange", "val", "handleCurrentChange", "openDialog", "row", "beforePhotoUpload", "file", "isJPGOrPNG", "type", "isLt2M", "size", "URL", "createObjectURL", "handlePhotoSuccess", "success", "path", "handlePhotoError", "err", "submitForm", "validate", "valid", "formDataToSubmit", "FormData", "undefined", "config", "put", "post", "handleDelete", "confirm", "confirmButtonText", "cancelButtonText", "then", "delete", "length", "catch", "info", "viewDetails", "push", "downloadTemplate", "responseType", "url", "window", "Blob", "link", "document", "createElement", "href", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "openImportDialog", "beforeExcelUpload", "isExcel", "isLt10M", "handleUpload", "warning", "submit", "handleImportSuccess", "handleImportError"], "sources": ["D:\\admin\\202506\\督导系统\\后台管理系统\\ms\\src\\views\\teachers\\TeacherList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"teacher-list-container\">\r\n    <el-card class=\"box-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span class=\"title\">教师管理</span>\r\n          <div>\r\n            <el-button type=\"success\" @click=\"downloadTemplate\">下载模板</el-button>\r\n            <el-button type=\"danger\" @click=\"openImportDialog\">一键导入</el-button>\r\n            <el-button type=\"primary\" @click=\"openDialog()\">添加教师</el-button>\r\n          </div>\r\n          \r\n        </div>\r\n      </template>\r\n\r\n      <!-- 搜索区域 -->\r\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\r\n        <el-form-item label=\"姓名\">\r\n          <el-input v-model=\"searchForm.name\" placeholder=\"教师姓名\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"科室\">\r\n          <el-input v-model=\"searchForm.department\" placeholder=\"所属科室\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"在聘状态\">\r\n          <el-select v-model=\"searchForm.is_employed\" placeholder=\"是否在聘\" clearable style=\"width: 120px\">\r\n            <el-option label=\"在聘\" :value=\"1\" />\r\n            <el-option label=\"不在聘\" :value=\"0\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n          <el-button @click=\"resetSearch\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <!-- 表格区域 -->\r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"teacherList\"\r\n        border\r\n        style=\"width: 100%\"\r\n      >\r\n        <el-table-column prop=\"id\" label=\"ID\"  />\r\n        <el-table-column prop=\"name\" label=\"姓名\"  />\r\n        <el-table-column prop=\"gender\" label=\"性别\" />\r\n        <el-table-column prop=\"department\" label=\"科室\" />\r\n        <el-table-column prop=\"school\" label=\"学校\" />\r\n        <el-table-column prop=\"major\" label=\"专业\" />\r\n        <el-table-column prop=\"education\" label=\"学历\" />\r\n        <el-table-column label=\"在聘状态\" >\r\n          <template #default=\"scope\">\r\n            <el-tag :type=\"scope.row.is_employed ? 'success' : 'danger'\">\r\n              {{ scope.row.is_employed ? '在聘' : '不在聘' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"employment_period\" label=\"聘期\" min-width=\"120\" />\r\n        <el-table-column label=\"照片\" width=\"80\">\r\n          <template #default=\"scope\">\r\n            <el-image\r\n              v-if=\"scope.row.photo\"\r\n              :src=\"`${baseUrl}${scope.row.photo}`\"\r\n              :preview-src-list=\"[`${baseUrl}${scope.row.photo}`]\"\r\n              fit=\"cover\"\r\n              style=\"width: 50px; height: 50px\"\r\n            />\r\n            <el-avatar v-else :size=\"50\" icon=\"UserFilled\" />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" fixed=\"right\" width=\"200\">\r\n          <template #default=\"scope\">\r\n            <el-button size=\"small\" @click=\"viewDetails(scope.row.id)\">详情</el-button>\r\n            <el-button size=\"small\" type=\"primary\" @click=\"openDialog(scope.row)\">编辑</el-button>\r\n            <el-button size=\"small\" type=\"danger\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          v-model:current-page=\"currentPage\"\r\n          v-model:page-size=\"pageSize\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 教师表单对话框 -->\r\n    <el-dialog\r\n      v-model=\"dialogVisible\"\r\n      :title=\"formData.id ? '编辑教师' : '添加教师'\"\r\n      width=\"50%\"\r\n      destroy-on-close\r\n    >\r\n      <el-form\r\n        ref=\"teacherFormRef\"\r\n        :model=\"formData\"\r\n        :rules=\"formRules\"\r\n        label-width=\"80px\"\r\n        label-position=\"right\"\r\n      >\r\n        <el-form-item label=\"姓名\" prop=\"name\">\r\n          <el-input v-model=\"formData.name\" placeholder=\"请输入姓名\" />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"性别\" prop=\"gender\">\r\n          <el-radio-group v-model=\"formData.gender\">\r\n            <el-radio label=\"男\">男</el-radio>\r\n            <el-radio label=\"女\">女</el-radio>\r\n          </el-radio-group>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"科室\" prop=\"department\">\r\n          <el-input v-model=\"formData.department\" placeholder=\"请输入科室\" />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"学校\" prop=\"school\">\r\n          <el-input v-model=\"formData.school\" placeholder=\"请输入学校\" />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"专业\" prop=\"major\">\r\n          <el-input v-model=\"formData.major\" placeholder=\"请输入专业\" />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"学历\" prop=\"education\">\r\n          <el-select v-model=\"formData.education\" placeholder=\"请选择学历\" style=\"width: 100%\">\r\n            <el-option label=\"博士\" value=\"博士\" />\r\n            <el-option label=\"硕士\" value=\"硕士\" />\r\n            <el-option label=\"本科\" value=\"本科\" />\r\n            <el-option label=\"专科\" value=\"专科\" />\r\n            <el-option label=\"其他\" value=\"其他\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"在聘状态\" prop=\"is_employed\">\r\n          <el-switch\r\n            v-model=\"formData.is_employed\"\r\n            :active-value=\"1\"\r\n            :inactive-value=\"0\"\r\n          />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"聘期\" prop=\"employment_period\" v-if=\"formData.is_employed === 1\">\r\n          <el-input v-model=\"formData.employment_period\" placeholder=\"例如：2023年6月-2026年5月\" />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"联系方式\" prop=\"phone\">\r\n          <el-input v-model=\"formData.phone\" placeholder=\"请输入联系方式\" />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"照片\" prop=\"photo\">\r\n          <el-upload\r\n            class=\"avatar-uploader\"\r\n            :action=\"`${baseUrl}/api/teachers/upload/photo`\"\r\n            :headers=\"uploadHeaders\"\r\n            name=\"photo\"\r\n            :show-file-list=\"false\"\r\n            :before-upload=\"beforePhotoUpload\"\r\n            :on-success=\"handlePhotoSuccess\"\r\n            :on-error=\"handlePhotoError\"\r\n          >\r\n            <img v-if=\"photoPreview\" :src=\"photoPreview\" class=\"avatar\" />\r\n            <el-icon v-else class=\"avatar-uploader-icon\"><Plus /></el-icon>\r\n          </el-upload>\r\n          <div class=\"upload-tip\">点击上传照片，JPG/PNG格式，小于2MB</div>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- Excel导入对话框 -->\r\n    <el-dialog\r\n      v-model=\"importDialogVisible\"\r\n      title=\"批量导入教师\"\r\n      width=\"50%\"\r\n      destroy-on-close\r\n    >\r\n      <div class=\"import-content\">\r\n        <el-alert\r\n          title=\"导入说明\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          style=\"margin-bottom: 20px\"\r\n        >\r\n          <template #default>\r\n            <div>\r\n              <p>1. 请先下载Excel模板，按照模板格式填写教师信息</p>\r\n              <p>2. 必填字段：姓名、性别、科室、学校、专业、学历</p>\r\n              <p>3. 性别只能填写\"男\"或\"女\"</p>\r\n              <p>4. 是否在聘可填写：是/否、true/false、1/0</p>\r\n              <p>5. 支持.xlsx和.xls格式，文件大小不超过10MB</p>\r\n            </div>\r\n          </template>\r\n        </el-alert>\r\n\r\n        <el-upload\r\n          ref=\"uploadRef\"\r\n          class=\"upload-demo\"\r\n          :action=\"`${baseUrl}/api/teachers/import/excel`\"\r\n          :headers=\"uploadHeaders\"\r\n          :before-upload=\"beforeExcelUpload\"\r\n          :on-success=\"handleImportSuccess\"\r\n          :on-error=\"handleImportError\"\r\n          :on-change=\"handleFileChange\"\r\n          :file-list=\"fileList\"\r\n          :auto-upload=\"false\"\r\n          accept=\".xlsx,.xls\"\r\n          :limit=\"1\"\r\n        >\r\n          <el-button type=\"primary\">选择Excel文件</el-button>\r\n          <template #tip>\r\n            <div class=\"el-upload__tip\">\r\n              只能上传xlsx/xls文件，且不超过10MB\r\n            </div>\r\n          </template>\r\n        </el-upload>\r\n\r\n        <!-- 导入结果显示 -->\r\n        <div v-if=\"importResult\" class=\"import-result\" style=\"margin-top: 20px\">\r\n          <el-alert\r\n            :title=\"`导入完成！成功 ${importResult.success} 条，失败 ${importResult.failed} 条`\"\r\n            :type=\"importResult.failed > 0 ? 'warning' : 'success'\"\r\n            :closable=\"false\"\r\n          />\r\n\r\n          <!-- 失败记录详情 -->\r\n          <div v-if=\"importResult.failedRecords && importResult.failedRecords.length > 0\" style=\"margin-top: 15px\">\r\n            <el-collapse>\r\n              <el-collapse-item title=\"查看失败记录\" name=\"failed\">\r\n                <el-table :data=\"importResult.failedRecords\" border size=\"small\">\r\n                  <el-table-column prop=\"row\" label=\"行号\" width=\"80\" />\r\n                  <el-table-column prop=\"error\" label=\"错误原因\" />\r\n                </el-table>\r\n              </el-collapse-item>\r\n            </el-collapse>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"importDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"handleUpload\" :loading=\"uploading\">开始导入</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, computed, onMounted, watch } from 'vue'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport axios from 'axios'\r\nimport { Plus } from '@element-plus/icons-vue'\r\nimport { useRouter } from 'vue-router'\r\n\r\nexport default {\r\n  name: 'TeacherList',\r\n  components: { Plus },\r\n  setup() {\r\n    // 路由器\r\n    const router = useRouter()\r\n    let token = localStorage.getItem('token')\r\n    if (token) {\r\n      axios.defaults.headers['Authorization'] = `Bearer ${token}`\r\n    }\r\n    console.log(axios.defaults.headers['Authorization'])\r\n    // API基础URL\r\n    const baseUrl = 'http://localhost:3000'\r\n    \r\n    // 上传头像的headers\r\n    const uploadHeaders = {\r\n      // 如果需要认证可以在这里添加\r\n    }\r\n    \r\n    // 基础数据\r\n    const loading = ref(false)\r\n    const dialogVisible = ref(false)\r\n    const teacherFormRef = ref(null)\r\n    const teacherList = ref([])\r\n    const total = ref(0)\r\n    const currentPage = ref(1)\r\n    const pageSize = ref(10)\r\n    const photoPreview = ref('')\r\n\r\n    // 导入相关数据\r\n    const importDialogVisible = ref(false)\r\n    const uploadRef = ref(null)\r\n    const uploading = ref(false)\r\n    const fileList = ref([])\r\n    const importResult = ref(null)\r\n    \r\n    // 搜索表单\r\n    const searchForm = reactive({\r\n      name: '',\r\n      department: '',\r\n      is_employed: ''\r\n    })\r\n    \r\n    // 表单数据\r\n    const formData = reactive({\r\n      id: '',\r\n      name: '',\r\n      gender: '男',\r\n      department: '',\r\n      school: '',\r\n      major: '',\r\n      education: '',\r\n      is_employed: 1,\r\n      employment_period: '',\r\n      phone: '',\r\n      photo: ''\r\n    })\r\n    \r\n    // 表单校验规则\r\n    const formRules = reactive({\r\n      name: [\r\n        { required: true, message: '请输入姓名', trigger: 'blur' },\r\n        { min: 2, max: 10, message: '长度在 2 到 10 个字符', trigger: 'blur' }\r\n      ],\r\n      gender: [\r\n        { required: true, message: '请选择性别', trigger: 'change' }\r\n      ],\r\n      department: [\r\n        { required: true, message: '请输入科室', trigger: 'blur' }\r\n      ],\r\n      school: [\r\n        { required: true, message: '请输入学校', trigger: 'blur' }\r\n      ],\r\n      major: [\r\n        { required: true, message: '请输入专业', trigger: 'blur' }\r\n      ],\r\n      education: [\r\n        { required: true, message: '请选择学历', trigger: 'change' }\r\n      ]\r\n    })\r\n    \r\n    // 监听表单数据变化，更新照片预览\r\n    watch(() => formData.photo, (newVal) => {\r\n      if (newVal) {\r\n        if (newVal.startsWith('http')) {\r\n          photoPreview.value = newVal\r\n        } else {\r\n          photoPreview.value = `${baseUrl}${newVal}`\r\n        }\r\n      } else {\r\n        photoPreview.value = ''\r\n      }\r\n    }, { immediate: true })\r\n    \r\n    // 生命周期钩子\r\n    onMounted(() => {\r\n      fetchTeachers()\r\n    })\r\n    \r\n    // 获取教师列表\r\n    const fetchTeachers = async () => {\r\n      loading.value = true\r\n      try {\r\n        // 构建查询参数\r\n        const params = new URLSearchParams()\r\n        if (searchForm.name) params.append('name', searchForm.name)\r\n        if (searchForm.department) params.append('department', searchForm.department)\r\n        if (searchForm.is_employed !== '') params.append('is_employed', searchForm.is_employed)\r\n        params.append('page', currentPage.value)\r\n        params.append('limit', pageSize.value)\r\n        \r\n        const response = await axios.get(`${baseUrl}/api/teachers`, { params })\r\n        teacherList.value = response.data.data\r\n        total.value = response.data.count\r\n      } catch (error) {\r\n        console.error('获取教师列表失败:', error)\r\n        ElMessage.error('获取教师列表失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 搜索操作\r\n    const handleSearch = () => {\r\n      currentPage.value = 1\r\n      fetchTeachers()\r\n    }\r\n    \r\n    // 重置搜索\r\n    const resetSearch = () => {\r\n      Object.keys(searchForm).forEach(key => {\r\n        searchForm[key] = ''\r\n      })\r\n      currentPage.value = 1\r\n      fetchTeachers()\r\n    }\r\n    \r\n    // 分页操作\r\n    const handleSizeChange = (val) => {\r\n      pageSize.value = val\r\n      fetchTeachers()\r\n    }\r\n    \r\n    const handleCurrentChange = (val) => {\r\n      currentPage.value = val\r\n      fetchTeachers()\r\n    }\r\n    \r\n    // 打开对话框\r\n    const openDialog = (row) => {\r\n      if (row) {\r\n        // 编辑模式\r\n        Object.keys(formData).forEach(key => {\r\n          formData[key] = row[key]\r\n        })\r\n        // 更新照片预览\r\n        if (row.photo) {\r\n          photoPreview.value = `${baseUrl}${row.photo}`\r\n        }\r\n      } else {\r\n        // 新增模式\r\n        Object.keys(formData).forEach(key => {\r\n          formData[key] = key === 'gender' ? '男' : \r\n                          key === 'is_employed' ? 1 : ''\r\n        })\r\n        photoPreview.value = ''\r\n      }\r\n      dialogVisible.value = true\r\n    }\r\n    \r\n    // 照片上传前验证\r\n    const beforePhotoUpload = (file) => {\r\n      const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/png'\r\n      const isLt2M = file.size / 1024 / 1024 < 2\r\n      \r\n      if (!isJPGOrPNG) {\r\n        ElMessage.error('上传头像图片只能是 JPG 或 PNG 格式!')\r\n        return false\r\n      }\r\n      \r\n      if (!isLt2M) {\r\n        ElMessage.error('上传头像图片大小不能超过 2MB!')\r\n        return false\r\n      }\r\n      \r\n      // 创建临时预览\r\n      photoPreview.value = URL.createObjectURL(file)\r\n      return true\r\n    }\r\n    \r\n    // 照片上传成功回调\r\n    const handlePhotoSuccess = (response) => {\r\n      console.log('照片上传成功响应:', response)\r\n      if (response.success) {\r\n        formData.photo = response.path\r\n        console.log('设置照片路径:', formData.photo)\r\n        ElMessage.success('照片上传成功')\r\n      } else {\r\n        ElMessage.error(response.message || '照片上传失败')\r\n      }\r\n    }\r\n \r\n    // 照片上传失败回调\r\n    const handlePhotoError = (err) => {\r\n      console.error('照片上传失败:', err)\r\n      ElMessage.error('照片上传失败，请检查网络连接')\r\n    }\r\n    \r\n    // 提交表单\r\n    const submitForm = async () => {\r\n      if (!teacherFormRef.value) return\r\n      \r\n      await teacherFormRef.value.validate(async (valid) => {\r\n        if (valid) {\r\n          try {\r\n            console.log('提交表单数据:', formData)\r\n            \r\n            // 创建表单数据对象\r\n            const formDataToSubmit = new FormData()\r\n            \r\n            // 添加所有字段到FormData\r\n            Object.keys(formData).forEach(key => {\r\n              if (formData[key] !== null && formData[key] !== undefined && formData[key] !== '') {\r\n                formDataToSubmit.append(key, formData[key])\r\n              }\r\n            })\r\n            \r\n            const config = {\r\n              headers: {\r\n                'Content-Type': 'multipart/form-data'\r\n              }\r\n            }\r\n            \r\n            if (formData.id) {\r\n              // 编辑\r\n              await axios.put(`${baseUrl}/api/teachers/${formData.id}`, formDataToSubmit, config)\r\n              ElMessage.success('教师信息更新成功')\r\n            } else {\r\n              // 新增\r\n              await axios.post(`${baseUrl}/api/teachers`, formDataToSubmit, config)\r\n              ElMessage.success('教师添加成功')\r\n            }\r\n            \r\n            dialogVisible.value = false\r\n            fetchTeachers()\r\n          } catch (error) {\r\n            console.error('操作失败:', error)\r\n            ElMessage.error(error.response?.data?.message || '操作失败')\r\n          }\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    }\r\n    \r\n    // 删除教师\r\n    const handleDelete = (row) => {\r\n      ElMessageBox.confirm(\r\n        `确定要删除教师 ${row.name} 吗?`,\r\n        '警告',\r\n        {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n        }\r\n      )\r\n        .then(async () => {\r\n          try {\r\n            await axios.delete(`${baseUrl}/api/teachers/${row.id}`)\r\n            ElMessage.success('删除成功')\r\n            // 如果当前页只有一条数据且不是第一页，删除后跳转到上一页\r\n            if (teacherList.value.length === 1 && currentPage.value > 1) {\r\n              currentPage.value--\r\n            }\r\n            fetchTeachers()\r\n          } catch (error) {\r\n            console.error('删除失败:', error)\r\n            ElMessage.error(error.response?.data?.message || '删除失败')\r\n          }\r\n        })\r\n        .catch(() => {\r\n          ElMessage.info('已取消删除')\r\n        })\r\n    }\r\n    \r\n    // 查看详情\r\n    const viewDetails = (id) => {\r\n      // 跳转到详情页面\r\n      router.push(`/teachers/detail/${id}`)\r\n    }\r\n\r\n    // 下载模板\r\n    const downloadTemplate = async () => {\r\n      try {\r\n        const response = await axios.get(`${baseUrl}/api/teachers/import/template`, {\r\n          responseType: 'blob'\r\n        })\r\n\r\n        // 创建下载链接\r\n        const url = window.URL.createObjectURL(new Blob([response.data]))\r\n        const link = document.createElement('a')\r\n        link.href = url\r\n        link.setAttribute('download', 'teacher_import_template.xlsx')\r\n        document.body.appendChild(link)\r\n        link.click()\r\n        document.body.removeChild(link)\r\n        window.URL.revokeObjectURL(url)\r\n\r\n        ElMessage.success('模板下载成功')\r\n      } catch (error) {\r\n        console.error('下载模板失败:', error)\r\n        ElMessage.error('下载模板失败')\r\n      }\r\n    }\r\n\r\n    // 打开导入对话框\r\n    const openImportDialog = () => {\r\n      importDialogVisible.value = true\r\n      fileList.value = []\r\n      importResult.value = null\r\n    }\r\n\r\n    // Excel文件上传前验证\r\n    const beforeExcelUpload = (file) => {\r\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\r\n                     file.type === 'application/vnd.ms-excel'\r\n      const isLt10M = file.size / 1024 / 1024 < 10\r\n\r\n      if (!isExcel) {\r\n        ElMessage.error('只能上传Excel文件!')\r\n        return false\r\n      }\r\n\r\n      if (!isLt10M) {\r\n        ElMessage.error('文件大小不能超过10MB!')\r\n        return false\r\n      }\r\n\r\n      return true\r\n    }\r\n\r\n    // 手动上传\r\n    const handleUpload = () => {\r\n      if (fileList.value.length === 0) {\r\n        ElMessage.warning('请先选择要上传的文件')\r\n        return\r\n      }\r\n\r\n      uploading.value = true\r\n      uploadRef.value.submit()\r\n    }\r\n\r\n    // 导入成功回调\r\n    const handleImportSuccess = (response) => {\r\n      uploading.value = false\r\n      console.log('导入成功响应:', response)\r\n\r\n      if (response.success) {\r\n        importResult.value = response.data\r\n        ElMessage.success(response.message)\r\n        // 刷新教师列表\r\n        fetchTeachers()\r\n      } else {\r\n        ElMessage.error(response.message || '导入失败')\r\n      }\r\n    }\r\n\r\n    // 导入失败回调\r\n    const handleImportError = (error) => {\r\n      uploading.value = false\r\n      console.error('导入失败:', error)\r\n      ElMessage.error('导入失败，请检查文件格式和网络连接')\r\n    }\r\n    \r\n    return {\r\n      baseUrl,\r\n      uploadHeaders,\r\n      loading,\r\n      teacherList,\r\n      searchForm,\r\n      formData,\r\n      formRules,\r\n      dialogVisible,\r\n      teacherFormRef,\r\n      currentPage,\r\n      pageSize,\r\n      total,\r\n      photoPreview,\r\n      // 导入相关\r\n      importDialogVisible,\r\n      uploadRef,\r\n      uploading,\r\n      fileList,\r\n      importResult,\r\n      // 方法\r\n      handleSearch,\r\n      resetSearch,\r\n      handleSizeChange,\r\n      handleCurrentChange,\r\n      openDialog,\r\n      submitForm,\r\n      beforePhotoUpload,\r\n      handlePhotoSuccess,\r\n      handlePhotoError,\r\n      handleDelete,\r\n      viewDetails,\r\n      downloadTemplate,\r\n      openImportDialog,\r\n      beforeExcelUpload,\r\n      handleUpload,\r\n      handleImportSuccess,\r\n      handleImportError\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.teacher-list-container {\r\n  padding: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.search-form {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.avatar-uploader {\r\n  width: 100px;\r\n  height: 100px;\r\n  border: 1px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  cursor: pointer;\r\n  position: relative;\r\n  overflow: hidden;\r\n}\r\n\r\n.avatar-uploader:hover {\r\n  border-color: #409EFF;\r\n}\r\n\r\n.avatar-uploader-icon {\r\n  font-size: 28px;\r\n  color: #8c939d;\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.avatar {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: block;\r\n  object-fit: cover;\r\n}\r\n\r\n.upload-tip {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-top: 5px;\r\n}\r\n\r\n.import-content {\r\n  padding: 10px 0;\r\n}\r\n\r\n.import-result {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  padding: 15px;\r\n  background-color: #f8f9fa;\r\n}\r\n\r\n.upload-demo {\r\n  text-align: center;\r\n  padding: 20px;\r\n  border: 2px dashed #d9d9d9;\r\n  border-radius: 6px;\r\n  background-color: #fafafa;\r\n}\r\n\r\n.upload-demo:hover {\r\n  border-color: #409EFF;\r\n}\r\n</style> "], "mappings": ";;;;;;AAoQA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAI,QAAS,KAAI;AAC9D,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,OAAOC,KAAI,MAAO,OAAM;AACxB,SAASC,IAAG,QAAS,yBAAwB;AAC7C,SAASC,SAAQ,QAAS,YAAW;AAErC,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE;IAAEH;EAAK,CAAC;EACpBI,KAAKA,CAAA,EAAG;IACN;IACA,MAAMC,MAAK,GAAIJ,SAAS,CAAC;IACzB,IAAIK,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,OAAO;IACxC,IAAIF,KAAK,EAAE;MACTP,KAAK,CAACU,QAAQ,CAACC,OAAO,CAAC,eAAe,IAAI,UAAUJ,KAAK,EAAC;IAC5D;IACAK,OAAO,CAACC,GAAG,CAACb,KAAK,CAACU,QAAQ,CAACC,OAAO,CAAC,eAAe,CAAC;IACnD;IACA,MAAMG,OAAM,GAAI,uBAAsB;;IAEtC;IACA,MAAMC,aAAY,GAAI;MACpB;IAAA,CACF;;IAEA;IACA,MAAMC,OAAM,GAAIvB,GAAG,CAAC,KAAK;IACzB,MAAMwB,aAAY,GAAIxB,GAAG,CAAC,KAAK;IAC/B,MAAMyB,cAAa,GAAIzB,GAAG,CAAC,IAAI;IAC/B,MAAM0B,WAAU,GAAI1B,GAAG,CAAC,EAAE;IAC1B,MAAM2B,KAAI,GAAI3B,GAAG,CAAC,CAAC;IACnB,MAAM4B,WAAU,GAAI5B,GAAG,CAAC,CAAC;IACzB,MAAM6B,QAAO,GAAI7B,GAAG,CAAC,EAAE;IACvB,MAAM8B,YAAW,GAAI9B,GAAG,CAAC,EAAE;;IAE3B;IACA,MAAM+B,mBAAkB,GAAI/B,GAAG,CAAC,KAAK;IACrC,MAAMgC,SAAQ,GAAIhC,GAAG,CAAC,IAAI;IAC1B,MAAMiC,SAAQ,GAAIjC,GAAG,CAAC,KAAK;IAC3B,MAAMkC,QAAO,GAAIlC,GAAG,CAAC,EAAE;IACvB,MAAMmC,YAAW,GAAInC,GAAG,CAAC,IAAI;;IAE7B;IACA,MAAMoC,UAAS,GAAInC,QAAQ,CAAC;MAC1BS,IAAI,EAAE,EAAE;MACR2B,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE;IACf,CAAC;;IAED;IACA,MAAMC,QAAO,GAAItC,QAAQ,CAAC;MACxBuC,EAAE,EAAE,EAAE;MACN9B,IAAI,EAAE,EAAE;MACR+B,MAAM,EAAE,GAAG;MACXJ,UAAU,EAAE,EAAE;MACdK,MAAM,EAAE,EAAE;MACVC,KAAK,EAAE,EAAE;MACTC,SAAS,EAAE,EAAE;MACbN,WAAW,EAAE,CAAC;MACdO,iBAAiB,EAAE,EAAE;MACrBC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE;IACT,CAAC;;IAED;IACA,MAAMC,SAAQ,GAAI/C,QAAQ,CAAC;MACzBS,IAAI,EAAE,CACJ;QAAEuC,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,gBAAgB;QAAEC,OAAO,EAAE;MAAO,EAC/D;MACDV,MAAM,EAAE,CACN;QAAEQ,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,EACvD;MACDd,UAAU,EAAE,CACV;QAAEY,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,EACrD;MACDT,MAAM,EAAE,CACN;QAAEO,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,EACrD;MACDR,KAAK,EAAE,CACL;QAAEM,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,EACrD;MACDP,SAAS,EAAE,CACT;QAAEK,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS;IAE1D,CAAC;;IAED;IACA/C,KAAK,CAAC,MAAMmC,QAAQ,CAACQ,KAAK,EAAGO,MAAM,IAAK;MACtC,IAAIA,MAAM,EAAE;QACV,IAAIA,MAAM,CAACC,UAAU,CAAC,MAAM,CAAC,EAAE;UAC7BzB,YAAY,CAAC0B,KAAI,GAAIF,MAAK;QAC5B,OAAO;UACLxB,YAAY,CAAC0B,KAAI,GAAI,GAAGnC,OAAO,GAAGiC,MAAM,EAAC;QAC3C;MACF,OAAO;QACLxB,YAAY,CAAC0B,KAAI,GAAI,EAAC;MACxB;IACF,CAAC,EAAE;MAAEC,SAAS,EAAE;IAAK,CAAC;;IAEtB;IACAtD,SAAS,CAAC,MAAM;MACduD,aAAa,CAAC;IAChB,CAAC;;IAED;IACA,MAAMA,aAAY,GAAI,MAAAA,CAAA,KAAY;MAChCnC,OAAO,CAACiC,KAAI,GAAI,IAAG;MACnB,IAAI;QACF;QACA,MAAMG,MAAK,GAAI,IAAIC,eAAe,CAAC;QACnC,IAAIxB,UAAU,CAAC1B,IAAI,EAAEiD,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEzB,UAAU,CAAC1B,IAAI;QAC1D,IAAI0B,UAAU,CAACC,UAAU,EAAEsB,MAAM,CAACE,MAAM,CAAC,YAAY,EAAEzB,UAAU,CAACC,UAAU;QAC5E,IAAID,UAAU,CAACE,WAAU,KAAM,EAAE,EAAEqB,MAAM,CAACE,MAAM,CAAC,aAAa,EAAEzB,UAAU,CAACE,WAAW;QACtFqB,MAAM,CAACE,MAAM,CAAC,MAAM,EAAEjC,WAAW,CAAC4B,KAAK;QACvCG,MAAM,CAACE,MAAM,CAAC,OAAO,EAAEhC,QAAQ,CAAC2B,KAAK;QAErC,MAAMM,QAAO,GAAI,MAAMvD,KAAK,CAACwD,GAAG,CAAC,GAAG1C,OAAO,eAAe,EAAE;UAAEsC;QAAO,CAAC;QACtEjC,WAAW,CAAC8B,KAAI,GAAIM,QAAQ,CAACE,IAAI,CAACA,IAAG;QACrCrC,KAAK,CAAC6B,KAAI,GAAIM,QAAQ,CAACE,IAAI,CAACC,KAAI;MAClC,EAAE,OAAOC,KAAK,EAAE;QACd/C,OAAO,CAAC+C,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC7D,SAAS,CAAC6D,KAAK,CAAC,UAAU;MAC5B,UAAU;QACR3C,OAAO,CAACiC,KAAI,GAAI,KAAI;MACtB;IACF;;IAEA;IACA,MAAMW,YAAW,GAAIA,CAAA,KAAM;MACzBvC,WAAW,CAAC4B,KAAI,GAAI;MACpBE,aAAa,CAAC;IAChB;;IAEA;IACA,MAAMU,WAAU,GAAIA,CAAA,KAAM;MACxBC,MAAM,CAACC,IAAI,CAAClC,UAAU,CAAC,CAACmC,OAAO,CAACC,GAAE,IAAK;QACrCpC,UAAU,CAACoC,GAAG,IAAI,EAAC;MACrB,CAAC;MACD5C,WAAW,CAAC4B,KAAI,GAAI;MACpBE,aAAa,CAAC;IAChB;;IAEA;IACA,MAAMe,gBAAe,GAAKC,GAAG,IAAK;MAChC7C,QAAQ,CAAC2B,KAAI,GAAIkB,GAAE;MACnBhB,aAAa,CAAC;IAChB;IAEA,MAAMiB,mBAAkB,GAAKD,GAAG,IAAK;MACnC9C,WAAW,CAAC4B,KAAI,GAAIkB,GAAE;MACtBhB,aAAa,CAAC;IAChB;;IAEA;IACA,MAAMkB,UAAS,GAAKC,GAAG,IAAK;MAC1B,IAAIA,GAAG,EAAE;QACP;QACAR,MAAM,CAACC,IAAI,CAAC/B,QAAQ,CAAC,CAACgC,OAAO,CAACC,GAAE,IAAK;UACnCjC,QAAQ,CAACiC,GAAG,IAAIK,GAAG,CAACL,GAAG;QACzB,CAAC;QACD;QACA,IAAIK,GAAG,CAAC9B,KAAK,EAAE;UACbjB,YAAY,CAAC0B,KAAI,GAAI,GAAGnC,OAAO,GAAGwD,GAAG,CAAC9B,KAAK,EAAC;QAC9C;MACF,OAAO;QACL;QACAsB,MAAM,CAACC,IAAI,CAAC/B,QAAQ,CAAC,CAACgC,OAAO,CAACC,GAAE,IAAK;UACnCjC,QAAQ,CAACiC,GAAG,IAAIA,GAAE,KAAM,QAAO,GAAI,GAAE,GACrBA,GAAE,KAAM,aAAY,GAAI,IAAI,EAAC;QAC/C,CAAC;QACD1C,YAAY,CAAC0B,KAAI,GAAI,EAAC;MACxB;MACAhC,aAAa,CAACgC,KAAI,GAAI,IAAG;IAC3B;;IAEA;IACA,MAAMsB,iBAAgB,GAAKC,IAAI,IAAK;MAClC,MAAMC,UAAS,GAAID,IAAI,CAACE,IAAG,KAAM,YAAW,IAAKF,IAAI,CAACE,IAAG,KAAM,WAAU;MACzE,MAAMC,MAAK,GAAIH,IAAI,CAACI,IAAG,GAAI,IAAG,GAAI,IAAG,GAAI;MAEzC,IAAI,CAACH,UAAU,EAAE;QACf3E,SAAS,CAAC6D,KAAK,CAAC,yBAAyB;QACzC,OAAO,KAAI;MACb;MAEA,IAAI,CAACgB,MAAM,EAAE;QACX7E,SAAS,CAAC6D,KAAK,CAAC,mBAAmB;QACnC,OAAO,KAAI;MACb;;MAEA;MACApC,YAAY,CAAC0B,KAAI,GAAI4B,GAAG,CAACC,eAAe,CAACN,IAAI;MAC7C,OAAO,IAAG;IACZ;;IAEA;IACA,MAAMO,kBAAiB,GAAKxB,QAAQ,IAAK;MACvC3C,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE0C,QAAQ;MACjC,IAAIA,QAAQ,CAACyB,OAAO,EAAE;QACpBhD,QAAQ,CAACQ,KAAI,GAAIe,QAAQ,CAAC0B,IAAG;QAC7BrE,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEmB,QAAQ,CAACQ,KAAK;QACrC1C,SAAS,CAACkF,OAAO,CAAC,QAAQ;MAC5B,OAAO;QACLlF,SAAS,CAAC6D,KAAK,CAACJ,QAAQ,CAACZ,OAAM,IAAK,QAAQ;MAC9C;IACF;;IAEA;IACA,MAAMuC,gBAAe,GAAKC,GAAG,IAAK;MAChCvE,OAAO,CAAC+C,KAAK,CAAC,SAAS,EAAEwB,GAAG;MAC5BrF,SAAS,CAAC6D,KAAK,CAAC,gBAAgB;IAClC;;IAEA;IACA,MAAMyB,UAAS,GAAI,MAAAA,CAAA,KAAY;MAC7B,IAAI,CAAClE,cAAc,CAAC+B,KAAK,EAAE;MAE3B,MAAM/B,cAAc,CAAC+B,KAAK,CAACoC,QAAQ,CAAC,MAAOC,KAAK,IAAK;QACnD,IAAIA,KAAK,EAAE;UACT,IAAI;YACF1E,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEmB,QAAQ;;YAE/B;YACA,MAAMuD,gBAAe,GAAI,IAAIC,QAAQ,CAAC;;YAEtC;YACA1B,MAAM,CAACC,IAAI,CAAC/B,QAAQ,CAAC,CAACgC,OAAO,CAACC,GAAE,IAAK;cACnC,IAAIjC,QAAQ,CAACiC,GAAG,MAAM,IAAG,IAAKjC,QAAQ,CAACiC,GAAG,MAAMwB,SAAQ,IAAKzD,QAAQ,CAACiC,GAAG,MAAM,EAAE,EAAE;gBACjFsB,gBAAgB,CAACjC,MAAM,CAACW,GAAG,EAAEjC,QAAQ,CAACiC,GAAG,CAAC;cAC5C;YACF,CAAC;YAED,MAAMyB,MAAK,GAAI;cACb/E,OAAO,EAAE;gBACP,cAAc,EAAE;cAClB;YACF;YAEA,IAAIqB,QAAQ,CAACC,EAAE,EAAE;cACf;cACA,MAAMjC,KAAK,CAAC2F,GAAG,CAAC,GAAG7E,OAAO,iBAAiBkB,QAAQ,CAACC,EAAE,EAAE,EAAEsD,gBAAgB,EAAEG,MAAM;cAClF5F,SAAS,CAACkF,OAAO,CAAC,UAAU;YAC9B,OAAO;cACL;cACA,MAAMhF,KAAK,CAAC4F,IAAI,CAAC,GAAG9E,OAAO,eAAe,EAAEyE,gBAAgB,EAAEG,MAAM;cACpE5F,SAAS,CAACkF,OAAO,CAAC,QAAQ;YAC5B;YAEA/D,aAAa,CAACgC,KAAI,GAAI,KAAI;YAC1BE,aAAa,CAAC;UAChB,EAAE,OAAOQ,KAAK,EAAE;YACd/C,OAAO,CAAC+C,KAAK,CAAC,OAAO,EAAEA,KAAK;YAC5B7D,SAAS,CAAC6D,KAAK,CAACA,KAAK,CAACJ,QAAQ,EAAEE,IAAI,EAAEd,OAAM,IAAK,MAAM;UACzD;QACF,OAAO;UACL,OAAO,KAAI;QACb;MACF,CAAC;IACH;;IAEA;IACA,MAAMkD,YAAW,GAAKvB,GAAG,IAAK;MAC5BvE,YAAY,CAAC+F,OAAO,CAClB,WAAWxB,GAAG,CAACnE,IAAI,KAAK,EACxB,IAAI,EACJ;QACE4F,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBtB,IAAI,EAAE;MACR,CACF,EACGuB,IAAI,CAAC,YAAY;QAChB,IAAI;UACF,MAAMjG,KAAK,CAACkG,MAAM,CAAC,GAAGpF,OAAO,iBAAiBwD,GAAG,CAACrC,EAAE,EAAE;UACtDnC,SAAS,CAACkF,OAAO,CAAC,MAAM;UACxB;UACA,IAAI7D,WAAW,CAAC8B,KAAK,CAACkD,MAAK,KAAM,KAAK9E,WAAW,CAAC4B,KAAI,GAAI,CAAC,EAAE;YAC3D5B,WAAW,CAAC4B,KAAK,EAAC;UACpB;UACAE,aAAa,CAAC;QAChB,EAAE,OAAOQ,KAAK,EAAE;UACd/C,OAAO,CAAC+C,KAAK,CAAC,OAAO,EAAEA,KAAK;UAC5B7D,SAAS,CAAC6D,KAAK,CAACA,KAAK,CAACJ,QAAQ,EAAEE,IAAI,EAAEd,OAAM,IAAK,MAAM;QACzD;MACF,CAAC,EACAyD,KAAK,CAAC,MAAM;QACXtG,SAAS,CAACuG,IAAI,CAAC,OAAO;MACxB,CAAC;IACL;;IAEA;IACA,MAAMC,WAAU,GAAKrE,EAAE,IAAK;MAC1B;MACA3B,MAAM,CAACiG,IAAI,CAAC,oBAAoBtE,EAAE,EAAE;IACtC;;IAEA;IACA,MAAMuE,gBAAe,GAAI,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAMjD,QAAO,GAAI,MAAMvD,KAAK,CAACwD,GAAG,CAAC,GAAG1C,OAAO,+BAA+B,EAAE;UAC1E2F,YAAY,EAAE;QAChB,CAAC;;QAED;QACA,MAAMC,GAAE,GAAIC,MAAM,CAAC9B,GAAG,CAACC,eAAe,CAAC,IAAI8B,IAAI,CAAC,CAACrD,QAAQ,CAACE,IAAI,CAAC,CAAC;QAChE,MAAMoD,IAAG,GAAIC,QAAQ,CAACC,aAAa,CAAC,GAAG;QACvCF,IAAI,CAACG,IAAG,GAAIN,GAAE;QACdG,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,8BAA8B;QAC5DH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI;QAC9BA,IAAI,CAACO,KAAK,CAAC;QACXN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI;QAC9BF,MAAM,CAAC9B,GAAG,CAACyC,eAAe,CAACZ,GAAG;QAE9B5G,SAAS,CAACkF,OAAO,CAAC,QAAQ;MAC5B,EAAE,OAAOrB,KAAK,EAAE;QACd/C,OAAO,CAAC+C,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9B7D,SAAS,CAAC6D,KAAK,CAAC,QAAQ;MAC1B;IACF;;IAEA;IACA,MAAM4D,gBAAe,GAAIA,CAAA,KAAM;MAC7B/F,mBAAmB,CAACyB,KAAI,GAAI,IAAG;MAC/BtB,QAAQ,CAACsB,KAAI,GAAI,EAAC;MAClBrB,YAAY,CAACqB,KAAI,GAAI,IAAG;IAC1B;;IAEA;IACA,MAAMuE,iBAAgB,GAAKhD,IAAI,IAAK;MAClC,MAAMiD,OAAM,GAAIjD,IAAI,CAACE,IAAG,KAAM,mEAAkE,IACjFF,IAAI,CAACE,IAAG,KAAM,0BAAyB;MACtD,MAAMgD,OAAM,GAAIlD,IAAI,CAACI,IAAG,GAAI,IAAG,GAAI,IAAG,GAAI,EAAC;MAE3C,IAAI,CAAC6C,OAAO,EAAE;QACZ3H,SAAS,CAAC6D,KAAK,CAAC,cAAc;QAC9B,OAAO,KAAI;MACb;MAEA,IAAI,CAAC+D,OAAO,EAAE;QACZ5H,SAAS,CAAC6D,KAAK,CAAC,eAAe;QAC/B,OAAO,KAAI;MACb;MAEA,OAAO,IAAG;IACZ;;IAEA;IACA,MAAMgE,YAAW,GAAIA,CAAA,KAAM;MACzB,IAAIhG,QAAQ,CAACsB,KAAK,CAACkD,MAAK,KAAM,CAAC,EAAE;QAC/BrG,SAAS,CAAC8H,OAAO,CAAC,YAAY;QAC9B;MACF;MAEAlG,SAAS,CAACuB,KAAI,GAAI,IAAG;MACrBxB,SAAS,CAACwB,KAAK,CAAC4E,MAAM,CAAC;IACzB;;IAEA;IACA,MAAMC,mBAAkB,GAAKvE,QAAQ,IAAK;MACxC7B,SAAS,CAACuB,KAAI,GAAI,KAAI;MACtBrC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE0C,QAAQ;MAE/B,IAAIA,QAAQ,CAACyB,OAAO,EAAE;QACpBpD,YAAY,CAACqB,KAAI,GAAIM,QAAQ,CAACE,IAAG;QACjC3D,SAAS,CAACkF,OAAO,CAACzB,QAAQ,CAACZ,OAAO;QAClC;QACAQ,aAAa,CAAC;MAChB,OAAO;QACLrD,SAAS,CAAC6D,KAAK,CAACJ,QAAQ,CAACZ,OAAM,IAAK,MAAM;MAC5C;IACF;;IAEA;IACA,MAAMoF,iBAAgB,GAAKpE,KAAK,IAAK;MACnCjC,SAAS,CAACuB,KAAI,GAAI,KAAI;MACtBrC,OAAO,CAAC+C,KAAK,CAAC,OAAO,EAAEA,KAAK;MAC5B7D,SAAS,CAAC6D,KAAK,CAAC,mBAAmB;IACrC;IAEA,OAAO;MACL7C,OAAO;MACPC,aAAa;MACbC,OAAO;MACPG,WAAW;MACXU,UAAU;MACVG,QAAQ;MACRS,SAAS;MACTxB,aAAa;MACbC,cAAc;MACdG,WAAW;MACXC,QAAQ;MACRF,KAAK;MACLG,YAAY;MACZ;MACAC,mBAAmB;MACnBC,SAAS;MACTC,SAAS;MACTC,QAAQ;MACRC,YAAY;MACZ;MACAgC,YAAY;MACZC,WAAW;MACXK,gBAAgB;MAChBE,mBAAmB;MACnBC,UAAU;MACVe,UAAU;MACVb,iBAAiB;MACjBQ,kBAAkB;MAClBG,gBAAgB;MAChBW,YAAY;MACZS,WAAW;MACXE,gBAAgB;MAChBe,gBAAgB;MAChBC,iBAAiB;MACjBG,YAAY;MACZG,mBAAmB;MACnBC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}