{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createBlock as _createBlock, Fragment as _Fragment, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, resolveDirective as _resolveDirective, withDirectives as _withDirectives, renderList as _renderList } from \"vue\";\nconst _hoisted_1 = {\n  class: \"user-list-container\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  class: \"pagination-container\"\n};\nconst _hoisted_4 = {\n  class: \"teacher-option\"\n};\nconst _hoisted_5 = {\n  class: \"teacher-dept\"\n};\nconst _hoisted_6 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_7 = {\n  class: \"import-content\"\n};\nconst _hoisted_8 = {\n  key: 0,\n  class: \"import-result\",\n  style: {\n    \"margin-top\": \"20px\"\n  }\n};\nconst _hoisted_9 = {\n  key: 0,\n  style: {\n    \"margin-top\": \"15px\"\n  }\n};\nconst _hoisted_10 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_Upload = _resolveComponent(\"Upload\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_el_upload = _resolveComponent(\"el-upload\");\n  const _component_el_collapse_item = _resolveComponent(\"el-collapse-item\");\n  const _component_el_collapse = _resolveComponent(\"el-collapse\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createCommentVNode(\" 搜索和操作区域 \"), _createVNode(_component_el_card, {\n    class: \"search-card\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      inline: true,\n      model: $setup.searchForm,\n      class: \"search-form\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"用户名\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.searchForm.username,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.searchForm.username = $event),\n          placeholder: \"请输入用户名\",\n          clearable: \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"手机号\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.searchForm.phone,\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.searchForm.phone = $event),\n          placeholder: \"请输入手机号\",\n          clearable: \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, null, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: $setup.handleSearch\n        }, {\n          default: _withCtx(() => _cache[16] || (_cache[16] = [_createTextVNode(\"查询\")])),\n          _: 1 /* STABLE */,\n          __: [16]\n        }), _createVNode(_component_el_button, {\n          onClick: $setup.resetForm\n        }, {\n          default: _withCtx(() => _cache[17] || (_cache[17] = [_createTextVNode(\"重置\")])),\n          _: 1 /* STABLE */,\n          __: [17]\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 表格区域 \"), _createVNode(_component_el_card, {\n    class: \"table-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[22] || (_cache[22] = _createElementVNode(\"span\", null, \"用户列表\", -1 /* CACHED */)), _createElementVNode(\"div\", null, [_createVNode(_component_el_button, {\n      type: \"success\",\n      onClick: _ctx.downloadTemplate\n    }, {\n      default: _withCtx(() => _cache[18] || (_cache[18] = [_createTextVNode(\"下载模板\")])),\n      _: 1 /* STABLE */,\n      __: [18]\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      type: \"danger\",\n      onClick: _ctx.openImportDialog\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Upload)]),\n        _: 1 /* STABLE */\n      }), _cache[19] || (_cache[19] = _createTextVNode(\" 一键导入 \"))]),\n      _: 1 /* STABLE */,\n      __: [19]\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.handleAdd\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Plus\"])]),\n        _: 1 /* STABLE */\n      }), _cache[20] || (_cache[20] = _createTextVNode(\" 新增用户 \"))]),\n      _: 1 /* STABLE */,\n      __: [20]\n    }), _createVNode(_component_el_button, {\n      onClick: $setup.refreshTable\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Refresh\"])]),\n        _: 1 /* STABLE */\n      }), _cache[21] || (_cache[21] = _createTextVNode(\" 刷新 \"))]),\n      _: 1 /* STABLE */,\n      __: [21]\n    })])])]),\n    default: _withCtx(() => [_withDirectives((_openBlock(), _createBlock(_component_el_table, {\n      data: $setup.userList,\n      onSelectionChange: $setup.handleSelectionChange,\n      style: {\n        \"width\": \"100%\"\n      },\n      border: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        width: \"55\",\n        align: \"center\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"id\",\n        label: \"ID\",\n        width: \"80\",\n        align: \"center\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"username\",\n        label: \"用户名\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"name\",\n        label: \"姓名\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"phone\",\n        label: \"手机号\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"email\",\n        label: \"邮箱\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"role\",\n        label: \"角色\",\n        width: \"100\"\n      }, {\n        default: _withCtx(scope => [scope.row.role === 'admin' ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 0,\n          type: \"danger\"\n        }, {\n          default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\"管理员\")])),\n          _: 1 /* STABLE */,\n          __: [23]\n        })) : scope.row.role === 'teacher' ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 1,\n          type: \"warning\"\n        }, {\n          default: _withCtx(() => _cache[24] || (_cache[24] = [_createTextVNode(\"教师\")])),\n          _: 1 /* STABLE */,\n          __: [24]\n        })) : scope.row.role === 'teaching_admin' ? (_openBlock(), _createElementBlock(_Fragment, {\n          key: 2\n        }, [_createCommentVNode(\" 教学管理员、带教老师、教研室主任、教研室副主任、教学秘书 \"), _createVNode(_component_el_tag, {\n          type: \"warning\"\n        }, {\n          default: _withCtx(() => _cache[25] || (_cache[25] = [_createTextVNode(\"教学管理员\")])),\n          _: 1 /* STABLE */,\n          __: [25]\n        })], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : scope.row.role === 'teaching_teacher' ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 3,\n          type: \"warning\"\n        }, {\n          default: _withCtx(() => _cache[26] || (_cache[26] = [_createTextVNode(\"带教老师\")])),\n          _: 1 /* STABLE */,\n          __: [26]\n        })) : scope.row.role === 'department_head' ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 4,\n          type: \"warning\"\n        }, {\n          default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\"教研室主任\")])),\n          _: 1 /* STABLE */,\n          __: [27]\n        })) : scope.row.role === 'department_deputy_head' ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 5,\n          type: \"warning\"\n        }, {\n          default: _withCtx(() => _cache[28] || (_cache[28] = [_createTextVNode(\"教研室副主任\")])),\n          _: 1 /* STABLE */,\n          __: [28]\n        })) : scope.row.role === 'teaching_secretary' ? (_openBlock(), _createBlock(_component_el_tag, {\n          key: 6,\n          type: \"warning\"\n        }, {\n          default: _withCtx(() => _cache[29] || (_cache[29] = [_createTextVNode(\"教学秘书\")])),\n          _: 1 /* STABLE */,\n          __: [29]\n        })) : (_openBlock(), _createBlock(_component_el_tag, {\n          key: 7,\n          type: \"info\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString(scope.row.role), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1024 /* DYNAMIC_SLOTS */))]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"created_at\",\n        label: \"创建时间\",\n        width: \"160\"\n      }, {\n        default: _withCtx(scope => [_createTextVNode(_toDisplayString($setup.formatDate(scope.row.created_at)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        fixed: \"right\",\n        label: \"操作\",\n        width: \"180\",\n        align: \"center\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_button, {\n          size: \"small\",\n          onClick: $event => $setup.handleEdit(scope.row)\n        }, {\n          default: _withCtx(() => _cache[30] || (_cache[30] = [_createTextVNode(\"编辑\")])),\n          _: 2 /* DYNAMIC */,\n          __: [30]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n          size: \"small\",\n          type: \"danger\",\n          onClick: $event => $setup.handleDelete(scope.row)\n        }, {\n          default: _withCtx(() => _cache[31] || (_cache[31] = [_createTextVNode(\"删除\")])),\n          _: 2 /* DYNAMIC */,\n          __: [31]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"data\"])), [[_directive_loading, $setup.loading]]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_pagination, {\n      \"current-page\": $setup.currentPage,\n      \"onUpdate:currentPage\": _cache[2] || (_cache[2] = $event => $setup.currentPage = $event),\n      \"page-size\": $setup.pageSize,\n      \"onUpdate:pageSize\": _cache[3] || (_cache[3] = $event => $setup.pageSize = $event),\n      \"page-sizes\": [10, 20, 50, 100],\n      total: $setup.total,\n      layout: \"total, sizes, prev, pager, next, jumper\",\n      onSizeChange: $setup.handleSizeChange,\n      onCurrentChange: $setup.handleCurrentChange\n    }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\"])])]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 用户表单对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.dialogVisible,\n    \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $setup.dialogVisible = $event),\n    title: $setup.dialogType === 'add' ? '新增用户' : '编辑用户',\n    width: \"600px\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_button, {\n      onClick: _cache[12] || (_cache[12] = $event => $setup.dialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[33] || (_cache[33] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [33]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.submitForm\n    }, {\n      default: _withCtx(() => _cache[34] || (_cache[34] = [_createTextVNode(\"确定\")])),\n      _: 1 /* STABLE */,\n      __: [34]\n    })])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.userForm,\n      rules: $setup.userFormRules,\n      ref: \"userFormRef\",\n      \"label-width\": \"100px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"用户名\",\n        prop: \"username\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userForm.username,\n          \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.userForm.username = $event),\n          placeholder: \"请输入用户名\",\n          disabled: $setup.dialogType === 'edit'\n        }, null, 8 /* PROPS */, [\"modelValue\", \"disabled\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"姓名\",\n        prop: \"name\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userForm.name,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.userForm.name = $event),\n          placeholder: \"请输入姓名\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), $setup.dialogType === 'add' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 0,\n        label: \"密码\",\n        prop: \"password\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userForm.password,\n          \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.userForm.password = $event),\n          type: \"password\",\n          placeholder: \"请输入密码\",\n          \"show-password\": \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_form_item, {\n        label: \"手机号\",\n        prop: \"phone\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userForm.phone,\n          \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.userForm.phone = $event),\n          placeholder: \"请输入手机号\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"邮箱\",\n        prop: \"email\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userForm.email,\n          \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.userForm.email = $event),\n          placeholder: \"请输入邮箱\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), $setup.dialogType === 'add' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 1,\n        label: \"角色\",\n        prop: \"role\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.userForm.role,\n          \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $setup.userForm.role = $event),\n          placeholder: \"请选择角色\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_option, {\n            label: \"管理员\",\n            value: \"admin\"\n          }), _createVNode(_component_el_option, {\n            label: \"教师\",\n            value: \"teacher\"\n          }), _createVNode(_component_el_option, {\n            label: \"教学管理员\",\n            value: \"teaching_admin\"\n          }), _createVNode(_component_el_option, {\n            label: \"带教老师\",\n            value: \"teaching_teacher\"\n          }), _createVNode(_component_el_option, {\n            label: \"教研室主任\",\n            value: \"department_head\"\n          }), _createVNode(_component_el_option, {\n            label: \"教研室副主任\",\n            value: \"department_deputy_head\"\n          }), _createVNode(_component_el_option, {\n            label: \"教学秘书\",\n            value: \"teaching_secretary\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 当角色是教师时，显示教师选择器 \"), $setup.userForm.role === 'teacher' && $setup.dialogType === 'add' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 2,\n        label: \"关联教师\",\n        prop: \"teacher_id\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.userForm.teacher_id,\n          \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $setup.userForm.teacher_id = $event),\n          placeholder: \"请选择关联的教师\",\n          filterable: \"\",\n          loading: $setup.teacherListLoading\n        }, {\n          default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.teacherList, item => {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: item.id,\n              label: `${item.name} - ${item.department}`,\n              value: item.id\n            }, {\n              default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"span\", null, _toDisplayString(item.name), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_5, _toDisplayString(item.department), 1 /* TEXT */)])]),\n              _: 2 /* DYNAMIC */\n            }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\", \"loading\"]), _cache[32] || (_cache[32] = _createElementVNode(\"div\", {\n          class: \"form-tip\"\n        }, \"将用户账号关联到现有教师\", -1 /* CACHED */))]),\n        _: 1 /* STABLE */,\n        __: [32]\n      })) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 当角色不是教师时，隐藏教师ID字段 \"), $setup.userForm.role !== 'teacher' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 3,\n        label: \"教师ID\",\n        prop: \"teacher_id\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.userForm.teacher_id,\n          \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $setup.userForm.teacher_id = $event),\n          placeholder: \"请输入教师ID（选填）\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"title\"]), _createCommentVNode(\" Excel导入对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: _ctx.importDialogVisible,\n    \"onUpdate:modelValue\": _cache[15] || (_cache[15] = $event => _ctx.importDialogVisible = $event),\n    title: \"批量导入用户\",\n    width: \"50%\",\n    \"destroy-on-close\": \"\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_10, [_createVNode(_component_el_button, {\n      onClick: _cache[14] || (_cache[14] = $event => _ctx.importDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[38] || (_cache[38] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [38]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _ctx.handleUpload,\n      loading: _ctx.uploading\n    }, {\n      default: _withCtx(() => _cache[39] || (_cache[39] = [_createTextVNode(\"开始导入\")])),\n      _: 1 /* STABLE */,\n      __: [39]\n    }, 8 /* PROPS */, [\"onClick\", \"loading\"])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_alert, {\n      title: \"导入说明\",\n      type: \"info\",\n      closable: false,\n      style: {\n        \"margin-bottom\": \"20px\"\n      }\n    }, {\n      default: _withCtx(() => _cache[35] || (_cache[35] = [_createElementVNode(\"div\", null, [_createElementVNode(\"p\", null, \"1. 请先下载Excel模板，按照模板格式填写用户信息\"), _createElementVNode(\"p\", null, \"2. 必填字段：用户名、密码、角色、姓名\"), _createElementVNode(\"p\", null, \"3. 角色只能填写\\\"admin\\\"或\\\"teacher\\\"\"), _createElementVNode(\"p\", null, \"4. 状态可填写：启用/禁用、true/false、1/0\"), _createElementVNode(\"p\", null, \"5. 支持.xlsx和.xls格式，文件大小不超过10MB\"), _createElementVNode(\"p\", null, \"6. 注意：不包含关联教师功能，如需关联请在导入后手动编辑\")], -1 /* CACHED */)])),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_upload, {\n      ref: \"uploadRef\",\n      class: \"upload-demo\",\n      action: `${_ctx.baseUrl}/api/users/import/excel`,\n      headers: _ctx.uploadHeaders,\n      \"before-upload\": _ctx.beforeExcelUpload,\n      \"on-success\": _ctx.handleImportSuccess,\n      \"on-error\": _ctx.handleImportError,\n      \"on-change\": _ctx.handleFileChange,\n      \"file-list\": _ctx.fileList,\n      \"auto-upload\": false,\n      accept: \".xlsx,.xls\",\n      limit: 1,\n      name: \"excel\"\n    }, {\n      tip: _withCtx(() => _cache[37] || (_cache[37] = [_createElementVNode(\"div\", {\n        class: \"el-upload__tip\"\n      }, \" 只能上传xlsx/xls文件，且不超过10MB \", -1 /* CACHED */)])),\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        type: \"primary\"\n      }, {\n        default: _withCtx(() => _cache[36] || (_cache[36] = [_createTextVNode(\"选择Excel文件\")])),\n        _: 1 /* STABLE */,\n        __: [36]\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"action\", \"headers\", \"before-upload\", \"on-success\", \"on-error\", \"on-change\", \"file-list\"]), _createCommentVNode(\" 导入结果显示 \"), _ctx.importResult ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_createVNode(_component_el_alert, {\n      title: `导入完成！成功 ${_ctx.importResult.success} 条，失败 ${_ctx.importResult.failed} 条`,\n      type: _ctx.importResult.failed > 0 ? 'warning' : 'success',\n      closable: false\n    }, null, 8 /* PROPS */, [\"title\", \"type\"]), _createCommentVNode(\" 失败记录详情 \"), _ctx.importResult.failedRecords && _ctx.importResult.failedRecords.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createVNode(_component_el_collapse, null, {\n      default: _withCtx(() => [_createVNode(_component_el_collapse_item, {\n        title: \"查看失败记录\",\n        name: \"failed\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_table, {\n          data: _ctx.importResult.failedRecords,\n          border: \"\",\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_table_column, {\n            prop: \"row\",\n            label: \"行号\",\n            width: \"80\"\n          }), _createVNode(_component_el_table_column, {\n            prop: \"error\",\n            label: \"错误原因\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"data\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true)])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "style", "_createElementBlock", "_hoisted_1", "_createCommentVNode", "_createVNode", "_component_el_card", "_component_el_form", "inline", "model", "$setup", "searchForm", "_component_el_form_item", "label", "_component_el_input", "username", "$event", "placeholder", "clearable", "phone", "_component_el_button", "type", "onClick", "handleSearch", "_cache", "resetForm", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_ctx", "downloadTemplate", "openImportDialog", "_component_el_icon", "_component_Upload", "handleAdd", "refreshTable", "_createBlock", "_component_el_table", "data", "userList", "onSelectionChange", "handleSelectionChange", "border", "_component_el_table_column", "width", "align", "prop", "default", "scope", "row", "role", "_component_el_tag", "_Fragment", "key", "formatDate", "created_at", "fixed", "size", "handleEdit", "handleDelete", "loading", "_hoisted_3", "_component_el_pagination", "currentPage", "pageSize", "total", "layout", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "_component_el_dialog", "dialogVisible", "title", "dialogType", "footer", "_hoisted_6", "submitForm", "userForm", "rules", "userFormRules", "ref", "disabled", "name", "password", "email", "_component_el_select", "_component_el_option", "value", "teacher_id", "filterable", "teacherListL<PERSON>ding", "_renderList", "teacherList", "item", "id", "department", "_hoisted_4", "_toDisplayString", "_hoisted_5", "importDialogVisible", "_hoisted_10", "handleUpload", "uploading", "_hoisted_7", "_component_el_alert", "closable", "_component_el_upload", "action", "baseUrl", "headers", "uploadHeaders", "beforeExcelUpload", "handleImportSuccess", "handleImportError", "handleFileChange", "fileList", "accept", "limit", "tip", "importResult", "_hoisted_8", "success", "failed", "failedRecords", "length", "_hoisted_9", "_component_el_collapse", "_component_el_collapse_item"], "sources": ["D:\\admin\\202506\\督导系统\\后台管理系统\\ms\\src\\views\\users\\UserList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"user-list-container\">\r\n    <!-- 搜索和操作区域 -->\r\n    <el-card class=\"search-card\">\r\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\r\n        <el-form-item label=\"用户名\">\r\n          <el-input v-model=\"searchForm.username\" placeholder=\"请输入用户名\" clearable></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\">\r\n          <el-input v-model=\"searchForm.phone\" placeholder=\"请输入手机号\" clearable></el-input>\r\n        </el-form-item>\r\n      \r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n          <el-button @click=\"resetForm\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-card>\r\n    \r\n    <!-- 表格区域 -->\r\n    <el-card class=\"table-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span>用户列表</span>\r\n          <div>\r\n            <el-button type=\"success\" @click=\"downloadTemplate\">下载模板</el-button>\r\n            <el-button type=\"danger\" @click=\"openImportDialog\">\r\n              <el-icon><Upload /></el-icon> 一键导入\r\n            </el-button>\r\n            <el-button type=\"primary\" @click=\"handleAdd\">\r\n              <el-icon><Plus /></el-icon> 新增用户\r\n            </el-button>\r\n            <el-button @click=\"refreshTable\">\r\n              <el-icon><Refresh /></el-icon> 刷新\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n      \r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"userList\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        style=\"width: 100%\"\r\n        border\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column prop=\"id\" label=\"ID\" width=\"80\" align=\"center\" />\r\n        <el-table-column prop=\"username\" label=\"用户名\" />\r\n        <el-table-column prop=\"name\" label=\"姓名\" />\r\n        <el-table-column prop=\"phone\" label=\"手机号\" />\r\n        <el-table-column prop=\"email\" label=\"邮箱\" />\r\n        <el-table-column prop=\"role\" label=\"角色\" width=\"100\">\r\n          <template #default=\"scope\">\r\n            <el-tag v-if=\"scope.row.role === 'admin'\" type=\"danger\">管理员</el-tag>\r\n            <el-tag v-else-if=\"scope.row.role === 'teacher'\" type=\"warning\">教师</el-tag>\r\n            <!-- 教学管理员、带教老师、教研室主任、教研室副主任、教学秘书 -->\r\n             <el-tag v-else-if=\"scope.row.role === 'teaching_admin'\" type=\"warning\">教学管理员</el-tag>\r\n             <el-tag v-else-if=\"scope.row.role === 'teaching_teacher'\" type=\"warning\">带教老师</el-tag>\r\n             <el-tag v-else-if=\"scope.row.role === 'department_head'\" type=\"warning\">教研室主任</el-tag>\r\n             <el-tag v-else-if=\"scope.row.role === 'department_deputy_head'\" type=\"warning\">教研室副主任</el-tag>\r\n             <el-tag v-else-if=\"scope.row.role === 'teaching_secretary'\" type=\"warning\">教学秘书</el-tag>\r\n            <el-tag v-else type=\"info\">{{ scope.row.role }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n       \r\n        \r\n       \r\n       \r\n        <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"160\">\r\n          <template #default=\"scope\">\r\n            {{ formatDate(scope.row.created_at) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"180\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <el-button size=\"small\" @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n            <el-button \r\n              size=\"small\" \r\n              type=\"danger\" \r\n              @click=\"handleDelete(scope.row)\"\r\n            >删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <!-- 分页 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          v-model:current-page=\"currentPage\"\r\n          v-model:page-size=\"pageSize\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          :total=\"total\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 用户表单对话框 -->\r\n    <el-dialog\r\n      v-model=\"dialogVisible\"\r\n      :title=\"dialogType === 'add' ? '新增用户' : '编辑用户'\"\r\n      width=\"600px\"\r\n    >\r\n      <el-form\r\n        :model=\"userForm\"\r\n        :rules=\"userFormRules\"\r\n        ref=\"userFormRef\"\r\n        label-width=\"100px\"\r\n      >\r\n        <el-form-item label=\"用户名\" prop=\"username\">\r\n          <el-input v-model=\"userForm.username\" placeholder=\"请输入用户名\" :disabled=\"dialogType === 'edit'\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"姓名\" prop=\"name\">\r\n          <el-input v-model=\"userForm.name\" placeholder=\"请输入姓名\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"dialogType === 'add'\" label=\"密码\" prop=\"password\">\r\n          <el-input v-model=\"userForm.password\" type=\"password\" placeholder=\"请输入密码\" show-password></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\" prop=\"phone\">\r\n          <el-input v-model=\"userForm.phone\" placeholder=\"请输入手机号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱\" prop=\"email\" >\r\n          <el-input v-model=\"userForm.email\" placeholder=\"请输入邮箱\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"角色\" prop=\"role\" v-if=\"dialogType === 'add'\">\r\n          <el-select v-model=\"userForm.role\" placeholder=\"请选择角色\">\r\n            <el-option label=\"管理员\" value=\"admin\"></el-option>\r\n            <el-option label=\"教师\" value=\"teacher\"></el-option>\r\n            <el-option label=\"教学管理员\" value=\"teaching_admin\"></el-option>\r\n            <el-option label=\"带教老师\" value=\"teaching_teacher\"></el-option>\r\n            <el-option label=\"教研室主任\" value=\"department_head\"></el-option>\r\n            <el-option label=\"教研室副主任\" value=\"department_deputy_head\"></el-option>\r\n            <el-option label=\"教学秘书\" value=\"teaching_secretary\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 当角色是教师时，显示教师选择器 -->\r\n        <el-form-item v-if=\"userForm.role === 'teacher' && dialogType === 'add'\" label=\"关联教师\" prop=\"teacher_id\">\r\n          <el-select \r\n            v-model=\"userForm.teacher_id\" \r\n            placeholder=\"请选择关联的教师\" \r\n            filterable\r\n            :loading=\"teacherListLoading\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in teacherList\"\r\n              :key=\"item.id\"\r\n              :label=\"`${item.name} - ${item.department}`\"\r\n              :value=\"item.id\"\r\n            >\r\n              <div class=\"teacher-option\">\r\n                <span>{{ item.name }}</span>\r\n                <span class=\"teacher-dept\">{{ item.department }}</span>\r\n              </div>\r\n            </el-option>\r\n          </el-select>\r\n          <div class=\"form-tip\">将用户账号关联到现有教师</div>\r\n        </el-form-item>\r\n\r\n        <!-- 当角色不是教师时，隐藏教师ID字段 -->\r\n        <el-form-item v-if=\"userForm.role !== 'teacher'\" label=\"教师ID\" prop=\"teacher_id\">\r\n          <el-input v-model=\"userForm.teacher_id\" placeholder=\"请输入教师ID（选填）\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- Excel导入对话框 -->\r\n    <el-dialog\r\n      v-model=\"importDialogVisible\"\r\n      title=\"批量导入用户\"\r\n      width=\"50%\"\r\n      destroy-on-close\r\n    >\r\n      <div class=\"import-content\">\r\n        <el-alert\r\n          title=\"导入说明\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          style=\"margin-bottom: 20px\"\r\n        >\r\n          <template #default>\r\n            <div>\r\n              <p>1. 请先下载Excel模板，按照模板格式填写用户信息</p>\r\n              <p>2. 必填字段：用户名、密码、角色、姓名</p>\r\n              <p>3. 角色只能填写\"admin\"或\"teacher\"</p>\r\n              <p>4. 状态可填写：启用/禁用、true/false、1/0</p>\r\n              <p>5. 支持.xlsx和.xls格式，文件大小不超过10MB</p>\r\n              <p>6. 注意：不包含关联教师功能，如需关联请在导入后手动编辑</p>\r\n            </div>\r\n          </template>\r\n        </el-alert>\r\n\r\n        <el-upload\r\n          ref=\"uploadRef\"\r\n          class=\"upload-demo\"\r\n          :action=\"`${baseUrl}/api/users/import/excel`\"\r\n          :headers=\"uploadHeaders\"\r\n          :before-upload=\"beforeExcelUpload\"\r\n          :on-success=\"handleImportSuccess\"\r\n          :on-error=\"handleImportError\"\r\n          :on-change=\"handleFileChange\"\r\n          :file-list=\"fileList\"\r\n          :auto-upload=\"false\"\r\n          accept=\".xlsx,.xls\"\r\n          :limit=\"1\"\r\n          name=\"excel\"\r\n        >\r\n          <el-button type=\"primary\">选择Excel文件</el-button>\r\n          <template #tip>\r\n            <div class=\"el-upload__tip\">\r\n              只能上传xlsx/xls文件，且不超过10MB\r\n            </div>\r\n          </template>\r\n        </el-upload>\r\n\r\n        <!-- 导入结果显示 -->\r\n        <div v-if=\"importResult\" class=\"import-result\" style=\"margin-top: 20px\">\r\n          <el-alert\r\n            :title=\"`导入完成！成功 ${importResult.success} 条，失败 ${importResult.failed} 条`\"\r\n            :type=\"importResult.failed > 0 ? 'warning' : 'success'\"\r\n            :closable=\"false\"\r\n          />\r\n\r\n          <!-- 失败记录详情 -->\r\n          <div v-if=\"importResult.failedRecords && importResult.failedRecords.length > 0\" style=\"margin-top: 15px\">\r\n            <el-collapse>\r\n              <el-collapse-item title=\"查看失败记录\" name=\"failed\">\r\n                <el-table :data=\"importResult.failedRecords\" border size=\"small\">\r\n                  <el-table-column prop=\"row\" label=\"行号\" width=\"80\" />\r\n                  <el-table-column prop=\"error\" label=\"错误原因\" />\r\n                </el-table>\r\n              </el-collapse-item>\r\n            </el-collapse>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"importDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"handleUpload\" :loading=\"uploading\">开始导入</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted, watch } from 'vue'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { Plus, Delete, Refresh } from '@element-plus/icons-vue'\r\nimport userService from '@/services/userService'\r\nimport teacherService from '@/services/teacherService'\r\n\r\nconst loading = ref(false)\r\nconst teacherListLoading = ref(false)\r\nconst currentPage = ref(1)\r\nconst pageSize = ref(10)\r\nconst total = ref(0)\r\nconst multipleSelection = ref([])\r\nconst dialogVisible = ref(false)\r\nconst dialogType = ref('add') // 'add' or 'edit'\r\nconst userFormRef = ref(null)\r\nconst teacherList = ref([]) // 教师列表\r\n\r\n// 搜索表单\r\nconst searchForm = reactive({\r\n  username: '',\r\n  phone: '',\r\n  status: ''\r\n})\r\n\r\n// 用户表单\r\nconst userForm = reactive({\r\n  id: '',\r\n  username: '',\r\n  name: '',\r\n  password: '',\r\n  phone: '',\r\n  email: '',\r\n  role: 'teacher',\r\n  teacher_id: '',\r\n  status: 1\r\n})\r\n\r\n// 获取教师列表\r\nconst fetchTeacherList = async () => {\r\n  teacherListLoading.value = true\r\n  try {\r\n    const response = await teacherService.getTeachers()\r\n    teacherList.value = response.data.data\r\n  } catch (error) {\r\n    console.error('获取教师列表失败:', error)\r\n    ElMessage.error('获取教师列表失败')\r\n  } finally {\r\n    teacherListLoading.value = false\r\n  }\r\n}\r\n\r\n// 当角色选择为教师时，加载教师列表\r\nwatch(() => userForm.role, (newRole) => {\r\n  if (newRole === 'teacher' && teacherList.value.length === 0) {\r\n    fetchTeacherList()\r\n  }\r\n})\r\n\r\n// 当对话框打开时，如果角色是教师且教师列表为空，则获取教师列表\r\nwatch(() => dialogVisible.value, (newVal) => {\r\n  if (newVal && userForm.role === 'teacher' && teacherList.value.length === 0) {\r\n    fetchTeacherList()\r\n  }\r\n})\r\n\r\n// 表单校验规则\r\nconst userFormRules = {\r\n  username: [\r\n    { required: true, message: '请输入用户名', trigger: 'blur' },\r\n    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  name: [\r\n    { required: true, message: '请输入姓名', trigger: 'blur' }\r\n  ],\r\n  password: [\r\n    { required: true, message: '请输入密码', trigger: 'blur' },\r\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  phone: [\r\n    { required: true, message: '请输入手机号', trigger: 'blur' },\r\n    { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\r\n  ],\r\n  email: [\r\n    { required: true, message: '请输入邮箱', trigger: 'blur' },\r\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\r\n  ],\r\n  role: [\r\n    { required: true, message: '请选择角色', trigger: 'change' }\r\n  ],\r\n  teacher_id: [\r\n    { \r\n      validator: (rule, value, callback) => {\r\n        if (userForm.role === 'teacher' && !value) {\r\n          callback(new Error('请选择关联的教师'));\r\n        } else {\r\n          callback();\r\n        }\r\n      }, \r\n      trigger: 'change' \r\n    }\r\n  ]\r\n}\r\n\r\n// 用户数据\r\nconst userList = ref([])\r\n\r\nonMounted(() => {\r\n  fetchData()\r\n})\r\n\r\n// 格式化日期\r\nconst formatDate = (dateString) => {\r\n  if (!dateString) return ''\r\n  const date = new Date(dateString)\r\n  const year = date.getFullYear()\r\n  const month = String(date.getMonth() + 1).padStart(2, '0')\r\n  const day = String(date.getDate()).padStart(2, '0')\r\n  const hours = String(date.getHours()).padStart(2, '0')\r\n  const minutes = String(date.getMinutes()).padStart(2, '0')\r\n  return `${year}-${month}-${day} ${hours}:${minutes}`\r\n}\r\n\r\n// 获取数据\r\nconst fetchData = async () => {\r\n  loading.value = true\r\n  try {\r\n    const response = await userService.getUsers({\r\n      page: currentPage.value,\r\n      limit: pageSize.value,\r\n      username: searchForm.username || undefined,\r\n      phone: searchForm.phone || undefined,\r\n      status: searchForm.status || undefined\r\n    })\r\n    userList.value = response.data.data\r\n    total.value = response.data.count || 0\r\n  } catch (error) {\r\n    console.error('获取用户列表失败:', error)\r\n    ElMessage.error('获取用户列表失败')\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\n// 查询\r\nconst handleSearch = () => {\r\n  currentPage.value = 1\r\n  fetchData()\r\n}\r\n\r\n// 重置表单\r\nconst resetForm = () => {\r\n  Object.keys(searchForm).forEach(key => {\r\n    searchForm[key] = ''\r\n  })\r\n  handleSearch()\r\n}\r\n\r\n// 刷新表格\r\nconst refreshTable = () => {\r\n  fetchData()\r\n}\r\n\r\n// 多选变化\r\nconst handleSelectionChange = (selection) => {\r\n  multipleSelection.value = selection\r\n}\r\n\r\n// 新增用户\r\nconst handleAdd = () => {\r\n  dialogType.value = 'add'\r\n  resetUserForm()\r\n  dialogVisible.value = true\r\n}\r\n\r\n// 编辑用户\r\nconst handleEdit = (row) => {\r\n  dialogType.value = 'edit'\r\n  resetUserForm()\r\n  Object.keys(userForm).forEach(key => {\r\n    if (key !== 'password') {\r\n      userForm[key] = row[key]\r\n    }\r\n  })\r\n  dialogVisible.value = true\r\n}\r\n\r\n// 重置用户表单\r\nconst resetUserForm = () => {\r\n  if (userFormRef.value) {\r\n    userFormRef.value.resetFields()\r\n  }\r\n  Object.assign(userForm, {\r\n    id: '',\r\n    username: '',\r\n    name: '',\r\n    password: '',\r\n    phone: '',\r\n    email: '',\r\n    role: 'teacher',\r\n    teacher_id: '',\r\n    status: 1\r\n  })\r\n}\r\n\r\n// 提交表单\r\nconst submitForm = async () => {\r\n  if (!userFormRef.value) return\r\n  \r\n  await userFormRef.value.validate(async (valid) => {\r\n    if (valid) {\r\n      try {\r\n        if (dialogType.value === 'add') {\r\n          // 新增用户\r\n          if (userForm.role !== 'teacher') {\r\n           delete userForm.teacher_id\r\n          }\r\n          await userService.createUser(userForm)\r\n          ElMessage.success('新增用户成功')\r\n        } else {\r\n          // 编辑用户\r\n          await userService.updateUser(userForm.id, userForm)\r\n          ElMessage.success('编辑用户成功')\r\n        }\r\n        dialogVisible.value = false\r\n        fetchData()\r\n      } catch (error) {\r\n        console.error('保存用户失败:', error)\r\n        ElMessage.error('保存用户失败: ' + (error.response?.data?.message || error.message))\r\n      }\r\n    } else {\r\n      return false\r\n    }\r\n  })\r\n}\r\n\r\n// 删除用户\r\nconst handleDelete = (row) => {\r\n  ElMessageBox.confirm(`确定要删除用户 ${row.username} 吗?`, '警告', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    try {\r\n      await userService.deleteUser(row.id)\r\n      ElMessage.success(`用户 ${row.username} 已删除`)\r\n      fetchData()\r\n    } catch (error) {\r\n      console.error('删除用户失败:', error)\r\n      ElMessage.error('删除用户失败: ' + (error.response?.data?.message || error.message))\r\n    }\r\n  }).catch(() => {})\r\n}\r\n\r\n// 批量删除\r\nconst handleBatchDelete = () => {\r\n  if (multipleSelection.value.length === 0) {\r\n    ElMessage.warning('请至少选择一条记录')\r\n    return\r\n  }\r\n  \r\n  const names = multipleSelection.value.map(item => item.username).join('、')\r\n  const ids = multipleSelection.value.map(item => item.id)\r\n  \r\n  ElMessageBox.confirm(`确定要删除选中的 ${multipleSelection.value.length} 条记录吗?`, '警告', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    try {\r\n      await userService.batchDeleteUsers(ids)\r\n      ElMessage.success('批量删除成功')\r\n      fetchData()\r\n    } catch (error) {\r\n      console.error('批量删除失败:', error)\r\n      ElMessage.error('批量删除失败: ' + (error.response?.data?.message || error.message))\r\n    }\r\n  }).catch(() => {})\r\n}\r\n\r\n// 修改状态\r\nconst handleStatusChange = async (val, row) => {\r\n  try {\r\n    await userService.updateUserStatus(row.id, val);\r\n    const status = val === 1 ? '启用' : '禁用';\r\n    ElMessage.success(`已${status}用户 ${row.username}`);\r\n  } catch (error) {\r\n    console.error('修改状态失败:', error);\r\n    ElMessage.error('修改状态失败: ' + (error.response?.data?.message || error.message));\r\n    // 回滚状态\r\n    row.status = val === 1 ? 0 : 1;\r\n  }\r\n};\r\n\r\n// 分页大小变化\r\nconst handleSizeChange = (size) => {\r\n  pageSize.value = size\r\n  fetchData()\r\n}\r\n\r\n// 页码变化\r\nconst handleCurrentChange = (page) => {\r\n  currentPage.value = page\r\n  fetchData()\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.user-list-container {\r\n  padding: 10px;\r\n}\r\n\r\n.search-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.teacher-option {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.teacher-dept {\r\n  color: #909399;\r\n  font-size: 0.9em;\r\n}\r\n\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 5px;\r\n}\r\n</style> "], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EAqBrBA,KAAK,EAAC;AAAa;;EAiErBA,KAAK,EAAC;AAAsB;;EAkEpBA,KAAK,EAAC;AAAgB;;EAEnBA,KAAK,EAAC;AAAc;;EAa7BA,KAAK,EAAC;AAAe;;EAcvBA,KAAK,EAAC;AAAgB;;;EA2CAA,KAAK,EAAC,eAAe;EAACC,KAAwB,EAAxB;IAAA;EAAA;;;;EAQmCA,KAAwB,EAAxB;IAAA;EAAA;;;EAc5ED,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;;;;;uBAtPjCE,mBAAA,CA4PM,OA5PNC,UA4PM,GA3PJC,mBAAA,aAAgB,EAChBC,YAAA,CAcUC,kBAAA;IAdDN,KAAK,EAAC;EAAa;sBAC1B,MAYU,CAZVK,YAAA,CAYUE,kBAAA;MAZAC,MAAM,EAAE,IAAI;MAAGC,KAAK,EAAEC,MAAA,CAAAC,UAAU;MAAEX,KAAK,EAAC;;wBAChD,MAEe,CAFfK,YAAA,CAEeO,uBAAA;QAFDC,KAAK,EAAC;MAAK;0BACvB,MAAkF,CAAlFR,YAAA,CAAkFS,mBAAA;sBAA/DJ,MAAA,CAAAC,UAAU,CAACI,QAAQ;qEAAnBL,MAAA,CAAAC,UAAU,CAACI,QAAQ,GAAAC,MAAA;UAAEC,WAAW,EAAC,QAAQ;UAACC,SAAS,EAAT;;;UAE/Db,YAAA,CAEeO,uBAAA;QAFDC,KAAK,EAAC;MAAK;0BACvB,MAA+E,CAA/ER,YAAA,CAA+ES,mBAAA;sBAA5DJ,MAAA,CAAAC,UAAU,CAACQ,KAAK;qEAAhBT,MAAA,CAAAC,UAAU,CAACQ,KAAK,GAAAH,MAAA;UAAEC,WAAW,EAAC,QAAQ;UAACC,SAAS,EAAT;;;UAG5Db,YAAA,CAGeO,uBAAA;0BAFb,MAA8D,CAA9DP,YAAA,CAA8De,oBAAA;UAAnDC,IAAI,EAAC,SAAS;UAAEC,OAAK,EAAEZ,MAAA,CAAAa;;4BAAc,MAAEC,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;YAClDnB,YAAA,CAA4Ce,oBAAA;UAAhCE,OAAK,EAAEZ,MAAA,CAAAe;QAAS;4BAAE,MAAED,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;;;;;MAKtCpB,mBAAA,UAAa,EACbC,YAAA,CA8EUC,kBAAA;IA9EDN,KAAK,EAAC;EAAY;IACd0B,MAAM,EAAAC,QAAA,CACf,MAcM,CAdNC,mBAAA,CAcM,OAdNC,UAcM,G,4BAbJD,mBAAA,CAAiB,cAAX,MAAI,qBACVA,mBAAA,CAWM,cAVJvB,YAAA,CAAoEe,oBAAA;MAAzDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEQ,IAAA,CAAAC;;wBAAkB,MAAIP,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;oCACxDnB,YAAA,CAEYe,oBAAA;MAFDC,IAAI,EAAC,QAAQ;MAAEC,OAAK,EAAEQ,IAAA,CAAAE;;wBAC/B,MAA6B,CAA7B3B,YAAA,CAA6B4B,kBAAA;0BAApB,MAAU,CAAV5B,YAAA,CAAU6B,iBAAA,E;;uDAAU,QAC/B,G;;;oCACA7B,YAAA,CAEYe,oBAAA;MAFDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEZ,MAAA,CAAAyB;;wBAChC,MAA2B,CAA3B9B,YAAA,CAA2B4B,kBAAA;0BAAlB,MAAQ,CAAR5B,YAAA,CAAQK,MAAA,U;;uDAAU,QAC7B,G;;;QACAL,YAAA,CAEYe,oBAAA;MAFAE,OAAK,EAAEZ,MAAA,CAAA0B;IAAY;wBAC7B,MAA8B,CAA9B/B,YAAA,CAA8B4B,kBAAA;0BAArB,MAAW,CAAX5B,YAAA,CAAWK,MAAA,a;;uDAAU,MAChC,G;;;;sBAKN,MA6CW,C,+BA7CX2B,YAAA,CA6CWC,mBAAA;MA3CRC,IAAI,EAAE7B,MAAA,CAAA8B,QAAQ;MACdC,iBAAgB,EAAE/B,MAAA,CAAAgC,qBAAqB;MACxCzC,KAAmB,EAAnB;QAAA;MAAA,CAAmB;MACnB0C,MAAM,EAAN;;wBAEA,MAA8D,CAA9DtC,YAAA,CAA8DuC,0BAAA;QAA7CvB,IAAI,EAAC,WAAW;QAACwB,KAAK,EAAC,IAAI;QAACC,KAAK,EAAC;UACnDzC,YAAA,CAAkEuC,0BAAA;QAAjDG,IAAI,EAAC,IAAI;QAAClC,KAAK,EAAC,IAAI;QAACgC,KAAK,EAAC,IAAI;QAACC,KAAK,EAAC;UACvDzC,YAAA,CAA+CuC,0BAAA;QAA9BG,IAAI,EAAC,UAAU;QAAClC,KAAK,EAAC;UACvCR,YAAA,CAA0CuC,0BAAA;QAAzBG,IAAI,EAAC,MAAM;QAAClC,KAAK,EAAC;UACnCR,YAAA,CAA4CuC,0BAAA;QAA3BG,IAAI,EAAC,OAAO;QAAClC,KAAK,EAAC;UACpCR,YAAA,CAA2CuC,0BAAA;QAA1BG,IAAI,EAAC,OAAO;QAAClC,KAAK,EAAC;UACpCR,YAAA,CAYkBuC,0BAAA;QAZDG,IAAI,EAAC,MAAM;QAAClC,KAAK,EAAC,IAAI;QAACgC,KAAK,EAAC;;QACjCG,OAAO,EAAArB,QAAA,CACoDsB,KAD7C,KACTA,KAAK,CAACC,GAAG,CAACC,IAAI,gB,cAA5Bd,YAAA,CAAoEe,iBAAA;;UAA1B/B,IAAI,EAAC;;4BAAS,MAAGG,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;cACxCyB,KAAK,CAACC,GAAG,CAACC,IAAI,kB,cAAjCd,YAAA,CAA2Ee,iBAAA;;UAA1B/B,IAAI,EAAC;;4BAAU,MAAEG,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;cAE9CyB,KAAK,CAACC,GAAG,CAACC,IAAI,yB,cAAjCjD,mBAAA,CAAqFmD,SAAA;UAAAC,GAAA;QAAA,IADtFlD,mBAAA,kCAAqC,EACpCC,YAAA,CAAqF+C,iBAAA;UAA7B/B,IAAI,EAAC;QAAS;4BAAC,MAAKG,MAAA,SAAAA,MAAA,Q,iBAAL,OAAK,E;;;+DACzDyB,KAAK,CAACC,GAAG,CAACC,IAAI,2B,cAAjCd,YAAA,CAAsFe,iBAAA;;UAA5B/B,IAAI,EAAC;;4BAAU,MAAIG,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;cAC1DyB,KAAK,CAACC,GAAG,CAACC,IAAI,0B,cAAjCd,YAAA,CAAsFe,iBAAA;;UAA7B/B,IAAI,EAAC;;4BAAU,MAAKG,MAAA,SAAAA,MAAA,Q,iBAAL,OAAK,E;;;cAC1DyB,KAAK,CAACC,GAAG,CAACC,IAAI,iC,cAAjCd,YAAA,CAA8Fe,iBAAA;;UAA9B/B,IAAI,EAAC;;4BAAU,MAAMG,MAAA,SAAAA,MAAA,Q,iBAAN,QAAM,E;;;cAClEyB,KAAK,CAACC,GAAG,CAACC,IAAI,6B,cAAjCd,YAAA,CAAwFe,iBAAA;;UAA5B/B,IAAI,EAAC;;4BAAU,MAAIG,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;6BAChFa,YAAA,CAAwDe,iBAAA;;UAAzC/B,IAAI,EAAC;;4BAAO,MAAoB,C,kCAAjB4B,KAAK,CAACC,GAAG,CAACC,IAAI,iB;;;;UAOhD9C,YAAA,CAIkBuC,0BAAA;QAJDG,IAAI,EAAC,YAAY;QAAClC,KAAK,EAAC,MAAM;QAACgC,KAAK,EAAC;;QACzCG,OAAO,EAAArB,QAAA,CACsBsB,KADf,K,kCACpBvC,MAAA,CAAA6C,UAAU,CAACN,KAAK,CAACC,GAAG,CAACM,UAAU,kB;;UAGtCnD,YAAA,CASkBuC,0BAAA;QATDa,KAAK,EAAC,OAAO;QAAC5C,KAAK,EAAC,IAAI;QAACgC,KAAK,EAAC,KAAK;QAACC,KAAK,EAAC;;QAC/CE,OAAO,EAAArB,QAAA,CACqDsB,KAD9C,KACvB5C,YAAA,CAAqEe,oBAAA;UAA1DsC,IAAI,EAAC,OAAO;UAAEpC,OAAK,EAAAN,MAAA,IAAEN,MAAA,CAAAiD,UAAU,CAACV,KAAK,CAACC,GAAG;;4BAAG,MAAE1B,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;0DACzDnB,YAAA,CAIee,oBAAA;UAHbsC,IAAI,EAAC,OAAO;UACZrC,IAAI,EAAC,QAAQ;UACZC,OAAK,EAAAN,MAAA,IAAEN,MAAA,CAAAkD,YAAY,CAACX,KAAK,CAACC,GAAG;;4BAC/B,MAAE1B,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;;;wDAzCId,MAAA,CAAAmD,OAAO,E,GA+CpBjC,mBAAA,CAUM,OAVNkC,UAUM,GATJzD,YAAA,CAQE0D,wBAAA;MAPQ,cAAY,EAAErD,MAAA,CAAAsD,WAAW;kEAAXtD,MAAA,CAAAsD,WAAW,GAAAhD,MAAA;MACzB,WAAS,EAAEN,MAAA,CAAAuD,QAAQ;+DAARvD,MAAA,CAAAuD,QAAQ,GAAAjD,MAAA;MAC1B,YAAU,EAAE,iBAAiB;MAC7BkD,KAAK,EAAExD,MAAA,CAAAwD,KAAK;MACbC,MAAM,EAAC,yCAAyC;MAC/CC,YAAW,EAAE1D,MAAA,CAAA2D,gBAAgB;MAC7BC,eAAc,EAAE5D,MAAA,CAAA6D;;;MAKvBnE,mBAAA,aAAgB,EAChBC,YAAA,CAwEYmE,oBAAA;gBAvED9D,MAAA,CAAA+D,aAAa;iEAAb/D,MAAA,CAAA+D,aAAa,GAAAzD,MAAA;IACrB0D,KAAK,EAAEhE,MAAA,CAAAiE,UAAU;IAClB9B,KAAK,EAAC;;IA+DK+B,MAAM,EAAAjD,QAAA,CACf,MAGM,CAHNC,mBAAA,CAGM,OAHNiD,UAGM,GAFJxE,YAAA,CAAwDe,oBAAA;MAA5CE,OAAK,EAAAE,MAAA,SAAAA,MAAA,OAAAR,MAAA,IAAEN,MAAA,CAAA+D,aAAa;;wBAAU,MAAEjD,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC5CnB,YAAA,CAA4De,oBAAA;MAAjDC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEZ,MAAA,CAAAoE;;wBAAY,MAAEtD,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;sBAhEpD,MA4DU,CA5DVnB,YAAA,CA4DUE,kBAAA;MA3DPE,KAAK,EAAEC,MAAA,CAAAqE,QAAQ;MACfC,KAAK,EAAEtE,MAAA,CAAAuE,aAAa;MACrBC,GAAG,EAAC,aAAa;MACjB,aAAW,EAAC;;wBAEZ,MAEe,CAFf7E,YAAA,CAEeO,uBAAA;QAFDC,KAAK,EAAC,KAAK;QAACkC,IAAI,EAAC;;0BAC7B,MAAwG,CAAxG1C,YAAA,CAAwGS,mBAAA;sBAArFJ,MAAA,CAAAqE,QAAQ,CAAChE,QAAQ;qEAAjBL,MAAA,CAAAqE,QAAQ,CAAChE,QAAQ,GAAAC,MAAA;UAAEC,WAAW,EAAC,QAAQ;UAAEkE,QAAQ,EAAEzE,MAAA,CAAAiE,UAAU;;;UAElFtE,YAAA,CAEeO,uBAAA;QAFDC,KAAK,EAAC,IAAI;QAACkC,IAAI,EAAC;;0BAC5B,MAAiE,CAAjE1C,YAAA,CAAiES,mBAAA;sBAA9CJ,MAAA,CAAAqE,QAAQ,CAACK,IAAI;qEAAb1E,MAAA,CAAAqE,QAAQ,CAACK,IAAI,GAAApE,MAAA;UAAEC,WAAW,EAAC;;;UAE5BP,MAAA,CAAAiE,UAAU,c,cAA9BtC,YAAA,CAEezB,uBAAA;;QAF2BC,KAAK,EAAC,IAAI;QAACkC,IAAI,EAAC;;0BACxD,MAAmG,CAAnG1C,YAAA,CAAmGS,mBAAA;sBAAhFJ,MAAA,CAAAqE,QAAQ,CAACM,QAAQ;qEAAjB3E,MAAA,CAAAqE,QAAQ,CAACM,QAAQ,GAAArE,MAAA;UAAEK,IAAI,EAAC,UAAU;UAACJ,WAAW,EAAC,OAAO;UAAC,eAAa,EAAb;;;+CAE5EZ,YAAA,CAEeO,uBAAA;QAFDC,KAAK,EAAC,KAAK;QAACkC,IAAI,EAAC;;0BAC7B,MAAmE,CAAnE1C,YAAA,CAAmES,mBAAA;sBAAhDJ,MAAA,CAAAqE,QAAQ,CAAC5D,KAAK;qEAAdT,MAAA,CAAAqE,QAAQ,CAAC5D,KAAK,GAAAH,MAAA;UAAEC,WAAW,EAAC;;;UAEjDZ,YAAA,CAEeO,uBAAA;QAFDC,KAAK,EAAC,IAAI;QAACkC,IAAI,EAAC;;0BAC5B,MAAkE,CAAlE1C,YAAA,CAAkES,mBAAA;sBAA/CJ,MAAA,CAAAqE,QAAQ,CAACO,KAAK;qEAAd5E,MAAA,CAAAqE,QAAQ,CAACO,KAAK,GAAAtE,MAAA;UAAEC,WAAW,EAAC;;;UAENP,MAAA,CAAAiE,UAAU,c,cAArDtC,YAAA,CAUezB,uBAAA;;QAVDC,KAAK,EAAC,IAAI;QAACkC,IAAI,EAAC;;0BAC5B,MAQY,CARZ1C,YAAA,CAQYkF,oBAAA;sBARQ7E,MAAA,CAAAqE,QAAQ,CAAC5B,IAAI;qEAAbzC,MAAA,CAAAqE,QAAQ,CAAC5B,IAAI,GAAAnC,MAAA;UAAEC,WAAW,EAAC;;4BAC7C,MAAiD,CAAjDZ,YAAA,CAAiDmF,oBAAA;YAAtC3E,KAAK,EAAC,KAAK;YAAC4E,KAAK,EAAC;cAC7BpF,YAAA,CAAkDmF,oBAAA;YAAvC3E,KAAK,EAAC,IAAI;YAAC4E,KAAK,EAAC;cAC5BpF,YAAA,CAA4DmF,oBAAA;YAAjD3E,KAAK,EAAC,OAAO;YAAC4E,KAAK,EAAC;cAC/BpF,YAAA,CAA6DmF,oBAAA;YAAlD3E,KAAK,EAAC,MAAM;YAAC4E,KAAK,EAAC;cAC9BpF,YAAA,CAA6DmF,oBAAA;YAAlD3E,KAAK,EAAC,OAAO;YAAC4E,KAAK,EAAC;cAC/BpF,YAAA,CAAqEmF,oBAAA;YAA1D3E,KAAK,EAAC,QAAQ;YAAC4E,KAAK,EAAC;cAChCpF,YAAA,CAA+DmF,oBAAA;YAApD3E,KAAK,EAAC,MAAM;YAAC4E,KAAK,EAAC;;;;;+CAIlCrF,mBAAA,qBAAwB,EACJM,MAAA,CAAAqE,QAAQ,CAAC5B,IAAI,kBAAkBzC,MAAA,CAAAiE,UAAU,c,cAA7DtC,YAAA,CAoBezB,uBAAA;;QApB0DC,KAAK,EAAC,MAAM;QAACkC,IAAI,EAAC;;0BACzF,MAiBY,CAjBZ1C,YAAA,CAiBYkF,oBAAA;sBAhBD7E,MAAA,CAAAqE,QAAQ,CAACW,UAAU;uEAAnBhF,MAAA,CAAAqE,QAAQ,CAACW,UAAU,GAAA1E,MAAA;UAC5BC,WAAW,EAAC,UAAU;UACtB0E,UAAU,EAAV,EAAU;UACT9B,OAAO,EAAEnD,MAAA,CAAAkF;;4BAGR,MAA2B,E,kBAD7B1F,mBAAA,CAUYmD,SAAA,QAAAwC,WAAA,CATKnF,MAAA,CAAAoF,WAAW,EAAnBC,IAAI;iCADb1D,YAAA,CAUYmD,oBAAA;cARTlC,GAAG,EAAEyC,IAAI,CAACC,EAAE;cACZnF,KAAK,KAAKkF,IAAI,CAACX,IAAI,MAAMW,IAAI,CAACE,UAAU;cACxCR,KAAK,EAAEM,IAAI,CAACC;;gCAEb,MAGM,CAHNpE,mBAAA,CAGM,OAHNsE,UAGM,GAFJtE,mBAAA,CAA4B,cAAAuE,gBAAA,CAAnBJ,IAAI,CAACX,IAAI,kBAClBxD,mBAAA,CAAuD,QAAvDwE,UAAuD,EAAAD,gBAAA,CAAzBJ,IAAI,CAACE,UAAU,iB;;;;;kFAInDrE,mBAAA,CAAwC;UAAnC5B,KAAK,EAAC;QAAU,GAAC,cAAY,oB;;;+CAGpCI,mBAAA,uBAA0B,EACNM,MAAA,CAAAqE,QAAQ,CAAC5B,IAAI,kB,cAAjCd,YAAA,CAEezB,uBAAA;;QAFkCC,KAAK,EAAC,MAAM;QAACkC,IAAI,EAAC;;0BACjE,MAA6E,CAA7E1C,YAAA,CAA6ES,mBAAA;sBAA1DJ,MAAA,CAAAqE,QAAQ,CAACW,UAAU;uEAAnBhF,MAAA,CAAAqE,QAAQ,CAACW,UAAU,GAAA1E,MAAA;UAAEC,WAAW,EAAC;;;;;;;8CAW1Db,mBAAA,gBAAmB,EACnBC,YAAA,CA4EYmE,oBAAA;gBA3ED1C,IAAA,CAAAuE,mBAAmB;iEAAnBvE,IAAA,CAAAuE,mBAAmB,GAAArF,MAAA;IAC5B0D,KAAK,EAAC,QAAQ;IACd7B,KAAK,EAAC,KAAK;IACX,kBAAgB,EAAhB;;IAkEW+B,MAAM,EAAAjD,QAAA,CACf,MAGO,CAHPC,mBAAA,CAGO,QAHP0E,WAGO,GAFLjG,YAAA,CAA8De,oBAAA;MAAlDE,OAAK,EAAAE,MAAA,SAAAA,MAAA,OAAAR,MAAA,IAAEc,IAAA,CAAAuE,mBAAmB;;wBAAU,MAAE7E,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAClDnB,YAAA,CAAqFe,oBAAA;MAA1EC,IAAI,EAAC,SAAS;MAAEC,OAAK,EAAEQ,IAAA,CAAAyE,YAAY;MAAG1C,OAAO,EAAE/B,IAAA,CAAA0E;;wBAAW,MAAIhF,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;;sBAnE7E,MA8DM,CA9DNI,mBAAA,CA8DM,OA9DN6E,UA8DM,GA7DJpG,YAAA,CAgBWqG,mBAAA;MAfThC,KAAK,EAAC,MAAM;MACZrD,IAAI,EAAC,MAAM;MACVsF,QAAQ,EAAE,KAAK;MAChB1G,KAA2B,EAA3B;QAAA;MAAA;;MAEW+C,OAAO,EAAArB,QAAA,CAChB,MAOMH,MAAA,SAAAA,MAAA,QAPNI,mBAAA,CAOM,cANJA,mBAAA,CAAkC,WAA/B,6BAA2B,GAC9BA,mBAAA,CAA2B,WAAxB,sBAAoB,GACvBA,mBAAA,CAAiC,WAA9B,gCAA0B,GAC7BA,mBAAA,CAAoC,WAAjC,+BAA6B,GAChCA,mBAAA,CAAoC,WAAjC,+BAA6B,GAChCA,mBAAA,CAAoC,WAAjC,+BAA6B,E;;QAKtCvB,YAAA,CAqBYuG,oBAAA;MApBV1B,GAAG,EAAC,WAAW;MACflF,KAAK,EAAC,aAAa;MAClB6G,MAAM,KAAK/E,IAAA,CAAAgF,OAAO;MAClBC,OAAO,EAAEjF,IAAA,CAAAkF,aAAa;MACtB,eAAa,EAAElF,IAAA,CAAAmF,iBAAiB;MAChC,YAAU,EAAEnF,IAAA,CAAAoF,mBAAmB;MAC/B,UAAQ,EAAEpF,IAAA,CAAAqF,iBAAiB;MAC3B,WAAS,EAAErF,IAAA,CAAAsF,gBAAgB;MAC3B,WAAS,EAAEtF,IAAA,CAAAuF,QAAQ;MACnB,aAAW,EAAE,KAAK;MACnBC,MAAM,EAAC,YAAY;MAClBC,KAAK,EAAE,CAAC;MACTnC,IAAI,EAAC;;MAGMoC,GAAG,EAAA7F,QAAA,CACZ,MAEMH,MAAA,SAAAA,MAAA,QAFNI,mBAAA,CAEM;QAFD5B,KAAK,EAAC;MAAgB,GAAC,2BAE5B,mB;wBAJF,MAA+C,CAA/CK,YAAA,CAA+Ce,oBAAA;QAApCC,IAAI,EAAC;MAAS;0BAAC,MAASG,MAAA,SAAAA,MAAA,Q,iBAAT,WAAS,E;;;;;mHAQrCpB,mBAAA,YAAe,EACJ0B,IAAA,CAAA2F,YAAY,I,cAAvBvH,mBAAA,CAkBM,OAlBNwH,UAkBM,GAjBJrH,YAAA,CAIEqG,mBAAA;MAHChC,KAAK,aAAa5C,IAAA,CAAA2F,YAAY,CAACE,OAAO,SAAS7F,IAAA,CAAA2F,YAAY,CAACG,MAAM;MAClEvG,IAAI,EAAES,IAAA,CAAA2F,YAAY,CAACG,MAAM;MACzBjB,QAAQ,EAAE;gDAGbvG,mBAAA,YAAe,EACJ0B,IAAA,CAAA2F,YAAY,CAACI,aAAa,IAAI/F,IAAA,CAAA2F,YAAY,CAACI,aAAa,CAACC,MAAM,Q,cAA1E5H,mBAAA,CASM,OATN6H,UASM,GARJ1H,YAAA,CAOc2H,sBAAA;wBANZ,MAKmB,CALnB3H,YAAA,CAKmB4H,2BAAA;QALDvD,KAAK,EAAC,QAAQ;QAACU,IAAI,EAAC;;0BACpC,MAGW,CAHX/E,YAAA,CAGWiC,mBAAA;UAHAC,IAAI,EAAET,IAAA,CAAA2F,YAAY,CAACI,aAAa;UAAElF,MAAM,EAAN,EAAM;UAACe,IAAI,EAAC;;4BACvD,MAAoD,CAApDrD,YAAA,CAAoDuC,0BAAA;YAAnCG,IAAI,EAAC,KAAK;YAAClC,KAAK,EAAC,IAAI;YAACgC,KAAK,EAAC;cAC7CxC,YAAA,CAA6CuC,0BAAA;YAA5BG,IAAI,EAAC,OAAO;YAAClC,KAAK,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}