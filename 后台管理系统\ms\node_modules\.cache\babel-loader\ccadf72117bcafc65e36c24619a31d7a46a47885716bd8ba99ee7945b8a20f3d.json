{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\nimport { ref, reactive, onMounted, computed } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { UploadFilled } from '@element-plus/icons-vue';\nimport { useStore } from 'vuex';\nimport { useRouter } from 'vue-router';\nimport examService from '@/services/examService';\nimport { formatDate, formatRelativeTime } from '@/utils/dateFormat';\nexport default {\n  __name: 'ExamList',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const store = useStore();\n    const router = useRouter();\n    const loading = ref(true);\n    const currentPage = ref(1);\n    const pageSize = ref(10);\n    const total = ref(0);\n    const dialogVisible = ref(false);\n    const dialogTitle = ref('新增考试');\n    const examFormRef = ref(null);\n    const currentLoadingExam = ref(null); // 当前正在加载的考试ID\n    let userInfo = localStorage.getItem(\"userInfo\") ? JSON.parse(localStorage.getItem(\"userInfo\")) : '';\n    // 用户角色\n    const isAdmin = userInfo?.role == 'admin';\n    const isTeacher = userInfo?.role == 'teacher';\n    const isStudent = computed(() => store.getters.isStudent);\n    const canManageExams = userInfo?.role == 'admin';\n\n    // 试题管理相关\n    const questionDialogVisible = ref(false);\n    const questionEditDialogVisible = ref(false);\n    const questionDialogTitle = ref('新增试题');\n    const questionFormRef = ref(null);\n    const currentExam = ref(null);\n    const questionList = ref([]);\n    const questionLoading = ref(false);\n    const questionCurrentPage = ref(1);\n    const questionTotal = ref(0);\n\n    // 导入试题相关\n    const importDialogVisible = ref(false);\n    const fileList = ref([]);\n    const uploadLoading = ref(false);\n\n    // 成绩查看相关\n    const resultsDialogVisible = ref(false);\n    const resultsList = ref([]);\n    const resultsLoading = ref(false);\n    const examStatistics = reactive({\n      total_students: 0,\n      passed_students: 0,\n      pass_rate: 0,\n      average_score: 0\n    });\n\n    // Add remaining attempts tracking\n    const remainingAttempts = ref({});\n    const attemptedExams = ref({}); // 记录已参加过的考试\n\n    // 检查学生是否参加过某个考试\n    const hasAttemptedExam = examId => {\n      if (!examId) return false;\n      return attemptedExams.value[examId] === true;\n    };\n\n    // 过滤条件\n    const filterForm = reactive({\n      title: ''\n    });\n\n    // 考试表单\n    const examForm = reactive({\n      id: null,\n      title: '',\n      description: '',\n      duration: 60,\n      pass_score: 60,\n      total_score: 100\n    });\n\n    // 验证规则\n    const examRules = {\n      title: [{\n        required: true,\n        message: '请输入考试标题',\n        trigger: 'blur'\n      }],\n      duration: [{\n        required: true,\n        message: '请输入考试时长',\n        trigger: 'blur'\n      }, {\n        type: 'number',\n        min: 1,\n        message: '考试时长必须大于0',\n        trigger: 'blur'\n      }],\n      pass_score: [{\n        required: true,\n        message: '请输入及格分数',\n        trigger: 'blur'\n      }, {\n        type: 'number',\n        min: 1,\n        message: '及格分数必须大于0',\n        trigger: 'blur'\n      }],\n      total_score: [{\n        required: true,\n        message: '请输入总分',\n        trigger: 'blur'\n      }, {\n        type: 'number',\n        min: 1,\n        message: '总分必须大于0',\n        trigger: 'blur'\n      }]\n    };\n\n    // 试题表单\n    const questionForm = reactive({\n      id: null,\n      question: '',\n      question_type: '单选题',\n      correct_answer: '',\n      score: 5,\n      options: [{\n        text: '',\n        isCorrect: false\n      }, {\n        text: '',\n        isCorrect: false\n      }, {\n        text: '',\n        isCorrect: false\n      }, {\n        text: '',\n        isCorrect: false\n      }]\n    });\n\n    // 试题验证规则\n    const questionRules = {\n      question: [{\n        required: true,\n        message: '请输入题目内容',\n        trigger: 'blur'\n      }],\n      question_type: [{\n        required: true,\n        message: '请选择题目类型',\n        trigger: 'change'\n      }],\n      score: [{\n        required: true,\n        message: '请输入分值',\n        trigger: 'blur'\n      }, {\n        type: 'number',\n        min: 1,\n        message: '分值必须大于0',\n        trigger: 'blur'\n      }]\n    };\n\n    // 模拟考试数据\n    const examList = ref([]);\n\n    // 模拟试题数据\n    const mockQuestions = [{\n      id: 1,\n      question: '公司的上班时间是几点到几点？',\n      options: [{\n        text: 'A. 8:30-17:30',\n        isCorrect: true\n      }, {\n        text: 'B. 9:00-18:00',\n        isCorrect: false\n      }, {\n        text: 'C. 9:30-18:30',\n        isCorrect: false\n      }, {\n        text: 'D. 8:00-17:00',\n        isCorrect: false\n      }],\n      correct_answer: 'A',\n      question_type: '单选题',\n      score: 5,\n      exam_id: 1\n    }, {\n      id: 2,\n      question: '公司允许员工在工作时间做以下哪些事情？（多选）',\n      options: [{\n        text: 'A. 喝水',\n        isCorrect: true\n      }, {\n        text: 'B. 短暂休息',\n        isCorrect: true\n      }, {\n        text: 'C. 玩游戏',\n        isCorrect: false\n      }, {\n        text: 'D. 睡觉',\n        isCorrect: false\n      }],\n      correct_answer: 'AB',\n      question_type: '多选题',\n      score: 10,\n      exam_id: 1\n    }, {\n      id: 3,\n      question: '公司规定必须遵守考勤制度。',\n      options: [],\n      correct_answer: '正确',\n      question_type: '判断题',\n      score: 5,\n      exam_id: 1\n    }, {\n      id: 4,\n      question: '请简述公司的请假流程。',\n      options: [],\n      correct_answer: '1. 提前向部门负责人口头说明\\n2. 在OA系统填写请假申请\\n3. 等待审批通过\\n4. 销假时提交相关证明',\n      question_type: '简答题',\n      score: 15,\n      exam_id: 1\n    }];\n\n    // 模拟成绩数据\n    const mockResults = [{\n      id: 1,\n      student_id: 1,\n      student_name: '张三',\n      exam_id: 1,\n      score: 85,\n      exam_date: '2023-01-20 10:30:00'\n    }, {\n      id: 2,\n      student_id: 2,\n      student_name: '李四',\n      exam_id: 1,\n      score: 75,\n      exam_date: '2023-01-20 10:45:00'\n    }, {\n      id: 3,\n      student_id: 3,\n      student_name: '王五',\n      exam_id: 1,\n      score: 55,\n      exam_date: '2023-01-20 11:00:00'\n    }, {\n      id: 4,\n      student_id: 4,\n      student_name: '赵六',\n      exam_id: 1,\n      score: 92,\n      exam_date: '2023-01-20 10:15:00'\n    }];\n\n    // 设置总数\n    total.value = examList.length;\n\n    // 获取所有考试\n    const fetchExams = async () => {\n      loading.value = true;\n      try {\n        const response = await examService.getExams({\n          page: currentPage.value,\n          limit: pageSize.value,\n          title: filterForm.title\n        });\n        examList.value = response.data.data;\n        total.value = response.data.total || examList.value.length;\n\n        // If student, check attempts for each exam\n        if (isStudent.value) {\n          await checkRemainingAttempts();\n        }\n        loading.value = false;\n      } catch (error) {\n        console.error('获取考试列表失败', error);\n        ElMessage.error('获取考试列表失败');\n        loading.value = false;\n      }\n    };\n\n    // Check remaining attempts for each exam\n    const checkRemainingAttempts = async () => {\n      const studentId = localStorage.getItem('studentId');\n      if (!studentId) {\n        console.error('未找到有效的学生ID');\n        ElMessage.warning('未找到有效的学生ID，请重新登录后再试');\n        return;\n      }\n      try {\n        console.log('正在获取所有考试的尝试次数...学生ID:', studentId);\n        // 创建一个Promise数组，同时请求所有考试的尝试次数\n        const attemptPromises = examList.value.map(async exam => {\n          try {\n            console.log(`正在查询考试 ${exam.id} (${exam.title}) 的尝试记录...`);\n            const response = await examService.getExamResults(exam.id, {\n              student_id: studentId\n            });\n            console.log(`考试 ${exam.id} 返回数据:`, response.data);\n\n            // 确保我们有结果数据\n            if (response.data && response.data.data) {\n              const results = response.data.data.results || [];\n              const attempts = results.length;\n              const remaining = Math.max(0, 2 - attempts);\n              console.log(`考试 ${exam.id} (${exam.title}): 已尝试 ${attempts} 次，剩余 ${remaining} 次`);\n              remainingAttempts.value[exam.id] = remaining;\n              attemptedExams.value[exam.id] = attempts > 0; // 只有实际参加过才标记为true\n\n              // 记录每次尝试的详情，便于调试\n              if (results.length > 0) {\n                results.forEach((result, index) => {\n                  console.log(`  尝试 ${index + 1}: 得分 ${result.score}，时间 ${result.exam_date}`);\n                });\n              }\n            } else {\n              console.warn(`考试 ${exam.id} 未返回有效数据，设置默认剩余次数为2`);\n              remainingAttempts.value[exam.id] = 2;\n              attemptedExams.value[exam.id] = false; // 没有数据，标记为未参加\n            }\n          } catch (err) {\n            console.error(`获取考试 ${exam.id} 尝试次数失败:`, err);\n            remainingAttempts.value[exam.id] = 2;\n            attemptedExams.value[exam.id] = false; // 出错，标记为未参加\n          }\n        });\n\n        // 等待所有请求完成\n        await Promise.all(attemptPromises);\n        console.log('所有考试尝试次数获取完成:', remainingAttempts.value);\n        console.log('已参加过的考试:', attemptedExams.value);\n      } catch (error) {\n        console.error('获取考试尝试次数失败:', error);\n        // 设置默认值\n        examList.value.forEach(exam => {\n          remainingAttempts.value[exam.id] = 2;\n          attemptedExams.value[exam.id] = false; // 出错，标记为未参加\n        });\n      }\n    };\n\n    // 参加考试\n    const handleTakeExam = row => {\n      // 设置当前正在加载的考试ID\n      currentLoadingExam.value = row.id;\n\n      // 确保已经加载了尝试次数\n      if (!remainingAttempts.value[row.id] && remainingAttempts.value[row.id] !== 0) {\n        // 如果还没加载尝试次数信息，先加载\n        const teacherId = localStorage.getItem('teacherId');\n        if (!teacherId) {\n          ElMessage.warning('未找到有效的教师ID，请重新登录');\n          currentLoadingExam.value = null;\n          return;\n        }\n        ElMessage.info('正在获取考试尝试信息，请稍候...');\n\n        // 立即获取该考试的尝试次数\n        examService.getExamResults(row.id, {\n          teacher_id: teacherId\n        }).then(response => {\n          console.log(`考试 ${row.id} 尝试信息:`, response.data);\n          if (response.data && response.data.data) {\n            const results = response.data.data.results || [];\n            const attempts = results.length;\n            const remaining = Math.max(0, 2 - attempts);\n            console.log(`考试 ${row.id} (${row.title}): 已尝试 ${attempts} 次，剩余 ${remaining} 次`);\n            remainingAttempts.value[row.id] = remaining;\n            attemptedExams.value[row.id] = attempts > 0;\n\n            // 获取到尝试次数后，继续参加考试逻辑\n            currentLoadingExam.value = null;\n            continueToTakeExam(row);\n          } else {\n            console.warn(`考试 ${row.id} 未返回有效数据，设置默认剩余次数为2`);\n            remainingAttempts.value[row.id] = 2;\n            attemptedExams.value[row.id] = false;\n            currentLoadingExam.value = null;\n            continueToTakeExam(row);\n          }\n        }).catch(error => {\n          console.error('获取考试尝试次数失败', error);\n          ElMessage.error('获取考试尝试次数失败，请刷新页面重试');\n          currentLoadingExam.value = null;\n        });\n        return;\n      }\n      currentLoadingExam.value = null;\n      continueToTakeExam(row);\n    };\n\n    // 继续参加考试流程\n    const continueToTakeExam = row => {\n      // Check if student still has attempts left\n      if (remainingAttempts.value[row.id] === 0) {\n        ElMessage.warning('您已达到该考试的最大尝试次数（2次）');\n        return;\n      }\n\n      // Confirm before taking the exam\n      ElMessageBox.confirm(`您总共有2次尝试机会，已使用 ${2 - remainingAttempts.value[row.id]} 次，还剩 ${remainingAttempts.value[row.id]} 次机会。确定要参加考试吗？`, '参加考试', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 记录考试ID和剩余次数到localStorage，以便考试页面使用\n        localStorage.setItem('currentExamId', row.id);\n        localStorage.setItem('currentExamTitle', row.title);\n        localStorage.setItem('currentExamRemainingAttempts', remainingAttempts.value[row.id]);\n\n        // 跳转到考试页面\n        router.push(`/exams/take/${row.id}`);\n      }).catch(() => {\n        console.log('用户取消参加考试');\n      });\n    };\n\n    // 查看我的成绩\n    const handleViewMyResult = async row => {\n      try {\n        const studentId = localStorage.getItem('studentId');\n        if (!studentId) {\n          ElMessage.error('未找到有效的学生ID，请重新登录');\n          return;\n        }\n        console.log('查询成绩 - 学生ID:', studentId, '考试ID:', row.id);\n        const response = await examService.getExamResults(row.id, {\n          student_id: studentId\n        });\n        console.log('查询成绩 - 返回数据:', response.data);\n\n        // 检查是否有考试记录\n        if (response.data.data.results && response.data.data.results.length > 0) {\n          // Get all attempts sorted by score (highest first)\n          const myResults = response.data.data.results.filter(r => r.student_id === parseInt(studentId)).sort((a, b) => b.score - a.score);\n          console.log('查询成绩 - 过滤后的结果:', myResults);\n          if (myResults.length > 0) {\n            // Format results to show all attempts\n            const attemptsList = myResults.map((result, index) => {\n              return `<div class=\"result-item ${result.score >= row.pass_score ? 'pass' : 'fail'}\">\n            <h4>尝试 ${index + 1}</h4>\n            <p><strong>得分:</strong> ${result.score} / ${row.total_score}</p>\n            <p><strong>考试时间:</strong> ${formatDate(result.exam_date, 'YYYY-MM-DD HH:mm:ss')}</p>\n            <p><strong>状态:</strong> ${result.score >= row.pass_score ? '通过' : '未通过'}</p>\n          </div>`;\n            }).join('<hr>');\n            ElMessageBox.alert(`<div class=\"results-container\">\n            <h3>您的考试成绩</h3>\n            <div class=\"results-list\">${attemptsList}</div>\n            <div class=\"attempts-info\">\n              <p>总尝试次数: ${myResults.length}/2</p>\n              <p>剩余次数: ${Math.max(0, 2 - myResults.length)}</p>\n              <p class=\"view-all-results\"><a href=\"#/exams/my-results\">查看我的所有考试成绩 &raquo;</a></p>\n            </div>\n          </div>`, '考试成绩', {\n              dangerouslyUseHTMLString: true,\n              confirmButtonText: '确定',\n              customClass: 'result-dialog'\n            });\n            return;\n          }\n        }\n        ElMessage.info('您暂无该考试的成绩记录');\n      } catch (error) {\n        console.error('获取成绩失败', error);\n        ElMessage.error('获取成绩失败: ' + (error.response?.data?.message || error.message));\n      }\n    };\n    onMounted(() => {\n      fetchExams();\n    });\n\n    // 重置过滤条件\n    const resetFilter = () => {\n      filterForm.title = '';\n      handleSearch();\n    };\n\n    // 搜索\n    const handleSearch = () => {\n      currentPage.value = 1;\n      fetchExams();\n    };\n\n    // 新增考试\n    const handleAddExam = () => {\n      dialogTitle.value = '新增考试';\n      dialogVisible.value = true;\n      // 重置表单\n      examForm.id = null;\n      examForm.title = '';\n      examForm.description = '';\n      examForm.duration = 60;\n      examForm.pass_score = 60;\n      examForm.total_score = 100;\n    };\n\n    // 编辑考试\n    const handleEdit = row => {\n      dialogTitle.value = '编辑考试';\n      dialogVisible.value = true;\n      // 填充表单数据\n      Object.keys(examForm).forEach(key => {\n        examForm[key] = row[key];\n      });\n    };\n\n    // 删除考试\n    const handleDelete = row => {\n      ElMessageBox.confirm(`确定要删除考试 ${row.title} 吗?`, '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async () => {\n        try {\n          await examService.deleteExam(row.id);\n          ElMessage.success(`考试 ${row.title} 已删除`);\n          fetchExams(); // 重新加载列表\n        } catch (error) {\n          console.error('删除考试失败', error);\n          ElMessage.error('删除考试失败，请重试');\n        }\n      }).catch(() => {});\n    };\n\n    // 提交表单\n    const submitForm = async () => {\n      if (!examFormRef.value) return;\n      await examFormRef.value.validate(async valid => {\n        if (valid) {\n          try {\n            if (examForm.id) {\n              // 编辑模式\n              await examService.updateExam(examForm.id, examForm);\n              ElMessage.success(`考试 ${examForm.title} 信息已更新`);\n            } else {\n              // 新增模式\n              await examService.createExam(examForm);\n              ElMessage.success(`考试 ${examForm.title} 添加成功`);\n            }\n            dialogVisible.value = false;\n            fetchExams(); // 重新加载列表\n          } catch (error) {\n            console.error('保存考试失败', error);\n            ElMessage.error('保存考试失败，请重试');\n          }\n        } else {\n          return false;\n        }\n      });\n    };\n\n    // 试题管理\n    const handleManageQuestions = row => {\n      currentExam.value = row;\n      questionDialogVisible.value = true;\n      // 设置为第一页（即使我们不再使用分页）\n      questionCurrentPage.value = 1;\n\n      // 加载试题数据\n      loadQuestions(row.id);\n\n      // 确保对话框完全打开后调整表格高度\n      setTimeout(() => {\n        const questionTable = document.querySelector('.question-table-wrapper .el-table');\n        if (questionTable) {\n          questionTable.style.height = '550px';\n        }\n      }, 100);\n    };\n\n    // 加载试题数据\n    const loadQuestions = async examId => {\n      questionLoading.value = true;\n      try {\n        // 不使用分页参数，一次性获取所有试题\n        const response = await examService.getExamQuestions(examId);\n\n        // 检查返回数据结构\n        console.log('加载试题数据返回:', response.data);\n\n        // 确保获取完整的数据列表\n        if (response.data && response.data.data) {\n          questionList.value = response.data.data;\n          // 依然保存总数，用于展示\n          questionTotal.value = response.data.count || questionList.value.length;\n          console.log(`加载了 ${questionList.value.length} 道试题`);\n        } else {\n          questionList.value = [];\n          questionTotal.value = 0;\n          console.warn('未找到试题数据');\n        }\n        questionLoading.value = false;\n      } catch (error) {\n        console.error('获取试题失败', error);\n        ElMessage.error('获取试题失败');\n        questionLoading.value = false;\n      }\n    };\n\n    // 新增试题\n    const handleAddQuestion = () => {\n      questionDialogTitle.value = '新增试题';\n      questionEditDialogVisible.value = true;\n      // 重置表单\n      questionForm.id = null;\n      questionForm.question = '';\n      questionForm.question_type = '单选题';\n      questionForm.correct_answer = '';\n      questionForm.score = 5;\n      questionForm.options = [{\n        text: '',\n        isCorrect: false\n      }, {\n        text: '',\n        isCorrect: false\n      }, {\n        text: '',\n        isCorrect: false\n      }, {\n        text: '',\n        isCorrect: false\n      }];\n    };\n\n    // 编辑试题\n    const handleEditQuestion = row => {\n      questionDialogTitle.value = '编辑试题';\n      questionEditDialogVisible.value = true;\n\n      // 填充表单数据\n      questionForm.id = row.id;\n      questionForm.question = row.question;\n      questionForm.question_type = row.question_type;\n      questionForm.correct_answer = row.correct_answer;\n      questionForm.score = row.score;\n\n      // 根据题型处理选项\n      if (row.question_type === '单选题' || row.question_type === '多选题') {\n        questionForm.options = row.options ? [...row.options] : [];\n      } else if (row.question_type === '判断题') {\n        questionForm.correct_answer = row.correct_answer;\n      }\n    };\n\n    // 删除试题\n    const handleDeleteQuestion = row => {\n      ElMessageBox.confirm(`确定要删除该试题吗?`, '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async () => {\n        try {\n          await examService.deleteQuestion(row.id);\n          ElMessage.success('试题已删除');\n          // 重新加载试题列表\n          loadQuestions(currentExam.value.id);\n        } catch (error) {\n          console.error('删除试题失败', error);\n          ElMessage.error('删除试题失败，请重试');\n        }\n      }).catch(() => {});\n    };\n\n    // 批量导入试题\n    const handleImportQuestions = () => {\n      importDialogVisible.value = true;\n      fileList.value = [];\n    };\n\n    // 文件上传前检查\n    const beforeUpload = file => {\n      const isWordDoc = file.type === 'application/msword' || file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';\n      if (!isWordDoc) {\n        ElMessage.error('请上传Word格式的文件!');\n        return false;\n      }\n      const isLt10M = file.size / 1024 / 1024 < 10;\n      if (!isLt10M) {\n        ElMessage.error('文件大小不能超过10MB!');\n        return false;\n      }\n      return true;\n    };\n\n    // 处理文件选择\n    const handleFileChange = (file, uploadFileList) => {\n      // 记录最新选择的文件\n      console.log('文件选择变化:', file, uploadFileList);\n      fileList.value = uploadFileList;\n    };\n\n    // 处理文件上传\n    const handleFileUpload = options => {\n      const {\n        file\n      } = options;\n      // 这个函数只在auto-upload=true时调用\n      // 我们设置为false，所以这里只需返回false\n      return false; // 阻止默认上传行为，使用我们自己的提交方式\n    };\n\n    // 提交上传\n    const submitUpload = async () => {\n      console.log('当前文件列表:', fileList.value);\n      if (!fileList.value || fileList.value.length === 0) {\n        ElMessage.warning('请选择要上传的文件');\n        return;\n      }\n      uploadLoading.value = true;\n      try {\n        // 创建FormData对象\n        const formData = new FormData();\n        // 获取原始文件对象\n        const fileObject = fileList.value[0];\n        const rawFile = fileObject.raw || fileObject;\n        console.log('上传文件:', rawFile);\n\n        // 使用field name 'file' 匹配后端控制器\n        formData.append('template', rawFile);\n\n        // 调用实际API\n        const response = await examService.importQuestionsFromWord(currentExam.value.id, formData);\n\n        // 处理成功响应\n        ElMessage.success(`试题导入成功，共导入${response.data.count || 0}道题目`);\n        importDialogVisible.value = false;\n\n        // 重新加载试题列表\n        loadQuestions(currentExam.value.id);\n      } catch (error) {\n        console.error('导入试题失败', error);\n        let errorMsg = '导入试题失败';\n\n        // 获取详细错误信息\n        if (error.response && error.response.data && error.response.data.message) {\n          errorMsg += `：${error.response.data.message}`;\n        }\n        ElMessage.error(errorMsg);\n      } finally {\n        uploadLoading.value = false;\n      }\n    };\n\n    // 下载模板\n    const downloadTemplate = async () => {\n      try {\n        ElMessage.success('模板下载中...');\n\n        // 创建下载链接\n        const link = document.createElement('a');\n        // 设置下载链接为后端API地址\n        link.href = `${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/api/exams/template/download`;\n\n        // 添加token到URL，以便通过授权\n        const token = localStorage.getItem('token');\n        if (token) {\n          link.href += `?token=${token}`;\n        }\n        link.download = '试题导入模板.docx';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n      } catch (error) {\n        console.error('下载模板失败', error);\n        ElMessage.error('下载模板失败，请重试');\n      }\n    };\n\n    // 添加选项\n    const addOption = () => {\n      questionForm.options.push({\n        text: '',\n        isCorrect: false\n      });\n    };\n\n    // 移除选项\n    const removeOption = index => {\n      if (questionForm.options.length <= 2) {\n        ElMessage.warning('至少需要2个选项');\n        return;\n      }\n      questionForm.options.splice(index, 1);\n    };\n\n    // 提交试题表单\n    const submitQuestionForm = async () => {\n      if (!questionFormRef.value) return;\n      await questionFormRef.value.validate(async valid => {\n        if (valid) {\n          // 选择题验证选项\n          if (questionForm.question_type === '单选题' || questionForm.question_type === '多选题') {\n            // 验证选项内容\n            const emptyOption = questionForm.options.find(opt => !opt.text.trim());\n            if (emptyOption) {\n              ElMessage.error('选项内容不能为空');\n              return;\n            }\n\n            // 验证是否选择了正确答案\n            const hasCorrect = questionForm.options.some(opt => opt.isCorrect);\n            if (!hasCorrect) {\n              ElMessage.error('请至少选择一个正确答案');\n              return;\n            }\n\n            // 单选题只能有一个正确答案\n            if (questionForm.question_type === '单选题') {\n              const correctCount = questionForm.options.filter(opt => opt.isCorrect).length;\n              if (correctCount > 1) {\n                ElMessage.error('单选题只能有一个正确答案');\n                return;\n              }\n            }\n          }\n          try {\n            // 准备提交的数据\n            const submitData = {\n              ...questionForm\n            };\n\n            // 处理正确答案\n            if (questionForm.question_type === '单选题') {\n              const correctOption = questionForm.options.find(opt => opt.isCorrect);\n              if (correctOption) {\n                // 获取正确选项的索引，转换为A、B、C...\n                const index = questionForm.options.findIndex(opt => opt.isCorrect);\n                submitData.correct_answer = String.fromCharCode(65 + index); // A, B, C...\n              }\n            } else if (questionForm.question_type === '多选题') {\n              // 将所有正确选项索引转换为字符串，如\"ABC\"\n              const correctAnswers = questionForm.options.map((opt, index) => opt.isCorrect ? String.fromCharCode(65 + index) : null).filter(Boolean).join('');\n              submitData.correct_answer = correctAnswers;\n            }\n\n            // 提交表单\n            if (questionForm.id) {\n              // 编辑模式\n              await examService.updateQuestion(questionForm.id, submitData);\n              ElMessage.success('试题更新成功');\n            } else {\n              // 新增模式\n              await examService.createQuestion(currentExam.value.id, submitData);\n              ElMessage.success('试题添加成功');\n            }\n            questionEditDialogVisible.value = false;\n            // 重新加载试题列表\n            loadQuestions(currentExam.value.id);\n          } catch (error) {\n            console.error('保存试题失败', error);\n            ElMessage.error('保存试题失败，请重试');\n          }\n        } else {\n          return false;\n        }\n      });\n    };\n\n    // 格式化试题内容\n    const formatQuestionContent = content => {\n      if (!content) return '';\n      // 最多显示100个字符\n      return content.length > 100 ? content.substring(0, 100) + '...' : content;\n    };\n\n    // 试题不再使用分页\n\n    // 查看考试成绩\n    const handleViewResults = async row => {\n      currentExam.value = row;\n      resultsDialogVisible.value = true;\n      resultsLoading.value = true;\n      try {\n        const response = await examService.getExamResults(row.id);\n        const data = response.data.data;\n        resultsList.value = data.results || [];\n\n        // 设置统计数据\n        examStatistics.total_students = data.summary.total_students || 0;\n        examStatistics.passed_students = data.summary.passed_students || 0;\n        examStatistics.pass_rate = data.summary.pass_rate || 0;\n        examStatistics.average_score = data.summary.average_score || 0;\n        resultsLoading.value = false;\n      } catch (error) {\n        console.error('获取成绩列表失败', error);\n        ElMessage.error('获取成绩列表失败');\n        resultsLoading.value = false;\n      }\n    };\n\n    // 查看考试详情\n    const handleViewDetail = row => {\n      ElMessage.success(`查看 ${row.student_name} 的考试详情`);\n    };\n\n    // 分页处理\n    const handleSizeChange = size => {\n      pageSize.value = size;\n      currentPage.value = 1;\n      fetchExams();\n    };\n    const handleCurrentChange = page => {\n      currentPage.value = page;\n      fetchExams();\n    };\n    const __returned__ = {\n      store,\n      router,\n      loading,\n      currentPage,\n      pageSize,\n      total,\n      dialogVisible,\n      dialogTitle,\n      examFormRef,\n      currentLoadingExam,\n      get userInfo() {\n        return userInfo;\n      },\n      set userInfo(v) {\n        userInfo = v;\n      },\n      isAdmin,\n      isTeacher,\n      isStudent,\n      canManageExams,\n      questionDialogVisible,\n      questionEditDialogVisible,\n      questionDialogTitle,\n      questionFormRef,\n      currentExam,\n      questionList,\n      questionLoading,\n      questionCurrentPage,\n      questionTotal,\n      importDialogVisible,\n      fileList,\n      uploadLoading,\n      resultsDialogVisible,\n      resultsList,\n      resultsLoading,\n      examStatistics,\n      remainingAttempts,\n      attemptedExams,\n      hasAttemptedExam,\n      filterForm,\n      examForm,\n      examRules,\n      questionForm,\n      questionRules,\n      examList,\n      mockQuestions,\n      mockResults,\n      fetchExams,\n      checkRemainingAttempts,\n      handleTakeExam,\n      continueToTakeExam,\n      handleViewMyResult,\n      resetFilter,\n      handleSearch,\n      handleAddExam,\n      handleEdit,\n      handleDelete,\n      submitForm,\n      handleManageQuestions,\n      loadQuestions,\n      handleAddQuestion,\n      handleEditQuestion,\n      handleDeleteQuestion,\n      handleImportQuestions,\n      beforeUpload,\n      handleFileChange,\n      handleFileUpload,\n      submitUpload,\n      downloadTemplate,\n      addOption,\n      removeOption,\n      submitQuestionForm,\n      formatQuestionContent,\n      handleViewResults,\n      handleViewDetail,\n      handleSizeChange,\n      handleCurrentChange,\n      ref,\n      reactive,\n      onMounted,\n      computed,\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      },\n      get UploadFilled() {\n        return UploadFilled;\n      },\n      get useStore() {\n        return useStore;\n      },\n      get useRouter() {\n        return useRouter;\n      },\n      get examService() {\n        return examService;\n      },\n      get formatDate() {\n        return formatDate;\n      },\n      get formatRelativeTime() {\n        return formatRelativeTime;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "computed", "ElMessage", "ElMessageBox", "UploadFilled", "useStore", "useRouter", "examService", "formatDate", "formatRelativeTime", "store", "router", "loading", "currentPage", "pageSize", "total", "dialogVisible", "dialogTitle", "examFormRef", "currentLoadingExam", "userInfo", "localStorage", "getItem", "JSON", "parse", "isAdmin", "role", "<PERSON><PERSON><PERSON>er", "isStudent", "getters", "canManageExams", "questionDialogVisible", "questionEditDialogVisible", "questionDialogTitle", "questionFormRef", "currentExam", "questionList", "questionLoading", "questionCurrentPage", "questionTotal", "importDialogVisible", "fileList", "uploadLoading", "resultsDialogVisible", "resultsList", "resultsLoading", "examStatistics", "total_students", "passed_students", "pass_rate", "average_score", "remainingAttempts", "attemptedExams", "hasAttemptedExam", "examId", "value", "filterForm", "title", "examForm", "id", "description", "duration", "pass_score", "total_score", "examRules", "required", "message", "trigger", "type", "min", "questionForm", "question", "question_type", "correct_answer", "score", "options", "text", "isCorrect", "questionRules", "examList", "mockQuestions", "exam_id", "mockResults", "student_id", "student_name", "exam_date", "length", "fetchExams", "response", "getExams", "page", "limit", "data", "checkRemainingAttempts", "error", "console", "studentId", "warning", "log", "attemptPromises", "map", "exam", "getExamResults", "results", "attempts", "remaining", "Math", "max", "for<PERSON>ach", "result", "index", "warn", "err", "Promise", "all", "handleTakeExam", "row", "teacherId", "info", "teacher_id", "then", "continueToTakeExam", "catch", "confirm", "confirmButtonText", "cancelButtonText", "setItem", "push", "handleViewMyResult", "myResults", "filter", "r", "parseInt", "sort", "a", "b", "attemptsList", "join", "alert", "dangerouslyUseHTMLString", "customClass", "resetFilter", "handleSearch", "handleAddExam", "handleEdit", "Object", "keys", "key", "handleDelete", "deleteExam", "success", "submitForm", "validate", "valid", "updateExam", "createExam", "handleManageQuestions", "loadQuestions", "setTimeout", "questionTable", "document", "querySelector", "style", "height", "getExamQuestions", "count", "handleAddQuestion", "handleEditQuestion", "handleDeleteQuestion", "deleteQuestion", "handleImportQuestions", "beforeUpload", "file", "isWordDoc", "isLt10M", "size", "handleFileChange", "uploadFileList", "handleFileUpload", "submitUpload", "formData", "FormData", "fileObject", "rawFile", "raw", "append", "importQuestionsFromWord", "errorMsg", "downloadTemplate", "link", "createElement", "href", "import", "meta", "env", "VITE_API_URL", "token", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "addOption", "removeOption", "splice", "submitQuestionForm", "emptyOption", "find", "opt", "trim", "hasCorrect", "some", "correctCount", "submitData", "correctOption", "findIndex", "String", "fromCharCode", "correctAnswers", "Boolean", "updateQuestion", "createQuestion", "formatQuestionContent", "content", "substring", "handleViewResults", "summary", "handleViewDetail", "handleSizeChange", "handleCurrentChange"], "sources": ["D:/admin/202506/督导系统/后台管理系统/ms/src/views/exams/ExamList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"exam-list-container\">\r\n    <el-card class=\"filter-card\">\r\n      <div class=\"filter-container\">\r\n        <el-form :model=\"filterForm\" inline>\r\n          <el-form-item label=\"考试标题\">\r\n            <el-input v-model=\"filterForm.title\" placeholder=\"请输入考试标题\" clearable></el-input>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" @click=\"handleSearch\">搜索</el-button>\r\n            <el-button @click=\"resetFilter\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </el-card>\r\n    \r\n    <el-card class=\"table-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span>考试列表</span>\r\n          <div>\r\n            <el-button type=\"primary\" @click=\"handleAddExam\" v-if=\"canManageExams\">新增考试</el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n      \r\n      <el-table :data=\"examList\" stripe border style=\"width: 100%\" v-loading=\"loading\">\r\n        <el-table-column type=\"index\" width=\"50\" />\r\n        <el-table-column prop=\"title\" label=\"考试标题\" min-width=\"200\" />\r\n        <el-table-column prop=\"description\" label=\"考试描述\" min-width=\"200\" show-overflow-tooltip />\r\n        <el-table-column prop=\"duration\" label=\"考试时长(分钟)\" width=\"120\" />\r\n        <el-table-column prop=\"pass_score\" label=\"及格分数\" width=\"100\" />\r\n        <el-table-column prop=\"total_score\" label=\"总分\" width=\"80\" />\r\n        <!-- Add remaining attempts column for students -->\r\n        <el-table-column label=\"考试机会\" width=\"120\" v-if=\"isStudent\">\r\n          <template #default=\"scope\">\r\n            <el-tag :type=\"remainingAttempts[scope.row.id] > 0 ? 'success' : 'danger'\" effect=\"plain\">\r\n              已用 {{ 2 - (remainingAttempts[scope.row.id]) }}/2 次\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"创建时间\" width=\"160\">\r\n          <template #default=\"scope\">\r\n        \r\n            {{ formatDate(scope.row.created_at, 'YYYY-MM-DD HH:mm') }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"320\" fixed=\"right\">\r\n          <template #default=\"scope\">\r\n          \r\n            <template v-if=\"canManageExams\">\r\n           \r\n              <el-button type=\"primary\" size=\"small\" @click=\"handleManageQuestions(scope.row)\">试题管理</el-button>\r\n              <el-button type=\"success\" size=\"small\" @click=\"handleViewResults(scope.row)\">成绩查看</el-button>\r\n              <el-button type=\"warning\" size=\"small\" @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n              <el-button type=\"danger\" size=\"small\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n            </template>\r\n            <template v-else>\r\n              <el-button \r\n                type=\"primary\" \r\n                size=\"small\" \r\n                @click=\"handleTakeExam(scope.row)\" \r\n                :disabled=\"remainingAttempts[scope.row.id] === 0\"\r\n                :loading=\"loading && currentLoadingExam === scope.row.id\"\r\n              >\r\n                {{ remainingAttempts[scope.row.id] === 0 ? '已无机会' : '参加考试' }}\r\n              </el-button>\r\n            \r\n            </template>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          background\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :current-page=\"currentPage\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          :page-size=\"pageSize\"\r\n          :total=\"total\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 新增/编辑考试对话框 -->\r\n    <el-dialog\r\n      :title=\"dialogTitle\"\r\n      v-model=\"dialogVisible\"\r\n      width=\"600px\"\r\n    >\r\n      <el-form :model=\"examForm\" :rules=\"examRules\" ref=\"examFormRef\" label-width=\"100px\">\r\n        <el-form-item label=\"考试标题\" prop=\"title\">\r\n          <el-input v-model=\"examForm.title\" placeholder=\"请输入考试标题\"></el-input>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"考试描述\" prop=\"description\">\r\n          <el-input type=\"textarea\" v-model=\"examForm.description\" placeholder=\"请输入考试描述\" rows=\"3\"></el-input>\r\n        </el-form-item>\r\n        \r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"考试时长\" prop=\"duration\">\r\n              <el-input-number v-model=\"examForm.duration\" :min=\"1\" :max=\"240\" placeholder=\"分钟\" style=\"width: 100%\"></el-input-number>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"及格分数\" prop=\"pass_score\">\r\n              <el-input-number v-model=\"examForm.pass_score\" :min=\"1\" :max=\"examForm.total_score\" style=\"width: 100%\"></el-input-number>\r\n            </el-form-item>\r\n          </el-col>\r\n          <el-col :span=\"24\">\r\n            <el-form-item label=\"总分\" prop=\"total_score\">\r\n              <el-input-number v-model=\"examForm.total_score\" :min=\"1\" :max=\"1000\" style=\"width: 100%\"></el-input-number>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n    \r\n    <!-- 试题管理对话框 -->\r\n    <el-dialog\r\n      title=\"试题管理\"\r\n      v-model=\"questionDialogVisible\"\r\n      width=\"850px\"\r\n      :fullscreen=\"false\"\r\n      :close-on-click-modal=\"false\"\r\n    >\r\n      <div v-if=\"currentExam\" class=\"question-dialog-content\">\r\n        <div class=\"question-header\">\r\n          <h3>{{ currentExam.title }}</h3>\r\n          <div class=\"question-actions\">\r\n            <el-button type=\"primary\" @click=\"handleAddQuestion\">新增试题</el-button>\r\n            <el-button type=\"success\" @click=\"handleImportQuestions\">批量导入</el-button>\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"question-table-wrapper\">\r\n          <el-table \r\n            :data=\"questionList\" \r\n            stripe \r\n            border \r\n            style=\"width: 100%; margin-top: 15px;\" \r\n            v-loading=\"questionLoading\"\r\n            :max-height=\"550\"\r\n            :show-header=\"true\"\r\n          >\r\n            <el-table-column type=\"index\" width=\"50\" />\r\n            <el-table-column label=\"题目内容\" min-width=\"300\">\r\n              <template #default=\"scope\">\r\n                <div v-html=\"formatQuestionContent(scope.row.question)\"></div>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"question_type\" label=\"题型\" width=\"100\" />\r\n            <el-table-column prop=\"score\" label=\"分值\" width=\"80\" />\r\n            <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\r\n              <template #default=\"scope\">\r\n                <el-button type=\"warning\" size=\"small\" @click=\"handleEditQuestion(scope.row)\">编辑</el-button>\r\n                <el-button type=\"danger\" size=\"small\" @click=\"handleDeleteQuestion(scope.row)\">删除</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n        \r\n        <!-- 移除分页，使用滚动条 -->\r\n      </div>\r\n    </el-dialog>\r\n    \r\n    <!-- 试题导入对话框 -->\r\n    <el-dialog\r\n      title=\"批量导入试题\"\r\n      v-model=\"importDialogVisible\"\r\n      width=\"500px\"\r\n    >\r\n      <div class=\"import-dialog-content\">\r\n        <el-alert\r\n          title=\"请上传Word格式的试题模板文件\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          show-icon\r\n          style=\"margin-bottom: 20px;\"\r\n        />\r\n        \r\n        <el-upload\r\n          class=\"upload-demo\"\r\n          drag\r\n          action=\"#\"\r\n          :http-request=\"handleFileUpload\"\r\n          :before-upload=\"beforeUpload\"\r\n          :limit=\"1\"\r\n          :file-list=\"fileList\"\r\n          :auto-upload=\"false\"\r\n          :on-change=\"handleFileChange\"\r\n          accept=\".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document\"\r\n        >\r\n          <el-icon class=\"el-icon--upload\"><upload-filled /></el-icon>\r\n          <div class=\"el-upload__text\">\r\n            拖拽文件到此处或 <em>点击上传</em>\r\n          </div>\r\n          <template #tip>\r\n            <div class=\"el-upload__tip\">\r\n              仅支持 .doc/.docx 格式文件\r\n            </div>\r\n          </template>\r\n        </el-upload>\r\n        \r\n        <div class=\"template-download\">\r\n          <span>没有模板？</span>\r\n          <el-button type=\"primary\" link @click=\"downloadTemplate\">下载试题模板</el-button>\r\n        </div>\r\n      </div>\r\n      \r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"importDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitUpload\" :loading=\"uploadLoading\">上传</el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n    \r\n    <!-- 试题编辑对话框 -->\r\n    <el-dialog\r\n      :title=\"questionDialogTitle\"\r\n      v-model=\"questionEditDialogVisible\"\r\n      width=\"700px\"\r\n    >\r\n      <el-form :model=\"questionForm\" :rules=\"questionRules\" ref=\"questionFormRef\" label-width=\"100px\">\r\n        <el-form-item label=\"题目类型\" prop=\"question_type\">\r\n          <el-select v-model=\"questionForm.question_type\" style=\"width: 100%\">\r\n            <el-option label=\"单选题\" value=\"单选题\"></el-option>\r\n            <el-option label=\"多选题\" value=\"多选题\"></el-option>\r\n            <el-option label=\"判断题\" value=\"判断题\"></el-option>\r\n            <el-option label=\"简答题\" value=\"简答题\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"分值\" prop=\"score\">\r\n          <el-input-number v-model=\"questionForm.score\" :min=\"1\" :max=\"100\" style=\"width: 100%\"></el-input-number>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"题目内容\" prop=\"question\">\r\n          <el-input type=\"textarea\" v-model=\"questionForm.question\" placeholder=\"请输入题目内容\" rows=\"4\"></el-input>\r\n        </el-form-item>\r\n        \r\n        <template v-if=\"questionForm.question_type === '单选题' || questionForm.question_type === '多选题'\">\r\n          <div class=\"options-header\">\r\n            <h4>选项</h4>\r\n            <el-button type=\"primary\" size=\"small\" @click=\"addOption\">添加选项</el-button>\r\n          </div>\r\n          \r\n          <el-form-item \r\n            v-for=\"(option, index) in questionForm.options\"\r\n            :key=\"index\"\r\n            :label=\"'选项 ' + String.fromCharCode(65 + index)\"\r\n          >\r\n            <div class=\"option-item\">\r\n              <el-input v-model=\"option.text\" placeholder=\"请输入选项内容\"></el-input>\r\n              <el-checkbox v-model=\"option.isCorrect\">正确答案</el-checkbox>\r\n              <el-button type=\"danger\" icon=\"Delete\" circle size=\"small\" @click=\"removeOption(index)\"></el-button>\r\n            </div>\r\n          </el-form-item>\r\n        </template>\r\n        \r\n        <template v-else-if=\"questionForm.question_type === '判断题'\">\r\n          <el-form-item label=\"正确答案\" prop=\"correct_answer\">\r\n            <el-radio-group v-model=\"questionForm.correct_answer\">\r\n              <el-radio label=\"正确\">正确</el-radio>\r\n              <el-radio label=\"错误\">错误</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </template>\r\n        \r\n        <template v-else>\r\n          <el-form-item label=\"参考答案\" prop=\"correct_answer\">\r\n            <el-input type=\"textarea\" v-model=\"questionForm.correct_answer\" placeholder=\"请输入参考答案\" rows=\"3\"></el-input>\r\n          </el-form-item>\r\n        </template>\r\n      </el-form>\r\n      \r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"questionEditDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitQuestionForm\">确定</el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n    \r\n    <!-- 成绩查看对话框 -->\r\n    <el-dialog\r\n      title=\"考试成绩\"\r\n      v-model=\"resultsDialogVisible\"\r\n      width=\"800px\"\r\n    >\r\n      <div v-if=\"currentExam\" class=\"results-dialog-content\">\r\n        <h3>{{ currentExam.title }}</h3>\r\n        \r\n        <el-card class=\"statistics-card\">\r\n          <div class=\"statistics-items\">\r\n            <div class=\"statistics-item\">\r\n              <div class=\"statistics-label\">总人数</div>\r\n              <div class=\"statistics-value\">{{ examStatistics.total_students || 0 }}</div>\r\n            </div>\r\n            <div class=\"statistics-item\">\r\n              <div class=\"statistics-label\">及格人数</div>\r\n              <div class=\"statistics-value\">{{ examStatistics.passed_students || 0 }}</div>\r\n            </div>\r\n            <div class=\"statistics-item\">\r\n              <div class=\"statistics-label\">及格率</div>\r\n              <div class=\"statistics-value\">{{ examStatistics.pass_rate || 0 }}%</div>\r\n            </div>\r\n            <div class=\"statistics-item\">\r\n              <div class=\"statistics-label\">平均分</div>\r\n              <div class=\"statistics-value\">{{ examStatistics.average_score || 0 }}</div>\r\n            </div>\r\n          </div>\r\n        </el-card>\r\n        \r\n        <el-table :data=\"resultsList\" stripe border style=\"width: 100%; margin-top: 15px;\" v-loading=\"resultsLoading\">\r\n          <el-table-column type=\"index\" width=\"50\" />\r\n          <el-table-column prop=\"student_name\" label=\"学生姓名\" />\r\n          <el-table-column prop=\"score\" label=\"得分\" width=\"100\" sortable>\r\n            <template #default=\"scope\">\r\n              <span :class=\"scope.row.score >= currentExam.pass_score ? 'pass-score' : 'fail-score'\">\r\n                {{ scope.row.score }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"考试时间\" width=\"160\">\r\n            <template #default=\"scope\">\r\n              {{ formatDate(scope.row.exam_date, 'YYYY-MM-DD HH:mm') }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"状态\" width=\"100\">\r\n            <template #default=\"scope\">\r\n              <el-tag :type=\"scope.row.score >= currentExam.pass_score ? 'success' : 'danger'\">\r\n                {{ scope.row.score >= currentExam.pass_score ? '及格' : '不及格' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"100\" fixed=\"right\">\r\n            <template #default=\"scope\">\r\n              <el-button type=\"primary\" size=\"small\" @click=\"handleViewDetail(scope.row)\">详情</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted, computed } from 'vue'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { UploadFilled } from '@element-plus/icons-vue'\r\nimport { useStore } from 'vuex'\r\nimport { useRouter } from 'vue-router'\r\nimport examService from '@/services/examService'\r\nimport { formatDate, formatRelativeTime } from '@/utils/dateFormat'\r\n\r\nconst store = useStore()\r\nconst router = useRouter()\r\nconst loading = ref(true)\r\nconst currentPage = ref(1)\r\nconst pageSize = ref(10)\r\nconst total = ref(0)\r\nconst dialogVisible = ref(false)\r\nconst dialogTitle = ref('新增考试')\r\nconst examFormRef = ref(null)\r\nconst currentLoadingExam = ref(null) // 当前正在加载的考试ID\r\nlet userInfo = localStorage.getItem(\"userInfo\") ? JSON.parse(localStorage.getItem(\"userInfo\")): ''\r\n// 用户角色\r\nconst isAdmin = userInfo?.role == 'admin'\r\nconst isTeacher = userInfo?.role == 'teacher'\r\nconst isStudent = computed(() => store.getters.isStudent)\r\nconst canManageExams = userInfo?.role == 'admin'\r\n\r\n// 试题管理相关\r\nconst questionDialogVisible = ref(false)\r\nconst questionEditDialogVisible = ref(false)\r\nconst questionDialogTitle = ref('新增试题')\r\nconst questionFormRef = ref(null)\r\nconst currentExam = ref(null)\r\nconst questionList = ref([])\r\nconst questionLoading = ref(false)\r\nconst questionCurrentPage = ref(1)\r\nconst questionTotal = ref(0)\r\n\r\n// 导入试题相关\r\nconst importDialogVisible = ref(false)\r\nconst fileList = ref([])\r\nconst uploadLoading = ref(false)\r\n\r\n// 成绩查看相关\r\nconst resultsDialogVisible = ref(false)\r\nconst resultsList = ref([])\r\nconst resultsLoading = ref(false)\r\nconst examStatistics = reactive({\r\n  total_students: 0,\r\n  passed_students: 0,\r\n  pass_rate: 0,\r\n  average_score: 0\r\n})\r\n\r\n// Add remaining attempts tracking\r\nconst remainingAttempts = ref({});\r\nconst attemptedExams = ref({}); // 记录已参加过的考试\r\n\r\n// 检查学生是否参加过某个考试\r\nconst hasAttemptedExam = (examId) => {\r\n  if (!examId) return false;\r\n  return attemptedExams.value[examId] === true;\r\n};\r\n\r\n// 过滤条件\r\nconst filterForm = reactive({\r\n  title: ''\r\n})\r\n\r\n// 考试表单\r\nconst examForm = reactive({\r\n  id: null,\r\n  title: '',\r\n  description: '',\r\n  duration: 60,\r\n  pass_score: 60,\r\n  total_score: 100\r\n})\r\n\r\n// 验证规则\r\nconst examRules = {\r\n  title: [\r\n    { required: true, message: '请输入考试标题', trigger: 'blur' }\r\n  ],\r\n  duration: [\r\n    { required: true, message: '请输入考试时长', trigger: 'blur' },\r\n    { type: 'number', min: 1, message: '考试时长必须大于0', trigger: 'blur' }\r\n  ],\r\n  pass_score: [\r\n    { required: true, message: '请输入及格分数', trigger: 'blur' },\r\n    { type: 'number', min: 1, message: '及格分数必须大于0', trigger: 'blur' }\r\n  ],\r\n  total_score: [\r\n    { required: true, message: '请输入总分', trigger: 'blur' },\r\n    { type: 'number', min: 1, message: '总分必须大于0', trigger: 'blur' }\r\n  ]\r\n}\r\n\r\n// 试题表单\r\nconst questionForm = reactive({\r\n  id: null,\r\n  question: '',\r\n  question_type: '单选题',\r\n  correct_answer: '',\r\n  score: 5,\r\n  options: [\r\n    { text: '', isCorrect: false },\r\n    { text: '', isCorrect: false },\r\n    { text: '', isCorrect: false },\r\n    { text: '', isCorrect: false }\r\n  ]\r\n})\r\n\r\n// 试题验证规则\r\nconst questionRules = {\r\n  question: [\r\n    { required: true, message: '请输入题目内容', trigger: 'blur' }\r\n  ],\r\n  question_type: [\r\n    { required: true, message: '请选择题目类型', trigger: 'change' }\r\n  ],\r\n  score: [\r\n    { required: true, message: '请输入分值', trigger: 'blur' },\r\n    { type: 'number', min: 1, message: '分值必须大于0', trigger: 'blur' }\r\n  ]\r\n}\r\n\r\n// 模拟考试数据\r\nconst examList = ref([])\r\n\r\n// 模拟试题数据\r\nconst mockQuestions = [\r\n  {\r\n    id: 1,\r\n    question: '公司的上班时间是几点到几点？',\r\n    options: [\r\n      { text: 'A. 8:30-17:30', isCorrect: true },\r\n      { text: 'B. 9:00-18:00', isCorrect: false },\r\n      { text: 'C. 9:30-18:30', isCorrect: false },\r\n      { text: 'D. 8:00-17:00', isCorrect: false }\r\n    ],\r\n    correct_answer: 'A',\r\n    question_type: '单选题',\r\n    score: 5,\r\n    exam_id: 1\r\n  },\r\n  {\r\n    id: 2,\r\n    question: '公司允许员工在工作时间做以下哪些事情？（多选）',\r\n    options: [\r\n      { text: 'A. 喝水', isCorrect: true },\r\n      { text: 'B. 短暂休息', isCorrect: true },\r\n      { text: 'C. 玩游戏', isCorrect: false },\r\n      { text: 'D. 睡觉', isCorrect: false }\r\n    ],\r\n    correct_answer: 'AB',\r\n    question_type: '多选题',\r\n    score: 10,\r\n    exam_id: 1\r\n  },\r\n  {\r\n    id: 3,\r\n    question: '公司规定必须遵守考勤制度。',\r\n    options: [],\r\n    correct_answer: '正确',\r\n    question_type: '判断题',\r\n    score: 5,\r\n    exam_id: 1\r\n  },\r\n  {\r\n    id: 4,\r\n    question: '请简述公司的请假流程。',\r\n    options: [],\r\n    correct_answer: '1. 提前向部门负责人口头说明\\n2. 在OA系统填写请假申请\\n3. 等待审批通过\\n4. 销假时提交相关证明',\r\n    question_type: '简答题',\r\n    score: 15,\r\n    exam_id: 1\r\n  }\r\n]\r\n\r\n// 模拟成绩数据\r\nconst mockResults = [\r\n  {\r\n    id: 1,\r\n    student_id: 1,\r\n    student_name: '张三',\r\n    exam_id: 1,\r\n    score: 85,\r\n    exam_date: '2023-01-20 10:30:00'\r\n  },\r\n  {\r\n    id: 2,\r\n    student_id: 2,\r\n    student_name: '李四',\r\n    exam_id: 1,\r\n    score: 75,\r\n    exam_date: '2023-01-20 10:45:00'\r\n  },\r\n  {\r\n    id: 3,\r\n    student_id: 3,\r\n    student_name: '王五',\r\n    exam_id: 1,\r\n    score: 55,\r\n    exam_date: '2023-01-20 11:00:00'\r\n  },\r\n  {\r\n    id: 4,\r\n    student_id: 4,\r\n    student_name: '赵六',\r\n    exam_id: 1,\r\n    score: 92,\r\n    exam_date: '2023-01-20 10:15:00'\r\n  }\r\n]\r\n\r\n// 设置总数\r\ntotal.value = examList.length\r\n\r\n// 获取所有考试\r\nconst fetchExams = async () => {\r\n  loading.value = true;\r\n  try {\r\n    const response = await examService.getExams({\r\n      page: currentPage.value,\r\n      limit: pageSize.value,\r\n      title: filterForm.title\r\n    });\r\n    \r\n    examList.value = response.data.data;\r\n    total.value = response.data.total || examList.value.length;\r\n    \r\n    // If student, check attempts for each exam\r\n    if (isStudent.value) {\r\n      await checkRemainingAttempts();\r\n    }\r\n    \r\n    loading.value = false;\r\n  } catch (error) {\r\n    console.error('获取考试列表失败', error);\r\n    ElMessage.error('获取考试列表失败');\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// Check remaining attempts for each exam\r\nconst checkRemainingAttempts = async () => {\r\n  const studentId = localStorage.getItem('studentId');\r\n  if (!studentId) {\r\n    console.error('未找到有效的学生ID');\r\n    ElMessage.warning('未找到有效的学生ID，请重新登录后再试');\r\n    return;\r\n  }\r\n  \r\n  try {\r\n    console.log('正在获取所有考试的尝试次数...学生ID:', studentId);\r\n    // 创建一个Promise数组，同时请求所有考试的尝试次数\r\n    const attemptPromises = examList.value.map(async (exam) => {\r\n      try {\r\n        console.log(`正在查询考试 ${exam.id} (${exam.title}) 的尝试记录...`);\r\n        const response = await examService.getExamResults(exam.id, { student_id: studentId });\r\n        console.log(`考试 ${exam.id} 返回数据:`, response.data);\r\n        \r\n        // 确保我们有结果数据\r\n        if (response.data && response.data.data) {\r\n          const results = response.data.data.results || [];\r\n          const attempts = results.length;\r\n          const remaining = Math.max(0, 2 - attempts);\r\n          \r\n          console.log(`考试 ${exam.id} (${exam.title}): 已尝试 ${attempts} 次，剩余 ${remaining} 次`);\r\n          remainingAttempts.value[exam.id] = remaining;\r\n          attemptedExams.value[exam.id] = attempts > 0; // 只有实际参加过才标记为true\r\n          \r\n          // 记录每次尝试的详情，便于调试\r\n          if (results.length > 0) {\r\n            results.forEach((result, index) => {\r\n              console.log(`  尝试 ${index + 1}: 得分 ${result.score}，时间 ${result.exam_date}`);\r\n            });\r\n          }\r\n        } else {\r\n          console.warn(`考试 ${exam.id} 未返回有效数据，设置默认剩余次数为2`);\r\n          remainingAttempts.value[exam.id] = 2;\r\n          attemptedExams.value[exam.id] = false; // 没有数据，标记为未参加\r\n        }\r\n      } catch (err) {\r\n        console.error(`获取考试 ${exam.id} 尝试次数失败:`, err);\r\n        remainingAttempts.value[exam.id] = 2;\r\n        attemptedExams.value[exam.id] = false; // 出错，标记为未参加\r\n      }\r\n    });\r\n    \r\n    // 等待所有请求完成\r\n    await Promise.all(attemptPromises);\r\n    console.log('所有考试尝试次数获取完成:', remainingAttempts.value);\r\n    console.log('已参加过的考试:', attemptedExams.value);\r\n  } catch (error) {\r\n    console.error('获取考试尝试次数失败:', error);\r\n    // 设置默认值\r\n    examList.value.forEach(exam => {\r\n      remainingAttempts.value[exam.id] = 2;\r\n      attemptedExams.value[exam.id] = false; // 出错，标记为未参加\r\n    });\r\n  }\r\n};\r\n\r\n// 参加考试\r\nconst handleTakeExam = (row) => {\r\n  // 设置当前正在加载的考试ID\r\n  currentLoadingExam.value = row.id;\r\n  \r\n  // 确保已经加载了尝试次数\r\n  if (!remainingAttempts.value[row.id] && remainingAttempts.value[row.id] !== 0) {\r\n    // 如果还没加载尝试次数信息，先加载\r\n    const teacherId = localStorage.getItem('teacherId');\r\n    if (!teacherId) {\r\n      ElMessage.warning('未找到有效的教师ID，请重新登录');\r\n      currentLoadingExam.value = null;\r\n      return;\r\n    }\r\n    \r\n    ElMessage.info('正在获取考试尝试信息，请稍候...');\r\n    \r\n    // 立即获取该考试的尝试次数\r\n    examService.getExamResults(row.id, { teacher_id: teacherId })\r\n      .then(response => {\r\n        console.log(`考试 ${row.id} 尝试信息:`, response.data);\r\n        \r\n        if (response.data && response.data.data) {\r\n          const results = response.data.data.results || [];\r\n          const attempts = results.length;\r\n          const remaining = Math.max(0, 2 - attempts);\r\n          \r\n          console.log(`考试 ${row.id} (${row.title}): 已尝试 ${attempts} 次，剩余 ${remaining} 次`);\r\n          remainingAttempts.value[row.id] = remaining;\r\n          attemptedExams.value[row.id] = attempts > 0;\r\n          \r\n          // 获取到尝试次数后，继续参加考试逻辑\r\n          currentLoadingExam.value = null;\r\n          continueToTakeExam(row);\r\n        } else {\r\n          console.warn(`考试 ${row.id} 未返回有效数据，设置默认剩余次数为2`);\r\n          remainingAttempts.value[row.id] = 2;\r\n          attemptedExams.value[row.id] = false;\r\n          currentLoadingExam.value = null;\r\n          continueToTakeExam(row);\r\n        }\r\n      })\r\n      .catch(error => {\r\n        console.error('获取考试尝试次数失败', error);\r\n        ElMessage.error('获取考试尝试次数失败，请刷新页面重试');\r\n        currentLoadingExam.value = null;\r\n      });\r\n    return;\r\n  }\r\n  \r\n  currentLoadingExam.value = null;\r\n  continueToTakeExam(row);\r\n};\r\n\r\n// 继续参加考试流程\r\nconst continueToTakeExam = (row) => {\r\n  // Check if student still has attempts left\r\n  if (remainingAttempts.value[row.id] === 0) {\r\n    ElMessage.warning('您已达到该考试的最大尝试次数（2次）');\r\n    return;\r\n  }\r\n  \r\n  // Confirm before taking the exam\r\n  ElMessageBox.confirm(\r\n    `您总共有2次尝试机会，已使用 ${2 - remainingAttempts.value[row.id]} 次，还剩 ${remainingAttempts.value[row.id]} 次机会。确定要参加考试吗？`,\r\n    '参加考试',\r\n    {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    }\r\n  ).then(() => {\r\n    // 记录考试ID和剩余次数到localStorage，以便考试页面使用\r\n    localStorage.setItem('currentExamId', row.id);\r\n    localStorage.setItem('currentExamTitle', row.title);\r\n    localStorage.setItem('currentExamRemainingAttempts', remainingAttempts.value[row.id]);\r\n    \r\n    // 跳转到考试页面\r\n    router.push(`/exams/take/${row.id}`);\r\n  }).catch(() => {\r\n    console.log('用户取消参加考试');\r\n  });\r\n};\r\n\r\n// 查看我的成绩\r\nconst handleViewMyResult = async (row) => {\r\n  try {\r\n    const studentId = localStorage.getItem('studentId');\r\n    if (!studentId) {\r\n      ElMessage.error('未找到有效的学生ID，请重新登录');\r\n      return;\r\n    }\r\n    \r\n    console.log('查询成绩 - 学生ID:', studentId, '考试ID:', row.id);\r\n    \r\n    const response = await examService.getExamResults(row.id, { student_id: studentId });\r\n    console.log('查询成绩 - 返回数据:', response.data);\r\n    \r\n    // 检查是否有考试记录\r\n    if (response.data.data.results && response.data.data.results.length > 0) {\r\n      // Get all attempts sorted by score (highest first)\r\n      const myResults = response.data.data.results\r\n        .filter(r => r.student_id === parseInt(studentId))\r\n        .sort((a, b) => b.score - a.score);\r\n      \r\n      console.log('查询成绩 - 过滤后的结果:', myResults);\r\n      \r\n      if (myResults.length > 0) {\r\n        // Format results to show all attempts\r\n        const attemptsList = myResults.map((result, index) => {\r\n          return `<div class=\"result-item ${result.score >= row.pass_score ? 'pass' : 'fail'}\">\r\n            <h4>尝试 ${index + 1}</h4>\r\n            <p><strong>得分:</strong> ${result.score} / ${row.total_score}</p>\r\n            <p><strong>考试时间:</strong> ${formatDate(result.exam_date, 'YYYY-MM-DD HH:mm:ss')}</p>\r\n            <p><strong>状态:</strong> ${result.score >= row.pass_score ? '通过' : '未通过'}</p>\r\n          </div>`;\r\n        }).join('<hr>');\r\n        \r\n        ElMessageBox.alert(\r\n          `<div class=\"results-container\">\r\n            <h3>您的考试成绩</h3>\r\n            <div class=\"results-list\">${attemptsList}</div>\r\n            <div class=\"attempts-info\">\r\n              <p>总尝试次数: ${myResults.length}/2</p>\r\n              <p>剩余次数: ${Math.max(0, 2 - myResults.length)}</p>\r\n              <p class=\"view-all-results\"><a href=\"#/exams/my-results\">查看我的所有考试成绩 &raquo;</a></p>\r\n            </div>\r\n          </div>`,\r\n          '考试成绩',\r\n          {\r\n            dangerouslyUseHTMLString: true,\r\n            confirmButtonText: '确定',\r\n            customClass: 'result-dialog'\r\n          }\r\n        );\r\n        return;\r\n      }\r\n    }\r\n    \r\n    ElMessage.info('您暂无该考试的成绩记录');\r\n  } catch (error) {\r\n    console.error('获取成绩失败', error);\r\n    ElMessage.error('获取成绩失败: ' + (error.response?.data?.message || error.message));\r\n  }\r\n};\r\n\r\nonMounted(() => {\r\n  fetchExams()\r\n})\r\n\r\n// 重置过滤条件\r\nconst resetFilter = () => {\r\n  filterForm.title = ''\r\n  handleSearch()\r\n}\r\n\r\n// 搜索\r\nconst handleSearch = () => {\r\n  currentPage.value = 1\r\n  fetchExams()\r\n}\r\n\r\n// 新增考试\r\nconst handleAddExam = () => {\r\n  dialogTitle.value = '新增考试'\r\n  dialogVisible.value = true\r\n  // 重置表单\r\n  examForm.id = null\r\n  examForm.title = ''\r\n  examForm.description = ''\r\n  examForm.duration = 60\r\n  examForm.pass_score = 60\r\n  examForm.total_score = 100\r\n}\r\n\r\n// 编辑考试\r\nconst handleEdit = (row) => {\r\n  dialogTitle.value = '编辑考试'\r\n  dialogVisible.value = true\r\n  // 填充表单数据\r\n  Object.keys(examForm).forEach(key => {\r\n    examForm[key] = row[key]\r\n  })\r\n}\r\n\r\n// 删除考试\r\nconst handleDelete = (row) => {\r\n  ElMessageBox.confirm(`确定要删除考试 ${row.title} 吗?`, '警告', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    try {\r\n      await examService.deleteExam(row.id)\r\n      ElMessage.success(`考试 ${row.title} 已删除`)\r\n      fetchExams() // 重新加载列表\r\n    } catch (error) {\r\n      console.error('删除考试失败', error)\r\n      ElMessage.error('删除考试失败，请重试')\r\n    }\r\n  }).catch(() => {})\r\n}\r\n\r\n// 提交表单\r\nconst submitForm = async () => {\r\n  if (!examFormRef.value) return\r\n  \r\n  await examFormRef.value.validate(async (valid) => {\r\n    if (valid) {\r\n      try {\r\n        if (examForm.id) {\r\n          // 编辑模式\r\n          await examService.updateExam(examForm.id, examForm)\r\n          ElMessage.success(`考试 ${examForm.title} 信息已更新`)\r\n        } else {\r\n          // 新增模式\r\n          await examService.createExam(examForm)\r\n          ElMessage.success(`考试 ${examForm.title} 添加成功`)\r\n        }\r\n        dialogVisible.value = false\r\n        fetchExams() // 重新加载列表\r\n      } catch (error) {\r\n        console.error('保存考试失败', error)\r\n        ElMessage.error('保存考试失败，请重试')\r\n      }\r\n    } else {\r\n      return false\r\n    }\r\n  })\r\n}\r\n\r\n// 试题管理\r\nconst handleManageQuestions = (row) => {\r\n  currentExam.value = row\r\n  questionDialogVisible.value = true\r\n  // 设置为第一页（即使我们不再使用分页）\r\n  questionCurrentPage.value = 1\r\n  \r\n  // 加载试题数据\r\n  loadQuestions(row.id)\r\n  \r\n  // 确保对话框完全打开后调整表格高度\r\n  setTimeout(() => {\r\n    const questionTable = document.querySelector('.question-table-wrapper .el-table')\r\n    if (questionTable) {\r\n      questionTable.style.height = '550px'\r\n    }\r\n  }, 100)\r\n}\r\n\r\n// 加载试题数据\r\nconst loadQuestions = async (examId) => {\r\n  questionLoading.value = true\r\n  \r\n  try {\r\n    // 不使用分页参数，一次性获取所有试题\r\n    const response = await examService.getExamQuestions(examId)\r\n    \r\n    // 检查返回数据结构\r\n    console.log('加载试题数据返回:', response.data)\r\n    \r\n    // 确保获取完整的数据列表\r\n    if (response.data && response.data.data) {\r\n      questionList.value = response.data.data\r\n      // 依然保存总数，用于展示\r\n      questionTotal.value = response.data.count || questionList.value.length\r\n      console.log(`加载了 ${questionList.value.length} 道试题`)\r\n    } else {\r\n      questionList.value = []\r\n      questionTotal.value = 0\r\n      console.warn('未找到试题数据')\r\n    }\r\n    \r\n    questionLoading.value = false\r\n  } catch (error) {\r\n    console.error('获取试题失败', error)\r\n    ElMessage.error('获取试题失败')\r\n    questionLoading.value = false\r\n  }\r\n}\r\n\r\n// 新增试题\r\nconst handleAddQuestion = () => {\r\n  questionDialogTitle.value = '新增试题'\r\n  questionEditDialogVisible.value = true\r\n  // 重置表单\r\n  questionForm.id = null\r\n  questionForm.question = ''\r\n  questionForm.question_type = '单选题'\r\n  questionForm.correct_answer = ''\r\n  questionForm.score = 5\r\n  questionForm.options = [\r\n    { text: '', isCorrect: false },\r\n    { text: '', isCorrect: false },\r\n    { text: '', isCorrect: false },\r\n    { text: '', isCorrect: false }\r\n  ]\r\n}\r\n\r\n// 编辑试题\r\nconst handleEditQuestion = (row) => {\r\n  questionDialogTitle.value = '编辑试题'\r\n  questionEditDialogVisible.value = true\r\n  \r\n  // 填充表单数据\r\n  questionForm.id = row.id\r\n  questionForm.question = row.question\r\n  questionForm.question_type = row.question_type\r\n  questionForm.correct_answer = row.correct_answer\r\n  questionForm.score = row.score\r\n  \r\n  // 根据题型处理选项\r\n  if (row.question_type === '单选题' || row.question_type === '多选题') {\r\n    questionForm.options = row.options ? [...row.options] : []\r\n  } else if (row.question_type === '判断题') {\r\n    questionForm.correct_answer = row.correct_answer\r\n  }\r\n}\r\n\r\n// 删除试题\r\nconst handleDeleteQuestion = (row) => {\r\n  ElMessageBox.confirm(`确定要删除该试题吗?`, '警告', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    try {\r\n      await examService.deleteQuestion(row.id)\r\n      ElMessage.success('试题已删除')\r\n      // 重新加载试题列表\r\n      loadQuestions(currentExam.value.id)\r\n    } catch (error) {\r\n      console.error('删除试题失败', error)\r\n      ElMessage.error('删除试题失败，请重试')\r\n    }\r\n  }).catch(() => {})\r\n}\r\n\r\n// 批量导入试题\r\nconst handleImportQuestions = () => {\r\n  importDialogVisible.value = true\r\n  fileList.value = []\r\n}\r\n\r\n// 文件上传前检查\r\nconst beforeUpload = (file) => {\r\n  const isWordDoc = \r\n    file.type === 'application/msword' || \r\n    file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\r\n  \r\n  if (!isWordDoc) {\r\n    ElMessage.error('请上传Word格式的文件!')\r\n    return false\r\n  }\r\n  \r\n  const isLt10M = file.size / 1024 / 1024 < 10\r\n  \r\n  if (!isLt10M) {\r\n    ElMessage.error('文件大小不能超过10MB!')\r\n    return false\r\n  }\r\n  \r\n  return true\r\n}\r\n\r\n// 处理文件选择\r\nconst handleFileChange = (file, uploadFileList) => {\r\n  // 记录最新选择的文件\r\n  console.log('文件选择变化:', file, uploadFileList)\r\n  fileList.value = uploadFileList\r\n}\r\n\r\n// 处理文件上传\r\nconst handleFileUpload = (options) => {\r\n  const { file } = options\r\n  // 这个函数只在auto-upload=true时调用\r\n  // 我们设置为false，所以这里只需返回false\r\n  return false // 阻止默认上传行为，使用我们自己的提交方式\r\n}\r\n\r\n// 提交上传\r\nconst submitUpload = async () => {\r\n  console.log('当前文件列表:', fileList.value)\r\n  if (!fileList.value || fileList.value.length === 0) {\r\n    ElMessage.warning('请选择要上传的文件')\r\n    return\r\n  }\r\n  \r\n  uploadLoading.value = true\r\n  \r\n  try {\r\n    // 创建FormData对象\r\n    const formData = new FormData()\r\n    // 获取原始文件对象\r\n    const fileObject = fileList.value[0]\r\n    const rawFile = fileObject.raw || fileObject\r\n    console.log('上传文件:', rawFile)\r\n    \r\n    // 使用field name 'file' 匹配后端控制器\r\n    formData.append('template', rawFile)\r\n    \r\n    // 调用实际API\r\n    const response = await examService.importQuestionsFromWord(currentExam.value.id, formData)\r\n    \r\n    // 处理成功响应\r\n    ElMessage.success(`试题导入成功，共导入${response.data.count || 0}道题目`)\r\n    importDialogVisible.value = false\r\n    \r\n    // 重新加载试题列表\r\n    loadQuestions(currentExam.value.id)\r\n  } catch (error) {\r\n    console.error('导入试题失败', error)\r\n    let errorMsg = '导入试题失败'\r\n    \r\n    // 获取详细错误信息\r\n    if (error.response && error.response.data && error.response.data.message) {\r\n      errorMsg += `：${error.response.data.message}`\r\n    }\r\n    \r\n    ElMessage.error(errorMsg)\r\n  } finally {\r\n    uploadLoading.value = false\r\n  }\r\n}\r\n\r\n// 下载模板\r\nconst downloadTemplate = async () => {\r\n  try {\r\n    ElMessage.success('模板下载中...')\r\n    \r\n    // 创建下载链接\r\n    const link = document.createElement('a')\r\n    // 设置下载链接为后端API地址\r\n    link.href = `${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/api/exams/template/download`\r\n    \r\n    // 添加token到URL，以便通过授权\r\n    const token = localStorage.getItem('token')\r\n    if (token) {\r\n      link.href += `?token=${token}`\r\n    }\r\n    \r\n    link.download = '试题导入模板.docx'\r\n    document.body.appendChild(link)\r\n    link.click()\r\n    document.body.removeChild(link)\r\n  } catch (error) {\r\n    console.error('下载模板失败', error)\r\n    ElMessage.error('下载模板失败，请重试')\r\n  }\r\n}\r\n\r\n// 添加选项\r\nconst addOption = () => {\r\n  questionForm.options.push({ text: '', isCorrect: false })\r\n}\r\n\r\n// 移除选项\r\nconst removeOption = (index) => {\r\n  if (questionForm.options.length <= 2) {\r\n    ElMessage.warning('至少需要2个选项')\r\n    return\r\n  }\r\n  questionForm.options.splice(index, 1)\r\n}\r\n\r\n// 提交试题表单\r\nconst submitQuestionForm = async () => {\r\n  if (!questionFormRef.value) return\r\n  \r\n  await questionFormRef.value.validate(async (valid) => {\r\n    if (valid) {\r\n      // 选择题验证选项\r\n      if ((questionForm.question_type === '单选题' || questionForm.question_type === '多选题')) {\r\n        // 验证选项内容\r\n        const emptyOption = questionForm.options.find(opt => !opt.text.trim())\r\n        if (emptyOption) {\r\n          ElMessage.error('选项内容不能为空')\r\n          return\r\n        }\r\n        \r\n        // 验证是否选择了正确答案\r\n        const hasCorrect = questionForm.options.some(opt => opt.isCorrect)\r\n        if (!hasCorrect) {\r\n          ElMessage.error('请至少选择一个正确答案')\r\n          return\r\n        }\r\n        \r\n        // 单选题只能有一个正确答案\r\n        if (questionForm.question_type === '单选题') {\r\n          const correctCount = questionForm.options.filter(opt => opt.isCorrect).length\r\n          if (correctCount > 1) {\r\n            ElMessage.error('单选题只能有一个正确答案')\r\n            return\r\n          }\r\n        }\r\n      }\r\n      \r\n      try {\r\n        // 准备提交的数据\r\n        const submitData = { ...questionForm }\r\n        \r\n        // 处理正确答案\r\n        if (questionForm.question_type === '单选题') {\r\n          const correctOption = questionForm.options.find(opt => opt.isCorrect)\r\n          if (correctOption) {\r\n            // 获取正确选项的索引，转换为A、B、C...\r\n            const index = questionForm.options.findIndex(opt => opt.isCorrect)\r\n            submitData.correct_answer = String.fromCharCode(65 + index) // A, B, C...\r\n          }\r\n        } else if (questionForm.question_type === '多选题') {\r\n          // 将所有正确选项索引转换为字符串，如\"ABC\"\r\n          const correctAnswers = questionForm.options\r\n            .map((opt, index) => opt.isCorrect ? String.fromCharCode(65 + index) : null)\r\n            .filter(Boolean)\r\n            .join('')\r\n          submitData.correct_answer = correctAnswers\r\n        }\r\n        \r\n        // 提交表单\r\n        if (questionForm.id) {\r\n          // 编辑模式\r\n          await examService.updateQuestion(questionForm.id, submitData)\r\n          ElMessage.success('试题更新成功')\r\n        } else {\r\n          // 新增模式\r\n          await examService.createQuestion(currentExam.value.id, submitData)\r\n          ElMessage.success('试题添加成功')\r\n        }\r\n        \r\n        questionEditDialogVisible.value = false\r\n        // 重新加载试题列表\r\n        loadQuestions(currentExam.value.id)\r\n      } catch (error) {\r\n        console.error('保存试题失败', error)\r\n        ElMessage.error('保存试题失败，请重试')\r\n      }\r\n    } else {\r\n      return false\r\n    }\r\n  })\r\n}\r\n\r\n// 格式化试题内容\r\nconst formatQuestionContent = (content) => {\r\n  if (!content) return ''\r\n  // 最多显示100个字符\r\n  return content.length > 100 ? content.substring(0, 100) + '...' : content\r\n}\r\n\r\n// 试题不再使用分页\r\n\r\n// 查看考试成绩\r\nconst handleViewResults = async (row) => {\r\n  currentExam.value = row\r\n  resultsDialogVisible.value = true\r\n  resultsLoading.value = true\r\n  \r\n  try {\r\n    const response = await examService.getExamResults(row.id)\r\n    const data = response.data.data\r\n    \r\n    resultsList.value = data.results || []\r\n    \r\n    // 设置统计数据\r\n    examStatistics.total_students = data.summary.total_students || 0\r\n    examStatistics.passed_students = data.summary.passed_students || 0\r\n    examStatistics.pass_rate = data.summary.pass_rate || 0\r\n    examStatistics.average_score = data.summary.average_score || 0\r\n    \r\n    resultsLoading.value = false\r\n  } catch (error) {\r\n    console.error('获取成绩列表失败', error)\r\n    ElMessage.error('获取成绩列表失败')\r\n    resultsLoading.value = false\r\n  }\r\n}\r\n\r\n// 查看考试详情\r\nconst handleViewDetail = (row) => {\r\n  ElMessage.success(`查看 ${row.student_name} 的考试详情`)\r\n}\r\n\r\n// 分页处理\r\nconst handleSizeChange = (size) => {\r\n  pageSize.value = size\r\n  currentPage.value = 1\r\n  fetchExams()\r\n}\r\n\r\nconst handleCurrentChange = (page) => {\r\n  currentPage.value = page\r\n  fetchExams()\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.exam-list-container {\r\n  padding: 20px;\r\n}\r\n\r\n.filter-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.filter-container {\r\n  padding: 10px 0;\r\n}\r\n\r\n.table-card {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-header span {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n/* 试题管理样式 */\r\n.question-dialog-content {\r\n  min-height: 300px;\r\n}\r\n\r\n.question-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n/* 添加表格滚动容器样式 */\r\n.question-table-wrapper {\r\n  max-height: 600px; /* 增加高度以显示更多内容 */\r\n  overflow-y: auto;\r\n  overflow-x: hidden;\r\n}\r\n\r\n.question-header h3 {\r\n  margin: 0;\r\n  font-size: 18px;\r\n}\r\n\r\n.options-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-top: 15px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.options-header h4 {\r\n  margin: 0;\r\n}\r\n\r\n.option-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n/* 试题导入样式 */\r\n.import-dialog-content {\r\n  padding: 10px 0;\r\n}\r\n\r\n.upload-demo {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.template-download {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  margin-top: 20px;\r\n  gap: 5px;\r\n}\r\n\r\n/* 成绩查看样式 */\r\n.results-dialog-content h3 {\r\n  margin-top: 0;\r\n  margin-bottom: 15px;\r\n  font-size: 18px;\r\n}\r\n\r\n.statistics-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.statistics-items {\r\n  display: flex;\r\n  justify-content: space-around;\r\n}\r\n\r\n.statistics-item {\r\n  text-align: center;\r\n  flex: 1;\r\n}\r\n\r\n.statistics-label {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.statistics-value {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.pass-score {\r\n  color: #67C23A;\r\n  font-weight: bold;\r\n}\r\n\r\n.fail-score {\r\n  color: #F56C6C;\r\n  font-weight: bold;\r\n}\r\n\r\n/* Style the Element Plus components to match LoginView style */\r\n:deep(.el-button--primary) {\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n  border: none;\r\n}\r\n\r\n:deep(.el-button--primary:hover) {\r\n  background: linear-gradient(135deg, #66b1ff 0%, #5098fa 100%);\r\n  border: none;\r\n}\r\n\r\n:deep(.el-input__wrapper) {\r\n  border-radius: 6px;\r\n}\r\n\r\n:deep(.el-input__wrapper.is-focus) {\r\n  box-shadow: 0 0 0 1px #409EFF inset;\r\n}\r\n\r\n:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n}\r\n\r\n/* Exam results styles */\r\n:deep(.result-dialog) {\r\n  min-width: 400px;\r\n}\r\n\r\n:deep(.results-container) {\r\n  padding: 10px;\r\n}\r\n\r\n:deep(.result-item) {\r\n  padding: 10px;\r\n  margin-bottom: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n:deep(.result-item.pass) {\r\n  background-color: rgba(103, 194, 58, 0.1);\r\n  border-left: 3px solid #67C23A;\r\n}\r\n\r\n:deep(.result-item.fail) {\r\n  background-color: rgba(245, 108, 108, 0.1);\r\n  border-left: 3px solid #F56C6C;\r\n}\r\n\r\n:deep(.results-list) {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n:deep(.attempts-info) {\r\n  font-weight: bold;\r\n  border-top: 1px solid #eee;\r\n  padding-top: 10px;\r\n}\r\n\r\n:deep(.view-all-results) {\r\n  margin-top: 15px;\r\n  text-align: center;\r\n}\r\n\r\n:deep(.view-all-results a) {\r\n  color: #409EFF;\r\n  text-decoration: none;\r\n  font-weight: bold;\r\n}\r\n\r\n:deep(.view-all-results a:hover) {\r\n  text-decoration: underline;\r\n}\r\n</style> "], "mappings": ";;;;;;;AAuWA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,KAAK;AACxD,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,SAAS,QAAQ,YAAY;AACtC,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAASC,UAAU,EAAEC,kBAAkB,QAAQ,oBAAoB;;;;;;;IAEnE,MAAMC,KAAK,GAAGL,QAAQ,CAAC,CAAC;IACxB,MAAMM,MAAM,GAAGL,SAAS,CAAC,CAAC;IAC1B,MAAMM,OAAO,GAAGd,GAAG,CAAC,IAAI,CAAC;IACzB,MAAMe,WAAW,GAAGf,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAMgB,QAAQ,GAAGhB,GAAG,CAAC,EAAE,CAAC;IACxB,MAAMiB,KAAK,GAAGjB,GAAG,CAAC,CAAC,CAAC;IACpB,MAAMkB,aAAa,GAAGlB,GAAG,CAAC,KAAK,CAAC;IAChC,MAAMmB,WAAW,GAAGnB,GAAG,CAAC,MAAM,CAAC;IAC/B,MAAMoB,WAAW,GAAGpB,GAAG,CAAC,IAAI,CAAC;IAC7B,MAAMqB,kBAAkB,GAAGrB,GAAG,CAAC,IAAI,CAAC,EAAC;IACrC,IAAIsB,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC,GAAE,EAAE;IAClG;IACA,MAAMG,OAAO,GAAGL,QAAQ,EAAEM,IAAI,IAAI,OAAO;IACzC,MAAMC,SAAS,GAAGP,QAAQ,EAAEM,IAAI,IAAI,SAAS;IAC7C,MAAME,SAAS,GAAG3B,QAAQ,CAAC,MAAMS,KAAK,CAACmB,OAAO,CAACD,SAAS,CAAC;IACzD,MAAME,cAAc,GAAGV,QAAQ,EAAEM,IAAI,IAAI,OAAO;;IAEhD;IACA,MAAMK,qBAAqB,GAAGjC,GAAG,CAAC,KAAK,CAAC;IACxC,MAAMkC,yBAAyB,GAAGlC,GAAG,CAAC,KAAK,CAAC;IAC5C,MAAMmC,mBAAmB,GAAGnC,GAAG,CAAC,MAAM,CAAC;IACvC,MAAMoC,eAAe,GAAGpC,GAAG,CAAC,IAAI,CAAC;IACjC,MAAMqC,WAAW,GAAGrC,GAAG,CAAC,IAAI,CAAC;IAC7B,MAAMsC,YAAY,GAAGtC,GAAG,CAAC,EAAE,CAAC;IAC5B,MAAMuC,eAAe,GAAGvC,GAAG,CAAC,KAAK,CAAC;IAClC,MAAMwC,mBAAmB,GAAGxC,GAAG,CAAC,CAAC,CAAC;IAClC,MAAMyC,aAAa,GAAGzC,GAAG,CAAC,CAAC,CAAC;;IAE5B;IACA,MAAM0C,mBAAmB,GAAG1C,GAAG,CAAC,KAAK,CAAC;IACtC,MAAM2C,QAAQ,GAAG3C,GAAG,CAAC,EAAE,CAAC;IACxB,MAAM4C,aAAa,GAAG5C,GAAG,CAAC,KAAK,CAAC;;IAEhC;IACA,MAAM6C,oBAAoB,GAAG7C,GAAG,CAAC,KAAK,CAAC;IACvC,MAAM8C,WAAW,GAAG9C,GAAG,CAAC,EAAE,CAAC;IAC3B,MAAM+C,cAAc,GAAG/C,GAAG,CAAC,KAAK,CAAC;IACjC,MAAMgD,cAAc,GAAG/C,QAAQ,CAAC;MAC9BgD,cAAc,EAAE,CAAC;MACjBC,eAAe,EAAE,CAAC;MAClBC,SAAS,EAAE,CAAC;MACZC,aAAa,EAAE;IACjB,CAAC,CAAC;;IAEF;IACA,MAAMC,iBAAiB,GAAGrD,GAAG,CAAC,CAAC,CAAC,CAAC;IACjC,MAAMsD,cAAc,GAAGtD,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEhC;IACA,MAAMuD,gBAAgB,GAAIC,MAAM,IAAK;MACnC,IAAI,CAACA,MAAM,EAAE,OAAO,KAAK;MACzB,OAAOF,cAAc,CAACG,KAAK,CAACD,MAAM,CAAC,KAAK,IAAI;IAC9C,CAAC;;IAED;IACA,MAAME,UAAU,GAAGzD,QAAQ,CAAC;MAC1B0D,KAAK,EAAE;IACT,CAAC,CAAC;;IAEF;IACA,MAAMC,QAAQ,GAAG3D,QAAQ,CAAC;MACxB4D,EAAE,EAAE,IAAI;MACRF,KAAK,EAAE,EAAE;MACTG,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE;IACf,CAAC,CAAC;;IAEF;IACA,MAAMC,SAAS,GAAG;MAChBP,KAAK,EAAE,CACL;QAAEQ,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,CACxD;MACDN,QAAQ,EAAE,CACR;QAAEI,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,EACvD;QAAEC,IAAI,EAAE,QAAQ;QAAEC,GAAG,EAAE,CAAC;QAAEH,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE;MAAO,CAAC,CAClE;MACDL,UAAU,EAAE,CACV;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,EACvD;QAAEC,IAAI,EAAE,QAAQ;QAAEC,GAAG,EAAE,CAAC;QAAEH,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE;MAAO,CAAC,CAClE;MACDJ,WAAW,EAAE,CACX;QAAEE,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEC,IAAI,EAAE,QAAQ;QAAEC,GAAG,EAAE,CAAC;QAAEH,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC;IAEnE,CAAC;;IAED;IACA,MAAMG,YAAY,GAAGvE,QAAQ,CAAC;MAC5B4D,EAAE,EAAE,IAAI;MACRY,QAAQ,EAAE,EAAE;MACZC,aAAa,EAAE,KAAK;MACpBC,cAAc,EAAE,EAAE;MAClBC,KAAK,EAAE,CAAC;MACRC,OAAO,EAAE,CACP;QAAEC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,EAC9B;QAAED,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,EAC9B;QAAED,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,EAC9B;QAAED,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC;IAElC,CAAC,CAAC;;IAEF;IACA,MAAMC,aAAa,GAAG;MACpBP,QAAQ,EAAE,CACR;QAAEN,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,CACxD;MACDK,aAAa,EAAE,CACb;QAAEP,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAS,CAAC,CAC1D;MACDO,KAAK,EAAE,CACL;QAAET,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEC,IAAI,EAAE,QAAQ;QAAEC,GAAG,EAAE,CAAC;QAAEH,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC;IAEnE,CAAC;;IAED;IACA,MAAMY,QAAQ,GAAGjF,GAAG,CAAC,EAAE,CAAC;;IAExB;IACA,MAAMkF,aAAa,GAAG,CACpB;MACErB,EAAE,EAAE,CAAC;MACLY,QAAQ,EAAE,gBAAgB;MAC1BI,OAAO,EAAE,CACP;QAAEC,IAAI,EAAE,eAAe;QAAEC,SAAS,EAAE;MAAK,CAAC,EAC1C;QAAED,IAAI,EAAE,eAAe;QAAEC,SAAS,EAAE;MAAM,CAAC,EAC3C;QAAED,IAAI,EAAE,eAAe;QAAEC,SAAS,EAAE;MAAM,CAAC,EAC3C;QAAED,IAAI,EAAE,eAAe;QAAEC,SAAS,EAAE;MAAM,CAAC,CAC5C;MACDJ,cAAc,EAAE,GAAG;MACnBD,aAAa,EAAE,KAAK;MACpBE,KAAK,EAAE,CAAC;MACRO,OAAO,EAAE;IACX,CAAC,EACD;MACEtB,EAAE,EAAE,CAAC;MACLY,QAAQ,EAAE,yBAAyB;MACnCI,OAAO,EAAE,CACP;QAAEC,IAAI,EAAE,OAAO;QAAEC,SAAS,EAAE;MAAK,CAAC,EAClC;QAAED,IAAI,EAAE,SAAS;QAAEC,SAAS,EAAE;MAAK,CAAC,EACpC;QAAED,IAAI,EAAE,QAAQ;QAAEC,SAAS,EAAE;MAAM,CAAC,EACpC;QAAED,IAAI,EAAE,OAAO;QAAEC,SAAS,EAAE;MAAM,CAAC,CACpC;MACDJ,cAAc,EAAE,IAAI;MACpBD,aAAa,EAAE,KAAK;MACpBE,KAAK,EAAE,EAAE;MACTO,OAAO,EAAE;IACX,CAAC,EACD;MACEtB,EAAE,EAAE,CAAC;MACLY,QAAQ,EAAE,eAAe;MACzBI,OAAO,EAAE,EAAE;MACXF,cAAc,EAAE,IAAI;MACpBD,aAAa,EAAE,KAAK;MACpBE,KAAK,EAAE,CAAC;MACRO,OAAO,EAAE;IACX,CAAC,EACD;MACEtB,EAAE,EAAE,CAAC;MACLY,QAAQ,EAAE,aAAa;MACvBI,OAAO,EAAE,EAAE;MACXF,cAAc,EAAE,0DAA0D;MAC1ED,aAAa,EAAE,KAAK;MACpBE,KAAK,EAAE,EAAE;MACTO,OAAO,EAAE;IACX,CAAC,CACF;;IAED;IACA,MAAMC,WAAW,GAAG,CAClB;MACEvB,EAAE,EAAE,CAAC;MACLwB,UAAU,EAAE,CAAC;MACbC,YAAY,EAAE,IAAI;MAClBH,OAAO,EAAE,CAAC;MACVP,KAAK,EAAE,EAAE;MACTW,SAAS,EAAE;IACb,CAAC,EACD;MACE1B,EAAE,EAAE,CAAC;MACLwB,UAAU,EAAE,CAAC;MACbC,YAAY,EAAE,IAAI;MAClBH,OAAO,EAAE,CAAC;MACVP,KAAK,EAAE,EAAE;MACTW,SAAS,EAAE;IACb,CAAC,EACD;MACE1B,EAAE,EAAE,CAAC;MACLwB,UAAU,EAAE,CAAC;MACbC,YAAY,EAAE,IAAI;MAClBH,OAAO,EAAE,CAAC;MACVP,KAAK,EAAE,EAAE;MACTW,SAAS,EAAE;IACb,CAAC,EACD;MACE1B,EAAE,EAAE,CAAC;MACLwB,UAAU,EAAE,CAAC;MACbC,YAAY,EAAE,IAAI;MAClBH,OAAO,EAAE,CAAC;MACVP,KAAK,EAAE,EAAE;MACTW,SAAS,EAAE;IACb,CAAC,CACF;;IAED;IACAtE,KAAK,CAACwC,KAAK,GAAGwB,QAAQ,CAACO,MAAM;;IAE7B;IACA,MAAMC,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B3E,OAAO,CAAC2C,KAAK,GAAG,IAAI;MACpB,IAAI;QACF,MAAMiC,QAAQ,GAAG,MAAMjF,WAAW,CAACkF,QAAQ,CAAC;UAC1CC,IAAI,EAAE7E,WAAW,CAAC0C,KAAK;UACvBoC,KAAK,EAAE7E,QAAQ,CAACyC,KAAK;UACrBE,KAAK,EAAED,UAAU,CAACC;QACpB,CAAC,CAAC;QAEFsB,QAAQ,CAACxB,KAAK,GAAGiC,QAAQ,CAACI,IAAI,CAACA,IAAI;QACnC7E,KAAK,CAACwC,KAAK,GAAGiC,QAAQ,CAACI,IAAI,CAAC7E,KAAK,IAAIgE,QAAQ,CAACxB,KAAK,CAAC+B,MAAM;;QAE1D;QACA,IAAI1D,SAAS,CAAC2B,KAAK,EAAE;UACnB,MAAMsC,sBAAsB,CAAC,CAAC;QAChC;QAEAjF,OAAO,CAAC2C,KAAK,GAAG,KAAK;MACvB,CAAC,CAAC,OAAOuC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;QAChC5F,SAAS,CAAC4F,KAAK,CAAC,UAAU,CAAC;QAC3BlF,OAAO,CAAC2C,KAAK,GAAG,KAAK;MACvB;IACF,CAAC;;IAED;IACA,MAAMsC,sBAAsB,GAAG,MAAAA,CAAA,KAAY;MACzC,MAAMG,SAAS,GAAG3E,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;MACnD,IAAI,CAAC0E,SAAS,EAAE;QACdD,OAAO,CAACD,KAAK,CAAC,YAAY,CAAC;QAC3B5F,SAAS,CAAC+F,OAAO,CAAC,qBAAqB,CAAC;QACxC;MACF;MAEA,IAAI;QACFF,OAAO,CAACG,GAAG,CAAC,uBAAuB,EAAEF,SAAS,CAAC;QAC/C;QACA,MAAMG,eAAe,GAAGpB,QAAQ,CAACxB,KAAK,CAAC6C,GAAG,CAAC,MAAOC,IAAI,IAAK;UACzD,IAAI;YACFN,OAAO,CAACG,GAAG,CAAC,UAAUG,IAAI,CAAC1C,EAAE,KAAK0C,IAAI,CAAC5C,KAAK,YAAY,CAAC;YACzD,MAAM+B,QAAQ,GAAG,MAAMjF,WAAW,CAAC+F,cAAc,CAACD,IAAI,CAAC1C,EAAE,EAAE;cAAEwB,UAAU,EAAEa;YAAU,CAAC,CAAC;YACrFD,OAAO,CAACG,GAAG,CAAC,MAAMG,IAAI,CAAC1C,EAAE,QAAQ,EAAE6B,QAAQ,CAACI,IAAI,CAAC;;YAEjD;YACA,IAAIJ,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACA,IAAI,EAAE;cACvC,MAAMW,OAAO,GAAGf,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACW,OAAO,IAAI,EAAE;cAChD,MAAMC,QAAQ,GAAGD,OAAO,CAACjB,MAAM;cAC/B,MAAMmB,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGH,QAAQ,CAAC;cAE3CT,OAAO,CAACG,GAAG,CAAC,MAAMG,IAAI,CAAC1C,EAAE,KAAK0C,IAAI,CAAC5C,KAAK,UAAU+C,QAAQ,SAASC,SAAS,IAAI,CAAC;cACjFtD,iBAAiB,CAACI,KAAK,CAAC8C,IAAI,CAAC1C,EAAE,CAAC,GAAG8C,SAAS;cAC5CrD,cAAc,CAACG,KAAK,CAAC8C,IAAI,CAAC1C,EAAE,CAAC,GAAG6C,QAAQ,GAAG,CAAC,CAAC,CAAC;;cAE9C;cACA,IAAID,OAAO,CAACjB,MAAM,GAAG,CAAC,EAAE;gBACtBiB,OAAO,CAACK,OAAO,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAK;kBACjCf,OAAO,CAACG,GAAG,CAAC,QAAQY,KAAK,GAAG,CAAC,QAAQD,MAAM,CAACnC,KAAK,OAAOmC,MAAM,CAACxB,SAAS,EAAE,CAAC;gBAC7E,CAAC,CAAC;cACJ;YACF,CAAC,MAAM;cACLU,OAAO,CAACgB,IAAI,CAAC,MAAMV,IAAI,CAAC1C,EAAE,qBAAqB,CAAC;cAChDR,iBAAiB,CAACI,KAAK,CAAC8C,IAAI,CAAC1C,EAAE,CAAC,GAAG,CAAC;cACpCP,cAAc,CAACG,KAAK,CAAC8C,IAAI,CAAC1C,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;YACzC;UACF,CAAC,CAAC,OAAOqD,GAAG,EAAE;YACZjB,OAAO,CAACD,KAAK,CAAC,QAAQO,IAAI,CAAC1C,EAAE,UAAU,EAAEqD,GAAG,CAAC;YAC7C7D,iBAAiB,CAACI,KAAK,CAAC8C,IAAI,CAAC1C,EAAE,CAAC,GAAG,CAAC;YACpCP,cAAc,CAACG,KAAK,CAAC8C,IAAI,CAAC1C,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;UACzC;QACF,CAAC,CAAC;;QAEF;QACA,MAAMsD,OAAO,CAACC,GAAG,CAACf,eAAe,CAAC;QAClCJ,OAAO,CAACG,GAAG,CAAC,eAAe,EAAE/C,iBAAiB,CAACI,KAAK,CAAC;QACrDwC,OAAO,CAACG,GAAG,CAAC,UAAU,EAAE9C,cAAc,CAACG,KAAK,CAAC;MAC/C,CAAC,CAAC,OAAOuC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;QACnC;QACAf,QAAQ,CAACxB,KAAK,CAACqD,OAAO,CAACP,IAAI,IAAI;UAC7BlD,iBAAiB,CAACI,KAAK,CAAC8C,IAAI,CAAC1C,EAAE,CAAC,GAAG,CAAC;UACpCP,cAAc,CAACG,KAAK,CAAC8C,IAAI,CAAC1C,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC;QACzC,CAAC,CAAC;MACJ;IACF,CAAC;;IAED;IACA,MAAMwD,cAAc,GAAIC,GAAG,IAAK;MAC9B;MACAjG,kBAAkB,CAACoC,KAAK,GAAG6D,GAAG,CAACzD,EAAE;;MAEjC;MACA,IAAI,CAACR,iBAAiB,CAACI,KAAK,CAAC6D,GAAG,CAACzD,EAAE,CAAC,IAAIR,iBAAiB,CAACI,KAAK,CAAC6D,GAAG,CAACzD,EAAE,CAAC,KAAK,CAAC,EAAE;QAC7E;QACA,MAAM0D,SAAS,GAAGhG,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;QACnD,IAAI,CAAC+F,SAAS,EAAE;UACdnH,SAAS,CAAC+F,OAAO,CAAC,kBAAkB,CAAC;UACrC9E,kBAAkB,CAACoC,KAAK,GAAG,IAAI;UAC/B;QACF;QAEArD,SAAS,CAACoH,IAAI,CAAC,mBAAmB,CAAC;;QAEnC;QACA/G,WAAW,CAAC+F,cAAc,CAACc,GAAG,CAACzD,EAAE,EAAE;UAAE4D,UAAU,EAAEF;QAAU,CAAC,CAAC,CAC1DG,IAAI,CAAChC,QAAQ,IAAI;UAChBO,OAAO,CAACG,GAAG,CAAC,MAAMkB,GAAG,CAACzD,EAAE,QAAQ,EAAE6B,QAAQ,CAACI,IAAI,CAAC;UAEhD,IAAIJ,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACA,IAAI,EAAE;YACvC,MAAMW,OAAO,GAAGf,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACW,OAAO,IAAI,EAAE;YAChD,MAAMC,QAAQ,GAAGD,OAAO,CAACjB,MAAM;YAC/B,MAAMmB,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGH,QAAQ,CAAC;YAE3CT,OAAO,CAACG,GAAG,CAAC,MAAMkB,GAAG,CAACzD,EAAE,KAAKyD,GAAG,CAAC3D,KAAK,UAAU+C,QAAQ,SAASC,SAAS,IAAI,CAAC;YAC/EtD,iBAAiB,CAACI,KAAK,CAAC6D,GAAG,CAACzD,EAAE,CAAC,GAAG8C,SAAS;YAC3CrD,cAAc,CAACG,KAAK,CAAC6D,GAAG,CAACzD,EAAE,CAAC,GAAG6C,QAAQ,GAAG,CAAC;;YAE3C;YACArF,kBAAkB,CAACoC,KAAK,GAAG,IAAI;YAC/BkE,kBAAkB,CAACL,GAAG,CAAC;UACzB,CAAC,MAAM;YACLrB,OAAO,CAACgB,IAAI,CAAC,MAAMK,GAAG,CAACzD,EAAE,qBAAqB,CAAC;YAC/CR,iBAAiB,CAACI,KAAK,CAAC6D,GAAG,CAACzD,EAAE,CAAC,GAAG,CAAC;YACnCP,cAAc,CAACG,KAAK,CAAC6D,GAAG,CAACzD,EAAE,CAAC,GAAG,KAAK;YACpCxC,kBAAkB,CAACoC,KAAK,GAAG,IAAI;YAC/BkE,kBAAkB,CAACL,GAAG,CAAC;UACzB;QACF,CAAC,CAAC,CACDM,KAAK,CAAC5B,KAAK,IAAI;UACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;UAClC5F,SAAS,CAAC4F,KAAK,CAAC,oBAAoB,CAAC;UACrC3E,kBAAkB,CAACoC,KAAK,GAAG,IAAI;QACjC,CAAC,CAAC;QACJ;MACF;MAEApC,kBAAkB,CAACoC,KAAK,GAAG,IAAI;MAC/BkE,kBAAkB,CAACL,GAAG,CAAC;IACzB,CAAC;;IAED;IACA,MAAMK,kBAAkB,GAAIL,GAAG,IAAK;MAClC;MACA,IAAIjE,iBAAiB,CAACI,KAAK,CAAC6D,GAAG,CAACzD,EAAE,CAAC,KAAK,CAAC,EAAE;QACzCzD,SAAS,CAAC+F,OAAO,CAAC,oBAAoB,CAAC;QACvC;MACF;;MAEA;MACA9F,YAAY,CAACwH,OAAO,CAClB,kBAAkB,CAAC,GAAGxE,iBAAiB,CAACI,KAAK,CAAC6D,GAAG,CAACzD,EAAE,CAAC,SAASR,iBAAiB,CAACI,KAAK,CAAC6D,GAAG,CAACzD,EAAE,CAAC,gBAAgB,EAC7G,MAAM,EACN;QACEiE,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBzD,IAAI,EAAE;MACR,CACF,CAAC,CAACoD,IAAI,CAAC,MAAM;QACX;QACAnG,YAAY,CAACyG,OAAO,CAAC,eAAe,EAAEV,GAAG,CAACzD,EAAE,CAAC;QAC7CtC,YAAY,CAACyG,OAAO,CAAC,kBAAkB,EAAEV,GAAG,CAAC3D,KAAK,CAAC;QACnDpC,YAAY,CAACyG,OAAO,CAAC,8BAA8B,EAAE3E,iBAAiB,CAACI,KAAK,CAAC6D,GAAG,CAACzD,EAAE,CAAC,CAAC;;QAErF;QACAhD,MAAM,CAACoH,IAAI,CAAC,eAAeX,GAAG,CAACzD,EAAE,EAAE,CAAC;MACtC,CAAC,CAAC,CAAC+D,KAAK,CAAC,MAAM;QACb3B,OAAO,CAACG,GAAG,CAAC,UAAU,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAM8B,kBAAkB,GAAG,MAAOZ,GAAG,IAAK;MACxC,IAAI;QACF,MAAMpB,SAAS,GAAG3E,YAAY,CAACC,OAAO,CAAC,WAAW,CAAC;QACnD,IAAI,CAAC0E,SAAS,EAAE;UACd9F,SAAS,CAAC4F,KAAK,CAAC,kBAAkB,CAAC;UACnC;QACF;QAEAC,OAAO,CAACG,GAAG,CAAC,cAAc,EAAEF,SAAS,EAAE,OAAO,EAAEoB,GAAG,CAACzD,EAAE,CAAC;QAEvD,MAAM6B,QAAQ,GAAG,MAAMjF,WAAW,CAAC+F,cAAc,CAACc,GAAG,CAACzD,EAAE,EAAE;UAAEwB,UAAU,EAAEa;QAAU,CAAC,CAAC;QACpFD,OAAO,CAACG,GAAG,CAAC,cAAc,EAAEV,QAAQ,CAACI,IAAI,CAAC;;QAE1C;QACA,IAAIJ,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACW,OAAO,IAAIf,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACW,OAAO,CAACjB,MAAM,GAAG,CAAC,EAAE;UACvE;UACA,MAAM2C,SAAS,GAAGzC,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACW,OAAO,CACzC2B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAChD,UAAU,KAAKiD,QAAQ,CAACpC,SAAS,CAAC,CAAC,CACjDqC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC7D,KAAK,GAAG4D,CAAC,CAAC5D,KAAK,CAAC;UAEpCqB,OAAO,CAACG,GAAG,CAAC,gBAAgB,EAAE+B,SAAS,CAAC;UAExC,IAAIA,SAAS,CAAC3C,MAAM,GAAG,CAAC,EAAE;YACxB;YACA,MAAMkD,YAAY,GAAGP,SAAS,CAAC7B,GAAG,CAAC,CAACS,MAAM,EAAEC,KAAK,KAAK;cACpD,OAAO,2BAA2BD,MAAM,CAACnC,KAAK,IAAI0C,GAAG,CAACtD,UAAU,GAAG,MAAM,GAAG,MAAM;AAC5F,qBAAqBgD,KAAK,GAAG,CAAC;AAC9B,sCAAsCD,MAAM,CAACnC,KAAK,MAAM0C,GAAG,CAACrD,WAAW;AACvE,wCAAwCvD,UAAU,CAACqG,MAAM,CAACxB,SAAS,EAAE,qBAAqB,CAAC;AAC3F,sCAAsCwB,MAAM,CAACnC,KAAK,IAAI0C,GAAG,CAACtD,UAAU,GAAG,IAAI,GAAG,KAAK;AACnF,iBAAiB;YACT,CAAC,CAAC,CAAC2E,IAAI,CAAC,MAAM,CAAC;YAEftI,YAAY,CAACuI,KAAK,CAChB;AACV;AACA,wCAAwCF,YAAY;AACpD;AACA,0BAA0BP,SAAS,CAAC3C,MAAM;AAC1C,yBAAyBoB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAGsB,SAAS,CAAC3C,MAAM,CAAC;AAC1D;AACA;AACA,iBAAiB,EACP,MAAM,EACN;cACEqD,wBAAwB,EAAE,IAAI;cAC9Bf,iBAAiB,EAAE,IAAI;cACvBgB,WAAW,EAAE;YACf,CACF,CAAC;YACD;UACF;QACF;QAEA1I,SAAS,CAACoH,IAAI,CAAC,aAAa,CAAC;MAC/B,CAAC,CAAC,OAAOxB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;QAC9B5F,SAAS,CAAC4F,KAAK,CAAC,UAAU,IAAIA,KAAK,CAACN,QAAQ,EAAEI,IAAI,EAAE1B,OAAO,IAAI4B,KAAK,CAAC5B,OAAO,CAAC,CAAC;MAChF;IACF,CAAC;IAEDlE,SAAS,CAAC,MAAM;MACduF,UAAU,CAAC,CAAC;IACd,CAAC,CAAC;;IAEF;IACA,MAAMsD,WAAW,GAAGA,CAAA,KAAM;MACxBrF,UAAU,CAACC,KAAK,GAAG,EAAE;MACrBqF,YAAY,CAAC,CAAC;IAChB,CAAC;;IAED;IACA,MAAMA,YAAY,GAAGA,CAAA,KAAM;MACzBjI,WAAW,CAAC0C,KAAK,GAAG,CAAC;MACrBgC,UAAU,CAAC,CAAC;IACd,CAAC;;IAED;IACA,MAAMwD,aAAa,GAAGA,CAAA,KAAM;MAC1B9H,WAAW,CAACsC,KAAK,GAAG,MAAM;MAC1BvC,aAAa,CAACuC,KAAK,GAAG,IAAI;MAC1B;MACAG,QAAQ,CAACC,EAAE,GAAG,IAAI;MAClBD,QAAQ,CAACD,KAAK,GAAG,EAAE;MACnBC,QAAQ,CAACE,WAAW,GAAG,EAAE;MACzBF,QAAQ,CAACG,QAAQ,GAAG,EAAE;MACtBH,QAAQ,CAACI,UAAU,GAAG,EAAE;MACxBJ,QAAQ,CAACK,WAAW,GAAG,GAAG;IAC5B,CAAC;;IAED;IACA,MAAMiF,UAAU,GAAI5B,GAAG,IAAK;MAC1BnG,WAAW,CAACsC,KAAK,GAAG,MAAM;MAC1BvC,aAAa,CAACuC,KAAK,GAAG,IAAI;MAC1B;MACA0F,MAAM,CAACC,IAAI,CAACxF,QAAQ,CAAC,CAACkD,OAAO,CAACuC,GAAG,IAAI;QACnCzF,QAAQ,CAACyF,GAAG,CAAC,GAAG/B,GAAG,CAAC+B,GAAG,CAAC;MAC1B,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMC,YAAY,GAAIhC,GAAG,IAAK;MAC5BjH,YAAY,CAACwH,OAAO,CAAC,WAAWP,GAAG,CAAC3D,KAAK,KAAK,EAAE,IAAI,EAAE;QACpDmE,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBzD,IAAI,EAAE;MACR,CAAC,CAAC,CAACoD,IAAI,CAAC,YAAY;QAClB,IAAI;UACF,MAAMjH,WAAW,CAAC8I,UAAU,CAACjC,GAAG,CAACzD,EAAE,CAAC;UACpCzD,SAAS,CAACoJ,OAAO,CAAC,MAAMlC,GAAG,CAAC3D,KAAK,MAAM,CAAC;UACxC8B,UAAU,CAAC,CAAC,EAAC;QACf,CAAC,CAAC,OAAOO,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;UAC9B5F,SAAS,CAAC4F,KAAK,CAAC,YAAY,CAAC;QAC/B;MACF,CAAC,CAAC,CAAC4B,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED;IACA,MAAM6B,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI,CAACrI,WAAW,CAACqC,KAAK,EAAE;MAExB,MAAMrC,WAAW,CAACqC,KAAK,CAACiG,QAAQ,CAAC,MAAOC,KAAK,IAAK;QAChD,IAAIA,KAAK,EAAE;UACT,IAAI;YACF,IAAI/F,QAAQ,CAACC,EAAE,EAAE;cACf;cACA,MAAMpD,WAAW,CAACmJ,UAAU,CAAChG,QAAQ,CAACC,EAAE,EAAED,QAAQ,CAAC;cACnDxD,SAAS,CAACoJ,OAAO,CAAC,MAAM5F,QAAQ,CAACD,KAAK,QAAQ,CAAC;YACjD,CAAC,MAAM;cACL;cACA,MAAMlD,WAAW,CAACoJ,UAAU,CAACjG,QAAQ,CAAC;cACtCxD,SAAS,CAACoJ,OAAO,CAAC,MAAM5F,QAAQ,CAACD,KAAK,OAAO,CAAC;YAChD;YACAzC,aAAa,CAACuC,KAAK,GAAG,KAAK;YAC3BgC,UAAU,CAAC,CAAC,EAAC;UACf,CAAC,CAAC,OAAOO,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;YAC9B5F,SAAS,CAAC4F,KAAK,CAAC,YAAY,CAAC;UAC/B;QACF,CAAC,MAAM;UACL,OAAO,KAAK;QACd;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAM8D,qBAAqB,GAAIxC,GAAG,IAAK;MACrCjF,WAAW,CAACoB,KAAK,GAAG6D,GAAG;MACvBrF,qBAAqB,CAACwB,KAAK,GAAG,IAAI;MAClC;MACAjB,mBAAmB,CAACiB,KAAK,GAAG,CAAC;;MAE7B;MACAsG,aAAa,CAACzC,GAAG,CAACzD,EAAE,CAAC;;MAErB;MACAmG,UAAU,CAAC,MAAM;QACf,MAAMC,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAAC,mCAAmC,CAAC;QACjF,IAAIF,aAAa,EAAE;UACjBA,aAAa,CAACG,KAAK,CAACC,MAAM,GAAG,OAAO;QACtC;MACF,CAAC,EAAE,GAAG,CAAC;IACT,CAAC;;IAED;IACA,MAAMN,aAAa,GAAG,MAAOvG,MAAM,IAAK;MACtCjB,eAAe,CAACkB,KAAK,GAAG,IAAI;MAE5B,IAAI;QACF;QACA,MAAMiC,QAAQ,GAAG,MAAMjF,WAAW,CAAC6J,gBAAgB,CAAC9G,MAAM,CAAC;;QAE3D;QACAyC,OAAO,CAACG,GAAG,CAAC,WAAW,EAAEV,QAAQ,CAACI,IAAI,CAAC;;QAEvC;QACA,IAAIJ,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACA,IAAI,EAAE;UACvCxD,YAAY,CAACmB,KAAK,GAAGiC,QAAQ,CAACI,IAAI,CAACA,IAAI;UACvC;UACArD,aAAa,CAACgB,KAAK,GAAGiC,QAAQ,CAACI,IAAI,CAACyE,KAAK,IAAIjI,YAAY,CAACmB,KAAK,CAAC+B,MAAM;UACtES,OAAO,CAACG,GAAG,CAAC,OAAO9D,YAAY,CAACmB,KAAK,CAAC+B,MAAM,MAAM,CAAC;QACrD,CAAC,MAAM;UACLlD,YAAY,CAACmB,KAAK,GAAG,EAAE;UACvBhB,aAAa,CAACgB,KAAK,GAAG,CAAC;UACvBwC,OAAO,CAACgB,IAAI,CAAC,SAAS,CAAC;QACzB;QAEA1E,eAAe,CAACkB,KAAK,GAAG,KAAK;MAC/B,CAAC,CAAC,OAAOuC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;QAC9B5F,SAAS,CAAC4F,KAAK,CAAC,QAAQ,CAAC;QACzBzD,eAAe,CAACkB,KAAK,GAAG,KAAK;MAC/B;IACF,CAAC;;IAED;IACA,MAAM+G,iBAAiB,GAAGA,CAAA,KAAM;MAC9BrI,mBAAmB,CAACsB,KAAK,GAAG,MAAM;MAClCvB,yBAAyB,CAACuB,KAAK,GAAG,IAAI;MACtC;MACAe,YAAY,CAACX,EAAE,GAAG,IAAI;MACtBW,YAAY,CAACC,QAAQ,GAAG,EAAE;MAC1BD,YAAY,CAACE,aAAa,GAAG,KAAK;MAClCF,YAAY,CAACG,cAAc,GAAG,EAAE;MAChCH,YAAY,CAACI,KAAK,GAAG,CAAC;MACtBJ,YAAY,CAACK,OAAO,GAAG,CACrB;QAAEC,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,EAC9B;QAAED,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,EAC9B;QAAED,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,EAC9B;QAAED,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAC/B;IACH,CAAC;;IAED;IACA,MAAM0F,kBAAkB,GAAInD,GAAG,IAAK;MAClCnF,mBAAmB,CAACsB,KAAK,GAAG,MAAM;MAClCvB,yBAAyB,CAACuB,KAAK,GAAG,IAAI;;MAEtC;MACAe,YAAY,CAACX,EAAE,GAAGyD,GAAG,CAACzD,EAAE;MACxBW,YAAY,CAACC,QAAQ,GAAG6C,GAAG,CAAC7C,QAAQ;MACpCD,YAAY,CAACE,aAAa,GAAG4C,GAAG,CAAC5C,aAAa;MAC9CF,YAAY,CAACG,cAAc,GAAG2C,GAAG,CAAC3C,cAAc;MAChDH,YAAY,CAACI,KAAK,GAAG0C,GAAG,CAAC1C,KAAK;;MAE9B;MACA,IAAI0C,GAAG,CAAC5C,aAAa,KAAK,KAAK,IAAI4C,GAAG,CAAC5C,aAAa,KAAK,KAAK,EAAE;QAC9DF,YAAY,CAACK,OAAO,GAAGyC,GAAG,CAACzC,OAAO,GAAG,CAAC,GAAGyC,GAAG,CAACzC,OAAO,CAAC,GAAG,EAAE;MAC5D,CAAC,MAAM,IAAIyC,GAAG,CAAC5C,aAAa,KAAK,KAAK,EAAE;QACtCF,YAAY,CAACG,cAAc,GAAG2C,GAAG,CAAC3C,cAAc;MAClD;IACF,CAAC;;IAED;IACA,MAAM+F,oBAAoB,GAAIpD,GAAG,IAAK;MACpCjH,YAAY,CAACwH,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE;QACvCC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBzD,IAAI,EAAE;MACR,CAAC,CAAC,CAACoD,IAAI,CAAC,YAAY;QAClB,IAAI;UACF,MAAMjH,WAAW,CAACkK,cAAc,CAACrD,GAAG,CAACzD,EAAE,CAAC;UACxCzD,SAAS,CAACoJ,OAAO,CAAC,OAAO,CAAC;UAC1B;UACAO,aAAa,CAAC1H,WAAW,CAACoB,KAAK,CAACI,EAAE,CAAC;QACrC,CAAC,CAAC,OAAOmC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;UAC9B5F,SAAS,CAAC4F,KAAK,CAAC,YAAY,CAAC;QAC/B;MACF,CAAC,CAAC,CAAC4B,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED;IACA,MAAMgD,qBAAqB,GAAGA,CAAA,KAAM;MAClClI,mBAAmB,CAACe,KAAK,GAAG,IAAI;MAChCd,QAAQ,CAACc,KAAK,GAAG,EAAE;IACrB,CAAC;;IAED;IACA,MAAMoH,YAAY,GAAIC,IAAI,IAAK;MAC7B,MAAMC,SAAS,GACbD,IAAI,CAACxG,IAAI,KAAK,oBAAoB,IAClCwG,IAAI,CAACxG,IAAI,KAAK,yEAAyE;MAEzF,IAAI,CAACyG,SAAS,EAAE;QACd3K,SAAS,CAAC4F,KAAK,CAAC,eAAe,CAAC;QAChC,OAAO,KAAK;MACd;MAEA,MAAMgF,OAAO,GAAGF,IAAI,CAACG,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE;MAE5C,IAAI,CAACD,OAAO,EAAE;QACZ5K,SAAS,CAAC4F,KAAK,CAAC,eAAe,CAAC;QAChC,OAAO,KAAK;MACd;MAEA,OAAO,IAAI;IACb,CAAC;;IAED;IACA,MAAMkF,gBAAgB,GAAGA,CAACJ,IAAI,EAAEK,cAAc,KAAK;MACjD;MACAlF,OAAO,CAACG,GAAG,CAAC,SAAS,EAAE0E,IAAI,EAAEK,cAAc,CAAC;MAC5CxI,QAAQ,CAACc,KAAK,GAAG0H,cAAc;IACjC,CAAC;;IAED;IACA,MAAMC,gBAAgB,GAAIvG,OAAO,IAAK;MACpC,MAAM;QAAEiG;MAAK,CAAC,GAAGjG,OAAO;MACxB;MACA;MACA,OAAO,KAAK,EAAC;IACf,CAAC;;IAED;IACA,MAAMwG,YAAY,GAAG,MAAAA,CAAA,KAAY;MAC/BpF,OAAO,CAACG,GAAG,CAAC,SAAS,EAAEzD,QAAQ,CAACc,KAAK,CAAC;MACtC,IAAI,CAACd,QAAQ,CAACc,KAAK,IAAId,QAAQ,CAACc,KAAK,CAAC+B,MAAM,KAAK,CAAC,EAAE;QAClDpF,SAAS,CAAC+F,OAAO,CAAC,WAAW,CAAC;QAC9B;MACF;MAEAvD,aAAa,CAACa,KAAK,GAAG,IAAI;MAE1B,IAAI;QACF;QACA,MAAM6H,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/B;QACA,MAAMC,UAAU,GAAG7I,QAAQ,CAACc,KAAK,CAAC,CAAC,CAAC;QACpC,MAAMgI,OAAO,GAAGD,UAAU,CAACE,GAAG,IAAIF,UAAU;QAC5CvF,OAAO,CAACG,GAAG,CAAC,OAAO,EAAEqF,OAAO,CAAC;;QAE7B;QACAH,QAAQ,CAACK,MAAM,CAAC,UAAU,EAAEF,OAAO,CAAC;;QAEpC;QACA,MAAM/F,QAAQ,GAAG,MAAMjF,WAAW,CAACmL,uBAAuB,CAACvJ,WAAW,CAACoB,KAAK,CAACI,EAAE,EAAEyH,QAAQ,CAAC;;QAE1F;QACAlL,SAAS,CAACoJ,OAAO,CAAC,aAAa9D,QAAQ,CAACI,IAAI,CAACyE,KAAK,IAAI,CAAC,KAAK,CAAC;QAC7D7H,mBAAmB,CAACe,KAAK,GAAG,KAAK;;QAEjC;QACAsG,aAAa,CAAC1H,WAAW,CAACoB,KAAK,CAACI,EAAE,CAAC;MACrC,CAAC,CAAC,OAAOmC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;QAC9B,IAAI6F,QAAQ,GAAG,QAAQ;;QAEvB;QACA,IAAI7F,KAAK,CAACN,QAAQ,IAAIM,KAAK,CAACN,QAAQ,CAACI,IAAI,IAAIE,KAAK,CAACN,QAAQ,CAACI,IAAI,CAAC1B,OAAO,EAAE;UACxEyH,QAAQ,IAAI,IAAI7F,KAAK,CAACN,QAAQ,CAACI,IAAI,CAAC1B,OAAO,EAAE;QAC/C;QAEAhE,SAAS,CAAC4F,KAAK,CAAC6F,QAAQ,CAAC;MAC3B,CAAC,SAAS;QACRjJ,aAAa,CAACa,KAAK,GAAG,KAAK;MAC7B;IACF,CAAC;;IAED;IACA,MAAMqI,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF1L,SAAS,CAACoJ,OAAO,CAAC,UAAU,CAAC;;QAE7B;QACA,MAAMuC,IAAI,GAAG7B,QAAQ,CAAC8B,aAAa,CAAC,GAAG,CAAC;QACxC;QACAD,IAAI,CAACE,IAAI,GAAG,GAAGC,MAAM,CAACC,IAAI,CAACC,GAAG,CAACC,YAAY,IAAI,uBAAuB,8BAA8B;;QAEpG;QACA,MAAMC,KAAK,GAAG/K,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAI8K,KAAK,EAAE;UACTP,IAAI,CAACE,IAAI,IAAI,UAAUK,KAAK,EAAE;QAChC;QAEAP,IAAI,CAACQ,QAAQ,GAAG,aAAa;QAC7BrC,QAAQ,CAACsC,IAAI,CAACC,WAAW,CAACV,IAAI,CAAC;QAC/BA,IAAI,CAACW,KAAK,CAAC,CAAC;QACZxC,QAAQ,CAACsC,IAAI,CAACG,WAAW,CAACZ,IAAI,CAAC;MACjC,CAAC,CAAC,OAAO/F,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;QAC9B5F,SAAS,CAAC4F,KAAK,CAAC,YAAY,CAAC;MAC/B;IACF,CAAC;;IAED;IACA,MAAM4G,SAAS,GAAGA,CAAA,KAAM;MACtBpI,YAAY,CAACK,OAAO,CAACoD,IAAI,CAAC;QAAEnD,IAAI,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAM,CAAC,CAAC;IAC3D,CAAC;;IAED;IACA,MAAM8H,YAAY,GAAI7F,KAAK,IAAK;MAC9B,IAAIxC,YAAY,CAACK,OAAO,CAACW,MAAM,IAAI,CAAC,EAAE;QACpCpF,SAAS,CAAC+F,OAAO,CAAC,UAAU,CAAC;QAC7B;MACF;MACA3B,YAAY,CAACK,OAAO,CAACiI,MAAM,CAAC9F,KAAK,EAAE,CAAC,CAAC;IACvC,CAAC;;IAED;IACA,MAAM+F,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACrC,IAAI,CAAC3K,eAAe,CAACqB,KAAK,EAAE;MAE5B,MAAMrB,eAAe,CAACqB,KAAK,CAACiG,QAAQ,CAAC,MAAOC,KAAK,IAAK;QACpD,IAAIA,KAAK,EAAE;UACT;UACA,IAAKnF,YAAY,CAACE,aAAa,KAAK,KAAK,IAAIF,YAAY,CAACE,aAAa,KAAK,KAAK,EAAG;YAClF;YACA,MAAMsI,WAAW,GAAGxI,YAAY,CAACK,OAAO,CAACoI,IAAI,CAACC,GAAG,IAAI,CAACA,GAAG,CAACpI,IAAI,CAACqI,IAAI,CAAC,CAAC,CAAC;YACtE,IAAIH,WAAW,EAAE;cACf5M,SAAS,CAAC4F,KAAK,CAAC,UAAU,CAAC;cAC3B;YACF;;YAEA;YACA,MAAMoH,UAAU,GAAG5I,YAAY,CAACK,OAAO,CAACwI,IAAI,CAACH,GAAG,IAAIA,GAAG,CAACnI,SAAS,CAAC;YAClE,IAAI,CAACqI,UAAU,EAAE;cACfhN,SAAS,CAAC4F,KAAK,CAAC,aAAa,CAAC;cAC9B;YACF;;YAEA;YACA,IAAIxB,YAAY,CAACE,aAAa,KAAK,KAAK,EAAE;cACxC,MAAM4I,YAAY,GAAG9I,YAAY,CAACK,OAAO,CAACuD,MAAM,CAAC8E,GAAG,IAAIA,GAAG,CAACnI,SAAS,CAAC,CAACS,MAAM;cAC7E,IAAI8H,YAAY,GAAG,CAAC,EAAE;gBACpBlN,SAAS,CAAC4F,KAAK,CAAC,cAAc,CAAC;gBAC/B;cACF;YACF;UACF;UAEA,IAAI;YACF;YACA,MAAMuH,UAAU,GAAG;cAAE,GAAG/I;YAAa,CAAC;;YAEtC;YACA,IAAIA,YAAY,CAACE,aAAa,KAAK,KAAK,EAAE;cACxC,MAAM8I,aAAa,GAAGhJ,YAAY,CAACK,OAAO,CAACoI,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACnI,SAAS,CAAC;cACrE,IAAIyI,aAAa,EAAE;gBACjB;gBACA,MAAMxG,KAAK,GAAGxC,YAAY,CAACK,OAAO,CAAC4I,SAAS,CAACP,GAAG,IAAIA,GAAG,CAACnI,SAAS,CAAC;gBAClEwI,UAAU,CAAC5I,cAAc,GAAG+I,MAAM,CAACC,YAAY,CAAC,EAAE,GAAG3G,KAAK,CAAC,EAAC;cAC9D;YACF,CAAC,MAAM,IAAIxC,YAAY,CAACE,aAAa,KAAK,KAAK,EAAE;cAC/C;cACA,MAAMkJ,cAAc,GAAGpJ,YAAY,CAACK,OAAO,CACxCyB,GAAG,CAAC,CAAC4G,GAAG,EAAElG,KAAK,KAAKkG,GAAG,CAACnI,SAAS,GAAG2I,MAAM,CAACC,YAAY,CAAC,EAAE,GAAG3G,KAAK,CAAC,GAAG,IAAI,CAAC,CAC3EoB,MAAM,CAACyF,OAAO,CAAC,CACflF,IAAI,CAAC,EAAE,CAAC;cACX4E,UAAU,CAAC5I,cAAc,GAAGiJ,cAAc;YAC5C;;YAEA;YACA,IAAIpJ,YAAY,CAACX,EAAE,EAAE;cACnB;cACA,MAAMpD,WAAW,CAACqN,cAAc,CAACtJ,YAAY,CAACX,EAAE,EAAE0J,UAAU,CAAC;cAC7DnN,SAAS,CAACoJ,OAAO,CAAC,QAAQ,CAAC;YAC7B,CAAC,MAAM;cACL;cACA,MAAM/I,WAAW,CAACsN,cAAc,CAAC1L,WAAW,CAACoB,KAAK,CAACI,EAAE,EAAE0J,UAAU,CAAC;cAClEnN,SAAS,CAACoJ,OAAO,CAAC,QAAQ,CAAC;YAC7B;YAEAtH,yBAAyB,CAACuB,KAAK,GAAG,KAAK;YACvC;YACAsG,aAAa,CAAC1H,WAAW,CAACoB,KAAK,CAACI,EAAE,CAAC;UACrC,CAAC,CAAC,OAAOmC,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAAC;YAC9B5F,SAAS,CAAC4F,KAAK,CAAC,YAAY,CAAC;UAC/B;QACF,CAAC,MAAM;UACL,OAAO,KAAK;QACd;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMgI,qBAAqB,GAAIC,OAAO,IAAK;MACzC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;MACvB;MACA,OAAOA,OAAO,CAACzI,MAAM,GAAG,GAAG,GAAGyI,OAAO,CAACC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,GAAGD,OAAO;IAC3E,CAAC;;IAED;;IAEA;IACA,MAAME,iBAAiB,GAAG,MAAO7G,GAAG,IAAK;MACvCjF,WAAW,CAACoB,KAAK,GAAG6D,GAAG;MACvBzE,oBAAoB,CAACY,KAAK,GAAG,IAAI;MACjCV,cAAc,CAACU,KAAK,GAAG,IAAI;MAE3B,IAAI;QACF,MAAMiC,QAAQ,GAAG,MAAMjF,WAAW,CAAC+F,cAAc,CAACc,GAAG,CAACzD,EAAE,CAAC;QACzD,MAAMiC,IAAI,GAAGJ,QAAQ,CAACI,IAAI,CAACA,IAAI;QAE/BhD,WAAW,CAACW,KAAK,GAAGqC,IAAI,CAACW,OAAO,IAAI,EAAE;;QAEtC;QACAzD,cAAc,CAACC,cAAc,GAAG6C,IAAI,CAACsI,OAAO,CAACnL,cAAc,IAAI,CAAC;QAChED,cAAc,CAACE,eAAe,GAAG4C,IAAI,CAACsI,OAAO,CAAClL,eAAe,IAAI,CAAC;QAClEF,cAAc,CAACG,SAAS,GAAG2C,IAAI,CAACsI,OAAO,CAACjL,SAAS,IAAI,CAAC;QACtDH,cAAc,CAACI,aAAa,GAAG0C,IAAI,CAACsI,OAAO,CAAChL,aAAa,IAAI,CAAC;QAE9DL,cAAc,CAACU,KAAK,GAAG,KAAK;MAC9B,CAAC,CAAC,OAAOuC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;QAChC5F,SAAS,CAAC4F,KAAK,CAAC,UAAU,CAAC;QAC3BjD,cAAc,CAACU,KAAK,GAAG,KAAK;MAC9B;IACF,CAAC;;IAED;IACA,MAAM4K,gBAAgB,GAAI/G,GAAG,IAAK;MAChClH,SAAS,CAACoJ,OAAO,CAAC,MAAMlC,GAAG,CAAChC,YAAY,QAAQ,CAAC;IACnD,CAAC;;IAED;IACA,MAAMgJ,gBAAgB,GAAIrD,IAAI,IAAK;MACjCjK,QAAQ,CAACyC,KAAK,GAAGwH,IAAI;MACrBlK,WAAW,CAAC0C,KAAK,GAAG,CAAC;MACrBgC,UAAU,CAAC,CAAC;IACd,CAAC;IAED,MAAM8I,mBAAmB,GAAI3I,IAAI,IAAK;MACpC7E,WAAW,CAAC0C,KAAK,GAAGmC,IAAI;MACxBH,UAAU,CAAC,CAAC;IACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}