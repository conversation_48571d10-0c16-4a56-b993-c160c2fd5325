const { pool } = require('../config/db');

class Rotation {
  // 创建新的轮转记录
  static async create(rotationData) {
    try {
      const [result] = await pool.query(
        `INSERT INTO rotation_records 
         (student_id, department, start_date, end_date, entry_exam_content, 
          entry_exam_score, exit_exam_content, exit_exam_score, department_rating, notes) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          rotationData.student_id,
          rotationData.department,
          rotationData.start_date,
          rotationData.end_date,
          rotationData.entry_exam_content,
          rotationData.entry_exam_score,
          rotationData.exit_exam_content,
          rotationData.exit_exam_score,
          rotationData.department_rating,
          rotationData.notes
        ]
      );
      return result.insertId;
    } catch (error) {
      console.error('创建轮转记录失败:', error);
      throw error;
    }
  }

  // 获取学生的所有轮转记录
  static async findByStudentId(student_id) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM rotation_records WHERE student_id = ? ORDER BY start_date',
        [student_id]
      );
      return rows;
    } catch (error) {
      console.error('获取学生轮转记录失败:', error);
      throw error;
    }
  }

  // 获取特定科室的所有学生轮转记录
  static async findByDepartment(department) {
    try {
      const [rows] = await pool.query(
        `SELECT r.*, s.name, s.school, s.major
         FROM rotation_records r
         JOIN students s ON r.student_id = s.id
         WHERE r.department = ?
         ORDER BY r.start_date DESC`,
        [department]
      );
      return rows;
    } catch (error) {
      console.error('获取科室轮转记录失败:', error);
      throw error;
    }
  }

  // 根据ID获取轮转记录
  static async findById(id) {
    try {
      const [rows] = await pool.query(
        `SELECT r.*, s.name as student_name
         FROM rotation_records r
         JOIN students s ON r.student_id = s.id
         WHERE r.id = ?`,
        [id]
      );
      return rows[0];
    } catch (error) {
      console.error('获取轮转记录失败:', error);
      throw error;
    }
  }

  // 更新轮转记录
  static async update(id, rotationData) {
    try {
      const [result] = await pool.query(
        `UPDATE rotation_records SET 
         department = ?, start_date = ?, end_date = ?, 
         entry_exam_content = ?, entry_exam_score = ?, 
         exit_exam_content = ?, exit_exam_score = ?, 
         department_rating = ?, notes = ? 
         WHERE id = ?`,
        [
          rotationData.department,
          rotationData.start_date,
          rotationData.end_date,
          rotationData.entry_exam_content,
          rotationData.entry_exam_score,
          rotationData.exit_exam_content,
          rotationData.exit_exam_score,
          rotationData.department_rating,
          rotationData.notes,
          id
        ]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新轮转记录失败:', error);
      throw error;
    }
  }

  // 删除轮转记录
  static async delete(id) {
    try {
      const [result] = await pool.query('DELETE FROM rotation_records WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除轮转记录失败:', error);
      throw error;
    }
  }
}

class GraduationExam {
  // 创建结业考核记录
  static async create(examData) {
    try {
      const [result] = await pool.query(
        `INSERT INTO graduation_exams 
         (student_id, theory_score, practice_score, final_score, exam_date, notes) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [
          examData.student_id,
          examData.theory_score,
          examData.practice_score,
          examData.final_score,
          examData.exam_date,
          examData.notes
        ]
      );
      return result.insertId;
    } catch (error) {
      console.error('创建结业考核记录失败:', error);
      throw error;
    }
  }

  // 获取所有结业考核记录
  static async findAll() {
    try {
      const [rows] = await pool.query(
        `SELECT g.*, s.name, s.school, s.major
         FROM graduation_exams g
         JOIN students s ON g.student_id = s.id
         ORDER BY g.final_score DESC`
      );
      return rows;
    } catch (error) {
      console.error('获取结业考核记录失败:', error);
      throw error;
    }
  }

  // 根据学生ID获取结业考核记录
  static async findByStudentId(student_id) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM graduation_exams WHERE student_id = ?',
        [student_id]
      );
      return rows[0]; // 一个学生只有一条结业考核记录
    } catch (error) {
      console.error('获取学生结业考核记录失败:', error);
      throw error;
    }
  }

  // 根据ID获取结业考核记录
  static async findById(id) {
    try {
      const [rows] = await pool.query(
        `SELECT g.*, s.name as student_name, s.school, s.major
         FROM graduation_exams g
         JOIN students s ON g.student_id = s.id
         WHERE g.id = ?`,
        [id]
      );
      return rows[0];
    } catch (error) {
      console.error('获取结业考核记录失败:', error);
      throw error;
    }
  }

  // 更新结业考核记录
  static async update(id, examData) {
    try {
      const [result] = await pool.query(
        `UPDATE graduation_exams SET 
         theory_score = ?, practice_score = ?, final_score = ?, 
         exam_date = ?, notes = ? WHERE id = ?`,
        [
          examData.theory_score,
          examData.practice_score,
          examData.final_score,
          examData.exam_date,
          examData.notes,
          id
        ]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新结业考核记录失败:', error);
      throw error;
    }
  }

  // 删除结业考核记录
  static async delete(id) {
    try {
      const [result] = await pool.query('DELETE FROM graduation_exams WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除结业考核记录失败:', error);
      throw error;
    }
  }
}

module.exports = {
  Rotation,
  GraduationExam
}; 