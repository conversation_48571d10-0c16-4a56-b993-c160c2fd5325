const express = require('express');
const router = express.Router();
const rotationController = require('../controllers/rotationController');
const { protect, authorize } = require('../controllers/authController');

// 所有路由都需要授权
router.use(protect);

// ===== 实习轮转路由 =====

// 获取学生的轮转记录
router.get('/student/:studentId', rotationController.getStudentRotations);

// 获取单个轮转记录
router.get('/:id', rotationController.getRotationById);

// 获取特定科室的所有轮转记录 - 仅管理员和教师可访问
router.get('/department/:department', authorize('admin', 'teacher'), rotationController.getDepartmentRotations);

// 创建轮转记录 - 仅管理员和教师可访问
router.post('/', authorize('admin', 'teacher'), rotationController.createRotation);

// 更新轮转记录 - 仅管理员和教师可访问
router.put('/:id', authorize('admin', 'teacher'), rotationController.updateRotation);

// 删除轮转记录 - 仅管理员可访问
router.delete('/:id', authorize('admin'), rotationController.deleteRotation);

// ===== 结业考核路由 =====

// 获取所有结业考核记录 - 仅管理员和教师可访问
router.get('/graduation/all', authorize('admin', 'teacher'), rotationController.getAllGraduationExams);

// 获取学生的结业考核记录
router.get('/graduation/student/:studentId', rotationController.getStudentGraduationExam);

// 创建结业考核记录 - 仅管理员和教师可访问
router.post('/graduation', authorize('admin', 'teacher'), rotationController.createGraduationExam);

// 更新结业考核记录 - 仅管理员和教师可访问
router.put('/graduation/:id', authorize('admin', 'teacher'), rotationController.updateGraduationExam);

// 删除结业考核记录 - 仅管理员可访问
router.delete('/graduation/:id', authorize('admin'), rotationController.deleteGraduationExam);

module.exports = router; 