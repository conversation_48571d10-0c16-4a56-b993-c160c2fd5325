const express = require('express');
const router = express.Router();
const { pool } = require('../config/db');

// 获取所有督导评价
router.get('/', async (req, res, next) => {
  try {
    // 获取查询参数
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const offset = (page - 1) * limit;
    const teacherName = req.query.teacherName || '';

    // 构建SQL查询
    let sql = `
      SELECT te.*, 
        t.name as teacher_name, t.department as teacher_department,
        u.name as evaluator_name
      FROM teaching_evaluations te
      JOIN teachers t ON te.teacher_id = t.id
      JOIN users u ON te.evaluator_id = u.id
    `;
    
    // 添加搜索条件
    const params = [];
    if (teacherName) {
      sql += ` WHERE t.name LIKE ?`;
      params.push(`%${teacherName}%`);
    }
    
    // 获取总记录数
    const [countResult] = await pool.query(
      `SELECT COUNT(*) as total FROM teaching_evaluations te 
       JOIN teachers t ON te.teacher_id = t.id
       ${teacherName ? 'WHERE t.name LIKE ?' : ''}`,
      teacherName ? [`%${teacherName}%`] : []
    );
    
    const totalItems = countResult[0].total;
    const totalPages = Math.ceil(totalItems / limit);
    
    // 添加排序和分页
    sql += ` ORDER BY te.evaluation_date DESC LIMIT ? OFFSET ?`;
    params.push(limit, offset);
    
    // 执行查询
    const [rows] = await pool.query(sql, params);
    
    res.status(200).json({
      success: true,
      count: rows.length,
      pagination: {
        total: totalItems,
        page,
        limit,
        totalPages
      },
      data: rows
    });
  } catch (error) {
    next(error);
  }
});

// 获取单个督导评价
router.get('/:id', async (req, res, next) => {
  try {
    const [rows] = await pool.query(`
      SELECT te.*, 
        t.name as teacher_name, t.department as teacher_department,
        u.name as evaluator_name
      FROM teaching_evaluations te
      JOIN teachers t ON te.teacher_id = t.id
      JOIN users u ON te.evaluator_id = u.id
      WHERE te.id = ?
    `, [req.params.id]);
    
    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该督导评价'
      });
    }
    
    res.status(200).json({
      success: true,
      data: rows[0]
    });
  } catch (error) {
    next(error);
  }
});

// 创建督导评价
router.post('/', async (req, res, next) => {
  try {
    const {
      supervising_department,
      case_topic,
      teaching_form,
      teacher_id,
      teacher_title,
      student_name,
      student_type,
      average_score,
      highlights,
      shortcomings,
      improvement_suggestions,
      competency_approved,
      evaluator_id
    } = req.body;
    
    // 检查必填项
    if (!supervising_department || !case_topic || !teaching_form || !teacher_id || 
        !teacher_title || !student_name || !student_type || !average_score || !evaluator_id) {
      return res.status(400).json({
        success: false,
        message: '请填写必要的评价信息'
      });
    }
    
    // 检查教师是否存在
    const [teacherExists] = await pool.query('SELECT id FROM teachers WHERE id = ?', [teacher_id]);
    if (teacherExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该教师'
      });
    }
    
    // 检查评估人是否存在
    const [evaluatorExists] = await pool.query('SELECT id FROM users WHERE id = ?', [evaluator_id]);
    if (evaluatorExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该评估人'
      });
    }
    
    const [result] = await pool.query(`
      INSERT INTO teaching_evaluations (
        supervising_department, case_topic, teaching_form, teacher_id,
        teacher_title, student_name, student_type, average_score,
        highlights, shortcomings, improvement_suggestions, competency_approved,
        evaluator_id, evaluation_date
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `, [
      supervising_department,
      case_topic,
      teaching_form,
      teacher_id,
      teacher_title,
      student_name,
      student_type,
      average_score,
      highlights || null,
      shortcomings || null,
      improvement_suggestions || null,
      competency_approved === true || competency_approved === 'true' || competency_approved === 1 ? 1 : 0,
      evaluator_id
    ]);
    
    res.status(201).json({
      success: true,
      message: '督导评价创建成功',
      data: {
        id: result.insertId,
        supervising_department,
        case_topic,
        teaching_form,
        teacher_id,
        teacher_title,
        student_name,
        student_type,
        average_score,
        highlights: highlights || null,
        shortcomings: shortcomings || null,
        improvement_suggestions: improvement_suggestions || null,
        competency_approved: competency_approved === true || competency_approved === 'true' || competency_approved === 1 ? 1 : 0,
        evaluator_id,
        evaluation_date: new Date()
      }
    });
  } catch (error) {
    next(error);
  }
});

// 更新督导评价
router.put('/:id', async (req, res, next) => {
  try {
    const {
      supervising_department,
      case_topic,
      teaching_form,
      teacher_id,
      teacher_title,
      student_name,
      student_type,
      average_score,
      highlights,
      shortcomings,
      improvement_suggestions,
      competency_approved,
      evaluator_id
    } = req.body;
    
    // 检查必填项
    if (!supervising_department || !case_topic || !teaching_form || !teacher_id || 
        !teacher_title || !student_name || !student_type || !average_score || !evaluator_id) {
      return res.status(400).json({
        success: false,
        message: '请填写必要的评价信息'
      });
    }
    
    // 检查评价是否存在
    const [evaluationExists] = await pool.query('SELECT id FROM teaching_evaluations WHERE id = ?', [req.params.id]);
    if (evaluationExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该督导评价'
      });
    }
    
    // 检查教师是否存在
    const [teacherExists] = await pool.query('SELECT id FROM teachers WHERE id = ?', [teacher_id]);
    if (teacherExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该教师'
      });
    }
    
    // 检查评估人是否存在
    const [evaluatorExists] = await pool.query('SELECT id FROM users WHERE id = ?', [evaluator_id]);
    if (evaluatorExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该评估人'
      });
    }
    
    await pool.query(`
      UPDATE teaching_evaluations 
      SET supervising_department = ?, case_topic = ?, teaching_form = ?,
          teacher_id = ?, teacher_title = ?, student_name = ?,
          student_type = ?, average_score = ?, highlights = ?,
          shortcomings = ?, improvement_suggestions = ?,
          competency_approved = ?, evaluator_id = ?
      WHERE id = ?
    `, [
      supervising_department,
      case_topic,
      teaching_form,
      teacher_id,
      teacher_title,
      student_name,
      student_type,
      average_score,
      highlights || null,
      shortcomings || null,
      improvement_suggestions || null,
      competency_approved === true || competency_approved === 'true' || competency_approved === 1 ? 1 : 0,
      evaluator_id,
      req.params.id
    ]);
    
    res.status(200).json({
      success: true,
      message: '督导评价更新成功',
      data: {
        id: parseInt(req.params.id),
        supervising_department,
        case_topic,
        teaching_form,
        teacher_id,
        teacher_title,
        student_name,
        student_type,
        average_score,
        highlights: highlights || null,
        shortcomings: shortcomings || null,
        improvement_suggestions: improvement_suggestions || null,
        competency_approved: competency_approved === true || competency_approved === 'true' || competency_approved === 1 ? 1 : 0,
        evaluator_id
      }
    });
  } catch (error) {
    next(error);
  }
});

// 删除督导评价
router.delete('/:id', async (req, res, next) => {
  try {
    // 检查评价是否存在
    const [evaluationExists] = await pool.query('SELECT id FROM teaching_evaluations WHERE id = ?', [req.params.id]);
    if (evaluationExists.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该督导评价'
      });
    }
    
    await pool.query('DELETE FROM teaching_evaluations WHERE id = ?', [req.params.id]);
    
    res.status(200).json({
      success: true,
      message: '督导评价删除成功'
    });
  } catch (error) {
    next(error);
  }
});

// 获取教师的所有督导评价
router.get('/teacher/:teacherId', async (req, res, next) => {
  try {
    const teacherId = req.params.teacherId;
    
    const [rows] = await pool.query(`
      SELECT te.*, 
        t.name as teacher_name, t.department as teacher_department,
        u.name as evaluator_name
      FROM teaching_evaluations te
      JOIN teachers t ON te.teacher_id = t.id
      JOIN users u ON te.evaluator_id = u.id
      WHERE te.teacher_id = ?
      ORDER BY te.evaluation_date DESC
    `, [teacherId]);
    
    res.status(200).json({
      success: true,
      count: rows.length,
      data: rows
    });
  } catch (error) {
    next(error);
  }
});

// 获取教师能力认定状态
router.get('/competency/teacher/:teacherId', async (req, res, next) => {
  try {
    const teacherId = req.params.teacherId;
    
    const [rows] = await pool.query(`
      SELECT 
        COUNT(*) as total_evaluations,
        SUM(CASE WHEN competency_approved = 1 THEN 1 ELSE 0 END) as approved_count
      FROM teaching_evaluations
      WHERE teacher_id = ?
    `, [teacherId]);
    
    const [teacherInfo] = await pool.query('SELECT name, department FROM teachers WHERE id = ?', [teacherId]);
    
    if (teacherInfo.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该教师'
      });
    }
    
    const totalEvaluations = rows[0].total_evaluations || 0;
    const approvedCount = rows[0].approved_count || 0;
    const approvalRate = totalEvaluations > 0 ? (approvedCount / totalEvaluations * 100).toFixed(2) : 0;
    
    res.status(200).json({
      success: true,
      data: {
        teacher_id: parseInt(teacherId),
        teacher_name: teacherInfo[0].name,
        department: teacherInfo[0].department,
        total_evaluations: totalEvaluations,
        approved_count: approvedCount,
        approval_rate: parseFloat(approvalRate),
        is_certified: approvalRate >= 80 // 80%以上认可率视为已认证
      }
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router; 