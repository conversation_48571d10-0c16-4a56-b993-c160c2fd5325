<template>
  <div class="competency-list-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="title">教师能力认定</span>
        </div>
      </template>

      <!-- 搜索区域 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="教师姓名">
          <el-input v-model="searchForm.name" placeholder="教师姓名" clearable />
        </el-form-item>
        <el-form-item label="科室">
          <el-input v-model="searchForm.department" placeholder="所属科室" clearable />
        </el-form-item>
        
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="teacherList"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID"  />
        <el-table-column prop="name" label="教师姓名"  />
        <el-table-column prop="gender" label="性别"  />
        <el-table-column prop="department" label="科室"  />
        <el-table-column label="照片" width="100">
          <template #default="scope">
            <el-image
              v-if="scope.row.photo"
              :src="`http://localhost:3000${scope.row.photo}`"
              :preview-src-list="[`http://localhost:3000${scope.row.photo}`]"
              fit="cover"
              style="width: 50px; height: 50px"
            />
            <el-avatar v-else :size="50" icon="UserFilled" />
          </template>
        </el-table-column>
        <el-table-column label="评价数量" width="100">
          <template #default="scope">
            {{ getCompetencyData(scope.row.id, 'total_evaluations') || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="认可数量" width="100">
          <template #default="scope">
            {{ getCompetencyData(scope.row.id, 'approved_count') || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="认可率" width="180">
          <template #default="scope">
            <el-progress 
              :percentage="getCompetencyData(scope.row.id, 'approval_rate') || 0" 
              :format="percentFormat"
              :status="getCompetencyStatus(scope.row.id)"
            />
          </template>
        </el-table-column>
        <el-table-column label="认证状态" width="100">
          <template #default="scope">
            <el-tag :type="isCertified(scope.row.id) ? 'success' : 'info'">
              {{ isCertified(scope.row.id) ? '已认证' : '未认证' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="viewDetails(scope.row.id)">详情</el-button>
            <el-button 
              size="small" 
              type="primary"
              @click="viewEvaluations(scope.row.id)"
            >
              查看评价
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import axios from 'axios'

export default {
  name: 'CompetencyList',
  setup() {
    const router = useRouter()
      let token = localStorage.getItem('token')
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
    }
    // 基础数据
    const loading = ref(false)
    const teacherList = ref([])
    const competencyDataMap = ref({})
    const total = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(10)
    
    // 搜索表单
    const searchForm = reactive({
      name: '',
      department: '',
      isCertified: ''
    })
    
    // 生命周期钩子
    onMounted(() => {
      fetchTeachers()
    })
    
    // 获取教师列表
    const fetchTeachers = async () => {
      loading.value = true
      try {
        // 添加搜索参数
        const params = {
          page: currentPage.value,
          limit: pageSize.value
        }
        
        if (searchForm.name) params.name = searchForm.name
        if (searchForm.department) params.department = searchForm.department
        
        const response = await axios.get('http://localhost:3000/api/teachers', { params })
        teacherList.value = response.data.data
        total.value = response.data.count
        
        // 获取每个教师的能力认定数据
        await fetchAllTeachersCompetency()
      } catch (error) {
        console.error('获取教师列表失败:', error)
        ElMessage.error('获取教师列表失败')
      } finally {
        loading.value = false
      }
    }
    
    // 获取所有教师的能力认定数据
    const fetchAllTeachersCompetency = async () => {
      try {
        const promises = teacherList.value.map(teacher => 
          axios.get(`http://localhost:3000/api/evaluations/competency/teacher/${teacher.id}`)
            .then(response => {
              competencyDataMap.value[teacher.id] = response.data.data
            })
            .catch(error => {
              console.error(`获取教师${teacher.id}能力认定数据失败:`, error)
            })
        )
        
        await Promise.all(promises)
      } catch (error) {
        console.error('获取教师能力认定数据失败:', error)
      }
    }
    
    // 获取教师能力认定数据
    const getCompetencyData = (teacherId, field) => {
      if (competencyDataMap.value[teacherId] && field in competencyDataMap.value[teacherId]) {
        return competencyDataMap.value[teacherId][field]
      }
      return null
    }
    
    // 获取认证状态样式
    const getCompetencyStatus = (teacherId) => {
      const isCert = isCertified(teacherId)
      return isCert ? 'success' : ''
    }
    
    // 是否已认证
    const isCertified = (teacherId) => {
      return getCompetencyData(teacherId, 'is_certified') === true
    }
    
    // 格式化百分比
    const percentFormat = (percentage) => {
      return `${percentage}%`
    }
    
    // 搜索操作
    const handleSearch = () => {
      currentPage.value = 1
      fetchTeachers()
    }
    
    // 重置搜索
    const resetSearch = () => {
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
      })
      currentPage.value = 1
      fetchTeachers()
    }
    
    // 分页操作
    const handleSizeChange = (val) => {
      pageSize.value = val
      fetchTeachers()
    }
    
    const handleCurrentChange = (val) => {
      currentPage.value = val
      fetchTeachers()
    }
    
    // 查看详情
    const viewDetails = (id) => {
      router.push(`/competency/detail/${id}`)
    }
    
    // 查看教师评价
    const viewEvaluations = (id) => {
      router.push(`/teachers/detail/${id}`)
    }
    
    return {
      loading,
      teacherList,
      competencyDataMap,
      searchForm,
      currentPage,
      pageSize,
      total,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      viewDetails,
      viewEvaluations,
      getCompetencyData,
      getCompetencyStatus,
      isCertified,
      percentFormat
    }
  }
}
</script>

<style scoped>
.competency-list-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style> 