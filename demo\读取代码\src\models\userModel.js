const { pool } = require('../config/db');
const bcrypt = require('bcryptjs');

class User {
  // 创建新用户
  static async create(userData) {
    try {
      // 加密密码
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(userData.password, salt);
      
      const [result] = await pool.query(
        `INSERT INTO users 
         (username, password, role, name, email, phone, teacher_id, status) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          userData.username,
          hashedPassword,
          userData.role || 'teacher',
          userData.name,
          userData.email,
          userData.phone,
          userData.teacher_id,
          userData.status !== undefined ? userData.status : 1
        ]
      );
      return result.insertId;
    } catch (error) {
      console.error('创建用户失败:', error);
      throw error;
    }
  }

  // 获取所有用户
  static async findAll() {
    try {
      const [rows] = await pool.query('SELECT id, username, role, name, email, phone, teacher_id, status, last_login, created_at FROM users');
      return rows;
    } catch (error) {
      console.error('获取用户列表失败:', error);
      throw error;
    }
  }

  // 根据ID获取用户
  static async findById(id) {
    try {
      const [rows] = await pool.query(
        'SELECT id, username, role, name, email, phone, teacher_id, status, last_login, created_at FROM users WHERE id = ?',
        [id]
      );
      return rows[0];
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  }

  // 根据用户名查找用户（登录用）
  static async findByUsername(username) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM users WHERE username = ?',
        [username]
      );
      return rows[0];
    } catch (error) {
      console.error('查找用户失败:', error);
      throw error;
    }
  }

  // 更新用户信息
  static async update(id, userData) {
    try {
      let queryParams = [
        userData.role,
        userData.name,
        userData.email,
        userData.phone,
        userData.teacher_id,
        userData.status !== undefined ? userData.status : 1, // 确保status字段被包含
        id
      ];
      
      // 如果更新包含密码
      if (userData.password) {
        const salt = await bcrypt.genSalt(10);
        const hashedPassword = await bcrypt.hash(userData.password, salt);
        
        const [result] = await pool.query(
          `UPDATE users SET 
           role = ?, name = ?, email = ?, phone = ?, teacher_id = ?, status = ?, password = ? 
           WHERE id = ?`,
          [
            userData.role,
            userData.name,
            userData.email,
            userData.phone,
            userData.teacher_id,
            userData.status !== undefined ? userData.status : 1,
            hashedPassword,
            id
          ]
        );
        return result.affectedRows > 0;
      } else {
        // 不更新密码
        const [result] = await pool.query(
          `UPDATE users SET 
           role = ?, name = ?, email = ?, phone = ?, teacher_id = ?, status = ? 
           WHERE id = ?`,
          queryParams
        );
        return result.affectedRows > 0;
      }
    } catch (error) {
      console.error('更新用户信息失败:', error);
      throw error;
    }
  }

  // 更新最后登录时间
  static async updateLastLogin(id) {
    try {
      await pool.query(
        'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?',
        [id]
      );
      return true;
    } catch (error) {
      console.error('更新登录时间失败:', error);
      return false;
    }
  }

  // 删除用户
  static async delete(id) {
    try {
      const [result] = await pool.query('DELETE FROM users WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除用户失败:', error);
      throw error;
    }
  }

  // 修改密码
  static async changePassword(id, oldPassword, newPassword) {
    try {
      // 获取当前用户信息
      const [rows] = await pool.query('SELECT * FROM users WHERE id = ?', [id]);
      const user = rows[0];
      
      if (!user) {
        return { success: false, message: '用户不存在' };
      }
      
      // 验证旧密码
      const isMatch = await bcrypt.compare(oldPassword, user.password);
      if (!isMatch) {
        return { success: false, message: '原密码错误' };
      }
      
      // 加密新密码
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(newPassword, salt);
      
      // 更新密码
      await pool.query(
        'UPDATE users SET password = ? WHERE id = ?',
        [hashedPassword, id]
      );
      
      return { success: true, message: '密码修改成功' };
    } catch (error) {
      console.error('修改密码失败:', error);
      throw error;
    }
  }
  
  // 保存密码重置令牌
  static async saveResetToken(userId, resetToken) {
    try {
      // 这里应该将resetToken和过期时间存储到数据库中
      // 实际实现应该是修改数据库中的users表，添加reset_token和reset_token_expires字段
      console.log(`为用户 ${userId} 保存了密码重置令牌`);
      
      // 模拟数据库操作
      await pool.query(
        'UPDATE users SET reset_token = ?, reset_token_expires = DATE_ADD(NOW(), INTERVAL 30 MINUTE) WHERE id = ?',
        [resetToken, userId]
      );
      
      return true;
    } catch (error) {
      console.error('保存密码重置令牌出错:', error);
      throw new Error('保存密码重置令牌失败');
    }
  }

  // 通过重置令牌查找用户
  static async findByResetToken(resetToken) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM users WHERE reset_token = ? AND reset_token_expires > NOW()',
        [resetToken]
      );
      return rows[0];
    } catch (error) {
      console.error('通过重置令牌查找用户出错:', error);
      return null;
    }
  }

  // 重置用户密码
  static async resetPassword(userId, newPassword) {
    try {
      // 加密新密码
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(newPassword, salt);
      
      // 更新用户密码
      const [result] = await pool.query(
        'UPDATE users SET password = ? WHERE id = ?',
        [hashedPassword, userId]
      );
      
      return result.affectedRows > 0;
    } catch (error) {
      console.error('重置密码出错:', error);
      throw new Error('重置密码失败');
    }
  }

  // 清除密码重置令牌
  static async clearResetToken(userId) {
    try {
      const [result] = await pool.query(
        'UPDATE users SET reset_token = NULL, reset_token_expires = NULL WHERE id = ?',
        [userId]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error('清除密码重置令牌出错:', error);
      throw new Error('清除密码重置令牌失败');
    }
  }
}

module.exports = User; 