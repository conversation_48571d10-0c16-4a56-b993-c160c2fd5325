{"version": 3, "file": "js/636.1f1a582a.js", "mappings": "iLACOA,MAAM,+B,GAGAA,MAAM,e,GAYFA,MAAM,qB,GACJA,MAAM,kB,aAsCJA,MAAM,sB,GACJA,MAAM,wB,GAIJA,MAAM,sB,GAURA,MAAM,0B,GAQJA,MAAM,kB,GACJA,MAAM,a,GACJA,MAAM,c,GAGRA,MAAM,a,GACJA,MAAM,c,GAGRA,MAAM,a,GACJA,MAAM,c,GAOdA,MAAM,uB,SAWDA,MAAM,W,SA4C4CA,MAAM,W,kaArJhFC,EAAAA,EAAAA,IA8JM,MA9JNC,EA8JM,EA7JJC,EAAAA,EAAAA,IA4JUC,EAAA,CA5JDJ,MAAM,YAAU,CACZK,QAAMC,EAAAA,EAAAA,IACf,IAKM,EALNC,EAAAA,EAAAA,IAKM,MALNC,EAKM,C,aAJJD,EAAAA,EAAAA,IAAiC,QAA3BP,MAAM,SAAQ,UAAM,KAC1BO,EAAAA,EAAAA,IAEM,aADJJ,EAAAA,EAAAA,IAA2CM,EAAA,CAA/BC,QAAOC,EAAAC,QAAM,C,iBAAE,IAAIC,EAAA,KAAAA,EAAA,K,QAAJ,W,kDAKjC,IAiJM,E,qBAjJNZ,EAAAA,EAAAA,IAiJM,aAhJJE,EAAAA,EAAAA,IA+ISW,EAAA,CA/IAC,OAAQ,IAAE,C,iBAEjB,IA2BS,EA3BTZ,EAAAA,EAAAA,IA2BSa,EAAA,CA3BAC,KAAM,GAAC,C,iBACd,IAyBM,EAzBNV,EAAAA,EAAAA,IAyBM,MAzBNW,EAyBM,EAxBJX,EAAAA,EAAAA,IASM,MATNY,EASM,CAPIR,EAAAS,YAAYC,Q,WADpBC,EAAAA,EAAAA,IAMEC,EAAA,C,MAJCC,IAAG,0BAA4Bb,EAAAS,YAAYC,QAC5CI,IAAI,QACJzB,MAAM,eACL,mBAAgB,2BAA6BW,EAAAS,YAAYC,U,iDAE5DC,EAAAA,EAAAA,IAAkDI,EAAA,C,MAA/BC,KAAM,IAAKC,KAAK,mBAGrCzB,EAAAA,EAAAA,IAYkB0B,EAAA,CAZDC,MAAM,OAAOC,UAAU,WAAYC,OAAQ,EAAGC,OAAA,I,kBAC7D,IAA8E,EAA9E9B,EAAAA,EAAAA,IAA8E+B,EAAA,CAAxDC,MAAM,MAAI,C,iBAAC,IAAsB,E,iBAAnBxB,EAAAS,YAAYgB,MAAI,K,OACpDjC,EAAAA,EAAAA,IAAgF+B,EAAA,CAA1DC,MAAM,MAAI,C,iBAAC,IAAwB,E,iBAArBxB,EAAAS,YAAYiB,QAAM,K,OACtDlC,EAAAA,EAAAA,IAAoF+B,EAAA,CAA9DC,MAAM,MAAI,C,iBAAC,IAA4B,E,iBAAzBxB,EAAAS,YAAYkB,YAAU,K,OAC1DnC,EAAAA,EAAAA,IAAgF+B,EAAA,CAA1DC,MAAM,MAAI,C,iBAAC,IAAwB,E,iBAArBxB,EAAAS,YAAYmB,QAAM,K,OACtDpC,EAAAA,EAAAA,IAA+E+B,EAAA,CAAzDC,MAAM,MAAI,C,iBAAC,IAAuB,E,iBAApBxB,EAAAS,YAAYoB,OAAK,K,OACrDrC,EAAAA,EAAAA,IAAmF+B,EAAA,CAA7DC,MAAM,MAAI,C,iBAAC,IAA2B,E,iBAAxBxB,EAAAS,YAAYqB,WAAS,K,OACzDtC,EAAAA,EAAAA,IAIuB+B,EAAA,CAJDC,MAAM,QAAM,C,iBAChC,IAES,EAFThC,EAAAA,EAAAA,IAESuC,EAAA,CAFAC,KAAMhC,EAAAS,YAAYwB,YAAc,UAAY,U,kBACnD,IAA4C,E,iBAAzCjC,EAAAS,YAAYwB,YAAc,KAAO,OAAV,K,4CAQpCzC,EAAAA,EAAAA,IA8GSa,EAAA,CA9GAC,KAAM,IAAE,C,iBAEf,IA8DU,EA9DVd,EAAAA,EAAAA,IA8DUC,EAAA,CA9DDJ,MAAM,4BAA4B6C,OAAO,S,CACrCxC,QAAMC,EAAAA,EAAAA,IACf,IAEMO,EAAA,KAAAA,EAAA,KAFNN,EAAAA,EAAAA,IAEM,OAFDP,MAAM,wBAAsB,EAC/BO,EAAAA,EAAAA,IAAiB,YAAX,U,uBAIV,IAkDM,CAlDKI,EAAAmC,iB,WAAX7C,EAAAA,EAAAA,IAkDM,MAAA8C,EAAA,EAjDJxC,EAAAA,EAAAA,IAsCM,MAtCNyC,EAsCM,EArCJzC,EAAAA,EAAAA,IAYM,MAZN0C,EAYM,EAXJ9C,EAAAA,EAAAA,IAESuC,EAAA,CAFAC,KAAMhC,EAAAmC,eAAeI,aAAe,UAAY,OAAQvB,KAAK,S,kBACpE,IAAiD,E,iBAA9ChB,EAAAmC,eAAeI,aAAe,MAAQ,OAAX,K,kBAEhC3C,EAAAA,EAAAA,IAOM,MAPN4C,EAOM,EANJhD,EAAAA,EAAAA,IAKEiD,EAAA,CAJCtB,MAAOnB,EAAA0C,uBACPV,KAAMhC,EAAAmC,eAAeI,aAAe,UAAY,OAChDI,UAAU,EACX,gB,8BAKN/C,EAAAA,EAAAA,IAsBM,MAtBNgD,EAsBM,EArBJpD,EAAAA,EAAAA,IAKEqD,EAAA,CAJCC,WAAY9C,EAAAmC,eAAeY,eAAiB,EAC5CC,OAAQhD,EAAAmC,eAAeI,aAAe,UAAY,GAClD,eAAc,GACdU,OAAQjD,EAAAkD,e,0CAGXtD,EAAAA,EAAAA,IAaM,MAbNuD,EAaM,EAZJvD,EAAAA,EAAAA,IAGM,MAHNwD,EAGM,EAFJxD,EAAAA,EAAAA,IAAoE,MAApEyD,GAAoEC,EAAAA,EAAAA,IAAzCtD,EAAAmC,eAAeoB,mBAAiB,G,aAC3D3D,EAAAA,EAAAA,IAAkC,OAA7BP,MAAM,cAAa,QAAI,OAE9BO,EAAAA,EAAAA,IAGM,MAHN4D,EAGM,EAFJ5D,EAAAA,EAAAA,IAAiE,MAAjE6D,GAAiEH,EAAAA,EAAAA,IAAtCtD,EAAAmC,eAAeuB,gBAAc,G,aACxD9D,EAAAA,EAAAA,IAAiC,OAA5BP,MAAM,cAAa,OAAG,OAE7BO,EAAAA,EAAAA,IAGM,MAHN+D,EAGM,EAFJ/D,EAAAA,EAAAA,IAAiE,MAAjEgE,GAAiEN,EAAAA,EAAAA,IAAtCtD,EAAAmC,eAAeY,eAAgB,IAAC,G,aAC3DnD,EAAAA,EAAAA,IAAiC,OAA5BP,MAAM,cAAa,OAAG,aAMnCO,EAAAA,EAAAA,IAQM,MARNiE,EAQM,EAPJrE,EAAAA,EAAAA,IAAuDsE,EAAA,CAA3C,mBAAiB,QAAM,C,iBAAC,IAAM5D,EAAA,KAAAA,EAAA,K,QAAN,a,0BACpCN,EAAAA,EAAAA,IAKM,OALDP,MAAM,iBAAe,EACxBO,EAAAA,EAAAA,IAA6B,SAA1B,2BACHA,EAAAA,EAAAA,IAAgC,SAA7B,8BACHA,EAAAA,EAAAA,IAAoC,SAAjC,kCACHA,EAAAA,EAAAA,IAAyB,SAAtB,wB,sBAKTN,EAAAA,EAAAA,IAEM,MAFNyE,EAA4B,oB,OAM9BvE,EAAAA,EAAAA,IA0CUC,EAAA,CA1CDJ,MAAM,mBAAmB6C,OAAO,S,CAC5BxC,QAAMC,EAAAA,EAAAA,IACf,IAEMO,EAAA,KAAAA,EAAA,KAFNN,EAAAA,EAAAA,IAEM,OAFDP,MAAM,sBAAoB,EAC7BO,EAAAA,EAAAA,IAAiB,YAAX,U,uBAIV,IAkCM,E,qBAlCNN,EAAAA,EAAAA,IAkCM,aAjCJE,EAAAA,EAAAA,IA4BWwE,EAAA,CA3BRC,KAAMjE,EAAAkE,YACP5C,OAAA,GACA6C,MAAA,gB,kBAEA,IAAqD,EAArD3E,EAAAA,EAAAA,IAAqD4E,EAAA,CAApCpC,KAAK,QAAQqC,MAAM,KAAK7C,MAAM,OAC/ChC,EAAAA,EAAAA,IAA2E4E,EAAA,CAA1DE,KAAK,yBAAyB9C,MAAM,QAAQ6C,MAAM,SACnE7E,EAAAA,EAAAA,IAAyE4E,EAAA,CAAxDE,KAAK,aAAa9C,MAAM,QAAQ,8BACjDhC,EAAAA,EAAAA,IAAmE4E,EAAA,CAAlDE,KAAK,gBAAgB9C,MAAM,SAAS6C,MAAM,SAC3D7E,EAAAA,EAAAA,IAA+D4E,EAAA,CAA9CE,KAAK,gBAAgB9C,MAAM,MAAM6C,MAAM,QACxD7E,EAAAA,EAAAA,IAMkB4E,EAAA,CAND5C,MAAM,OAAO6C,MAAM,O,CACvBE,SAAO5E,EAAAA,EAAAA,IAGP6E,GAHc,EACvBhF,EAAAA,EAAAA,IAESuC,EAAA,CAFAC,KAAMwC,EAAMC,IAAIC,oBAAsB,UAAY,U,kBACzD,IAAkD,E,iBAA/CF,EAAMC,IAAIC,oBAAsB,KAAO,OAAV,K,6BAItClF,EAAAA,EAAAA,IAIkB4E,EAAA,CAJDE,KAAK,kBAAkB9C,MAAM,OAAO6C,MAAM,O,CAC9CE,SAAO5E,EAAAA,EAAAA,IAC2B6E,GADpB,E,iBACpBxE,EAAA2E,WAAWH,EAAMC,IAAIG,kBAAe,K,OAG3CpF,EAAAA,EAAAA,IAAiE4E,EAAA,CAAhDE,KAAK,iBAAiB9C,MAAM,MAAM6C,MAAM,SACzD7E,EAAAA,EAAAA,IAIkB4E,EAAA,CAJD5C,MAAM,KAAK6C,MAAM,O,CACrBE,SAAO5E,EAAAA,EAAAA,IAC4D6E,GADrD,EACvBhF,EAAAA,EAAAA,IAA4EM,EAAA,CAAjEkB,KAAK,QAASjB,QAAK8E,GAAE7E,EAAA8E,eAAeN,EAAMC,IAAIM,K,kBAAK,IAAE7E,EAAA,KAAAA,EAAA,K,QAAF,S,yDAKlC,IAAvBF,EAAAkE,YAAYc,QAAiBhF,EAAAiF,oB,4BAAxC3F,EAAAA,EAAAA,IAEM,MAFN4F,EAA4E,gB,IA/B9DlF,EAAAiF,wB,6BA3GRjF,EAAAmF,a,2DA4JtB,GACE1D,KAAM,mBACN2D,KAAAA,GACE,MAAMC,GAAQC,EAAAA,EAAAA,MACRC,GAASC,EAAAA,EAAAA,MACTC,EAAYJ,EAAMK,OAAOX,GAGzBI,GAAUQ,EAAAA,EAAAA,KAAI,GACdV,GAAqBU,EAAAA,EAAAA,KAAI,GACzBlF,GAAckF,EAAAA,EAAAA,IAAI,CAAC,GACnBxD,GAAiBwD,EAAAA,EAAAA,IAAI,MACrBzB,GAAcyB,EAAAA,EAAAA,IAAI,KAGxBC,EAAAA,EAAAA,IAAU,KACRC,IACAC,IACAC,MAIF,MAAMF,EAAmBG,UACvBb,EAAQc,OAAQ,EAChB,IACE,MAAMC,QAAiBC,EAAAA,EAAMC,IAAI,wCAAwCX,KACzEhF,EAAYwF,MAAQC,EAASjC,KAAKA,IACpC,CAAE,MAAOoC,GACPC,QAAQD,MAAM,YAAaA,GAC3BE,EAAAA,GAAUF,MAAM,WAClB,CAAE,QACAlB,EAAQc,OAAQ,CAClB,GAIIH,EAAwBE,UAC5B,IACE,MAAME,QAAiBC,EAAAA,EAAMC,IAAI,8DAA8DX,KAC/FtD,EAAe8D,MAAQC,EAASjC,KAAKA,IACvC,CAAE,MAAOoC,GACPC,QAAQD,MAAM,cAAeA,EAC/B,GAIIN,EAAmBC,UACvBf,EAAmBgB,OAAQ,EAC3B,IACE,MAAMC,QAAiBC,EAAAA,EAAMC,IAAI,mDAAmDX,KACpFvB,EAAY+B,MAAQC,EAASjC,KAAKA,IACpC,CAAE,MAAOoC,GACPC,QAAQD,MAAM,YAAaA,GAC3BE,EAAAA,GAAUF,MAAM,WAClB,CAAE,QACApB,EAAmBgB,OAAQ,CAC7B,GAIIvD,EAAuBA,IACtBP,EAAe8D,MAEhB9D,EAAe8D,MAAM1D,aAChB,mBAAmBJ,EAAe8D,MAAMlD,iBAE3CZ,EAAe8D,MAAM1C,kBAAoB,EACpC,6BAA6BpB,EAAe8D,MAAM1C,sBAElD,2BAA2BpB,EAAe8D,MAAMlD,kBARzB,GAc9BG,EAAiBJ,GACd,GAAGA,KAIN6B,EAAc6B,IAClB,IAAKA,EAAY,MAAO,IACxB,MAAMC,EAAO,IAAIC,KAAKF,GACtB,MAAO,GAAGC,EAAKE,kBAAkBF,EAAKG,WAAa,GAAGC,WAAWC,SAAS,EAAG,QAAQL,EAAKM,UAAUF,WAAWC,SAAS,EAAG,QAIvH7G,EAASA,KACbsF,EAAOyB,KAAK,qBAIRlC,EAAkBC,IACtBQ,EAAOyB,KAAK,uBAAuBjC,MAGrC,MAAO,CACLI,UACAF,qBACAxE,cACA0B,iBACA+B,cACAjE,SACA6E,iBACApC,uBACAQ,gBACAyB,aAEJ,G,UC7QF,MAAMsC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://ms/./src/views/competency/CompetencyDetail.vue", "webpack://ms/./src/views/competency/CompetencyDetail.vue?39c8"], "sourcesContent": ["<template>\r\n  <div class=\"competency-detail-container\">\r\n    <el-card class=\"box-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span class=\"title\">能力认定详情</span>\r\n          <div>\r\n            <el-button @click=\"goBack\">返回列表</el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <div v-loading=\"loading\">\r\n        <el-row :gutter=\"20\">\r\n          <!-- 左侧教师基本信息 -->\r\n          <el-col :span=\"6\">\r\n            <div class=\"teacher-info-card\">\r\n              <div class=\"teacher-avatar\">\r\n                <el-image\r\n                  v-if=\"teacherData.photo\"\r\n                  :src=\"`http://localhost:3000${teacherData.photo}`\"\r\n                  fit=\"cover\"\r\n                  class=\"avatar-image\"\r\n                  :preview-src-list=\"[`http://localhost:3000${teacherData.photo}`]\"\r\n                />\r\n                <el-avatar v-else :size=\"120\" icon=\"UserFilled\" />\r\n              </div>\r\n              \r\n              <el-descriptions title=\"教师信息\" direction=\"vertical\" :column=\"1\" border>\r\n                <el-descriptions-item label=\"姓名\">{{ teacherData.name }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"性别\">{{ teacherData.gender }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"科室\">{{ teacherData.department }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"学校\">{{ teacherData.school }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"专业\">{{ teacherData.major }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"学历\">{{ teacherData.education }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"在聘状态\">\r\n                  <el-tag :type=\"teacherData.is_employed ? 'success' : 'danger'\">\r\n                    {{ teacherData.is_employed ? '在聘' : '不在聘' }}\r\n                  </el-tag>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n            </div>\r\n          </el-col>\r\n          \r\n          <!-- 右侧认定信息 -->\r\n          <el-col :span=\"18\">\r\n            <!-- 认定状态卡片 -->\r\n            <el-card class=\"certification-status-card\" shadow=\"hover\">\r\n              <template #header>\r\n                <div class=\"certification-header\">\r\n                  <span>认定状态</span>\r\n                </div>\r\n              </template>\r\n              \r\n              <div v-if=\"competencyData\">\r\n                <div class=\"certification-info\">\r\n                  <div class=\"certification-status\">\r\n                    <el-tag :type=\"competencyData.is_certified ? 'success' : 'info'\" size=\"large\">\r\n                      {{ competencyData.is_certified ? '已认证' : '未认证' }}\r\n                    </el-tag>\r\n                    <div class=\"certification-note\">\r\n                      <el-alert\r\n                        :title=\"getCertificationNote()\"\r\n                        :type=\"competencyData.is_certified ? 'success' : 'info'\"\r\n                        :closable=\"false\"\r\n                        show-icon\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  <div class=\"certification-progress\">\r\n                    <el-progress \r\n                      :percentage=\"competencyData.approval_rate || 0\" \r\n                      :status=\"competencyData.is_certified ? 'success' : ''\" \r\n                      :stroke-width=\"20\"\r\n                      :format=\"percentFormat\"\r\n                    />\r\n                    \r\n                    <div class=\"progress-stats\">\r\n                      <div class=\"stat-item\">\r\n                        <div class=\"stat-value\">{{ competencyData.total_evaluations }}</div>\r\n                        <div class=\"stat-label\">评价总数</div>\r\n                      </div>\r\n                      <div class=\"stat-item\">\r\n                        <div class=\"stat-value\">{{ competencyData.approved_count }}</div>\r\n                        <div class=\"stat-label\">认可数</div>\r\n                      </div>\r\n                      <div class=\"stat-item\">\r\n                        <div class=\"stat-value\">{{ competencyData.approval_rate }}%</div>\r\n                        <div class=\"stat-label\">认可率</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div class=\"certification-rules\">\r\n                  <el-divider content-position=\"left\">认定规则说明</el-divider>\r\n                  <div class=\"rules-content\">\r\n                    <p>1. 需要至少参加3次督导评价才能被认定资格</p>\r\n                    <p>2. 督导评价的认可率需达到80%以上才能获得认证</p>\r\n                    <p>3. 每次评价中，督导老师需要对教师的教学能力进行认可评定</p>\r\n                    <p>4. 认证状态会随着新的评价自动更新</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              <div v-else class=\"no-data\">\r\n                该教师暂无能力认定数据\r\n              </div>\r\n            </el-card>\r\n\r\n            <!-- 评价列表卡片 -->\r\n            <el-card class=\"evaluations-card\" shadow=\"hover\">\r\n              <template #header>\r\n                <div class=\"evaluations-header\">\r\n                  <span>评价记录</span>\r\n                </div>\r\n              </template>\r\n              \r\n              <div v-loading=\"evaluationsLoading\">\r\n                <el-table\r\n                  :data=\"evaluations\"\r\n                  border\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <el-table-column type=\"index\" width=\"50\" label=\"#\" />\r\n                  <el-table-column prop=\"supervising_department\" label=\"督导教研室\" width=\"120\" />\r\n                  <el-table-column prop=\"case_topic\" label=\"病例/主题\" show-overflow-tooltip />\r\n                  <el-table-column prop=\"teaching_form\" label=\"教学活动形式\" width=\"120\" />\r\n                  <el-table-column prop=\"average_score\" label=\"平均分\" width=\"80\" />\r\n                  <el-table-column label=\"能力认定\" width=\"100\">\r\n                    <template #default=\"scope\">\r\n                      <el-tag :type=\"scope.row.competency_approved ? 'success' : 'danger'\">\r\n                        {{ scope.row.competency_approved ? '同意' : '不同意' }}\r\n                      </el-tag>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column prop=\"evaluation_date\" label=\"评价时间\" width=\"180\">\r\n                    <template #default=\"scope\">\r\n                      {{ formatDate(scope.row.evaluation_date) }}\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column prop=\"evaluator_name\" label=\"评估人\" width=\"100\" />\r\n                  <el-table-column label=\"操作\" width=\"100\">\r\n                    <template #default=\"scope\">\r\n                      <el-button size=\"small\" @click=\"viewEvaluation(scope.row.id)\">详情</el-button>\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n                \r\n                <div v-if=\"evaluations.length === 0 && !evaluationsLoading\" class=\"no-data\">\r\n                  暂无评价记录\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, computed, onMounted } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'CompetencyDetail',\r\n  setup() {\r\n    const route = useRoute()\r\n    const router = useRouter()\r\n    const teacherId = route.params.id\r\n    \r\n    // 基础数据\r\n    const loading = ref(false)\r\n    const evaluationsLoading = ref(false)\r\n    const teacherData = ref({})\r\n    const competencyData = ref(null)\r\n    const evaluations = ref([])\r\n    \r\n    // 生命周期钩子\r\n    onMounted(() => {\r\n      fetchTeacherData()\r\n      fetchCompetencyStatus()\r\n      fetchEvaluations()\r\n    })\r\n    \r\n    // 获取教师信息\r\n    const fetchTeacherData = async () => {\r\n      loading.value = true\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/teachers/${teacherId}`)\r\n        teacherData.value = response.data.data\r\n      } catch (error) {\r\n        console.error('获取教师信息失败:', error)\r\n        ElMessage.error('获取教师信息失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 获取能力认证状态\r\n    const fetchCompetencyStatus = async () => {\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/evaluations/competency/teacher/${teacherId}`)\r\n        competencyData.value = response.data.data\r\n      } catch (error) {\r\n        console.error('获取能力认证状态失败:', error)\r\n      }\r\n    }\r\n    \r\n    // 获取教师评价列表\r\n    const fetchEvaluations = async () => {\r\n      evaluationsLoading.value = true\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/evaluations/teacher/${teacherId}`)\r\n        evaluations.value = response.data.data\r\n      } catch (error) {\r\n        console.error('获取评价列表失败:', error)\r\n        ElMessage.error('获取评价列表失败')\r\n      } finally {\r\n        evaluationsLoading.value = false\r\n      }\r\n    }\r\n    \r\n    // 获取认证提示信息\r\n    const getCertificationNote = () => {\r\n      if (!competencyData.value) return ''\r\n      \r\n      if (competencyData.value.is_certified) {\r\n        return `该教师已通过能力认定，认可率为 ${competencyData.value.approval_rate}%`\r\n      } else {\r\n        if (competencyData.value.total_evaluations < 3) {\r\n          return `评价次数不足，至少需要3次评价才能进行认定（当前: ${competencyData.value.total_evaluations}次）`\r\n        } else {\r\n          return `认可率不足，需要80%以上才能获得认定（当前: ${competencyData.value.approval_rate}%）`\r\n        }\r\n      }\r\n    }\r\n    \r\n    // 格式化百分比\r\n    const percentFormat = (percentage) => {\r\n      return `${percentage}%`\r\n    }\r\n    \r\n    // 格式化日期\r\n    const formatDate = (dateString) => {\r\n      if (!dateString) return '-'\r\n      const date = new Date(dateString)\r\n      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`\r\n    }\r\n    \r\n    // 返回列表\r\n    const goBack = () => {\r\n      router.push('/competency/list')\r\n    }\r\n    \r\n    // 查看评价详情\r\n    const viewEvaluation = (id) => {\r\n      router.push(`/evaluations/detail/${id}`)\r\n    }\r\n    \r\n    return {\r\n      loading,\r\n      evaluationsLoading,\r\n      teacherData,\r\n      competencyData,\r\n      evaluations,\r\n      goBack,\r\n      viewEvaluation,\r\n      getCertificationNote,\r\n      percentFormat,\r\n      formatDate\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.competency-detail-container {\r\n  padding: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.teacher-info-card {\r\n  padding: 20px 0;\r\n}\r\n\r\n.teacher-avatar {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.avatar-image {\r\n  width: 120px;\r\n  height: 120px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 2px solid #eee;\r\n}\r\n\r\n.certification-status-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.certification-info {\r\n  display: flex;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.certification-status {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 200px;\r\n  padding-right: 20px;\r\n}\r\n\r\n.certification-note {\r\n  margin-top: 20px;\r\n  width: 100%;\r\n}\r\n\r\n.certification-progress {\r\n  flex: 1;\r\n  padding-left: 20px;\r\n  border-left: 1px solid #eee;\r\n}\r\n\r\n.progress-stats {\r\n  display: flex;\r\n  margin-top: 20px;\r\n}\r\n\r\n.stat-item {\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-top: 5px;\r\n}\r\n\r\n.certification-rules {\r\n  margin-top: 20px;\r\n}\r\n\r\n.rules-content {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  line-height: 1.8;\r\n  padding: 0 20px;\r\n}\r\n\r\n.evaluations-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.no-data {\r\n  padding: 30px;\r\n  text-align: center;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n</style> ", "import { render } from \"./CompetencyDetail.vue?vue&type=template&id=04ec8c68&scoped=true\"\nimport script from \"./CompetencyDetail.vue?vue&type=script&lang=js\"\nexport * from \"./CompetencyDetail.vue?vue&type=script&lang=js\"\n\nimport \"./CompetencyDetail.vue?vue&type=style&index=0&id=04ec8c68&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-04ec8c68\"]])\n\nexport default __exports__"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_component_el_button", "onClick", "$setup", "goBack", "_cache", "_component_el_row", "gutter", "_component_el_col", "span", "_hoisted_3", "_hoisted_4", "teacher<PERSON><PERSON>", "photo", "_createBlock", "_component_el_image", "src", "fit", "_component_el_avatar", "size", "icon", "_component_el_descriptions", "title", "direction", "column", "border", "_component_el_descriptions_item", "label", "name", "gender", "department", "school", "major", "education", "_component_el_tag", "type", "is_employed", "shadow", "competencyData", "_hoisted_5", "_hoisted_6", "_hoisted_7", "is_certified", "_hoisted_8", "_component_el_alert", "getCertificationNote", "closable", "_hoisted_9", "_component_el_progress", "percentage", "approval_rate", "status", "format", "percentFormat", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_toDisplayString", "total_evaluations", "_hoisted_13", "_hoisted_14", "approved_count", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_component_el_divider", "_hoisted_18", "_component_el_table", "data", "evaluations", "style", "_component_el_table_column", "width", "prop", "default", "scope", "row", "competency_approved", "formatDate", "evaluation_date", "$event", "viewEvaluation", "id", "length", "evaluationsLoading", "_hoisted_19", "loading", "setup", "route", "useRoute", "router", "useRouter", "teacherId", "params", "ref", "onMounted", "fetchTeacherData", "fetchCompetencyStatus", "fetchEvaluations", "async", "value", "response", "axios", "get", "error", "console", "ElMessage", "dateString", "date", "Date", "getFullYear", "getMonth", "toString", "padStart", "getDate", "push", "__exports__", "render"], "sourceRoot": ""}