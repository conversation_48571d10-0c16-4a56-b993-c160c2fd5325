{"version": 3, "file": "js/807.b12062b6.js", "mappings": "oqBAqKAA,EAAAA,EAAMC,aAAaC,QAAQC,IACzBC,IACE,MAAMC,EAAQC,aAAaC,QAAQ,SAInC,OAHIF,IACFD,EAAOI,QAAQC,cAAgB,UAAUJ,KAEpCD,GAETM,GACSC,QAAQC,OAAOF,IAK1BV,EAAAA,EAAMC,aAAaY,SAASV,IAC1BU,GAAYA,EACZH,IACMA,EAAMG,UAAsC,MAA1BH,EAAMG,SAASC,SAEnCR,aAAaS,WAAW,SACxBT,aAAaS,WAAW,YAES,WAA7BC,OAAOC,SAASC,WAClBC,EAAAA,GAAUT,MAAM,eAChBM,OAAOC,SAASG,KAAO,aAGpBT,QAAQC,OAAOF,KAI1B,MAAMW,GAASC,EAAAA,EAAAA,MACTC,GAAeC,EAAAA,EAAAA,IAAI,MACnBC,GAAUD,EAAAA,EAAAA,KAAI,GACdE,EAAUC,CAAAA,SAAAA,aAAAA,SAAAA,KAAYC,iBAAmB,8BAGzCC,GAAYC,EAAAA,EAAAA,IAAS,CACzBC,SAAU,GACVC,SAAU,GACVC,UAAU,IAGNC,EAAa,CACjBH,SAAU,CACR,CAAEI,UAAU,EAAMC,QAAS,SAAUC,QAAS,QAC9C,CAAEC,IAAK,EAAGC,IAAK,GAAIH,QAAS,iBAAkBC,QAAS,SAEzDL,SAAU,CACR,CAAEG,UAAU,EAAMC,QAAS,QAASC,QAAS,QAC7C,CAAEC,IAAK,EAAGC,IAAK,GAAIH,QAAS,iBAAkBC,QAAS,UAIrDG,EAAcC,UAClB,GAAKlB,EAAamB,MAElB,UACQnB,EAAamB,MAAMC,SAASF,UAChC,GAAIG,EAAO,CACTnB,EAAQiB,OAAQ,EAEhB,IACE,MAAM7B,QAAiBb,EAAAA,EAAM6C,KAAK,GAAGnB,eAAsB,CACzDK,SAAUF,EAAUE,SACpBC,SAAUH,EAAUG,YAIhB,MAAE3B,EAAK,KAAEyC,GAASjC,EAASiC,KAgBjC,GAfAxC,aAAayC,QAAQ,QAAS1C,GAG1BwB,EAAUI,SACZ3B,aAAayC,QAAQ,qBAAsBlB,EAAUE,UAErDzB,aAAaS,WAAW,sBAI1BT,aAAayC,QAAQ,WAAYC,KAAKC,UAAUH,IAGhDxC,aAAayC,QAAQ,SAAUD,EAAKI,IACpC5C,aAAayC,QAAQ,WAAYD,EAAKK,MAClCL,EAAKM,WACP9C,aAAayC,QAAQ,YAAaD,EAAKM,YACvCC,QAAQC,IAAI,UAAWR,EAAKM,iBACvB,GAAkB,YAAdN,EAAKK,KAEd,IACE,MAAMI,QAAwBvD,EAAAA,EAAMwD,IAAI,GAAG9B,sBAA4BoB,EAAKI,MACxEK,EAAgBT,KAAKW,SAAWF,EAAgBT,KAAKA,MACvDxC,aAAayC,QAAQ,YAAaQ,EAAgBT,KAAKA,KAAKI,IAC5DG,QAAQC,IAAI,mBAAoBC,EAAgBT,KAAKA,KAAKI,MAE1DG,QAAQ3C,MAAM,qBACdS,EAAAA,GAAUuC,QAAQ,yBAEtB,CAAE,MAAOC,GACPN,QAAQ3C,MAAM,YAAaiD,EAC7B,CAGFN,QAAQC,IAAI,aAAcR,GAE1B3B,EAAAA,GAAUsC,QAAQ,QAGA,UAAdX,EAAKK,MAAkC,kBAAdL,EAAKK,KAChC9B,EAAOuC,KAAK,kBACW,YAAdd,EAAKK,KACd9B,EAAOuC,KAAK,eAEZvC,EAAOuC,KAAK,aAEhB,CAAE,MAAOlD,GACP2C,QAAQ3C,MAAM,QAASA,GACvBS,EAAAA,GAAUT,MAAMA,EAAMG,UAAUiC,MAAMV,SAAW,iBACnD,CAAE,QACAX,EAAQiB,OAAQ,CAClB,CACF,GAEJ,CAAE,MAAOhC,GACPe,EAAQiB,OAAQ,EAChBvB,EAAAA,GAAUT,MAAM,SAClB,GAIImD,GAAwBrC,EAAAA,EAAAA,KAAI,GAC5BsC,GAAkBtC,EAAAA,EAAAA,IAAI,MACtBuC,GAAkBvC,EAAAA,EAAAA,KAAI,GAEtBwC,GAAelC,EAAAA,EAAAA,IAAS,CAC5BC,SAAU,GACVC,SAAU,GACViC,gBAAiB,GACjBC,KAAM,GACNC,MAAO,GACPC,MAAO,GACPC,WAAY,GACZlB,KAAM,SAGFmB,EAAeA,CAACC,EAAM7B,EAAO8B,KACnB,KAAV9B,EACF8B,EAAS,IAAIC,MAAM,YACV/B,IAAUsB,EAAahC,SAChCwC,EAAS,IAAIC,MAAM,eAEnBD,KAIEE,EAAgB,CACpB3C,SAAU,CACR,CAAEI,UAAU,EAAMC,QAAS,SAAUC,QAAS,QAC9C,CAAEC,IAAK,EAAGC,IAAK,GAAIH,QAAS,iBAAkBC,QAAS,SAEzDL,SAAU,CACR,CAAEG,UAAU,EAAMC,QAAS,QAASC,QAAS,QAC7C,CAAEC,IAAK,EAAGC,IAAK,GAAIH,QAAS,iBAAkBC,QAAS,SAEzD4B,gBAAiB,CACf,CAAE9B,UAAU,EAAMC,QAAS,UAAWC,QAAS,QAC/C,CAAEsC,UAAWL,EAAcjC,QAAS,SAEtC6B,KAAM,CACJ,CAAE/B,UAAU,EAAMC,QAAS,QAASC,QAAS,SAE/C8B,MAAO,CACL,CAAES,KAAM,QAASxC,QAAS,aAAcC,QAAS,SAEnD+B,MAAO,CACL,CAAES,QAAS,oBAAqBzC,QAAS,aAAcC,QAAS,UAQ9DyC,EAAiBrC,UACrB,GAAKqB,EAAgBpB,MAErB,UACQoB,EAAgBpB,MAAMC,SAASF,UACnC,GAAIG,EAAO,CACTmB,EAAgBrB,OAAQ,EAExB,IACE,MAAM,gBAAEuB,KAAoBc,GAAiBf,QAEtBhE,EAAAA,EAAM6C,KAAK,GAAGnB,kBAAyBqD,GAE9D5D,EAAAA,GAAUsC,QAAQ,YAClBI,EAAsBnB,OAAQ,EAG9Bb,EAAUE,SAAWiC,EAAajC,SAClCF,EAAUG,SAAW,EACvB,CAAE,MAAOtB,GACP2C,QAAQ3C,MAAM,QAASA,GACvBS,EAAAA,GAAUT,MAAMA,EAAMG,UAAUiC,MAAMV,SAAW,aACnD,CAAE,QACA2B,EAAgBrB,OAAQ,CAC1B,CACF,GAEJ,CAAE,MAAOhC,GACPqD,EAAgBrB,OAAQ,EACxBvB,EAAAA,GAAUT,MAAM,SAClB,GAIIsE,GAA8BxD,EAAAA,EAAAA,KAAI,GAClCyD,GAAwBzD,EAAAA,EAAAA,IAAI,MAC5B0D,GAAwB1D,EAAAA,EAAAA,KAAI,GAC5B2D,GAAmB3D,EAAAA,EAAAA,KAAI,GAEvB4D,GAAqBtD,EAAAA,EAAAA,IAAS,CAClCC,SAAU,GACVoC,MAAO,GACPkB,YAAa,GACbC,mBAAoB,KAGhBC,EAAkBA,CAAChB,EAAM7B,EAAO8B,KACtB,KAAV9B,EACF8B,EAAS,IAAIC,MAAM,WACV/B,EAAM8C,OAAS,GAAK9C,EAAM8C,OAAS,GAC5ChB,EAAS,IAAIC,MAAM,qBAG2B,KAA1CW,EAAmBE,oBACrBL,EAAsBvC,OAAO+C,cAAc,sBAE7CjB,MAIEkB,EAAyBA,CAACnB,EAAM7B,EAAO8B,KAC7B,KAAV9B,EACF8B,EAAS,IAAIC,MAAM,aACV/B,IAAU0C,EAAmBC,YACtCb,EAAS,IAAIC,MAAM,eAEnBD,KAIEmB,EAAsB,CAC1B5D,SAAU,CACR,CAAEI,UAAU,EAAMC,QAAS,SAAUC,QAAS,SAEhD8B,MAAO,CACL,CAAEhC,UAAU,EAAMC,QAAS,QAASC,QAAS,QAC7C,CAAEuC,KAAM,QAASxC,QAAS,aAAcC,QAAS,SAEnDgD,YAAa,CACX,CAAElD,UAAU,EAAMC,QAAS,SAAUC,QAAS,QAC9C,CAAEsC,UAAWY,EAAiBlD,QAAS,SAEzCiD,mBAAoB,CAClB,CAAEnD,UAAU,EAAMC,QAAS,WAAYC,QAAS,QAChD,CAAEsC,UAAWe,EAAwBrD,QAAS,UAI5CuD,EAAqBA,KACzBZ,EAA4BtC,OAAQ,EACpCyC,EAAiBzC,OAAQ,EACzB0C,EAAmBC,YAAc,GACjCD,EAAmBE,mBAAqB,IAGpCO,EAAuBpD,UAC3B,GAAKwC,EAAsBvC,MAE3B,IAEMyC,EAAiBzC,YAEbuC,EAAsBvC,MAAM+C,cAAc,CAAC,cAAe,6BAG1DR,EAAsBvC,MAAM+C,cAAc,CAAC,WAAY,UAG/DP,EAAsBxC,OAAQ,EAE9B,IACE,GAAKyC,EAAiBzC,MAaf,OAEkB1C,EAAAA,EAAM6C,KAAK,GAAGnB,wBAA+B,CAClEK,SAAUqD,EAAmBrD,SAC7BoC,MAAOiB,EAAmBjB,MAC1BkB,YAAaD,EAAmBC,cAGlClE,EAAAA,GAAUsC,QAAQ,mBAClBuB,EAA4BtC,OAAQ,EAGpCb,EAAUE,SAAWqD,EAAmBrD,SACxCF,EAAUG,SAAW,EACvB,KA3B6B,CAE3B,MAAMnB,QAAiBb,EAAAA,EAAM6C,KAAK,GAAGnB,yBAAgC,CACnEK,SAAUqD,EAAmBrD,SAC7BoC,MAAOiB,EAAmBjB,QAGxBtD,EAASiC,KAAKW,SAChBtC,EAAAA,GAAUsC,QAAQ,iBAClB0B,EAAiBzC,OAAQ,GAEzBvB,EAAAA,GAAUT,MAAMG,EAASiC,KAAKV,SAAW,YAE7C,CAeF,CAAE,MAAO1B,GACP2C,QAAQ3C,MAAM,QAASA,GACnByE,EAAiBzC,MACnBvB,EAAAA,GAAUT,MAAMA,EAAMG,UAAUiC,MAAMV,SAAW,gBAEjDjB,EAAAA,GAAUT,MAAMA,EAAMG,UAAUiC,MAAMV,SAAW,uBAErD,CAAE,QACA8C,EAAsBxC,OAAQ,CAChC,CACF,CAAE,MAAOhC,GACPwE,EAAsBxC,OAAQ,EAC9BvB,EAAAA,GAAUT,MAAM,UAChB2C,QAAQ3C,MAAMA,EAChB,GAIIoF,EAA0BA,KAC9B,MAAMC,EAAqBzF,aAAaC,QAAQ,sBAC5CwF,IACFlE,EAAUE,SAAWgE,EACrBlE,EAAUI,UAAW,I,OAKzB6D,I,wMAlgBEE,EAAAA,EAAAA,IAyJM,MAzJNC,EAyJM,EAxJJC,EAAAA,EAAAA,IAgFM,MAhFNC,EAgFM,EA9EJD,EAAAA,EAAAA,IAoCM,MApCNE,EAoCM,C,qZApBJF,EAAAA,EAAAA,IAmBM,MAnBNG,EAmBM,EAlBJH,EAAAA,EAAAA,IAKM,MALNI,EAKM,EAJJJ,EAAAA,EAAAA,IAEM,MAFNK,EAEM,EADJC,EAAAA,EAAAA,IAA4BC,EAAA,M,iBAAnB,IAAS,EAATD,EAAAA,EAAAA,KAASE,EAAAA,EAAAA,IAAAC,EAAAA,U,uBAEpBT,EAAAA,EAAAA,IAAwC,OAAnCU,MAAM,gBAAe,YAAQ,OAEpCV,EAAAA,EAAAA,IAKM,MALNW,EAKM,EAJJX,EAAAA,EAAAA,IAEM,MAFNY,EAEM,EADJN,EAAAA,EAAAA,IAA4BC,EAAA,M,iBAAnB,IAAS,EAATD,EAAAA,EAAAA,KAASE,EAAAA,EAAAA,IAAAC,EAAAA,U,uBAEpBT,EAAAA,EAAAA,IAAuC,OAAlCU,MAAM,gBAAe,WAAO,OAEnCV,EAAAA,EAAAA,IAKM,MALNa,EAKM,EAJJb,EAAAA,EAAAA,IAEM,MAFNc,EAEM,EADJR,EAAAA,EAAAA,IAA4BC,EAAA,M,iBAAnB,IAAS,EAATD,EAAAA,EAAAA,KAASE,EAAAA,EAAAA,IAAAC,EAAAA,U,uBAEpBT,EAAAA,EAAAA,IAAyC,OAApCU,MAAM,gBAAe,aAAS,WAMzCV,EAAAA,EAAAA,IAsCM,MAtCNe,EAsCM,EArCJf,EAAAA,EAAAA,IAoCM,MApCNgB,EAoCM,C,eAnCJhB,EAAAA,EAAAA,IAAgC,MAA5BU,MAAM,cAAa,QAAI,I,eAC3BV,EAAAA,EAAAA,IAAsC,KAAnCU,MAAM,iBAAgB,aAAS,KAElCJ,EAAAA,EAAAA,IA+BUW,EAAA,CA/BAC,MAAOvF,EAAYwF,MAAOnF,E,QAAgB,eAAJV,IAAID,EAAeqF,MAAM,c,kBACvE,IAMe,EANfJ,EAAAA,EAAAA,IAMec,EAAA,CANDC,KAAK,YAAU,C,iBAC3B,IAIW,EAJXf,EAAAA,EAAAA,IAIWgB,EAAA,C,WAHA3F,EAAUE,S,qCAAVF,EAAUE,SAAQ0F,GAC3BC,YAAY,GACX,eAAahB,EAAAA,EAAAA,IAAAiB,EAAAA,O,8CAIlBnB,EAAAA,EAAAA,IAQec,EAAA,CARDC,KAAK,YAAU,C,iBAC3B,IAMW,EANXf,EAAAA,EAAAA,IAMWgB,EAAA,C,WALA3F,EAAUG,S,qCAAVH,EAAUG,SAAQyF,GAC3B7C,KAAK,WACL8C,YAAY,GACX,eAAahB,EAAAA,EAAAA,IAAAkB,EAAAA,MACd,oB,8CAIJ1B,EAAAA,EAAAA,IAGM,MAHN2B,EAGM,EAFJrB,EAAAA,EAAAA,IAA2DsB,EAAA,C,WAArCjG,EAAUI,S,qCAAVJ,EAAUI,SAAQwF,I,kBAAE,IAAGM,EAAA,MAAAA,EAAA,M,QAAH,U,gCAC1C7B,EAAAA,EAAAA,IAA6E,KAA1E9E,KAAK,IAAIwF,MAAM,cAAeoB,SAAKC,EAAAA,EAAAA,IAAUrC,EAAkB,cAAE,YAGtEY,EAAAA,EAAAA,IAIec,EAAA,M,iBAHb,IAEY,EAFZd,EAAAA,EAAAA,IAEY0B,EAAA,CAFDtD,KAAK,UAAWnD,QAASA,EAAAiB,MAAUsF,QAAOxF,EAAaoE,MAAM,gB,kBAAe,IAEvFmB,EAAA,MAAAA,EAAA,M,QAFuF,W,+DAYjGvB,EAAAA,EAAAA,IAiCY2B,EAAA,CAhCVC,MAAM,O,WACGvE,EAAAnB,M,uCAAAmB,EAAqBnB,MAAA+E,GAC9BY,MAAM,QACNC,OAAA,GACA,uB,CAsBWC,QAAMC,EAAAA,EAAAA,IACf,IAGO,EAHPtC,EAAAA,EAAAA,IAGO,OAHPuC,EAGO,EAFLjC,EAAAA,EAAAA,IAAgE0B,EAAA,CAApDF,QAAKD,EAAA,KAAAA,EAAA,GAAAN,GAAE5D,EAAAnB,OAAwB,I,kBAAO,IAAEqF,EAAA,MAAAA,EAAA,M,QAAF,S,eAClDvB,EAAAA,EAAAA,IAA2F0B,EAAA,CAAhFtD,KAAK,UAAWnD,QAASsC,EAAArB,MAAkBsF,QAAOlD,G,kBAAgB,IAAEiD,EAAA,MAAAA,EAAA,M,QAAF,S,iDAvBjF,IAmBU,EAnBVvB,EAAAA,EAAAA,IAmBUW,EAAA,CAnBAC,MAAOpD,EAAeqD,MAAO3C,E,QAAmB,kBAAJlD,IAAIsC,EAAkB,cAAY,Q,kBACtF,IAEe,EAFf0C,EAAAA,EAAAA,IAEec,EAAA,CAFDoB,MAAM,MAAMnB,KAAK,Y,kBAC7B,IAA0E,EAA1Ef,EAAAA,EAAAA,IAA0EgB,EAAA,C,WAAvDxD,EAAajC,S,qCAAbiC,EAAajC,SAAQ0F,GAAEC,YAAY,U,gCAExDlB,EAAAA,EAAAA,IAEec,EAAA,CAFDoB,MAAM,KAAKnB,KAAK,Y,kBAC5B,IAAuG,EAAvGf,EAAAA,EAAAA,IAAuGgB,EAAA,C,WAApFxD,EAAahC,S,qCAAbgC,EAAahC,SAAQyF,GAAE7C,KAAK,WAAW8C,YAAY,QAAQ,oB,gCAEhFlB,EAAAA,EAAAA,IAEec,EAAA,CAFDoB,MAAM,OAAOnB,KAAK,mB,kBAC9B,IAAgH,EAAhHf,EAAAA,EAAAA,IAAgHgB,EAAA,C,WAA7FxD,EAAaC,gB,qCAAbD,EAAaC,gBAAewD,GAAE7C,KAAK,WAAW8C,YAAY,UAAU,oB,gCAEzFlB,EAAAA,EAAAA,IAEec,EAAA,CAFDoB,MAAM,KAAKnB,KAAK,Q,kBAC5B,IAAqE,EAArEf,EAAAA,EAAAA,IAAqEgB,EAAA,C,WAAlDxD,EAAaE,K,qCAAbF,EAAaE,KAAIuD,GAAEC,YAAY,S,gCAEpDlB,EAAAA,EAAAA,IAEec,EAAA,CAFDoB,MAAM,KAAKnB,KAAK,S,kBAC5B,IAAsE,EAAtEf,EAAAA,EAAAA,IAAsEgB,EAAA,C,WAAnDxD,EAAaG,M,qCAAbH,EAAaG,MAAKsD,GAAEC,YAAY,S,gCAErDlB,EAAAA,EAAAA,IAEec,EAAA,CAFDoB,MAAM,MAAMnB,KAAK,S,kBAC7B,IAAuE,EAAvEf,EAAAA,EAAAA,IAAuEgB,EAAA,C,WAApDxD,EAAaI,M,qCAAbJ,EAAaI,MAAKqD,GAAEC,YAAY,U,6EAYzDlB,EAAAA,EAAAA,IAgCY2B,EAAA,CA/BVC,MAAM,O,WACGpD,EAAAtC,M,uCAAAsC,EAA2BtC,MAAA+E,GACpCY,MAAM,QACNC,OAAA,GACA,uB,CAmBWC,QAAMC,EAAAA,EAAAA,IACf,IAKO,EALPtC,EAAAA,EAAAA,IAKO,OALPyC,EAKO,EAJLnC,EAAAA,EAAAA,IAAsE0B,EAAA,CAA1DF,QAAKD,EAAA,MAAAA,EAAA,IAAAN,GAAEzC,EAAAtC,OAA8B,I,kBAAO,IAAEqF,EAAA,MAAAA,EAAA,M,QAAF,S,eACxDvB,EAAAA,EAAAA,IAEY0B,EAAA,CAFDtD,KAAK,UAAWnD,QAASyD,EAAAxC,MAAwBsF,QAAOnC,G,kBACjE,IAAwC,E,iBAArCV,EAAAzC,MAAmB,OAAS,QAAZ,K,yCArBzB,IAgBU,EAhBV8D,EAAAA,EAAAA,IAgBUW,EAAA,CAhBAC,MAAOhC,EAAqBiC,MAAO1B,E,QAAyB,wBAAJnE,IAAIyD,EAAwB,cAAY,S,kBACxG,IAEe,EAFfuB,EAAAA,EAAAA,IAEec,EAAA,CAFDoB,MAAM,MAAMnB,KAAK,Y,kBAC7B,IAAgF,EAAhFf,EAAAA,EAAAA,IAAgFgB,EAAA,C,WAA7DpC,EAAmBrD,S,uCAAnBqD,EAAmBrD,SAAQ0F,GAAEC,YAAY,U,gCAE9DlB,EAAAA,EAAAA,IAEec,EAAA,CAFDoB,MAAM,KAAKnB,KAAK,S,kBAC5B,IAAgF,EAAhFf,EAAAA,EAAAA,IAAgFgB,EAAA,C,WAA7DpC,EAAmBjB,M,uCAAnBiB,EAAmBjB,MAAKsD,GAAEC,YAAY,a,+BAG3CvC,EAAAzC,Q,WAAhBsD,EAAAA,EAAAA,IAOW4C,EAAAA,GAAA,CAAAC,IAAA,KANTrC,EAAAA,EAAAA,IAEec,EAAA,CAFDoB,MAAM,MAAMnB,KAAK,e,kBAC7B,IAAiH,EAAjHf,EAAAA,EAAAA,IAAiHgB,EAAA,C,WAA9FpC,EAAmBC,Y,uCAAnBD,EAAmBC,YAAWoC,GAAE7C,KAAK,WAAW8C,YAAY,SAAS,oB,gCAE1FlB,EAAAA,EAAAA,IAEec,EAAA,CAFDoB,MAAM,QAAQnB,KAAK,sB,kBAC/B,IAA0H,EAA1Hf,EAAAA,EAAAA,IAA0HgB,EAAA,C,WAAvGpC,EAAmBE,mB,uCAAnBF,EAAmBE,mBAAkBmC,GAAE7C,KAAK,WAAW8C,YAAY,WAAW,oB,iHCvI7G,MAAMoB,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://ms/./src/views/LoginView.vue", "webpack://ms/./src/views/LoginView.vue?5755"], "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <div class=\"login-card\">\n      <!-- Left side -->\n      <div class=\"login-info\">\n        <div class=\"logo-wrapper\">\n          <div class=\"logo-icon\">\n            <img src=\"@/assets/logo.png\" alt=\"Logo\" class=\"logo-img\" />\n            <i class=\"el-icon-monitor\"></i>\n          </div>\n          <div class=\"logo-text\">\n            教学师资评价与能力认定系统\n          </div>\n        </div>\n        \n        <div class=\"welcome-text\">\n          <h2>欢迎回来</h2>\n          <p>登录您的账户以继续访问系统</p>\n        </div>\n        \n        <div class=\"feature-list\">\n          <div class=\"feature-item\">\n            <div class=\"feature-icon\">\n              <el-icon><Check /></el-icon>\n            </div>\n            <div class=\"feature-text\">现代化的管理界面</div>\n          </div>\n          <div class=\"feature-item\">\n            <div class=\"feature-icon\">\n              <el-icon><Check /></el-icon>\n            </div>\n            <div class=\"feature-text\">强大的功能模块</div>\n          </div>\n          <div class=\"feature-item\">\n            <div class=\"feature-icon\">\n              <el-icon><Check /></el-icon>\n            </div>\n            <div class=\"feature-text\">安全可靠的数据保护</div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- Right side -->\n      <div class=\"login-form-wrapper\">\n        <div class=\"login-form-container\">\n          <h2 class=\"form-title\">用户登录</h2>\n          <p class=\"form-subtitle\">请输入您的账户信息</p>\n          \n          <el-form :model=\"loginForm\" :rules=\"loginRules\" ref=\"loginFormRef\" class=\"login-form\">\n            <el-form-item prop=\"username\">\n              <el-input \n                v-model=\"loginForm.username\" \n                placeholder=\"\" \n                :prefix-icon=\"User\">\n              </el-input>\n            </el-form-item>\n            \n            <el-form-item prop=\"password\">\n              <el-input \n                v-model=\"loginForm.password\" \n                type=\"password\" \n                placeholder=\"\" \n                :prefix-icon=\"Lock\"\n                show-password>\n              </el-input>\n            </el-form-item>\n            \n            <div class=\"form-options\">\n              <el-checkbox v-model=\"loginForm.remember\">记住我</el-checkbox>\n              <a href=\"#\" class=\"forgot-link\" @click.prevent=\"showForgotPassword\">忘记密码?</a>\n            </div>\n            \n            <el-form-item>\n              <el-button type=\"primary\" :loading=\"loading\" @click=\"handleLogin\" class=\"login-button\">\n                登录\n              </el-button>\n            </el-form-item>\n            \n           \n          </el-form>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 注册对话框 -->\n    <el-dialog\n      title=\"用户注册\"\n      v-model=\"registerDialogVisible\"\n      width=\"400px\"\n      center\n      destroy-on-close\n    >\n      <el-form :model=\"registerForm\" :rules=\"registerRules\" ref=\"registerFormRef\" label-width=\"80px\">\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input v-model=\"registerForm.username\" placeholder=\"请输入用户名\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"密码\" prop=\"password\">\n          <el-input v-model=\"registerForm.password\" type=\"password\" placeholder=\"请输入密码\" show-password></el-input>\n        </el-form-item>\n        <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\n          <el-input v-model=\"registerForm.confirmPassword\" type=\"password\" placeholder=\"请再次输入密码\" show-password></el-input>\n        </el-form-item>\n        <el-form-item label=\"姓名\" prop=\"name\">\n          <el-input v-model=\"registerForm.name\" placeholder=\"请输入姓名\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"邮箱\" prop=\"email\">\n          <el-input v-model=\"registerForm.email\" placeholder=\"请输入邮箱\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"手机号\" prop=\"phone\">\n          <el-input v-model=\"registerForm.phone\" placeholder=\"请输入手机号\"></el-input>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"registerDialogVisible = false\">取消</el-button>\n          <el-button type=\"primary\" :loading=\"registerLoading\" @click=\"handleRegister\">注册</el-button>\n        </span>\n      </template>\n    </el-dialog>\n    \n    <!-- 忘记密码对话框 -->\n    <el-dialog\n      title=\"忘记密码\"\n      v-model=\"forgotPasswordDialogVisible\"\n      width=\"400px\"\n      center\n      destroy-on-close\n    >\n      <el-form :model=\"forgotPasswordForm\" :rules=\"forgotPasswordRules\" ref=\"forgotPasswordFormRef\" label-width=\"100px\">\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input v-model=\"forgotPasswordForm.username\" placeholder=\"请输入用户名\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"邮箱\" prop=\"email\">\n          <el-input v-model=\"forgotPasswordForm.email\" placeholder=\"请输入注册时的邮箱\"></el-input>\n        </el-form-item>\n        \n        <template v-if=\"verifiedIdentity\">\n          <el-form-item label=\"新密码\" prop=\"newPassword\">\n            <el-input v-model=\"forgotPasswordForm.newPassword\" type=\"password\" placeholder=\"请输入新密码\" show-password></el-input>\n          </el-form-item>\n          <el-form-item label=\"确认新密码\" prop=\"confirmNewPassword\">\n            <el-input v-model=\"forgotPasswordForm.confirmNewPassword\" type=\"password\" placeholder=\"请再次输入新密码\" show-password></el-input>\n          </el-form-item>\n        </template>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"forgotPasswordDialogVisible = false\">取消</el-button>\n          <el-button type=\"primary\" :loading=\"forgotPasswordLoading\" @click=\"handleForgotPassword\">\n            {{ verifiedIdentity ? '重置密码' : '验证身份' }}\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script setup>\nimport { reactive, ref, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage } from 'element-plus'\nimport { User, Lock, Check } from '@element-plus/icons-vue'\nimport axios from 'axios'\n\n// 配置全局API请求拦截器，自动添加token\naxios.interceptors.request.use(\n  config => {\n    const token = localStorage.getItem('token')\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`\n    }\n    return config\n  },\n  error => {\n    return Promise.reject(error)\n  }\n)\n\n// 响应拦截器，处理401错误\naxios.interceptors.response.use(\n  response => response,\n  error => {\n    if (error.response && error.response.status === 401) {\n      // 清除本地存储的token\n      localStorage.removeItem('token')\n      localStorage.removeItem('userInfo')\n      // 如果用户不在登录页，重定向到登录页\n      if (window.location.pathname !== '/login') {\n        ElMessage.error('登录已过期，请重新登录')\n        window.location.href = '/#/login'\n      }\n    }\n    return Promise.reject(error)\n  }\n)\n\nconst router = useRouter()\nconst loginFormRef = ref(null)\nconst loading = ref(false)\nconst API_URL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api'\n\n// 登录相关\nconst loginForm = reactive({\n  username: '',\n  password: '',\n  remember: false\n})\n\nconst loginRules = {\n  username: [\n    { required: true, message: '请输入用户名', trigger: 'blur' },\n    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }\n  ],\n  password: [\n    { required: true, message: '请输入密码', trigger: 'blur' },\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\n  ]\n}\n\nconst handleLogin = async () => {\n  if (!loginFormRef.value) return\n  \n  try {\n    await loginFormRef.value.validate(async (valid) => {\n      if (valid) {\n        loading.value = true\n        \n        try {\n          const response = await axios.post(`${API_URL}/auth/login`, {\n            username: loginForm.username,\n            password: loginForm.password\n          })\n          \n          // 登录成功，保存token和用户信息\n          const { token, data } = response.data\n          localStorage.setItem('token', token)\n\n          // 如果选择记住我，保存用户名\n          if (loginForm.remember) {\n            localStorage.setItem('rememberedUsername', loginForm.username)\n          } else {\n            localStorage.removeItem('rememberedUsername')\n          }\n\n          // 存储用户信息\n          localStorage.setItem('userInfo', JSON.stringify(data))\n\n          // 单独保存关键信息，方便使用\n          localStorage.setItem('userId', data.id)\n          localStorage.setItem('userRole', data.role)\n          if (data.teacher_id) {\n            localStorage.setItem('teacherId', data.teacher_id)\n            console.log('保存教师ID:', data.teacher_id)\n          } else if (data.role === 'student') {\n            // 如果是学生但没有student_id，尝试从其他地方获取\n            try {\n              const studentResponse = await axios.get(`${API_URL}/students/by-user/${data.id}`)\n              if (studentResponse.data.success && studentResponse.data.data) {\n                localStorage.setItem('studentId', studentResponse.data.data.id)\n                console.log('通过用户ID获取并保存学生ID:', studentResponse.data.data.id)\n              } else {\n                console.error('无法获取学生ID，但用户角色为学生')\n                ElMessage.warning('无法获取您的学生信息，部分功能可能无法使用')\n              }\n            } catch (err) {\n              console.error('获取学生信息失败:', err)\n            }\n          }\n\n          console.log('登录成功，用户信息:', data)\n          \n          ElMessage.success('登录成功')\n          \n          // 根据角色跳转到不同页面\n          if (data.role === 'admin' || data.role === 'administrator') {\n            router.push('/teachers/list')\n          } else if (data.role === 'teacher') {\n            router.push('/exams/list')\n          } else {\n            router.push('/dashboard') // 默认页面\n          }\n        } catch (error) {\n          console.error('登录失败:', error)\n          ElMessage.error(error.response?.data?.message || '登录失败，请检查用户名和密码')\n        } finally {\n          loading.value = false\n        }\n      }\n    })\n  } catch (error) {\n    loading.value = false\n    ElMessage.error('表单验证失败')\n  }\n}\n\n// 注册相关\nconst registerDialogVisible = ref(false)\nconst registerFormRef = ref(null)\nconst registerLoading = ref(false)\n\nconst registerForm = reactive({\n  username: '',\n  password: '',\n  confirmPassword: '',\n  name: '',\n  email: '',\n  phone: '',\n  student_id: '',\n  role: 'user'  // 默认注册为普通用户\n})\n\nconst validatePass = (rule, value, callback) => {\n  if (value === '') {\n    callback(new Error('请再次输入密码'))\n  } else if (value !== registerForm.password) {\n    callback(new Error('两次输入密码不一致!'))\n  } else {\n    callback()\n  }\n}\n\nconst registerRules = {\n  username: [\n    { required: true, message: '请输入用户名', trigger: 'blur' },\n    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }\n  ],\n  password: [\n    { required: true, message: '请输入密码', trigger: 'blur' },\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\n  ],\n  confirmPassword: [\n    { required: true, message: '请再次输入密码', trigger: 'blur' },\n    { validator: validatePass, trigger: 'blur' }\n  ],\n  name: [\n    { required: true, message: '请输入姓名', trigger: 'blur' }\n  ],\n  email: [\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\n  ],\n  phone: [\n    { pattern: /^1[3456789]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\n  ]\n}\n\nconst showRegister = () => {\n  registerDialogVisible.value = true\n}\n\nconst handleRegister = async () => {\n  if (!registerFormRef.value) return\n  \n  try {\n    await registerFormRef.value.validate(async (valid) => {\n      if (valid) {\n        registerLoading.value = true\n        \n        try {\n          const { confirmPassword, ...registerData } = registerForm\n          \n          const response = await axios.post(`${API_URL}/auth/register`, registerData)\n          \n          ElMessage.success('注册成功，请登录')\n          registerDialogVisible.value = false\n          \n          // 可选：自动填充登录表单\n          loginForm.username = registerForm.username\n          loginForm.password = ''\n        } catch (error) {\n          console.error('注册失败:', error)\n          ElMessage.error(error.response?.data?.message || '注册失败，请稍后重试')\n        } finally {\n          registerLoading.value = false\n        }\n      }\n    })\n  } catch (error) {\n    registerLoading.value = false\n    ElMessage.error('表单验证失败')\n  }\n}\n\n// 忘记密码相关\nconst forgotPasswordDialogVisible = ref(false)\nconst forgotPasswordFormRef = ref(null)\nconst forgotPasswordLoading = ref(false)\nconst verifiedIdentity = ref(false)\n\nconst forgotPasswordForm = reactive({\n  username: '',\n  email: '',\n  newPassword: '',\n  confirmNewPassword: ''\n})\n\nconst validateNewPass = (rule, value, callback) => {\n  if (value === '') {\n    callback(new Error('请输入新密码'))\n  } else if (value.length < 6 || value.length > 20) {\n    callback(new Error('密码长度应在6到20个字符之间'))\n  } else {\n    // 如果确认密码已输入，重新验证\n    if (forgotPasswordForm.confirmNewPassword !== '') {\n      forgotPasswordFormRef.value?.validateField('confirmNewPassword')\n    }\n    callback()\n  }\n}\n\nconst validateConfirmNewPass = (rule, value, callback) => {\n  if (value === '') {\n    callback(new Error('请再次输入新密码'))\n  } else if (value !== forgotPasswordForm.newPassword) {\n    callback(new Error('两次输入密码不一致!'))\n  } else {\n    callback()\n  }\n}\n\nconst forgotPasswordRules = {\n  username: [\n    { required: true, message: '请输入用户名', trigger: 'blur' }\n  ],\n  email: [\n    { required: true, message: '请输入邮箱', trigger: 'blur' },\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\n  ],\n  newPassword: [\n    { required: true, message: '请输入新密码', trigger: 'blur' },\n    { validator: validateNewPass, trigger: 'blur' }\n  ],\n  confirmNewPassword: [\n    { required: true, message: '请再次输入新密码', trigger: 'blur' },\n    { validator: validateConfirmNewPass, trigger: 'blur' }\n  ]\n}\n\nconst showForgotPassword = () => {\n  forgotPasswordDialogVisible.value = true\n  verifiedIdentity.value = false\n  forgotPasswordForm.newPassword = ''\n  forgotPasswordForm.confirmNewPassword = ''\n}\n\nconst handleForgotPassword = async () => {\n  if (!forgotPasswordFormRef.value) return\n  \n  try {\n    // 根据当前步骤验证不同的字段\n    if (verifiedIdentity.value) {\n      // 只验证密码字段\n      await forgotPasswordFormRef.value.validateField(['newPassword', 'confirmNewPassword'])\n    } else {\n      // 只验证用户名和邮箱\n      await forgotPasswordFormRef.value.validateField(['username', 'email'])\n    }\n    \n    forgotPasswordLoading.value = true\n    \n    try {\n      if (!verifiedIdentity.value) {\n        // 第一步：验证用户名和邮箱是否匹配\n        const response = await axios.post(`${API_URL}/auth/verify-identity`, {\n          username: forgotPasswordForm.username,\n          email: forgotPasswordForm.email\n        })\n        \n        if (response.data.success) {\n          ElMessage.success('身份验证成功，请设置新密码')\n          verifiedIdentity.value = true\n        } else {\n          ElMessage.error(response.data.message || '用户名和邮箱不匹配')\n        }\n      } else {\n        // 第二步：重置密码\n        const response = await axios.post(`${API_URL}/auth/reset-password`, {\n          username: forgotPasswordForm.username,\n          email: forgotPasswordForm.email,\n          newPassword: forgotPasswordForm.newPassword\n        })\n        \n        ElMessage.success('密码重置成功，请使用新密码登录')\n        forgotPasswordDialogVisible.value = false\n        \n        // 可以选择自动填充用户名到登录表单\n        loginForm.username = forgotPasswordForm.username\n        loginForm.password = ''\n      }\n    } catch (error) {\n      console.error('操作失败:', error)\n      if (verifiedIdentity.value) {\n        ElMessage.error(error.response?.data?.message || '密码重置失败，请稍后重试')\n      } else {\n        ElMessage.error(error.response?.data?.message || '身份验证失败，请确认用户名和邮箱是否正确')\n      }\n    } finally {\n      forgotPasswordLoading.value = false\n    }\n  } catch (error) {\n    forgotPasswordLoading.value = false\n    ElMessage.error('表单验证失败')\n    console.error(error)\n  }\n}\n\n// 检查是否有记住的用户名\nconst checkRememberedUsername = () => {\n  const rememberedUsername = localStorage.getItem('rememberedUsername')\n  if (rememberedUsername) {\n    loginForm.username = rememberedUsername\n    loginForm.remember = true\n  }\n}\n\n// 组件挂载时检查记住的用户名\ncheckRememberedUsername()\n</script>\n\n<style scoped>\n.login-container {\n  height: 100vh;\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgb(149, 117, 205);\n}\n\n.login-card {\n  width: 1000px;\n  height: 600px;\n  display: flex;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.1);\n}\n\n/* Left side */\n.login-info {\n  width: 50%;\n  background: linear-gradient(135deg, #8E44AD 0%, #7D3C98 100%);\n  padding: 40px;\n  display: flex;\n  flex-direction: column;\n  color: white;\n}\n\n.logo-wrapper {\n  display: flex;\n  align-items: center;\n  margin-bottom: 60px;\n}\n\n.logo-icon {\n  width: 120px;\n  height: 120px;\n  background-color: white;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 12px;\n  font-size: 24px;\n  color: #8E44AD;\n}\n\n.logo-text {\n  font-size: 20px;\n  font-weight: bold;\n}\n\n.welcome-text {\n  margin-bottom: 60px;\n}\n\n.welcome-text h2 {\n  font-size: 32px;\n  margin-bottom: 12px;\n  font-weight: 600;\n}\n\n.welcome-text p {\n  font-size: 16px;\n  opacity: 0.8;\n}\n\n.feature-list {\n  margin-top: auto;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.feature-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background-color: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 12px;\n}\n\n.feature-text {\n  font-size: 16px;\n}\n\n/* Right side */\n.login-form-wrapper {\n  width: 50%;\n  background-color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40px;\n}\n\n.login-form-container {\n  width: 100%;\n  max-width: 320px;\n}\n\n.form-title {\n  font-size: 24px;\n  color: #333;\n  margin-bottom: 8px;\n  text-align: center;\n}\n\n.form-subtitle {\n  font-size: 14px;\n  color: #999;\n  margin-bottom: 30px;\n  text-align: center;\n}\n\n.login-form :deep(.el-input__wrapper) {\n  padding: 0 15px;\n  height: 50px;\n  box-shadow: 0 0 0 1px #e4e7ed inset;\n}\n\n.login-form :deep(.el-input__wrapper.is-focus) {\n  box-shadow: 0 0 0 1px #8E44AD inset;\n}\n\n.form-options {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.forgot-link {\n  color: #9B59B6;\n  text-decoration: none;\n  font-size: 14px;\n}\n\n.login-button {\n  width: 100%;\n  height: 50px;\n  border-radius: 6px;\n  font-size: 16px;\n  background: linear-gradient(135deg, #8E44AD 0%, #7D3C98 100%);\n  border: none;\n  margin-bottom: 20px;\n}\n\n.register-link {\n  text-align: center;\n  font-size: 14px;\n  color: #666;\n}\n\n.register-link a {\n  color: #9B59B6;\n  text-decoration: none;\n  margin-left: 5px;\n}\n\n/* Responsive */\n@media (max-width: 992px) {\n  .login-card {\n    width: 90%;\n    height: auto;\n    flex-direction: column;\n  }\n  \n  .login-info,\n  .login-form-wrapper {\n    width: 100%;\n    padding: 30px;\n  }\n  \n  .login-info {\n    padding-bottom: 40px;\n  }\n  \n  .welcome-text {\n    margin-bottom: 30px;\n  }\n  \n  .feature-list {\n    margin-top: 0;\n  }\n}\n.logo-img {\n  width: 80px;\n  height: 80px;\n  /* margin-right: 4px; */\n  object-fit: contain;\n}\n\n\n</style> ", "import script from \"./LoginView.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./LoginView.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./LoginView.vue?vue&type=style&index=0&id=6af90103&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-6af90103\"]])\n\nexport default __exports__"], "names": ["axios", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "error", "Promise", "reject", "response", "status", "removeItem", "window", "location", "pathname", "ElMessage", "href", "router", "useRouter", "loginFormRef", "ref", "loading", "API_URL", "process", "VUE_APP_API_URL", "loginForm", "reactive", "username", "password", "remember", "loginRules", "required", "message", "trigger", "min", "max", "handleLogin", "async", "value", "validate", "valid", "post", "data", "setItem", "JSON", "stringify", "id", "role", "teacher_id", "console", "log", "studentResponse", "get", "success", "warning", "err", "push", "registerDialogVisible", "registerFormRef", "registerLoading", "registerForm", "confirmPassword", "name", "email", "phone", "student_id", "validatePass", "rule", "callback", "Error", "registerRules", "validator", "type", "pattern", "handleRegister", "registerData", "forgotPasswordDialogVisible", "forgotPasswordFormRef", "forgotPasswordLoading", "verifiedIdentity", "forgotPasswordForm", "newPassword", "confirmNewPassword", "validateNewPass", "length", "validateField", "validateConfirmNewPass", "forgotPasswordRules", "showForgotPassword", "handleForgotPassword", "checkRememberedUsername", "rememberedUsername", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_createVNode", "_component_el_icon", "_unref", "Check", "class", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_component_el_form", "model", "rules", "_component_el_form_item", "prop", "_component_el_input", "$event", "placeholder", "User", "Lock", "_hoisted_13", "_component_el_checkbox", "_cache", "onClick", "_withModifiers", "_component_el_button", "_component_el_dialog", "title", "width", "center", "footer", "_withCtx", "_hoisted_14", "label", "_hoisted_15", "_Fragment", "key", "__exports__"], "sourceRoot": ""}