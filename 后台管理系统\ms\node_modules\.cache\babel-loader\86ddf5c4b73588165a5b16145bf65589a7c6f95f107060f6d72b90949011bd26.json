{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementBlock as _createElementBlock, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"evaluation-detail-container\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  class: \"description-content\"\n};\nconst _hoisted_4 = {\n  class: \"description-content\"\n};\nconst _hoisted_5 = {\n  class: \"description-content\"\n};\nconst _hoisted_6 = {\n  class: \"teacher-card-header\"\n};\nconst _hoisted_7 = {\n  class: \"teacher-avatar\"\n};\nconst _hoisted_8 = {\n  key: 0,\n  class: \"teacher-stats\"\n};\nconst _hoisted_9 = {\n  class: \"teacher-stats-item\"\n};\nconst _hoisted_10 = {\n  class: \"stats-value\"\n};\nconst _hoisted_11 = {\n  class: \"teacher-stats-item\"\n};\nconst _hoisted_12 = {\n  class: \"stats-value\"\n};\nconst _hoisted_13 = {\n  class: \"teacher-stats-item\"\n};\nconst _hoisted_14 = {\n  class: \"stats-value\"\n};\nconst _hoisted_15 = {\n  class: \"teacher-stats-item\"\n};\nconst _hoisted_16 = {\n  class: \"stats-value\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_descriptions_item = _resolveComponent(\"el-descriptions-item\");\n  const _component_el_descriptions = _resolveComponent(\"el-descriptions\");\n  const _component_el_divider = _resolveComponent(\"el-divider\");\n  const _component_el_rate = _resolveComponent(\"el-rate\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_el_image = _resolveComponent(\"el-image\");\n  const _component_el_avatar = _resolveComponent(\"el-avatar\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, {\n    class: \"box-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[3] || (_cache[3] = _createElementVNode(\"span\", {\n      class: \"title\"\n    }, \"督导评价详情\", -1 /* CACHED */)), _createElementVNode(\"div\", null, [_createVNode(_component_el_button, {\n      onClick: $setup.goBack\n    }, {\n      default: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\"返回列表\")])),\n      _: 1 /* STABLE */,\n      __: [1]\n    }, 8 /* PROPS */, [\"onClick\"]), $setup.isAdmin ? (_openBlock(), _createBlock(_component_el_button, {\n      key: 0,\n      type: \"primary\",\n      onClick: $setup.editEvaluation\n    }, {\n      default: _withCtx(() => _cache[2] || (_cache[2] = [_createTextVNode(\"编辑\")])),\n      _: 1 /* STABLE */,\n      __: [2]\n    }, 8 /* PROPS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])])]),\n    default: _withCtx(() => [_withDirectives((_openBlock(), _createElementBlock(\"div\", null, [_createVNode(_component_el_row, {\n      gutter: 20\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_col, {\n        span: 24\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_descriptions, {\n          title: \"评价基本信息\",\n          column: 3,\n          border: \"\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n            label: \"督导教研室\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.evaluationData.supervising_department), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_descriptions_item, {\n            label: \"评价日期\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDate($setup.evaluationData.evaluation_date)), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_descriptions_item, {\n            label: \"评估人\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.evaluationData.evaluator_name), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_divider), _createVNode(_component_el_descriptions, {\n          title: \"教学活动信息\",\n          column: 3,\n          border: \"\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n            label: \"病例/主题\",\n            span: 3\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.evaluationData.case_topic), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_descriptions_item, {\n            label: \"教学活动形式\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.evaluationData.teaching_form), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_descriptions_item, {\n            label: \"带教老师\",\n            span: 2\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.evaluationData.teacher_name) + \" (\" + _toDisplayString($setup.evaluationData.teacher_title) + \") \", 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_descriptions_item, {\n            label: \"学员姓名\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.evaluationData.student_name), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_descriptions_item, {\n            label: \"学员类别\",\n            span: 2\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.evaluationData.student_type), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_divider), _createVNode(_component_el_descriptions, {\n          title: \"评价内容\",\n          column: 1,\n          border: \"\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n            label: \"平均分\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_rate, {\n              modelValue: $setup.score,\n              \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.score = $event),\n              max: 10,\n              \"show-score\": \"\",\n              disabled: \"\",\n              \"score-template\": \"{value}\"\n            }, null, 8 /* PROPS */, [\"modelValue\"])]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_descriptions_item, {\n            label: \"亮点\"\n          }, {\n            default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_3, _toDisplayString($setup.evaluationData.highlights || '无'), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_descriptions_item, {\n            label: \"不足\"\n          }, {\n            default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, _toDisplayString($setup.evaluationData.shortcomings || '无'), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_descriptions_item, {\n            label: \"改进建议\"\n          }, {\n            default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_5, _toDisplayString($setup.evaluationData.improvement_suggestions || '无'), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_descriptions_item, {\n            label: \"能力认定\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_tag, {\n              type: $setup.evaluationData.competency_approved ? 'success' : 'danger',\n              size: \"large\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.evaluationData.competency_approved ? '同意' : '不同意'), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }, 8 /* PROPS */, [\"type\"])]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_divider), _createCommentVNode(\" 教师信息卡片 \"), $setup.teacherData.id ? (_openBlock(), _createBlock(_component_el_card, {\n      key: 0,\n      class: \"teacher-info-card\",\n      shadow: \"hover\"\n    }, {\n      header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_6, [_cache[5] || (_cache[5] = _createElementVNode(\"span\", null, \"带教老师信息\", -1 /* CACHED */)), _createVNode(_component_el_button, {\n        type: \"text\",\n        onClick: $setup.viewTeacherDetail\n      }, {\n        default: _withCtx(() => _cache[4] || (_cache[4] = [_createTextVNode(\"查看详情\")])),\n        _: 1 /* STABLE */,\n        __: [4]\n      }, 8 /* PROPS */, [\"onClick\"])])]),\n      default: _withCtx(() => [_createVNode(_component_el_row, {\n        gutter: 20\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_col, {\n          span: 4\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, [$setup.teacherData.photo ? (_openBlock(), _createBlock(_component_el_image, {\n            key: 0,\n            src: `http://localhost:3000${$setup.teacherData.photo}`,\n            fit: \"cover\",\n            class: \"avatar-image\",\n            \"preview-src-list\": [`http://localhost:3000${$setup.teacherData.photo}`]\n          }, null, 8 /* PROPS */, [\"src\", \"preview-src-list\"])) : (_openBlock(), _createBlock(_component_el_avatar, {\n            key: 1,\n            size: 100,\n            icon: \"UserFilled\"\n          }))])]),\n          _: 1 /* STABLE */\n        }), _createVNode(_component_el_col, {\n          span: 20\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_descriptions, {\n            column: 3,\n            border: \"\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n              label: \"姓名\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.teacherData.name), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_el_descriptions_item, {\n              label: \"性别\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.teacherData.gender), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_el_descriptions_item, {\n              label: \"科室\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.teacherData.department), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_el_descriptions_item, {\n              label: \"学校\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.teacherData.school), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_el_descriptions_item, {\n              label: \"专业\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.teacherData.major), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_el_descriptions_item, {\n              label: \"学历\"\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.teacherData.education), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }), $setup.competencyData ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n            class: \"stats-label\"\n          }, \"评价总数:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_10, _toDisplayString($setup.competencyData.total_evaluations), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_11, [_cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n            class: \"stats-label\"\n          }, \"认可数:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_12, _toDisplayString($setup.competencyData.approved_count), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_13, [_cache[8] || (_cache[8] = _createElementVNode(\"div\", {\n            class: \"stats-label\"\n          }, \"认可率:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_14, _toDisplayString($setup.competencyData.approval_rate) + \"%\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_15, [_cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n            class: \"stats-label\"\n          }, \"认证状态:\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_16, [_createVNode(_component_el_tag, {\n            type: $setup.competencyData.is_certified ? 'success' : 'info'\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.competencyData.is_certified ? '已认证' : '未认证'), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"type\"])])])])) : _createCommentVNode(\"v-if\", true)]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })) : _createCommentVNode(\"v-if\", true)])), [[_directive_loading, $setup.loading]])]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_component_el_button", "onClick", "$setup", "goBack", "_cache", "isAdmin", "_createBlock", "type", "editEvaluation", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_descriptions", "title", "column", "border", "_component_el_descriptions_item", "label", "evaluationData", "supervising_department", "formatDate", "evaluation_date", "evaluator_name", "_component_el_divider", "case_topic", "teaching_form", "teacher_name", "_toDisplayString", "teacher_title", "student_name", "student_type", "_component_el_rate", "score", "$event", "max", "disabled", "_hoisted_3", "highlights", "_hoisted_4", "shortcomings", "_hoisted_5", "improvement_suggestions", "_component_el_tag", "competency_approved", "size", "_createCommentVNode", "teacher<PERSON><PERSON>", "id", "shadow", "_hoisted_6", "viewTeacherDetail", "_hoisted_7", "photo", "_component_el_image", "src", "fit", "_component_el_avatar", "icon", "name", "gender", "department", "school", "major", "education", "competencyData", "_hoisted_8", "_hoisted_9", "_hoisted_10", "total_evaluations", "_hoisted_11", "_hoisted_12", "approved_count", "_hoisted_13", "_hoisted_14", "approval_rate", "_hoisted_15", "_hoisted_16", "is_certified", "loading"], "sources": ["D:\\admin\\202506\\督导系统\\后台管理系统\\ms\\src\\views\\evaluations\\EvaluationDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"evaluation-detail-container\">\r\n    <el-card class=\"box-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span class=\"title\">督导评价详情</span>\r\n          <div>\r\n            <el-button @click=\"goBack\">返回列表</el-button>\r\n            <el-button type=\"primary\" @click=\"editEvaluation\" v-if=\"isAdmin\">编辑</el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <div v-loading=\"loading\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-descriptions title=\"评价基本信息\" :column=\"3\" border>\r\n              <el-descriptions-item label=\"督导教研室\">{{ evaluationData.supervising_department }}</el-descriptions-item>\r\n              <el-descriptions-item label=\"评价日期\">{{ formatDate(evaluationData.evaluation_date) }}</el-descriptions-item>\r\n              <el-descriptions-item label=\"评估人\">{{ evaluationData.evaluator_name }}</el-descriptions-item>\r\n            </el-descriptions>\r\n            \r\n            <el-divider />\r\n            \r\n            <el-descriptions title=\"教学活动信息\" :column=\"3\" border>\r\n              <el-descriptions-item label=\"病例/主题\" :span=\"3\">{{ evaluationData.case_topic }}</el-descriptions-item>\r\n              <el-descriptions-item label=\"教学活动形式\">{{ evaluationData.teaching_form }}</el-descriptions-item>\r\n              <el-descriptions-item label=\"带教老师\" :span=\"2\">\r\n                {{ evaluationData.teacher_name }} ({{ evaluationData.teacher_title }})\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"学员姓名\">{{ evaluationData.student_name }}</el-descriptions-item>\r\n              <el-descriptions-item label=\"学员类别\" :span=\"2\">{{ evaluationData.student_type }}</el-descriptions-item>\r\n            </el-descriptions>\r\n            \r\n            <el-divider />\r\n            \r\n            <el-descriptions title=\"评价内容\" :column=\"1\" border>\r\n              <el-descriptions-item label=\"平均分\">\r\n                <el-rate\r\n                  v-model=\"score\"\r\n                  :max=\"10\"\r\n                  show-score\r\n                  disabled\r\n                  score-template=\"{value}\"\r\n                />\r\n              </el-descriptions-item>\r\n              \r\n              <el-descriptions-item label=\"亮点\">\r\n                <div class=\"description-content\">\r\n                  {{ evaluationData.highlights || '无' }}\r\n                </div>\r\n              </el-descriptions-item>\r\n              \r\n              <el-descriptions-item label=\"不足\">\r\n                <div class=\"description-content\">\r\n                  {{ evaluationData.shortcomings || '无' }}\r\n                </div>\r\n              </el-descriptions-item>\r\n              \r\n              <el-descriptions-item label=\"改进建议\">\r\n                <div class=\"description-content\">\r\n                  {{ evaluationData.improvement_suggestions || '无' }}\r\n                </div>\r\n              </el-descriptions-item>\r\n              \r\n              <el-descriptions-item label=\"能力认定\">\r\n                <el-tag :type=\"evaluationData.competency_approved ? 'success' : 'danger'\" size=\"large\">\r\n                  {{ evaluationData.competency_approved ? '同意' : '不同意' }}\r\n                </el-tag>\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-divider />\r\n        \r\n        <!-- 教师信息卡片 -->\r\n        <el-card class=\"teacher-info-card\" shadow=\"hover\" v-if=\"teacherData.id\">\r\n          <template #header>\r\n            <div class=\"teacher-card-header\">\r\n              <span>带教老师信息</span>\r\n              <el-button type=\"text\" @click=\"viewTeacherDetail\">查看详情</el-button>\r\n            </div>\r\n          </template>\r\n          \r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"4\">\r\n              <div class=\"teacher-avatar\">\r\n                <el-image\r\n                  v-if=\"teacherData.photo\"\r\n                  :src=\"`http://localhost:3000${teacherData.photo}`\"\r\n                  fit=\"cover\"\r\n                  class=\"avatar-image\"\r\n                  :preview-src-list=\"[`http://localhost:3000${teacherData.photo}`]\"\r\n                />\r\n                <el-avatar v-else :size=\"100\" icon=\"UserFilled\" />\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"20\">\r\n              <el-descriptions :column=\"3\" border>\r\n                <el-descriptions-item label=\"姓名\">{{ teacherData.name }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"性别\">{{ teacherData.gender }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"科室\">{{ teacherData.department }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"学校\">{{ teacherData.school }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"专业\">{{ teacherData.major }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"学历\">{{ teacherData.education }}</el-descriptions-item>\r\n              </el-descriptions>\r\n              \r\n              <div class=\"teacher-stats\" v-if=\"competencyData\">\r\n                <div class=\"teacher-stats-item\">\r\n                  <div class=\"stats-label\">评价总数:</div>\r\n                  <div class=\"stats-value\">{{ competencyData.total_evaluations }}</div>\r\n                </div>\r\n                <div class=\"teacher-stats-item\">\r\n                  <div class=\"stats-label\">认可数:</div>\r\n                  <div class=\"stats-value\">{{ competencyData.approved_count }}</div>\r\n                </div>\r\n                <div class=\"teacher-stats-item\">\r\n                  <div class=\"stats-label\">认可率:</div>\r\n                  <div class=\"stats-value\">{{ competencyData.approval_rate }}%</div>\r\n                </div>\r\n                <div class=\"teacher-stats-item\">\r\n                  <div class=\"stats-label\">认证状态:</div>\r\n                  <div class=\"stats-value\">\r\n                    <el-tag :type=\"competencyData.is_certified ? 'success' : 'info'\">\r\n                      {{ competencyData.is_certified ? '已认证' : '未认证' }}\r\n                    </el-tag>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, computed, onMounted } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'EvaluationDetail',\r\n  setup() {\r\n    const route = useRoute()\r\n    const router = useRouter()\r\n    const evaluationId = route.params.id\r\n      let token = localStorage.getItem('token')\r\n    if (token) {\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`\r\n    }\r\n    // 基础数据\r\n    const loading = ref(false)\r\n    const evaluationData = ref({})\r\n    const teacherData = ref({})\r\n    const competencyData = ref(null)\r\n    \r\n    // 是否为管理员或督导\r\n    const isAdmin = computed(() => {\r\n      // 这里可以根据实际的用户角色判断\r\n      // 简单起见，这里暂时返回 true\r\n      return true\r\n    })\r\n    \r\n    // 评分\r\n    const score = computed(() => {\r\n      return evaluationData.value.average_score || 0\r\n    })\r\n    \r\n    // 生命周期钩子\r\n    onMounted(() => {\r\n      fetchEvaluationDetail()\r\n    })\r\n    \r\n    // 获取评价详情\r\n    const fetchEvaluationDetail = async () => {\r\n      loading.value = true\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/evaluations/${evaluationId}`)\r\n        evaluationData.value = response.data.data\r\n        \r\n        // 如果有教师ID，获取教师信息\r\n        if (evaluationData.value.teacher_id) {\r\n          await fetchTeacherData(evaluationData.value.teacher_id)\r\n          await fetchCompetencyStatus(evaluationData.value.teacher_id)\r\n        }\r\n      } catch (error) {\r\n        console.error('获取评价详情失败:', error)\r\n        ElMessage.error('获取评价详情失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 获取教师信息\r\n    const fetchTeacherData = async (teacherId) => {\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/teachers/${teacherId}`)\r\n        teacherData.value = response.data.data\r\n      } catch (error) {\r\n        console.error('获取教师信息失败:', error)\r\n      }\r\n    }\r\n    \r\n    // 获取能力认证状态\r\n    const fetchCompetencyStatus = async (teacherId) => {\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/evaluations/competency/teacher/${teacherId}`)\r\n        competencyData.value = response.data.data\r\n      } catch (error) {\r\n        console.error('获取能力认证状态失败:', error)\r\n      }\r\n    }\r\n    \r\n    // 格式化日期\r\n    const formatDate = (dateString) => {\r\n      if (!dateString) return '-'\r\n      const date = new Date(dateString)\r\n      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`\r\n    }\r\n    \r\n    // 返回列表\r\n    const goBack = () => {\r\n      router.push('/evaluations/list')\r\n    }\r\n    \r\n    // 编辑评价\r\n    const editEvaluation = () => {\r\n      router.push(`/evaluations/add?id=${evaluationId}`)\r\n    }\r\n    \r\n    // 查看教师详情\r\n    const viewTeacherDetail = () => {\r\n      if (teacherData.value.id) {\r\n        router.push(`/teachers/detail/${teacherData.value.id}`)\r\n      }\r\n    }\r\n    \r\n    return {\r\n      loading,\r\n      evaluationData,\r\n      teacherData,\r\n      competencyData,\r\n      score,\r\n      isAdmin,\r\n      formatDate,\r\n      goBack,\r\n      editEvaluation,\r\n      viewTeacherDetail\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.evaluation-detail-container {\r\n  padding: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.description-content {\r\n  white-space: pre-line;\r\n  padding: 10px;\r\n  background-color: #f7f7f7;\r\n  border-radius: 4px;\r\n  min-height: 50px;\r\n}\r\n\r\n.teacher-info-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.teacher-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.teacher-avatar {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.avatar-image {\r\n  width: 100px;\r\n  height: 100px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 2px solid #eee;\r\n}\r\n\r\n.teacher-stats {\r\n  display: flex;\r\n  margin-top: 15px;\r\n  background-color: #f7f7f7;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.teacher-stats-item {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.stats-label {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stats-value {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n</style> "], "mappings": ";;EACOA,KAAK,EAAC;AAA6B;;EAG7BA,KAAK,EAAC;AAAa;;EA4CXA,KAAK,EAAC;AAAqB;;EAM3BA,KAAK,EAAC;AAAqB;;EAM3BA,KAAK,EAAC;AAAqB;;EAmB/BA,KAAK,EAAC;AAAqB;;EAQzBA,KAAK,EAAC;AAAgB;;;EAqBtBA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAoB;;EAExBA,KAAK,EAAC;AAAa;;EAErBA,KAAK,EAAC;AAAoB;;EAExBA,KAAK,EAAC;AAAa;;EAErBA,KAAK,EAAC;AAAoB;;EAExBA,KAAK,EAAC;AAAa;;EAErBA,KAAK,EAAC;AAAoB;;EAExBA,KAAK,EAAC;AAAa;;;;;;;;;;;;;;uBA1HxCC,mBAAA,CAsIM,OAtINC,UAsIM,GArIJC,YAAA,CAoIUC,kBAAA;IApIDJ,KAAK,EAAC;EAAU;IACZK,MAAM,EAAAC,QAAA,CACf,MAMM,CANNC,mBAAA,CAMM,OANNC,UAMM,G,0BALJD,mBAAA,CAAiC;MAA3BP,KAAK,EAAC;IAAO,GAAC,QAAM,qBAC1BO,mBAAA,CAGM,cAFJJ,YAAA,CAA2CM,oBAAA;MAA/BC,OAAK,EAAEC,MAAA,CAAAC;IAAM;wBAAE,MAAIC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;oCACyBF,MAAA,CAAAG,OAAO,I,cAA/DC,YAAA,CAA+EN,oBAAA;;MAApEO,IAAI,EAAC,SAAS;MAAEN,OAAK,EAAEC,MAAA,CAAAM;;wBAA+B,MAAEJ,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;sBAKzE,MAwHM,C,+BAxHNZ,mBAAA,CAwHM,cAvHJE,YAAA,CA0DSe,iBAAA;MA1DAC,MAAM,EAAE;IAAE;wBACjB,MAwDS,CAxDThB,YAAA,CAwDSiB,iBAAA;QAxDAC,IAAI,EAAE;MAAE;0BACf,MAIkB,CAJlBlB,YAAA,CAIkBmB,0BAAA;UAJDC,KAAK,EAAC,QAAQ;UAAEC,MAAM,EAAE,CAAC;UAAEC,MAAM,EAAN;;4BAC1C,MAAsG,CAAtGtB,YAAA,CAAsGuB,+BAAA;YAAhFC,KAAK,EAAC;UAAO;8BAAC,MAA2C,C,kCAAxChB,MAAA,CAAAiB,cAAc,CAACC,sBAAsB,iB;;cAC5E1B,YAAA,CAA0GuB,+BAAA;YAApFC,KAAK,EAAC;UAAM;8BAAC,MAAgD,C,kCAA7ChB,MAAA,CAAAmB,UAAU,CAACnB,MAAA,CAAAiB,cAAc,CAACG,eAAe,kB;;cAC/E5B,YAAA,CAA4FuB,+BAAA;YAAtEC,KAAK,EAAC;UAAK;8BAAC,MAAmC,C,kCAAhChB,MAAA,CAAAiB,cAAc,CAACI,cAAc,iB;;;;YAGpE7B,YAAA,CAAc8B,qBAAA,GAEd9B,YAAA,CAQkBmB,0BAAA;UARDC,KAAK,EAAC,QAAQ;UAAEC,MAAM,EAAE,CAAC;UAAEC,MAAM,EAAN;;4BAC1C,MAAoG,CAApGtB,YAAA,CAAoGuB,+BAAA;YAA9EC,KAAK,EAAC,OAAO;YAAEN,IAAI,EAAE;;8BAAG,MAA+B,C,kCAA5BV,MAAA,CAAAiB,cAAc,CAACM,UAAU,iB;;cAC1E/B,YAAA,CAA8FuB,+BAAA;YAAxEC,KAAK,EAAC;UAAQ;8BAAC,MAAkC,C,kCAA/BhB,MAAA,CAAAiB,cAAc,CAACO,aAAa,iB;;cACpEhC,YAAA,CAEuBuB,+BAAA;YAFDC,KAAK,EAAC,MAAM;YAAEN,IAAI,EAAE;;8BACxC,MAAiC,C,kCAA9BV,MAAA,CAAAiB,cAAc,CAACQ,YAAY,IAAG,IAAE,GAAAC,gBAAA,CAAG1B,MAAA,CAAAiB,cAAc,CAACU,aAAa,IAAG,IACvE,gB;;cACAnC,YAAA,CAA2FuB,+BAAA;YAArEC,KAAK,EAAC;UAAM;8BAAC,MAAiC,C,kCAA9BhB,MAAA,CAAAiB,cAAc,CAACW,YAAY,iB;;cACjEpC,YAAA,CAAqGuB,+BAAA;YAA/EC,KAAK,EAAC,MAAM;YAAEN,IAAI,EAAE;;8BAAG,MAAiC,C,kCAA9BV,MAAA,CAAAiB,cAAc,CAACY,YAAY,iB;;;;YAG7ErC,YAAA,CAAc8B,qBAAA,GAEd9B,YAAA,CAkCkBmB,0BAAA;UAlCDC,KAAK,EAAC,MAAM;UAAEC,MAAM,EAAE,CAAC;UAAEC,MAAM,EAAN;;4BACxC,MAQuB,CARvBtB,YAAA,CAQuBuB,+BAAA;YARDC,KAAK,EAAC;UAAK;8BAC/B,MAME,CANFxB,YAAA,CAMEsC,kBAAA;0BALS9B,MAAA,CAAA+B,KAAK;yEAAL/B,MAAA,CAAA+B,KAAK,GAAAC,MAAA;cACbC,GAAG,EAAE,EAAE;cACR,YAAU,EAAV,EAAU;cACVC,QAAQ,EAAR,EAAQ;cACR,gBAAc,EAAC;;;cAInB1C,YAAA,CAIuBuB,+BAAA;YAJDC,KAAK,EAAC;UAAI;8BAC9B,MAEM,CAFNpB,mBAAA,CAEM,OAFNuC,UAEM,EAAAT,gBAAA,CADD1B,MAAA,CAAAiB,cAAc,CAACmB,UAAU,wB;;cAIhC5C,YAAA,CAIuBuB,+BAAA;YAJDC,KAAK,EAAC;UAAI;8BAC9B,MAEM,CAFNpB,mBAAA,CAEM,OAFNyC,UAEM,EAAAX,gBAAA,CADD1B,MAAA,CAAAiB,cAAc,CAACqB,YAAY,wB;;cAIlC9C,YAAA,CAIuBuB,+BAAA;YAJDC,KAAK,EAAC;UAAM;8BAChC,MAEM,CAFNpB,mBAAA,CAEM,OAFN2C,UAEM,EAAAb,gBAAA,CADD1B,MAAA,CAAAiB,cAAc,CAACuB,uBAAuB,wB;;cAI7ChD,YAAA,CAIuBuB,+BAAA;YAJDC,KAAK,EAAC;UAAM;8BAChC,MAES,CAFTxB,YAAA,CAESiD,iBAAA;cAFApC,IAAI,EAAEL,MAAA,CAAAiB,cAAc,CAACyB,mBAAmB;cAAyBC,IAAI,EAAC;;gCAC7E,MAAuD,C,kCAApD3C,MAAA,CAAAiB,cAAc,CAACyB,mBAAmB,gC;;;;;;;;;;QAO/ClD,YAAA,CAAc8B,qBAAA,GAEdsB,mBAAA,YAAe,EACyC5C,MAAA,CAAA6C,WAAW,CAACC,EAAE,I,cAAtE1C,YAAA,CAuDUX,kBAAA;;MAvDDJ,KAAK,EAAC,mBAAmB;MAAC0D,MAAM,EAAC;;MAC7BrD,MAAM,EAAAC,QAAA,CACf,MAGM,CAHNC,mBAAA,CAGM,OAHNoD,UAGM,G,0BAFJpD,mBAAA,CAAmB,cAAb,QAAM,qBACZJ,YAAA,CAAkEM,oBAAA;QAAvDO,IAAI,EAAC,MAAM;QAAEN,OAAK,EAAEC,MAAA,CAAAiD;;0BAAmB,MAAI/C,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;;wBAI1D,MA8CS,CA9CTV,YAAA,CA8CSe,iBAAA;QA9CAC,MAAM,EAAE;MAAE;0BACjB,MAWS,CAXThB,YAAA,CAWSiB,iBAAA;UAXAC,IAAI,EAAE;QAAC;4BACd,MASM,CATNd,mBAAA,CASM,OATNsD,UASM,GAPIlD,MAAA,CAAA6C,WAAW,CAACM,KAAK,I,cADzB/C,YAAA,CAMEgD,mBAAA;;YAJCC,GAAG,0BAA0BrD,MAAA,CAAA6C,WAAW,CAACM,KAAK;YAC/CG,GAAG,EAAC,OAAO;YACXjE,KAAK,EAAC,cAAc;YACnB,kBAAgB,2BAA2BW,MAAA,CAAA6C,WAAW,CAACM,KAAK;iFAE/D/C,YAAA,CAAkDmD,oBAAA;;YAA/BZ,IAAI,EAAE,GAAG;YAAEa,IAAI,EAAC;;;YAGvChE,YAAA,CAgCSiB,iBAAA;UAhCAC,IAAI,EAAE;QAAE;4BACf,MAOkB,CAPlBlB,YAAA,CAOkBmB,0BAAA;YAPAE,MAAM,EAAE,CAAC;YAAEC,MAAM,EAAN;;8BAC3B,MAA8E,CAA9EtB,YAAA,CAA8EuB,+BAAA;cAAxDC,KAAK,EAAC;YAAI;gCAAC,MAAsB,C,kCAAnBhB,MAAA,CAAA6C,WAAW,CAACY,IAAI,iB;;gBACpDjE,YAAA,CAAgFuB,+BAAA;cAA1DC,KAAK,EAAC;YAAI;gCAAC,MAAwB,C,kCAArBhB,MAAA,CAAA6C,WAAW,CAACa,MAAM,iB;;gBACtDlE,YAAA,CAAoFuB,+BAAA;cAA9DC,KAAK,EAAC;YAAI;gCAAC,MAA4B,C,kCAAzBhB,MAAA,CAAA6C,WAAW,CAACc,UAAU,iB;;gBAC1DnE,YAAA,CAAgFuB,+BAAA;cAA1DC,KAAK,EAAC;YAAI;gCAAC,MAAwB,C,kCAArBhB,MAAA,CAAA6C,WAAW,CAACe,MAAM,iB;;gBACtDpE,YAAA,CAA+EuB,+BAAA;cAAzDC,KAAK,EAAC;YAAI;gCAAC,MAAuB,C,kCAApBhB,MAAA,CAAA6C,WAAW,CAACgB,KAAK,iB;;gBACrDrE,YAAA,CAAmFuB,+BAAA;cAA7DC,KAAK,EAAC;YAAI;gCAAC,MAA2B,C,kCAAxBhB,MAAA,CAAA6C,WAAW,CAACiB,SAAS,iB;;;;cAG1B9D,MAAA,CAAA+D,cAAc,I,cAA/CzE,mBAAA,CAqBM,OArBN0E,UAqBM,GApBJpE,mBAAA,CAGM,OAHNqE,UAGM,G,0BAFJrE,mBAAA,CAAoC;YAA/BP,KAAK,EAAC;UAAa,GAAC,OAAK,qBAC9BO,mBAAA,CAAqE,OAArEsE,WAAqE,EAAAxC,gBAAA,CAAzC1B,MAAA,CAAA+D,cAAc,CAACI,iBAAiB,iB,GAE9DvE,mBAAA,CAGM,OAHNwE,WAGM,G,0BAFJxE,mBAAA,CAAmC;YAA9BP,KAAK,EAAC;UAAa,GAAC,MAAI,qBAC7BO,mBAAA,CAAkE,OAAlEyE,WAAkE,EAAA3C,gBAAA,CAAtC1B,MAAA,CAAA+D,cAAc,CAACO,cAAc,iB,GAE3D1E,mBAAA,CAGM,OAHN2E,WAGM,G,0BAFJ3E,mBAAA,CAAmC;YAA9BP,KAAK,EAAC;UAAa,GAAC,MAAI,qBAC7BO,mBAAA,CAAkE,OAAlE4E,WAAkE,EAAA9C,gBAAA,CAAtC1B,MAAA,CAAA+D,cAAc,CAACU,aAAa,IAAG,GAAC,gB,GAE9D7E,mBAAA,CAOM,OAPN8E,WAOM,G,0BANJ9E,mBAAA,CAAoC;YAA/BP,KAAK,EAAC;UAAa,GAAC,OAAK,qBAC9BO,mBAAA,CAIM,OAJN+E,WAIM,GAHJnF,YAAA,CAESiD,iBAAA;YAFApC,IAAI,EAAEL,MAAA,CAAA+D,cAAc,CAACa,YAAY;;8BACxC,MAAiD,C,kCAA9C5E,MAAA,CAAA+D,cAAc,CAACa,YAAY,iC;;;;;;;;sEAhH9B5E,MAAA,CAAA6E,OAAO,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}