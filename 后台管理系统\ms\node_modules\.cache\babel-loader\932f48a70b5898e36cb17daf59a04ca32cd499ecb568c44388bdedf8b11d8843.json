{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { ref, reactive, onMounted } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { Document, Upload, More, Download } from '@element-plus/icons-vue';\nimport axios from 'axios';\nimport { API_URL } from '@/utils/api';\nimport * as trainingService from '@/services/trainingService';\nexport default {\n  name: 'TrainingList',\n  components: {\n    Document,\n    Upload,\n    More,\n    Download\n  },\n  setup() {\n    // 基础数据\n    const loading = ref(false);\n    const submitting = ref(false);\n    const trainingCourses = ref([]);\n    const dialogVisible = ref(false);\n    const courseFormRef = ref(null);\n    const total = ref(0);\n    const currentPage = ref(1);\n    const pageSize = ref(10);\n    const fileList = ref([]);\n    const uploadFile = ref(null); // 存储要上传的文件\n    let userInfo = localStorage.getItem(\"userInfo\") ? JSON.parse(localStorage.getItem(\"userInfo\")) : '';\n    // 用户角色\n    const isAdmin = ref(userInfo?.role == 'admin');\n    // 筛选条件\n    const filterForm = reactive({\n      course_type: ''\n    });\n\n    // 表单数据\n    const formData = reactive({\n      id: '',\n      title: '',\n      course_type: '',\n      description: '',\n      material_path: '',\n      original_filename: ''\n    });\n\n    // 表单验证规则\n    const formRules = {\n      title: [{\n        required: true,\n        message: '请输入课程标题',\n        trigger: 'blur'\n      }, {\n        min: 2,\n        max: 100,\n        message: '长度在 2 到 100 个字符',\n        trigger: 'blur'\n      }],\n      course_type: [{\n        required: true,\n        message: '请选择课程类型',\n        trigger: 'change'\n      }]\n    };\n\n    // 生命周期钩子\n    onMounted(() => {\n      fetchTrainingCourses();\n    });\n\n    // 获取培训课程列表\n    const fetchTrainingCourses = async () => {\n      loading.value = true;\n      try {\n        let response;\n        const params = {\n          page: currentPage.value,\n          limit: pageSize.value\n        };\n        if (filterForm.course_type) {\n          response = await trainingService.getTrainingsByType(filterForm.course_type, params);\n        } else {\n          response = await trainingService.getAllTrainings(params);\n        }\n        trainingCourses.value = response.data;\n        total.value = response.count;\n      } catch (error) {\n        console.error('获取培训课程失败:', error);\n        ElMessage.error('获取培训课程失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 筛选操作\n    const handleFilter = () => {\n      currentPage.value = 1;\n      fetchTrainingCourses();\n    };\n\n    // 重置筛选\n    const resetFilter = () => {\n      filterForm.course_type = '';\n      handleFilter();\n    };\n\n    // 分页操作\n    const handleSizeChange = val => {\n      pageSize.value = val;\n      fetchTrainingCourses();\n    };\n    const handleCurrentChange = val => {\n      currentPage.value = val;\n      fetchTrainingCourses();\n    };\n\n    // 文件上传前检查\n    const beforeMaterialUpload = file => {\n      const isLt100M = file.size / 1024 / 1024 < 100;\n      if (!isLt100M) {\n        ElMessage.error('上传文件大小不能超过 100MB!');\n      }\n      return isLt100M;\n    };\n\n    // 文件改变处理\n    const handleFileChange = file => {\n      uploadFile.value = file.raw;\n    };\n\n    // 移除文件\n    const handleRemove = () => {\n      formData.material_path = '';\n      formData.original_filename = '';\n      fileList.value = [];\n      uploadFile.value = null;\n    };\n\n    // 打开对话框\n    const openDialog = course => {\n      if (course) {\n        // 编辑模式\n        Object.keys(formData).forEach(key => {\n          formData[key] = course[key];\n        });\n\n        // 如果有文件，添加到文件列表\n        fileList.value = [];\n        if (course.material_path && course.original_filename) {\n          fileList.value = [{\n            name: decodeURIComponent(course.original_filename),\n            url: `http://localhost:3000${course.material_path}`\n          }];\n        }\n      } else {\n        // 新增模式\n        Object.keys(formData).forEach(key => {\n          formData[key] = '';\n        });\n        fileList.value = [];\n        uploadFile.value = null;\n      }\n      dialogVisible.value = true;\n    };\n\n    // 提交表单\n    const submitForm = async () => {\n      if (!courseFormRef.value) return;\n      await courseFormRef.value.validate(async valid => {\n        if (valid) {\n          submitting.value = true;\n          try {\n            // 提交表单数据，直接将文件传递给service\n            if (formData.id) {\n              // 编辑\n              await trainingService.updateTraining(formData.id, formData, uploadFile.value);\n              ElMessage.success('课程更新成功');\n            } else {\n              // 新增\n              await trainingService.createTraining(formData, uploadFile.value);\n              ElMessage.success('课程添加成功');\n            }\n            dialogVisible.value = false;\n            fetchTrainingCourses();\n          } catch (error) {\n            console.error('操作失败:', error);\n            ElMessage.error('操作失败: ' + (error.response?.data?.message || error.message));\n          } finally {\n            submitting.value = false;\n          }\n        } else {\n          return false;\n        }\n      });\n    };\n\n    // 处理删除\n    const handleDelete = course => {\n      ElMessageBox.confirm(`确定要删除培训课程 \"${course.title}\" 吗?`, '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async () => {\n        try {\n          await trainingService.deleteTraining(course.id);\n          ElMessage.success('删除成功');\n          fetchTrainingCourses();\n        } catch (error) {\n          console.error('删除失败:', error);\n          ElMessage.error('删除失败');\n        }\n      }).catch(() => {\n        ElMessage.info('已取消删除');\n      });\n    };\n\n    // 下拉菜单命令处理 - 保留但不再使用\n    const handleCommand = (command, course) => {\n      if (command === 'edit') {\n        openDialog(course);\n      } else if (command === 'delete') {\n        handleDelete(course);\n      }\n    };\n\n    // 根据课程类型获取标签类型\n    const getTagType = type => {\n      switch (type) {\n        case '小讲课':\n          return 'success';\n        case '教学病例讨论':\n          return 'warning';\n        case '教学查房':\n          return 'danger';\n        default:\n          return 'info';\n      }\n    };\n\n    // 日期格式化\n    const formatDate = dateString => {\n      if (!dateString) return '-';\n      const date = new Date(dateString);\n      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;\n    };\n\n    // 添加预览文件方法\n    const previewFile = course => {\n      window.open(`http://localhost:3000${course.material_path}`, '_blank');\n    };\n    return {\n      loading,\n      submitting,\n      trainingCourses,\n      dialogVisible,\n      courseFormRef,\n      formData,\n      formRules,\n      filterForm,\n      fileList,\n      uploadFile,\n      currentPage,\n      pageSize,\n      total,\n      isAdmin,\n      handleFilter,\n      resetFilter,\n      handleSizeChange,\n      handleCurrentChange,\n      openDialog,\n      submitForm,\n      beforeMaterialUpload,\n      handleFileChange,\n      handleRemove,\n      handleCommand,\n      handleDelete,\n      getTagType,\n      formatDate,\n      previewFile\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "ElMessage", "ElMessageBox", "Document", "Upload", "More", "Download", "axios", "API_URL", "trainingService", "name", "components", "setup", "loading", "submitting", "trainingCourses", "dialogVisible", "courseFormRef", "total", "currentPage", "pageSize", "fileList", "uploadFile", "userInfo", "localStorage", "getItem", "JSON", "parse", "isAdmin", "role", "filterForm", "course_type", "formData", "id", "title", "description", "material_path", "original_filename", "formRules", "required", "message", "trigger", "min", "max", "fetchTrainingCourses", "value", "response", "params", "page", "limit", "getTrainingsByType", "getAllTrainings", "data", "count", "error", "console", "handleFilter", "resetFilter", "handleSizeChange", "val", "handleCurrentChange", "beforeMaterialUpload", "file", "isLt100M", "size", "handleFileChange", "raw", "handleRemove", "openDialog", "course", "Object", "keys", "for<PERSON>ach", "key", "decodeURIComponent", "url", "submitForm", "validate", "valid", "updateTraining", "success", "createTraining", "handleDelete", "confirm", "confirmButtonText", "cancelButtonText", "type", "then", "deleteTraining", "catch", "info", "handleCommand", "command", "getTagType", "formatDate", "dateString", "date", "Date", "getFullYear", "getMonth", "toString", "padStart", "getDate", "previewFile", "window", "open"], "sources": ["D:\\admin\\202506\\督导系统\\后台管理系统\\ms\\src\\views\\trainings\\TrainingList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"training-list-container\">\r\n    <el-card class=\"box-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span class=\"title\">专项培训</span>\r\n          <el-button type=\"primary\" @click=\"openDialog()\" v-if=\"isAdmin\">添加培训课程</el-button>\r\n        </div>\r\n      </template>\r\n      \r\n      <!-- 筛选区域 -->\r\n      <div class=\"filter-container\">\r\n        <div class=\"filter-item\">\r\n          <span class=\"filter-label\">课程类型</span>\r\n          <el-select v-model=\"filterForm.course_type\" placeholder=\"选择课程类型\" clearable class=\"filter-select\">\r\n            <el-option label=\"小讲课\" value=\"小讲课\" />\r\n            <el-option label=\"教学病例讨论\" value=\"教学病例讨论\" />\r\n            <el-option label=\"教学查房\" value=\"教学查房\" />\r\n            <el-option label=\"其他\" value=\"其他\" />\r\n          </el-select>\r\n        </div>\r\n        <div class=\"filter-buttons\">\r\n          <el-button type=\"primary\" @click=\"handleFilter\">筛选</el-button>\r\n          <el-button @click=\"resetFilter\">重置</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 课程列表表格 -->\r\n      <div v-loading=\"loading\">\r\n        <div v-if=\"trainingCourses.length === 0 && !loading\" class=\"empty-data\">\r\n          暂无培训课程，请添加新课程\r\n        </div>\r\n        \r\n        <el-table\r\n          v-else\r\n          :data=\"trainingCourses\"\r\n          style=\"width: 100%\"\r\n          border\r\n        >\r\n          <el-table-column prop=\"title\" label=\"课程标题\" min-width=\"180\" show-overflow-tooltip />\r\n          \r\n          <el-table-column prop=\"course_type\" label=\"课程类型\" width=\"150\" align=\"center\">\r\n            <template #default=\"scope\">\r\n              <el-tag :type=\"getTagType(scope.row.course_type)\">{{ scope.row.course_type }}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column prop=\"description\" label=\"课程描述\" min-width=\"200\" show-overflow-tooltip />\r\n          \r\n          <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"120\" align=\"center\">\r\n            <template #default=\"scope\">\r\n              {{ formatDate(scope.row.created_at) }}\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column label=\"课程资料\" width=\"220\" align=\"center\">\r\n            <template #default=\"scope\">\r\n              <div v-if=\"scope.row.material_path\" class=\"file-actions\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  link\r\n                  @click=\"previewFile(scope.row)\"\r\n                  title=\"预览文件\"\r\n                >\r\n                  <el-icon><Document /></el-icon> {{ scope.row.original_filename }}\r\n                </el-button>\r\n                \r\n              </div>\r\n              <span v-else>无资料</span>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column label=\"操作\" width=\"150\" align=\"center\" fixed=\"right\">\r\n            <template #default=\"scope\">\r\n              <el-button type=\"primary\" link size=\"small\" @click=\"openDialog(scope.row)\">编辑</el-button>\r\n              <el-button type=\"danger\" link size=\"small\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-container\" v-if=\"trainingCourses.length > 0\">\r\n          <el-pagination\r\n            v-model:current-page=\"currentPage\"\r\n            v-model:page-size=\"pageSize\"\r\n            :page-sizes=\"[10, 20, 30, 50]\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 添加/编辑课程对话框 -->\r\n    <el-dialog\r\n      v-model=\"dialogVisible\"\r\n      :title=\"formData.id ? '编辑培训课程' : '添加培训课程'\"\r\n      width=\"50%\"\r\n      destroy-on-close\r\n    >\r\n      <el-form\r\n        ref=\"courseFormRef\"\r\n        :model=\"formData\"\r\n        :rules=\"formRules\"\r\n        label-width=\"100px\"\r\n        label-position=\"right\"\r\n      >\r\n        <el-form-item label=\"课程标题\" prop=\"title\">\r\n          <el-input v-model=\"formData.title\" placeholder=\"请输入课程标题\" />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"课程类型\" prop=\"course_type\">\r\n          <el-select v-model=\"formData.course_type\" placeholder=\"选择课程类型\" style=\"width: 100%\">\r\n            <el-option label=\"小讲课\" value=\"小讲课\" />\r\n            <el-option label=\"教学病例讨论\" value=\"教学病例讨论\" />\r\n            <el-option label=\"教学查房\" value=\"教学查房\" />\r\n            <el-option label=\"其他\" value=\"其他\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"课程描述\" prop=\"description\">\r\n          <el-input\r\n            v-model=\"formData.description\"\r\n            type=\"textarea\"\r\n            rows=\"3\"\r\n            placeholder=\"请输入课程描述\"\r\n          />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"课件/视频\">\r\n          <el-upload\r\n            class=\"material-uploader\"\r\n            drag\r\n            action=\"#\"\r\n            :auto-upload=\"false\"\r\n            :on-change=\"handleFileChange\"\r\n            :on-remove=\"handleRemove\"\r\n            :before-upload=\"beforeMaterialUpload\"\r\n            :file-list=\"fileList\"\r\n            :limit=\"1\"\r\n          >\r\n            <el-icon class=\"el-icon--upload\"><Upload /></el-icon>\r\n            <div class=\"el-upload__text\">\r\n              拖拽文件到此处或 <em>点击上传</em>\r\n            </div>\r\n            <template #tip>\r\n              <div class=\"el-upload__tip\">\r\n                支持各种文档、PPT、视频等格式，文件大小不超过100MB\r\n              </div>\r\n            </template>\r\n          </el-upload>\r\n        </el-form-item>\r\n      </el-form>\r\n      \r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitting\">确定</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, onMounted } from 'vue'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { Document, Upload, More, Download } from '@element-plus/icons-vue'\r\nimport axios from 'axios'\r\nimport { API_URL } from '@/utils/api'\r\nimport * as trainingService from '@/services/trainingService'\r\n\r\nexport default {\r\n  name: 'TrainingList',\r\n  components: {\r\n    Document,\r\n    Upload,\r\n    More,\r\n    Download\r\n  },\r\n  setup() {\r\n    // 基础数据\r\n    const loading = ref(false)\r\n    const submitting = ref(false)\r\n    const trainingCourses = ref([])\r\n    const dialogVisible = ref(false)\r\n    const courseFormRef = ref(null)\r\n    const total = ref(0)\r\n    const currentPage = ref(1)\r\n    const pageSize = ref(10)\r\n    const fileList = ref([])\r\n    const uploadFile = ref(null) // 存储要上传的文件\r\n    let userInfo = localStorage.getItem(\"userInfo\") ? JSON.parse(localStorage.getItem(\"userInfo\")): ''\r\n// 用户角色\r\nconst isAdmin = ref(userInfo?.role == 'admin')\r\n    // 筛选条件\r\n    const filterForm = reactive({\r\n      course_type: ''\r\n    })\r\n    \r\n    // 表单数据\r\n    const formData = reactive({\r\n      id: '',\r\n      title: '',\r\n      course_type: '',\r\n      description: '',\r\n      material_path: '',\r\n      original_filename: ''\r\n    })\r\n    \r\n    // 表单验证规则\r\n    const formRules = {\r\n      title: [\r\n        { required: true, message: '请输入课程标题', trigger: 'blur' },\r\n        { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }\r\n      ],\r\n      course_type: [\r\n        { required: true, message: '请选择课程类型', trigger: 'change' }\r\n      ]\r\n    }\r\n    \r\n    // 生命周期钩子\r\n    onMounted(() => {\r\n      fetchTrainingCourses()\r\n    })\r\n    \r\n    // 获取培训课程列表\r\n    const fetchTrainingCourses = async () => {\r\n      loading.value = true\r\n      try {\r\n        let response\r\n        const params = {\r\n          page: currentPage.value,\r\n          limit: pageSize.value\r\n        }\r\n        \r\n        if (filterForm.course_type) {\r\n          response = await trainingService.getTrainingsByType(filterForm.course_type, params)\r\n        } else {\r\n          response = await trainingService.getAllTrainings(params)\r\n        }\r\n        \r\n        trainingCourses.value = response.data\r\n        total.value = response.count\r\n      } catch (error) {\r\n        console.error('获取培训课程失败:', error)\r\n        ElMessage.error('获取培训课程失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 筛选操作\r\n    const handleFilter = () => {\r\n      currentPage.value = 1\r\n      fetchTrainingCourses()\r\n    }\r\n    \r\n    // 重置筛选\r\n    const resetFilter = () => {\r\n      filterForm.course_type = ''\r\n      handleFilter()\r\n    }\r\n    \r\n    // 分页操作\r\n    const handleSizeChange = (val) => {\r\n      pageSize.value = val\r\n      fetchTrainingCourses()\r\n    }\r\n    \r\n    const handleCurrentChange = (val) => {\r\n      currentPage.value = val\r\n      fetchTrainingCourses()\r\n    }\r\n    \r\n    // 文件上传前检查\r\n    const beforeMaterialUpload = (file) => {\r\n      const isLt100M = file.size / 1024 / 1024 < 100\r\n      \r\n      if (!isLt100M) {\r\n        ElMessage.error('上传文件大小不能超过 100MB!')\r\n      }\r\n      \r\n      return isLt100M\r\n    }\r\n    \r\n    // 文件改变处理\r\n    const handleFileChange = (file) => {\r\n      uploadFile.value = file.raw\r\n    }\r\n    \r\n    // 移除文件\r\n    const handleRemove = () => {\r\n      formData.material_path = ''\r\n      formData.original_filename = ''\r\n      fileList.value = []\r\n      uploadFile.value = null\r\n    }\r\n    \r\n    // 打开对话框\r\n    const openDialog = (course) => {\r\n      if (course) {\r\n        // 编辑模式\r\n        Object.keys(formData).forEach(key => {\r\n          formData[key] = course[key]\r\n        })\r\n        \r\n        // 如果有文件，添加到文件列表\r\n        fileList.value = []\r\n        if (course.material_path && course.original_filename) {\r\n          fileList.value = [\r\n            {\r\n              name: decodeURIComponent(course.original_filename),\r\n              url: `http://localhost:3000${course.material_path}`\r\n            }\r\n          ]\r\n        }\r\n      } else {\r\n        // 新增模式\r\n        Object.keys(formData).forEach(key => {\r\n          formData[key] = ''\r\n        })\r\n        fileList.value = []\r\n        uploadFile.value = null\r\n      }\r\n      \r\n      dialogVisible.value = true\r\n    }\r\n    \r\n    // 提交表单\r\n    const submitForm = async () => {\r\n      if (!courseFormRef.value) return\r\n      \r\n      await courseFormRef.value.validate(async (valid) => {\r\n        if (valid) {\r\n          submitting.value = true\r\n          \r\n          try {\r\n            // 提交表单数据，直接将文件传递给service\r\n            if (formData.id) {\r\n              // 编辑\r\n              await trainingService.updateTraining(formData.id, formData, uploadFile.value);\r\n              ElMessage.success('课程更新成功')\r\n            } else {\r\n              // 新增\r\n              await trainingService.createTraining(formData, uploadFile.value);\r\n              ElMessage.success('课程添加成功')\r\n            }\r\n            \r\n            dialogVisible.value = false\r\n            fetchTrainingCourses()\r\n          } catch (error) {\r\n            console.error('操作失败:', error)\r\n            ElMessage.error('操作失败: ' + (error.response?.data?.message || error.message))\r\n          } finally {\r\n            submitting.value = false\r\n          }\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    }\r\n    \r\n    // 处理删除\r\n    const handleDelete = (course) => {\r\n      ElMessageBox.confirm(\r\n        `确定要删除培训课程 \"${course.title}\" 吗?`,\r\n        '警告',\r\n        {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n        }\r\n      )\r\n        .then(async () => {\r\n          try {\r\n            await trainingService.deleteTraining(course.id)\r\n            ElMessage.success('删除成功')\r\n            fetchTrainingCourses()\r\n          } catch (error) {\r\n            console.error('删除失败:', error)\r\n            ElMessage.error('删除失败')\r\n          }\r\n        })\r\n        .catch(() => {\r\n          ElMessage.info('已取消删除')\r\n        })\r\n    }\r\n    \r\n    // 下拉菜单命令处理 - 保留但不再使用\r\n    const handleCommand = (command, course) => {\r\n      if (command === 'edit') {\r\n        openDialog(course)\r\n      } else if (command === 'delete') {\r\n        handleDelete(course)\r\n      }\r\n    }\r\n    \r\n    // 根据课程类型获取标签类型\r\n    const getTagType = (type) => {\r\n      switch (type) {\r\n        case '小讲课':\r\n          return 'success'\r\n        case '教学病例讨论':\r\n          return 'warning'\r\n        case '教学查房':\r\n          return 'danger'\r\n        default:\r\n          return 'info'\r\n      }\r\n    }\r\n    \r\n    // 日期格式化\r\n    const formatDate = (dateString) => {\r\n      if (!dateString) return '-'\r\n      const date = new Date(dateString)\r\n      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`\r\n    }\r\n\r\n    // 添加预览文件方法\r\n    const previewFile = (course) => {\r\n      window.open(`http://localhost:3000${course.material_path}`, '_blank')\r\n    }\r\n    \r\n    return {\r\n      loading,\r\n      submitting,\r\n      trainingCourses,\r\n      dialogVisible,\r\n      courseFormRef,\r\n      formData,\r\n      formRules,\r\n      filterForm,\r\n      fileList,\r\n      uploadFile,\r\n      currentPage,\r\n      pageSize,\r\n      total,\r\n      isAdmin,\r\n      handleFilter,\r\n      resetFilter,\r\n      handleSizeChange,\r\n      handleCurrentChange,\r\n      openDialog,\r\n      submitForm,\r\n      beforeMaterialUpload,\r\n      handleFileChange,\r\n      handleRemove,\r\n      handleCommand,\r\n      handleDelete,\r\n      getTagType,\r\n      formatDate,\r\n      previewFile\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.training-list-container {\r\n  padding: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.filter-form {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.empty-data {\r\n  padding: 50px;\r\n  text-align: center;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.material-uploader {\r\n  width: 100%;\r\n}\r\n\r\n.material-uploader .el-upload-dragger {\r\n  width: 100%;\r\n}\r\n\r\n.filter-container {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  background-color: #f5f7fa;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.filter-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 20px;\r\n}\r\n\r\n.filter-label {\r\n  font-weight: bold;\r\n  margin-right: 10px;\r\n  min-width: 70px;\r\n}\r\n\r\n.filter-select {\r\n  width: 200px;\r\n}\r\n\r\n.filter-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n</style> "], "mappings": ";;AAuKA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAI;AAC7C,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,SAASC,QAAQ,EAAEC,MAAM,EAAEC,IAAI,EAAEC,QAAO,QAAS,yBAAwB;AACzE,OAAOC,KAAI,MAAO,OAAM;AACxB,SAASC,OAAM,QAAS,aAAY;AACpC,OAAO,KAAKC,eAAc,MAAO,4BAA2B;AAE5D,eAAe;EACbC,IAAI,EAAE,cAAc;EACpBC,UAAU,EAAE;IACVR,QAAQ;IACRC,MAAM;IACNC,IAAI;IACJC;EACF,CAAC;EACDM,KAAKA,CAAA,EAAG;IACN;IACA,MAAMC,OAAM,GAAIf,GAAG,CAAC,KAAK;IACzB,MAAMgB,UAAS,GAAIhB,GAAG,CAAC,KAAK;IAC5B,MAAMiB,eAAc,GAAIjB,GAAG,CAAC,EAAE;IAC9B,MAAMkB,aAAY,GAAIlB,GAAG,CAAC,KAAK;IAC/B,MAAMmB,aAAY,GAAInB,GAAG,CAAC,IAAI;IAC9B,MAAMoB,KAAI,GAAIpB,GAAG,CAAC,CAAC;IACnB,MAAMqB,WAAU,GAAIrB,GAAG,CAAC,CAAC;IACzB,MAAMsB,QAAO,GAAItB,GAAG,CAAC,EAAE;IACvB,MAAMuB,QAAO,GAAIvB,GAAG,CAAC,EAAE;IACvB,MAAMwB,UAAS,GAAIxB,GAAG,CAAC,IAAI,GAAE;IAC7B,IAAIyB,QAAO,GAAIC,YAAY,CAACC,OAAO,CAAC,UAAU,IAAIC,IAAI,CAACC,KAAK,CAACH,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,CAAC,GAAE,EAAC;IACrG;IACA,MAAMG,OAAM,GAAI9B,GAAG,CAACyB,QAAQ,EAAEM,IAAG,IAAK,OAAO;IACzC;IACA,MAAMC,UAAS,GAAI/B,QAAQ,CAAC;MAC1BgC,WAAW,EAAE;IACf,CAAC;;IAED;IACA,MAAMC,QAAO,GAAIjC,QAAQ,CAAC;MACxBkC,EAAE,EAAE,EAAE;MACNC,KAAK,EAAE,EAAE;MACTH,WAAW,EAAE,EAAE;MACfI,WAAW,EAAE,EAAE;MACfC,aAAa,EAAE,EAAE;MACjBC,iBAAiB,EAAE;IACrB,CAAC;;IAED;IACA,MAAMC,SAAQ,GAAI;MAChBJ,KAAK,EAAE,CACL;QAAEK,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,EACvD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,GAAG;QAAEH,OAAO,EAAE,iBAAiB;QAAEC,OAAO,EAAE;MAAO,EACjE;MACDV,WAAW,EAAE,CACX;QAAEQ,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAS;IAE5D;;IAEA;IACAzC,SAAS,CAAC,MAAM;MACd4C,oBAAoB,CAAC;IACvB,CAAC;;IAED;IACA,MAAMA,oBAAmB,GAAI,MAAAA,CAAA,KAAY;MACvC/B,OAAO,CAACgC,KAAI,GAAI,IAAG;MACnB,IAAI;QACF,IAAIC,QAAO;QACX,MAAMC,MAAK,GAAI;UACbC,IAAI,EAAE7B,WAAW,CAAC0B,KAAK;UACvBI,KAAK,EAAE7B,QAAQ,CAACyB;QAClB;QAEA,IAAIf,UAAU,CAACC,WAAW,EAAE;UAC1Be,QAAO,GAAI,MAAMrC,eAAe,CAACyC,kBAAkB,CAACpB,UAAU,CAACC,WAAW,EAAEgB,MAAM;QACpF,OAAO;UACLD,QAAO,GAAI,MAAMrC,eAAe,CAAC0C,eAAe,CAACJ,MAAM;QACzD;QAEAhC,eAAe,CAAC8B,KAAI,GAAIC,QAAQ,CAACM,IAAG;QACpClC,KAAK,CAAC2B,KAAI,GAAIC,QAAQ,CAACO,KAAI;MAC7B,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChCrD,SAAS,CAACqD,KAAK,CAAC,UAAU;MAC5B,UAAU;QACRzC,OAAO,CAACgC,KAAI,GAAI,KAAI;MACtB;IACF;;IAEA;IACA,MAAMW,YAAW,GAAIA,CAAA,KAAM;MACzBrC,WAAW,CAAC0B,KAAI,GAAI;MACpBD,oBAAoB,CAAC;IACvB;;IAEA;IACA,MAAMa,WAAU,GAAIA,CAAA,KAAM;MACxB3B,UAAU,CAACC,WAAU,GAAI,EAAC;MAC1ByB,YAAY,CAAC;IACf;;IAEA;IACA,MAAME,gBAAe,GAAKC,GAAG,IAAK;MAChCvC,QAAQ,CAACyB,KAAI,GAAIc,GAAE;MACnBf,oBAAoB,CAAC;IACvB;IAEA,MAAMgB,mBAAkB,GAAKD,GAAG,IAAK;MACnCxC,WAAW,CAAC0B,KAAI,GAAIc,GAAE;MACtBf,oBAAoB,CAAC;IACvB;;IAEA;IACA,MAAMiB,oBAAmB,GAAKC,IAAI,IAAK;MACrC,MAAMC,QAAO,GAAID,IAAI,CAACE,IAAG,GAAI,IAAG,GAAI,IAAG,GAAI,GAAE;MAE7C,IAAI,CAACD,QAAQ,EAAE;QACb9D,SAAS,CAACqD,KAAK,CAAC,mBAAmB;MACrC;MAEA,OAAOS,QAAO;IAChB;;IAEA;IACA,MAAME,gBAAe,GAAKH,IAAI,IAAK;MACjCxC,UAAU,CAACuB,KAAI,GAAIiB,IAAI,CAACI,GAAE;IAC5B;;IAEA;IACA,MAAMC,YAAW,GAAIA,CAAA,KAAM;MACzBnC,QAAQ,CAACI,aAAY,GAAI,EAAC;MAC1BJ,QAAQ,CAACK,iBAAgB,GAAI,EAAC;MAC9BhB,QAAQ,CAACwB,KAAI,GAAI,EAAC;MAClBvB,UAAU,CAACuB,KAAI,GAAI,IAAG;IACxB;;IAEA;IACA,MAAMuB,UAAS,GAAKC,MAAM,IAAK;MAC7B,IAAIA,MAAM,EAAE;QACV;QACAC,MAAM,CAACC,IAAI,CAACvC,QAAQ,CAAC,CAACwC,OAAO,CAACC,GAAE,IAAK;UACnCzC,QAAQ,CAACyC,GAAG,IAAIJ,MAAM,CAACI,GAAG;QAC5B,CAAC;;QAED;QACApD,QAAQ,CAACwB,KAAI,GAAI,EAAC;QAClB,IAAIwB,MAAM,CAACjC,aAAY,IAAKiC,MAAM,CAAChC,iBAAiB,EAAE;UACpDhB,QAAQ,CAACwB,KAAI,GAAI,CACf;YACEnC,IAAI,EAAEgE,kBAAkB,CAACL,MAAM,CAAChC,iBAAiB,CAAC;YAClDsC,GAAG,EAAE,wBAAwBN,MAAM,CAACjC,aAAa;UACnD,EACF;QACF;MACF,OAAO;QACL;QACAkC,MAAM,CAACC,IAAI,CAACvC,QAAQ,CAAC,CAACwC,OAAO,CAACC,GAAE,IAAK;UACnCzC,QAAQ,CAACyC,GAAG,IAAI,EAAC;QACnB,CAAC;QACDpD,QAAQ,CAACwB,KAAI,GAAI,EAAC;QAClBvB,UAAU,CAACuB,KAAI,GAAI,IAAG;MACxB;MAEA7B,aAAa,CAAC6B,KAAI,GAAI,IAAG;IAC3B;;IAEA;IACA,MAAM+B,UAAS,GAAI,MAAAA,CAAA,KAAY;MAC7B,IAAI,CAAC3D,aAAa,CAAC4B,KAAK,EAAE;MAE1B,MAAM5B,aAAa,CAAC4B,KAAK,CAACgC,QAAQ,CAAC,MAAOC,KAAK,IAAK;QAClD,IAAIA,KAAK,EAAE;UACThE,UAAU,CAAC+B,KAAI,GAAI,IAAG;UAEtB,IAAI;YACF;YACA,IAAIb,QAAQ,CAACC,EAAE,EAAE;cACf;cACA,MAAMxB,eAAe,CAACsE,cAAc,CAAC/C,QAAQ,CAACC,EAAE,EAAED,QAAQ,EAAEV,UAAU,CAACuB,KAAK,CAAC;cAC7E5C,SAAS,CAAC+E,OAAO,CAAC,QAAQ;YAC5B,OAAO;cACL;cACA,MAAMvE,eAAe,CAACwE,cAAc,CAACjD,QAAQ,EAAEV,UAAU,CAACuB,KAAK,CAAC;cAChE5C,SAAS,CAAC+E,OAAO,CAAC,QAAQ;YAC5B;YAEAhE,aAAa,CAAC6B,KAAI,GAAI,KAAI;YAC1BD,oBAAoB,CAAC;UACvB,EAAE,OAAOU,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;YAC5BrD,SAAS,CAACqD,KAAK,CAAC,QAAO,IAAKA,KAAK,CAACR,QAAQ,EAAEM,IAAI,EAAEZ,OAAM,IAAKc,KAAK,CAACd,OAAO,CAAC;UAC7E,UAAU;YACR1B,UAAU,CAAC+B,KAAI,GAAI,KAAI;UACzB;QACF,OAAO;UACL,OAAO,KAAI;QACb;MACF,CAAC;IACH;;IAEA;IACA,MAAMqC,YAAW,GAAKb,MAAM,IAAK;MAC/BnE,YAAY,CAACiF,OAAO,CAClB,cAAcd,MAAM,CAACnC,KAAK,MAAM,EAChC,IAAI,EACJ;QACEkD,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,IAAI,EAAE;MACR,CACF,EACGC,IAAI,CAAC,YAAY;QAChB,IAAI;UACF,MAAM9E,eAAe,CAAC+E,cAAc,CAACnB,MAAM,CAACpC,EAAE;UAC9ChC,SAAS,CAAC+E,OAAO,CAAC,MAAM;UACxBpC,oBAAoB,CAAC;QACvB,EAAE,OAAOU,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;UAC5BrD,SAAS,CAACqD,KAAK,CAAC,MAAM;QACxB;MACF,CAAC,EACAmC,KAAK,CAAC,MAAM;QACXxF,SAAS,CAACyF,IAAI,CAAC,OAAO;MACxB,CAAC;IACL;;IAEA;IACA,MAAMC,aAAY,GAAIA,CAACC,OAAO,EAAEvB,MAAM,KAAK;MACzC,IAAIuB,OAAM,KAAM,MAAM,EAAE;QACtBxB,UAAU,CAACC,MAAM;MACnB,OAAO,IAAIuB,OAAM,KAAM,QAAQ,EAAE;QAC/BV,YAAY,CAACb,MAAM;MACrB;IACF;;IAEA;IACA,MAAMwB,UAAS,GAAKP,IAAI,IAAK;MAC3B,QAAQA,IAAI;QACV,KAAK,KAAK;UACR,OAAO,SAAQ;QACjB,KAAK,QAAQ;UACX,OAAO,SAAQ;QACjB,KAAK,MAAM;UACT,OAAO,QAAO;QAChB;UACE,OAAO,MAAK;MAChB;IACF;;IAEA;IACA,MAAMQ,UAAS,GAAKC,UAAU,IAAK;MACjC,IAAI,CAACA,UAAU,EAAE,OAAO,GAAE;MAC1B,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU;MAChC,OAAO,GAAGC,IAAI,CAACE,WAAW,CAAC,CAAC,IAAI,CAACF,IAAI,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIL,IAAI,CAACM,OAAO,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC;IAClI;;IAEA;IACA,MAAME,WAAU,GAAKlC,MAAM,IAAK;MAC9BmC,MAAM,CAACC,IAAI,CAAC,wBAAwBpC,MAAM,CAACjC,aAAa,EAAE,EAAE,QAAQ;IACtE;IAEA,OAAO;MACLvB,OAAO;MACPC,UAAU;MACVC,eAAe;MACfC,aAAa;MACbC,aAAa;MACbe,QAAQ;MACRM,SAAS;MACTR,UAAU;MACVT,QAAQ;MACRC,UAAU;MACVH,WAAW;MACXC,QAAQ;MACRF,KAAK;MACLU,OAAO;MACP4B,YAAY;MACZC,WAAW;MACXC,gBAAgB;MAChBE,mBAAmB;MACnBQ,UAAU;MACVQ,UAAU;MACVf,oBAAoB;MACpBI,gBAAgB;MAChBE,YAAY;MACZwB,aAAa;MACbT,YAAY;MACZW,UAAU;MACVC,UAAU;MACVS;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}