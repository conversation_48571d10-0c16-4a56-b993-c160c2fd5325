{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport { useRoute, useRouter } from 'vue-router';\nimport { ElMessage } from 'element-plus';\nimport axios from 'axios';\nexport default {\n  name: 'EvaluationDetail',\n  setup() {\n    const route = useRoute();\n    const router = useRouter();\n    const evaluationId = route.params.id;\n    let token = localStorage.getItem('token');\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    }\n    // 基础数据\n    const loading = ref(false);\n    const evaluationData = ref({});\n    const teacherData = ref({});\n    const competencyData = ref(null);\n\n    // 是否为管理员或督导\n    const isAdmin = computed(() => {\n      // 这里可以根据实际的用户角色判断\n      // 简单起见，这里暂时返回 true\n      return true;\n    });\n\n    // 评分\n    const score = computed(() => {\n      return evaluationData.value.average_score || 0;\n    });\n\n    // 生命周期钩子\n    onMounted(() => {\n      fetchEvaluationDetail();\n    });\n\n    // 获取评价详情\n    const fetchEvaluationDetail = async () => {\n      loading.value = true;\n      try {\n        const response = await axios.get(`http://localhost:3000/api/evaluations/${evaluationId}`);\n        evaluationData.value = response.data.data;\n\n        // 如果有教师ID，获取教师信息\n        if (evaluationData.value.teacher_id) {\n          await fetchTeacherData(evaluationData.value.teacher_id);\n          await fetchCompetencyStatus(evaluationData.value.teacher_id);\n        }\n      } catch (error) {\n        console.error('获取评价详情失败:', error);\n        ElMessage.error('获取评价详情失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 获取教师信息\n    const fetchTeacherData = async teacherId => {\n      try {\n        const response = await axios.get(`http://localhost:3000/api/teachers/${teacherId}`);\n        teacherData.value = response.data.data;\n      } catch (error) {\n        console.error('获取教师信息失败:', error);\n      }\n    };\n\n    // 获取能力认证状态\n    const fetchCompetencyStatus = async teacherId => {\n      try {\n        const response = await axios.get(`http://localhost:3000/api/evaluations/competency/teacher/${teacherId}`);\n        competencyData.value = response.data.data;\n      } catch (error) {\n        console.error('获取能力认证状态失败:', error);\n      }\n    };\n\n    // 格式化日期\n    const formatDate = dateString => {\n      if (!dateString) return '-';\n      const date = new Date(dateString);\n      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;\n    };\n\n    // 返回列表\n    const goBack = () => {\n      router.push('/evaluations/list');\n    };\n\n    // 编辑评价\n    const editEvaluation = () => {\n      router.push(`/evaluations/add?id=${evaluationId}`);\n    };\n\n    // 查看教师详情\n    const viewTeacherDetail = () => {\n      if (teacherData.value.id) {\n        router.push(`/teachers/detail/${teacherData.value.id}`);\n      }\n    };\n    return {\n      loading,\n      evaluationData,\n      teacherData,\n      competencyData,\n      score,\n      isAdmin,\n      formatDate,\n      goBack,\n      editEvaluation,\n      viewTeacherDetail\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "useRoute", "useRouter", "ElMessage", "axios", "name", "setup", "route", "router", "evaluationId", "params", "id", "token", "localStorage", "getItem", "defaults", "headers", "common", "loading", "evaluationData", "teacher<PERSON><PERSON>", "competencyData", "isAdmin", "score", "value", "average_score", "fetchEvaluationDetail", "response", "get", "data", "teacher_id", "fetchTeacherData", "fetchCompetencyStatus", "error", "console", "teacherId", "formatDate", "dateString", "date", "Date", "getFullYear", "getMonth", "toString", "padStart", "getDate", "goBack", "push", "editEvaluation", "viewTeacherDetail"], "sources": ["D:\\admin\\202506\\督导系统\\后台管理系统\\ms\\src\\views\\evaluations\\EvaluationDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"evaluation-detail-container\">\r\n    <el-card class=\"box-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span class=\"title\">督导评价详情</span>\r\n          <div>\r\n            <el-button @click=\"goBack\">返回列表</el-button>\r\n            <el-button type=\"primary\" @click=\"editEvaluation\" v-if=\"isAdmin\">编辑</el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <div v-loading=\"loading\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"24\">\r\n            <el-descriptions title=\"评价基本信息\" :column=\"3\" border>\r\n              <el-descriptions-item label=\"督导教研室\">{{ evaluationData.supervising_department }}</el-descriptions-item>\r\n              <el-descriptions-item label=\"评价日期\">{{ formatDate(evaluationData.evaluation_date) }}</el-descriptions-item>\r\n              <el-descriptions-item label=\"评估人\">{{ evaluationData.evaluator_name }}</el-descriptions-item>\r\n            </el-descriptions>\r\n            \r\n            <el-divider />\r\n            \r\n            <el-descriptions title=\"教学活动信息\" :column=\"3\" border>\r\n              <el-descriptions-item label=\"病例/主题\" :span=\"3\">{{ evaluationData.case_topic }}</el-descriptions-item>\r\n              <el-descriptions-item label=\"教学活动形式\">{{ evaluationData.teaching_form }}</el-descriptions-item>\r\n              <el-descriptions-item label=\"带教老师\" :span=\"2\">\r\n                {{ evaluationData.teacher_name }} ({{ evaluationData.teacher_title }})\r\n              </el-descriptions-item>\r\n              <el-descriptions-item label=\"学员姓名\">{{ evaluationData.student_name }}</el-descriptions-item>\r\n              <el-descriptions-item label=\"学员类别\" :span=\"2\">{{ evaluationData.student_type }}</el-descriptions-item>\r\n            </el-descriptions>\r\n            \r\n            <el-divider />\r\n            \r\n            <el-descriptions title=\"评价内容\" :column=\"1\" border>\r\n              <el-descriptions-item label=\"平均分\">\r\n                <el-rate\r\n                  v-model=\"score\"\r\n                  :max=\"10\"\r\n                  show-score\r\n                  disabled\r\n                  score-template=\"{value}\"\r\n                />\r\n              </el-descriptions-item>\r\n              \r\n              <el-descriptions-item label=\"亮点\">\r\n                <div class=\"description-content\">\r\n                  {{ evaluationData.highlights || '无' }}\r\n                </div>\r\n              </el-descriptions-item>\r\n              \r\n              <el-descriptions-item label=\"不足\">\r\n                <div class=\"description-content\">\r\n                  {{ evaluationData.shortcomings || '无' }}\r\n                </div>\r\n              </el-descriptions-item>\r\n              \r\n              <el-descriptions-item label=\"改进建议\">\r\n                <div class=\"description-content\">\r\n                  {{ evaluationData.improvement_suggestions || '无' }}\r\n                </div>\r\n              </el-descriptions-item>\r\n              \r\n              <el-descriptions-item label=\"能力认定\">\r\n                <el-tag :type=\"evaluationData.competency_approved ? 'success' : 'danger'\" size=\"large\">\r\n                  {{ evaluationData.competency_approved ? '同意' : '不同意' }}\r\n                </el-tag>\r\n              </el-descriptions-item>\r\n            </el-descriptions>\r\n          </el-col>\r\n        </el-row>\r\n        \r\n        <el-divider />\r\n        \r\n        <!-- 教师信息卡片 -->\r\n        <el-card class=\"teacher-info-card\" shadow=\"hover\" v-if=\"teacherData.id\">\r\n          <template #header>\r\n            <div class=\"teacher-card-header\">\r\n              <span>带教老师信息</span>\r\n              <el-button type=\"text\" @click=\"viewTeacherDetail\">查看详情</el-button>\r\n            </div>\r\n          </template>\r\n          \r\n          <el-row :gutter=\"20\">\r\n            <el-col :span=\"4\">\r\n              <div class=\"teacher-avatar\">\r\n                <el-image\r\n                  v-if=\"teacherData.photo\"\r\n                  :src=\"`http://localhost:3000${teacherData.photo}`\"\r\n                  fit=\"cover\"\r\n                  class=\"avatar-image\"\r\n                  :preview-src-list=\"[`http://localhost:3000${teacherData.photo}`]\"\r\n                />\r\n                <el-avatar v-else :size=\"100\" icon=\"UserFilled\" />\r\n              </div>\r\n            </el-col>\r\n            <el-col :span=\"20\">\r\n              <el-descriptions :column=\"3\" border>\r\n                <el-descriptions-item label=\"姓名\">{{ teacherData.name }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"性别\">{{ teacherData.gender }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"科室\">{{ teacherData.department }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"学校\">{{ teacherData.school }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"专业\">{{ teacherData.major }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"学历\">{{ teacherData.education }}</el-descriptions-item>\r\n              </el-descriptions>\r\n              \r\n              <div class=\"teacher-stats\" v-if=\"competencyData\">\r\n                <div class=\"teacher-stats-item\">\r\n                  <div class=\"stats-label\">评价总数:</div>\r\n                  <div class=\"stats-value\">{{ competencyData.total_evaluations }}</div>\r\n                </div>\r\n                <div class=\"teacher-stats-item\">\r\n                  <div class=\"stats-label\">认可数:</div>\r\n                  <div class=\"stats-value\">{{ competencyData.approved_count }}</div>\r\n                </div>\r\n                <div class=\"teacher-stats-item\">\r\n                  <div class=\"stats-label\">认可率:</div>\r\n                  <div class=\"stats-value\">{{ competencyData.approval_rate }}%</div>\r\n                </div>\r\n                <div class=\"teacher-stats-item\">\r\n                  <div class=\"stats-label\">认证状态:</div>\r\n                  <div class=\"stats-value\">\r\n                    <el-tag :type=\"competencyData.is_certified ? 'success' : 'info'\">\r\n                      {{ competencyData.is_certified ? '已认证' : '未认证' }}\r\n                    </el-tag>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-col>\r\n          </el-row>\r\n        </el-card>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, computed, onMounted } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'EvaluationDetail',\r\n  setup() {\r\n    const route = useRoute()\r\n    const router = useRouter()\r\n    const evaluationId = route.params.id\r\n      let token = localStorage.getItem('token')\r\n    if (token) {\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`\r\n    }\r\n    // 基础数据\r\n    const loading = ref(false)\r\n    const evaluationData = ref({})\r\n    const teacherData = ref({})\r\n    const competencyData = ref(null)\r\n    \r\n    // 是否为管理员或督导\r\n    const isAdmin = computed(() => {\r\n      // 这里可以根据实际的用户角色判断\r\n      // 简单起见，这里暂时返回 true\r\n      return true\r\n    })\r\n    \r\n    // 评分\r\n    const score = computed(() => {\r\n      return evaluationData.value.average_score || 0\r\n    })\r\n    \r\n    // 生命周期钩子\r\n    onMounted(() => {\r\n      fetchEvaluationDetail()\r\n    })\r\n    \r\n    // 获取评价详情\r\n    const fetchEvaluationDetail = async () => {\r\n      loading.value = true\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/evaluations/${evaluationId}`)\r\n        evaluationData.value = response.data.data\r\n        \r\n        // 如果有教师ID，获取教师信息\r\n        if (evaluationData.value.teacher_id) {\r\n          await fetchTeacherData(evaluationData.value.teacher_id)\r\n          await fetchCompetencyStatus(evaluationData.value.teacher_id)\r\n        }\r\n      } catch (error) {\r\n        console.error('获取评价详情失败:', error)\r\n        ElMessage.error('获取评价详情失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 获取教师信息\r\n    const fetchTeacherData = async (teacherId) => {\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/teachers/${teacherId}`)\r\n        teacherData.value = response.data.data\r\n      } catch (error) {\r\n        console.error('获取教师信息失败:', error)\r\n      }\r\n    }\r\n    \r\n    // 获取能力认证状态\r\n    const fetchCompetencyStatus = async (teacherId) => {\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/evaluations/competency/teacher/${teacherId}`)\r\n        competencyData.value = response.data.data\r\n      } catch (error) {\r\n        console.error('获取能力认证状态失败:', error)\r\n      }\r\n    }\r\n    \r\n    // 格式化日期\r\n    const formatDate = (dateString) => {\r\n      if (!dateString) return '-'\r\n      const date = new Date(dateString)\r\n      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`\r\n    }\r\n    \r\n    // 返回列表\r\n    const goBack = () => {\r\n      router.push('/evaluations/list')\r\n    }\r\n    \r\n    // 编辑评价\r\n    const editEvaluation = () => {\r\n      router.push(`/evaluations/add?id=${evaluationId}`)\r\n    }\r\n    \r\n    // 查看教师详情\r\n    const viewTeacherDetail = () => {\r\n      if (teacherData.value.id) {\r\n        router.push(`/teachers/detail/${teacherData.value.id}`)\r\n      }\r\n    }\r\n    \r\n    return {\r\n      loading,\r\n      evaluationData,\r\n      teacherData,\r\n      competencyData,\r\n      score,\r\n      isAdmin,\r\n      formatDate,\r\n      goBack,\r\n      editEvaluation,\r\n      viewTeacherDetail\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.evaluation-detail-container {\r\n  padding: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.description-content {\r\n  white-space: pre-line;\r\n  padding: 10px;\r\n  background-color: #f7f7f7;\r\n  border-radius: 4px;\r\n  min-height: 50px;\r\n}\r\n\r\n.teacher-info-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.teacher-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.teacher-avatar {\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.avatar-image {\r\n  width: 100px;\r\n  height: 100px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 2px solid #eee;\r\n}\r\n\r\n.teacher-stats {\r\n  display: flex;\r\n  margin-top: 15px;\r\n  background-color: #f7f7f7;\r\n  padding: 10px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.teacher-stats-item {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.stats-label {\r\n  font-size: 12px;\r\n  color: #606266;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.stats-value {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n</style> "], "mappings": ";AA2IA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAI;AACvD,SAASC,QAAQ,EAAEC,SAAQ,QAAS,YAAW;AAC/C,SAASC,SAAQ,QAAS,cAAa;AACvC,OAAOC,KAAI,MAAO,OAAM;AAExB,eAAe;EACbC,IAAI,EAAE,kBAAkB;EACxBC,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIN,QAAQ,CAAC;IACvB,MAAMO,MAAK,GAAIN,SAAS,CAAC;IACzB,MAAMO,YAAW,GAAIF,KAAK,CAACG,MAAM,CAACC,EAAC;IACjC,IAAIC,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,OAAO;IAC1C,IAAIF,KAAK,EAAE;MACTR,KAAK,CAACW,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,IAAI,UAAUL,KAAK,EAAC;IACnE;IACA;IACA,MAAMM,OAAM,GAAIrB,GAAG,CAAC,KAAK;IACzB,MAAMsB,cAAa,GAAItB,GAAG,CAAC,CAAC,CAAC;IAC7B,MAAMuB,WAAU,GAAIvB,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAMwB,cAAa,GAAIxB,GAAG,CAAC,IAAI;;IAE/B;IACA,MAAMyB,OAAM,GAAIvB,QAAQ,CAAC,MAAM;MAC7B;MACA;MACA,OAAO,IAAG;IACZ,CAAC;;IAED;IACA,MAAMwB,KAAI,GAAIxB,QAAQ,CAAC,MAAM;MAC3B,OAAOoB,cAAc,CAACK,KAAK,CAACC,aAAY,IAAK;IAC/C,CAAC;;IAED;IACAzB,SAAS,CAAC,MAAM;MACd0B,qBAAqB,CAAC;IACxB,CAAC;;IAED;IACA,MAAMA,qBAAoB,GAAI,MAAAA,CAAA,KAAY;MACxCR,OAAO,CAACM,KAAI,GAAI,IAAG;MACnB,IAAI;QACF,MAAMG,QAAO,GAAI,MAAMvB,KAAK,CAACwB,GAAG,CAAC,yCAAyCnB,YAAY,EAAE;QACxFU,cAAc,CAACK,KAAI,GAAIG,QAAQ,CAACE,IAAI,CAACA,IAAG;;QAExC;QACA,IAAIV,cAAc,CAACK,KAAK,CAACM,UAAU,EAAE;UACnC,MAAMC,gBAAgB,CAACZ,cAAc,CAACK,KAAK,CAACM,UAAU;UACtD,MAAME,qBAAqB,CAACb,cAAc,CAACK,KAAK,CAACM,UAAU;QAC7D;MACF,EAAE,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC9B,SAAS,CAAC8B,KAAK,CAAC,UAAU;MAC5B,UAAU;QACRf,OAAO,CAACM,KAAI,GAAI,KAAI;MACtB;IACF;;IAEA;IACA,MAAMO,gBAAe,GAAI,MAAOI,SAAS,IAAK;MAC5C,IAAI;QACF,MAAMR,QAAO,GAAI,MAAMvB,KAAK,CAACwB,GAAG,CAAC,sCAAsCO,SAAS,EAAE;QAClFf,WAAW,CAACI,KAAI,GAAIG,QAAQ,CAACE,IAAI,CAACA,IAAG;MACvC,EAAE,OAAOI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;MAClC;IACF;;IAEA;IACA,MAAMD,qBAAoB,GAAI,MAAOG,SAAS,IAAK;MACjD,IAAI;QACF,MAAMR,QAAO,GAAI,MAAMvB,KAAK,CAACwB,GAAG,CAAC,4DAA4DO,SAAS,EAAE;QACxGd,cAAc,CAACG,KAAI,GAAIG,QAAQ,CAACE,IAAI,CAACA,IAAG;MAC1C,EAAE,OAAOI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK;MACpC;IACF;;IAEA;IACA,MAAMG,UAAS,GAAKC,UAAU,IAAK;MACjC,IAAI,CAACA,UAAU,EAAE,OAAO,GAAE;MAC1B,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU;MAChC,OAAO,GAAGC,IAAI,CAACE,WAAW,CAAC,CAAC,IAAI,CAACF,IAAI,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIL,IAAI,CAACM,OAAO,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC;IAClI;;IAEA;IACA,MAAME,MAAK,GAAIA,CAAA,KAAM;MACnBrC,MAAM,CAACsC,IAAI,CAAC,mBAAmB;IACjC;;IAEA;IACA,MAAMC,cAAa,GAAIA,CAAA,KAAM;MAC3BvC,MAAM,CAACsC,IAAI,CAAC,uBAAuBrC,YAAY,EAAE;IACnD;;IAEA;IACA,MAAMuC,iBAAgB,GAAIA,CAAA,KAAM;MAC9B,IAAI5B,WAAW,CAACI,KAAK,CAACb,EAAE,EAAE;QACxBH,MAAM,CAACsC,IAAI,CAAC,oBAAoB1B,WAAW,CAACI,KAAK,CAACb,EAAE,EAAE;MACxD;IACF;IAEA,OAAO;MACLO,OAAO;MACPC,cAAc;MACdC,WAAW;MACXC,cAAc;MACdE,KAAK;MACLD,OAAO;MACPc,UAAU;MACVS,MAAM;MACNE,cAAc;MACdC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}