const fs = require('fs');
const path = require('path');
const multer = require('multer');
const { v4: uuidv4 } = require('uuid');

// 确保上传目录存在
const ensureDirectoryExists = (dir) => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
};

// 配置课程材料存储
const courseStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../../uploads/courses');
    ensureDirectoryExists(uploadDir);
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    // 使用时间戳和随机数生成文件名，避免使用原始文件名中的中文
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'course-' + uniqueSuffix + ext);
    
    // 保存原始文件名到请求对象，以便后续处理
    req.originalFileName = file.originalname;
  }
});

// 配置头像上传存储
const avatarStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../../uploads/photos');
    ensureDirectoryExists(uploadDir);
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const ext = path.extname(file.originalname);
    const randomName = Math.round(Math.random() * 1E9);
    cb(null, `supervisor-${Date.now()}-${randomName}${ext}`);
  }
});

// 创建课程上传中间件
const uploadCourse = multer({
  storage: courseStorage,
  limits: { fileSize: 100 * 1024 * 1024 }, // 100MB限制
  fileFilter: function(req, file, cb) {
    // 允许任何类型文件
    cb(null, true);
  }
}).single('material');

// 创建头像上传中间件
const uploadAvatar = multer({
  storage: avatarStorage,
  limits: { fileSize: 2 * 1024 * 1024 }, // 2MB限制
  fileFilter: function(req, file, cb) {
    // 只允许图片文件
    if (file.mimetype.startsWith('image/')) {
      cb(null, true);
    } else {
      cb(new Error('只允许上传图片文件'), false);
    }
  }
}).single('avatar');

// 上传课程材料
exports.uploadCourseMaterial = (req, res) => {
  uploadCourse(req, res, function(err) {
    if (err instanceof multer.MulterError) {
      return res.status(400).json({
        success: false,
        message: '文件上传错误',
        error: err.message
      });
    } else if (err) {
      return res.status(500).json({
        success: false,
        message: '服务器错误',
        error: err.message
      });
    }

    // 文件上传成功
    res.status(200).json({
      success: true,
      message: '文件上传成功',
      data: {
        url: `/uploads/courses/${req.file.filename}`,
        filename: req.originalFileName || req.file.originalname // 使用保存的原始文件名
      }
    });
  });
};

// 上传头像
exports.uploadAvatar = (req, res) => {
  uploadAvatar(req, res, function(err) {
    if (err instanceof multer.MulterError) {
      return res.status(400).json({
        success: false,
        message: '文件上传错误',
        error: err.message
      });
    } else if (err) {
      return res.status(500).json({
        success: false,
        message: '服务器错误',
        error: err.message
      });
    }

    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '没有文件上传'
      });
    }

    // 文件上传成功
    res.status(200).json({
      success: true,
      message: '头像上传成功',
      data: {
        url: `/uploads/photos/${req.file.filename}`,
        filename: req.file.originalname
      }
    });
  });
}; 