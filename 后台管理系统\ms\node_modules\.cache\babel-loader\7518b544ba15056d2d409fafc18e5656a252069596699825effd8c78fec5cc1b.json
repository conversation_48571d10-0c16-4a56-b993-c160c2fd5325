{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, openBlock as _openBlock, createBlock as _createBlock, toDisplayString as _toDisplayString, createElementBlock as _createElementBlock, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nconst _hoisted_1 = {\n  class: \"competency-detail-container\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  class: \"teacher-info-card\"\n};\nconst _hoisted_4 = {\n  class: \"teacher-avatar\"\n};\nconst _hoisted_5 = {\n  key: 0\n};\nconst _hoisted_6 = {\n  class: \"certification-info\"\n};\nconst _hoisted_7 = {\n  class: \"certification-status\"\n};\nconst _hoisted_8 = {\n  class: \"certification-note\"\n};\nconst _hoisted_9 = {\n  class: \"certification-progress\"\n};\nconst _hoisted_10 = {\n  class: \"progress-stats\"\n};\nconst _hoisted_11 = {\n  class: \"stat-item\"\n};\nconst _hoisted_12 = {\n  class: \"stat-value\"\n};\nconst _hoisted_13 = {\n  class: \"stat-item\"\n};\nconst _hoisted_14 = {\n  class: \"stat-value\"\n};\nconst _hoisted_15 = {\n  class: \"stat-item\"\n};\nconst _hoisted_16 = {\n  class: \"stat-value\"\n};\nconst _hoisted_17 = {\n  class: \"certification-rules\"\n};\nconst _hoisted_18 = {\n  key: 1,\n  class: \"no-data\"\n};\nconst _hoisted_19 = {\n  key: 0,\n  class: \"no-data\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_image = _resolveComponent(\"el-image\");\n  const _component_el_avatar = _resolveComponent(\"el-avatar\");\n  const _component_el_descriptions_item = _resolveComponent(\"el-descriptions-item\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_descriptions = _resolveComponent(\"el-descriptions\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_el_progress = _resolveComponent(\"el-progress\");\n  const _component_el_divider = _resolveComponent(\"el-divider\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, {\n    class: \"box-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[1] || (_cache[1] = _createElementVNode(\"span\", {\n      class: \"title\"\n    }, \"能力认定详情\", -1 /* CACHED */)), _createElementVNode(\"div\", null, [_createVNode(_component_el_button, {\n      onClick: $setup.goBack\n    }, {\n      default: _withCtx(() => _cache[0] || (_cache[0] = [_createTextVNode(\"返回列表\")])),\n      _: 1 /* STABLE */,\n      __: [0]\n    }, 8 /* PROPS */, [\"onClick\"])])])]),\n    default: _withCtx(() => [_withDirectives((_openBlock(), _createElementBlock(\"div\", null, [_createVNode(_component_el_row, {\n      gutter: 20\n    }, {\n      default: _withCtx(() => [_createCommentVNode(\" 左侧教师基本信息 \"), _createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [$setup.teacherData.photo ? (_openBlock(), _createBlock(_component_el_image, {\n          key: 0,\n          src: `http://localhost:3000${$setup.teacherData.photo}`,\n          fit: \"cover\",\n          class: \"avatar-image\",\n          \"preview-src-list\": [`http://localhost:3000${$setup.teacherData.photo}`]\n        }, null, 8 /* PROPS */, [\"src\", \"preview-src-list\"])) : (_openBlock(), _createBlock(_component_el_avatar, {\n          key: 1,\n          size: 120,\n          icon: \"UserFilled\"\n        }))]), _createVNode(_component_el_descriptions, {\n          title: \"教师信息\",\n          direction: \"vertical\",\n          column: 1,\n          border: \"\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n            label: \"姓名\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.teacherData.name), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_descriptions_item, {\n            label: \"性别\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.teacherData.gender), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_descriptions_item, {\n            label: \"科室\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.teacherData.department), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_descriptions_item, {\n            label: \"学校\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.teacherData.school), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_descriptions_item, {\n            label: \"专业\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.teacherData.major), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_descriptions_item, {\n            label: \"学历\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.teacherData.education), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_descriptions_item, {\n            label: \"在聘状态\"\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_tag, {\n              type: $setup.teacherData.is_employed ? 'success' : 'danger'\n            }, {\n              default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.teacherData.is_employed ? '在聘' : '不在聘'), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }, 8 /* PROPS */, [\"type\"])]),\n            _: 1 /* STABLE */\n          })]),\n          _: 1 /* STABLE */\n        })])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 右侧认定信息 \"), _createVNode(_component_el_col, {\n        span: 18\n      }, {\n        default: _withCtx(() => [_createCommentVNode(\" 认定状态卡片 \"), _createVNode(_component_el_card, {\n          class: \"certification-status-card\",\n          shadow: \"hover\"\n        }, {\n          header: _withCtx(() => _cache[2] || (_cache[2] = [_createElementVNode(\"div\", {\n            class: \"certification-header\"\n          }, [_createElementVNode(\"span\", null, \"认定状态\")], -1 /* CACHED */)])),\n          default: _withCtx(() => [$setup.competencyData ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_tag, {\n            type: $setup.competencyData.is_certified ? 'success' : 'info',\n            size: \"large\"\n          }, {\n            default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.competencyData.is_certified ? '已认证' : '未认证'), 1 /* TEXT */)]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"type\"]), _createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_alert, {\n            title: $setup.getCertificationNote(),\n            type: $setup.competencyData.is_certified ? 'success' : 'info',\n            closable: false,\n            \"show-icon\": \"\"\n          }, null, 8 /* PROPS */, [\"title\", \"type\"])])]), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_el_progress, {\n            percentage: $setup.competencyData.approval_rate || 0,\n            status: $setup.competencyData.is_certified ? 'success' : '',\n            \"stroke-width\": 20,\n            format: $setup.percentFormat\n          }, null, 8 /* PROPS */, [\"percentage\", \"status\", \"format\"]), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, _toDisplayString($setup.competencyData.total_evaluations), 1 /* TEXT */), _cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n            class: \"stat-label\"\n          }, \"评价总数\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, _toDisplayString($setup.competencyData.approved_count), 1 /* TEXT */), _cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n            class: \"stat-label\"\n          }, \"认可数\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, _toDisplayString($setup.competencyData.approval_rate) + \"%\", 1 /* TEXT */), _cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n            class: \"stat-label\"\n          }, \"认可率\", -1 /* CACHED */))])])])]), _createElementVNode(\"div\", _hoisted_17, [_createVNode(_component_el_divider, {\n            \"content-position\": \"left\"\n          }, {\n            default: _withCtx(() => _cache[6] || (_cache[6] = [_createTextVNode(\"认定规则说明\")])),\n            _: 1 /* STABLE */,\n            __: [6]\n          }), _cache[7] || (_cache[7] = _createElementVNode(\"div\", {\n            class: \"rules-content\"\n          }, [_createElementVNode(\"p\", null, \"1. 认定资格考试成绩、现场督导评价，均作为能力认定依据。\"), _createElementVNode(\"p\", null, \"2. 以最新一次的督导评价或最近一次认定资格考试成绩，作为认定结果\"), _createElementVNode(\"p\", null, \"3. 认定资格考试成绩合格、现场督导评价认可，视为通过能力认定\")], -1 /* CACHED */))])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_18, \" 该教师暂无能力认定数据 \"))]),\n          _: 1 /* STABLE */\n        }), _createCommentVNode(\" 评价列表卡片 \"), _createVNode(_component_el_card, {\n          class: \"evaluations-card\",\n          shadow: \"hover\"\n        }, {\n          header: _withCtx(() => _cache[8] || (_cache[8] = [_createElementVNode(\"div\", {\n            class: \"evaluations-header\"\n          }, [_createElementVNode(\"span\", null, \"评价记录\")], -1 /* CACHED */)])),\n          default: _withCtx(() => [_withDirectives((_openBlock(), _createElementBlock(\"div\", null, [_createVNode(_component_el_table, {\n            data: $setup.evaluations,\n            border: \"\",\n            style: {\n              \"width\": \"100%\"\n            }\n          }, {\n            default: _withCtx(() => [_createVNode(_component_el_table_column, {\n              type: \"index\",\n              width: \"50\",\n              label: \"#\"\n            }), _createVNode(_component_el_table_column, {\n              prop: \"supervising_department\",\n              label: \"督导教研室\",\n              width: \"120\"\n            }), _createVNode(_component_el_table_column, {\n              prop: \"case_topic\",\n              label: \"病例/主题\",\n              \"show-overflow-tooltip\": \"\"\n            }), _createVNode(_component_el_table_column, {\n              prop: \"teaching_form\",\n              label: \"教学活动形式\",\n              width: \"120\"\n            }), _createVNode(_component_el_table_column, {\n              prop: \"average_score\",\n              label: \"平均分\",\n              width: \"80\"\n            }), _createVNode(_component_el_table_column, {\n              label: \"能力认定\",\n              width: \"100\"\n            }, {\n              default: _withCtx(scope => [_createVNode(_component_el_tag, {\n                type: scope.row.competency_approved ? 'success' : 'danger'\n              }, {\n                default: _withCtx(() => [_createTextVNode(_toDisplayString(scope.row.competency_approved ? '同意' : '不同意'), 1 /* TEXT */)]),\n                _: 2 /* DYNAMIC */\n              }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_el_table_column, {\n              prop: \"evaluation_date\",\n              label: \"评价时间\",\n              width: \"180\"\n            }, {\n              default: _withCtx(scope => [_createTextVNode(_toDisplayString($setup.formatDate(scope.row.evaluation_date)), 1 /* TEXT */)]),\n              _: 1 /* STABLE */\n            }), _createVNode(_component_el_table_column, {\n              prop: \"evaluator_name\",\n              label: \"评估人\",\n              width: \"100\"\n            }), _createVNode(_component_el_table_column, {\n              label: \"操作\",\n              width: \"100\"\n            }, {\n              default: _withCtx(scope => [_createVNode(_component_el_button, {\n                size: \"small\",\n                onClick: $event => $setup.viewEvaluation(scope.row.id)\n              }, {\n                default: _withCtx(() => _cache[9] || (_cache[9] = [_createTextVNode(\"详情\")])),\n                _: 2 /* DYNAMIC */,\n                __: [9]\n              }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n              _: 1 /* STABLE */\n            })]),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"data\"]), $setup.evaluations.length === 0 && !$setup.evaluationsLoading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_19, \" 暂无评价记录 \")) : _createCommentVNode(\"v-if\", true)])), [[_directive_loading, $setup.evaluationsLoading]])]),\n          _: 1 /* STABLE */\n        })]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])), [[_directive_loading, $setup.loading]])]),\n    _: 1 /* STABLE */\n  })]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_component_el_button", "onClick", "$setup", "goBack", "_cache", "_component_el_row", "gutter", "_createCommentVNode", "_component_el_col", "span", "_hoisted_3", "_hoisted_4", "teacher<PERSON><PERSON>", "photo", "_createBlock", "_component_el_image", "src", "fit", "_component_el_avatar", "size", "icon", "_component_el_descriptions", "title", "direction", "column", "border", "_component_el_descriptions_item", "label", "name", "gender", "department", "school", "major", "education", "_component_el_tag", "type", "is_employed", "shadow", "competencyData", "_hoisted_5", "_hoisted_6", "_hoisted_7", "is_certified", "_hoisted_8", "_component_el_alert", "getCertificationNote", "closable", "_hoisted_9", "_component_el_progress", "percentage", "approval_rate", "status", "format", "percentFormat", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_toDisplayString", "total_evaluations", "_hoisted_13", "_hoisted_14", "approved_count", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_component_el_divider", "_hoisted_18", "_component_el_table", "data", "evaluations", "style", "_component_el_table_column", "width", "prop", "default", "scope", "row", "competency_approved", "formatDate", "evaluation_date", "$event", "viewEvaluation", "id", "length", "evaluationsLoading", "_hoisted_19", "loading"], "sources": ["D:\\admin\\202506\\督导系统\\后台管理系统\\ms\\src\\views\\competency\\CompetencyDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"competency-detail-container\">\r\n    <el-card class=\"box-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span class=\"title\">能力认定详情</span>\r\n          <div>\r\n            <el-button @click=\"goBack\">返回列表</el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <div v-loading=\"loading\">\r\n        <el-row :gutter=\"20\">\r\n          <!-- 左侧教师基本信息 -->\r\n          <el-col :span=\"6\">\r\n            <div class=\"teacher-info-card\">\r\n              <div class=\"teacher-avatar\">\r\n                <el-image\r\n                  v-if=\"teacherData.photo\"\r\n                  :src=\"`http://localhost:3000${teacherData.photo}`\"\r\n                  fit=\"cover\"\r\n                  class=\"avatar-image\"\r\n                  :preview-src-list=\"[`http://localhost:3000${teacherData.photo}`]\"\r\n                />\r\n                <el-avatar v-else :size=\"120\" icon=\"UserFilled\" />\r\n              </div>\r\n              \r\n              <el-descriptions title=\"教师信息\" direction=\"vertical\" :column=\"1\" border>\r\n                <el-descriptions-item label=\"姓名\">{{ teacherData.name }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"性别\">{{ teacherData.gender }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"科室\">{{ teacherData.department }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"学校\">{{ teacherData.school }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"专业\">{{ teacherData.major }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"学历\">{{ teacherData.education }}</el-descriptions-item>\r\n                <el-descriptions-item label=\"在聘状态\">\r\n                  <el-tag :type=\"teacherData.is_employed ? 'success' : 'danger'\">\r\n                    {{ teacherData.is_employed ? '在聘' : '不在聘' }}\r\n                  </el-tag>\r\n                </el-descriptions-item>\r\n              </el-descriptions>\r\n            </div>\r\n          </el-col>\r\n          \r\n          <!-- 右侧认定信息 -->\r\n          <el-col :span=\"18\">\r\n            <!-- 认定状态卡片 -->\r\n            <el-card class=\"certification-status-card\" shadow=\"hover\">\r\n              <template #header>\r\n                <div class=\"certification-header\">\r\n                  <span>认定状态</span>\r\n                </div>\r\n              </template>\r\n              \r\n              <div v-if=\"competencyData\">\r\n                <div class=\"certification-info\">\r\n                  <div class=\"certification-status\">\r\n                    <el-tag :type=\"competencyData.is_certified ? 'success' : 'info'\" size=\"large\">\r\n                      {{ competencyData.is_certified ? '已认证' : '未认证' }}\r\n                    </el-tag>\r\n                    <div class=\"certification-note\">\r\n                      <el-alert\r\n                        :title=\"getCertificationNote()\"\r\n                        :type=\"competencyData.is_certified ? 'success' : 'info'\"\r\n                        :closable=\"false\"\r\n                        show-icon\r\n                      />\r\n                    </div>\r\n                  </div>\r\n                  \r\n                  <div class=\"certification-progress\">\r\n                    <el-progress \r\n                      :percentage=\"competencyData.approval_rate || 0\" \r\n                      :status=\"competencyData.is_certified ? 'success' : ''\" \r\n                      :stroke-width=\"20\"\r\n                      :format=\"percentFormat\"\r\n                    />\r\n                    \r\n                    <div class=\"progress-stats\">\r\n                      <div class=\"stat-item\">\r\n                        <div class=\"stat-value\">{{ competencyData.total_evaluations }}</div>\r\n                        <div class=\"stat-label\">评价总数</div>\r\n                      </div>\r\n                      <div class=\"stat-item\">\r\n                        <div class=\"stat-value\">{{ competencyData.approved_count }}</div>\r\n                        <div class=\"stat-label\">认可数</div>\r\n                      </div>\r\n                      <div class=\"stat-item\">\r\n                        <div class=\"stat-value\">{{ competencyData.approval_rate }}%</div>\r\n                        <div class=\"stat-label\">认可率</div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                \r\n                <div class=\"certification-rules\">\r\n                  <el-divider content-position=\"left\">认定规则说明</el-divider>\r\n                  <div class=\"rules-content\">\r\n                    <p>1. 认定资格考试成绩、现场督导评价，均作为能力认定依据。</p>\r\n                    <p>2. 以最新一次的督导评价或最近一次认定资格考试成绩，作为认定结果</p>\r\n                    <p>3. 认定资格考试成绩合格、现场督导评价认可，视为通过能力认定</p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              <div v-else class=\"no-data\">\r\n                该教师暂无能力认定数据\r\n              </div>\r\n            </el-card>\r\n\r\n            <!-- 评价列表卡片 -->\r\n            <el-card class=\"evaluations-card\" shadow=\"hover\">\r\n              <template #header>\r\n                <div class=\"evaluations-header\">\r\n                  <span>评价记录</span>\r\n                </div>\r\n              </template>\r\n              \r\n              <div v-loading=\"evaluationsLoading\">\r\n                <el-table\r\n                  :data=\"evaluations\"\r\n                  border\r\n                  style=\"width: 100%\"\r\n                >\r\n                  <el-table-column type=\"index\" width=\"50\" label=\"#\" />\r\n                  <el-table-column prop=\"supervising_department\" label=\"督导教研室\" width=\"120\" />\r\n                  <el-table-column prop=\"case_topic\" label=\"病例/主题\" show-overflow-tooltip />\r\n                  <el-table-column prop=\"teaching_form\" label=\"教学活动形式\" width=\"120\" />\r\n                  <el-table-column prop=\"average_score\" label=\"平均分\" width=\"80\" />\r\n                  <el-table-column label=\"能力认定\" width=\"100\">\r\n                    <template #default=\"scope\">\r\n                      <el-tag :type=\"scope.row.competency_approved ? 'success' : 'danger'\">\r\n                        {{ scope.row.competency_approved ? '同意' : '不同意' }}\r\n                      </el-tag>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column prop=\"evaluation_date\" label=\"评价时间\" width=\"180\">\r\n                    <template #default=\"scope\">\r\n                      {{ formatDate(scope.row.evaluation_date) }}\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column prop=\"evaluator_name\" label=\"评估人\" width=\"100\" />\r\n                  <el-table-column label=\"操作\" width=\"100\">\r\n                    <template #default=\"scope\">\r\n                      <el-button size=\"small\" @click=\"viewEvaluation(scope.row.id)\">详情</el-button>\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n                \r\n                <div v-if=\"evaluations.length === 0 && !evaluationsLoading\" class=\"no-data\">\r\n                  暂无评价记录\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, computed, onMounted } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'CompetencyDetail',\r\n  setup() {\r\n    const route = useRoute()\r\n    const router = useRouter()\r\n    const teacherId = route.params.id\r\n      let token = localStorage.getItem('token')\r\n    if (token) {\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`\r\n    }\r\n    // 基础数据\r\n    const loading = ref(false)\r\n    const evaluationsLoading = ref(false)\r\n    const teacherData = ref({})\r\n    const competencyData = ref(null)\r\n    const evaluations = ref([])\r\n    \r\n    // 生命周期钩子\r\n    onMounted(() => {\r\n      fetchTeacherData()\r\n      fetchCompetencyStatus()\r\n      fetchEvaluations()\r\n    })\r\n    \r\n    // 获取教师信息\r\n    const fetchTeacherData = async () => {\r\n      loading.value = true\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/teachers/${teacherId}`)\r\n        teacherData.value = response.data.data\r\n      } catch (error) {\r\n        console.error('获取教师信息失败:', error)\r\n        ElMessage.error('获取教师信息失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 获取能力认证状态\r\n    const fetchCompetencyStatus = async () => {\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/evaluations/competency/teacher/${teacherId}`)\r\n        competencyData.value = response.data.data\r\n      } catch (error) {\r\n        console.error('获取能力认证状态失败:', error)\r\n      }\r\n    }\r\n    \r\n    // 获取教师评价列表\r\n    const fetchEvaluations = async () => {\r\n      evaluationsLoading.value = true\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/evaluations/teacher/${teacherId}`)\r\n        evaluations.value = response.data.data\r\n      } catch (error) {\r\n        console.error('获取评价列表失败:', error)\r\n        ElMessage.error('获取评价列表失败')\r\n      } finally {\r\n        evaluationsLoading.value = false\r\n      }\r\n    }\r\n    \r\n    // 获取认证提示信息\r\n    const getCertificationNote = () => {\r\n      if (!competencyData.value) return ''\r\n      \r\n      if (competencyData.value.is_certified) {\r\n        return `该教师已通过能力认定，认可率为 ${competencyData.value.approval_rate}%`\r\n      } else {\r\n        if (competencyData.value.total_evaluations < 3) {\r\n          return `评价次数不足，至少需要3次评价才能进行认定（当前: ${competencyData.value.total_evaluations}次）`\r\n        } else {\r\n          return `认可率不足，需要80%以上才能获得认定（当前: ${competencyData.value.approval_rate}%）`\r\n        }\r\n      }\r\n    }\r\n    \r\n    // 格式化百分比\r\n    const percentFormat = (percentage) => {\r\n      return `${percentage}%`\r\n    }\r\n    \r\n    // 格式化日期\r\n    const formatDate = (dateString) => {\r\n      if (!dateString) return '-'\r\n      const date = new Date(dateString)\r\n      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`\r\n    }\r\n    \r\n    // 返回列表\r\n    const goBack = () => {\r\n      router.push('/competency/list')\r\n    }\r\n    \r\n    // 查看评价详情\r\n    const viewEvaluation = (id) => {\r\n      router.push(`/evaluations/detail/${id}`)\r\n    }\r\n    \r\n    return {\r\n      loading,\r\n      evaluationsLoading,\r\n      teacherData,\r\n      competencyData,\r\n      evaluations,\r\n      goBack,\r\n      viewEvaluation,\r\n      getCertificationNote,\r\n      percentFormat,\r\n      formatDate\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.competency-detail-container {\r\n  padding: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.teacher-info-card {\r\n  padding: 20px 0;\r\n}\r\n\r\n.teacher-avatar {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.avatar-image {\r\n  width: 120px;\r\n  height: 120px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 2px solid #eee;\r\n}\r\n\r\n.certification-status-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.certification-info {\r\n  display: flex;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.certification-status {\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 200px;\r\n  padding-right: 20px;\r\n}\r\n\r\n.certification-note {\r\n  margin-top: 20px;\r\n  width: 100%;\r\n}\r\n\r\n.certification-progress {\r\n  flex: 1;\r\n  padding-left: 20px;\r\n  border-left: 1px solid #eee;\r\n}\r\n\r\n.progress-stats {\r\n  display: flex;\r\n  margin-top: 20px;\r\n}\r\n\r\n.stat-item {\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-top: 5px;\r\n}\r\n\r\n.certification-rules {\r\n  margin-top: 20px;\r\n}\r\n\r\n.rules-content {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  line-height: 1.8;\r\n  padding: 0 20px;\r\n}\r\n\r\n.evaluations-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.no-data {\r\n  padding: 30px;\r\n  text-align: center;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n</style> "], "mappings": ";;EACOA,KAAK,EAAC;AAA6B;;EAG7BA,KAAK,EAAC;AAAa;;EAYfA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAgB;;;;;EAsCpBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAsB;;EAI1BA,KAAK,EAAC;AAAoB;;EAU5BA,KAAK,EAAC;AAAwB;;EAQ5BA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAGpBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAGpBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAO1BA,KAAK,EAAC;AAAqB;;;EAUtBA,KAAK,EAAC;;;;EA4C4CA,KAAK,EAAC;;;;;;;;;;;;;;;;;;uBApJhFC,mBAAA,CA6JM,OA7JNC,UA6JM,GA5JJC,YAAA,CA2JUC,kBAAA;IA3JDJ,KAAK,EAAC;EAAU;IACZK,MAAM,EAAAC,QAAA,CACf,MAKM,CALNC,mBAAA,CAKM,OALNC,UAKM,G,0BAJJD,mBAAA,CAAiC;MAA3BP,KAAK,EAAC;IAAO,GAAC,QAAM,qBAC1BO,mBAAA,CAEM,cADJJ,YAAA,CAA2CM,oBAAA;MAA/BC,OAAK,EAAEC,MAAA,CAAAC;IAAM;wBAAE,MAAIC,MAAA,QAAAA,MAAA,O,iBAAJ,MAAI,E;;;;sBAKrC,MAgJM,C,+BAhJNZ,mBAAA,CAgJM,cA/IJE,YAAA,CA8ISW,iBAAA;MA9IAC,MAAM,EAAE;IAAE;wBACjB,MAAiB,CAAjBC,mBAAA,cAAiB,EACjBb,YAAA,CA2BSc,iBAAA;QA3BAC,IAAI,EAAE;MAAC;0BACd,MAyBM,CAzBNX,mBAAA,CAyBM,OAzBNY,UAyBM,GAxBJZ,mBAAA,CASM,OATNa,UASM,GAPIT,MAAA,CAAAU,WAAW,CAACC,KAAK,I,cADzBC,YAAA,CAMEC,mBAAA;;UAJCC,GAAG,0BAA0Bd,MAAA,CAAAU,WAAW,CAACC,KAAK;UAC/CI,GAAG,EAAC,OAAO;UACX1B,KAAK,EAAC,cAAc;UACnB,kBAAgB,2BAA2BW,MAAA,CAAAU,WAAW,CAACC,KAAK;+EAE/DC,YAAA,CAAkDI,oBAAA;;UAA/BC,IAAI,EAAE,GAAG;UAAEC,IAAI,EAAC;eAGrC1B,YAAA,CAYkB2B,0BAAA;UAZDC,KAAK,EAAC,MAAM;UAACC,SAAS,EAAC,UAAU;UAAEC,MAAM,EAAE,CAAC;UAAEC,MAAM,EAAN;;4BAC7D,MAA8E,CAA9E/B,YAAA,CAA8EgC,+BAAA;YAAxDC,KAAK,EAAC;UAAI;8BAAC,MAAsB,C,kCAAnBzB,MAAA,CAAAU,WAAW,CAACgB,IAAI,iB;;cACpDlC,YAAA,CAAgFgC,+BAAA;YAA1DC,KAAK,EAAC;UAAI;8BAAC,MAAwB,C,kCAArBzB,MAAA,CAAAU,WAAW,CAACiB,MAAM,iB;;cACtDnC,YAAA,CAAoFgC,+BAAA;YAA9DC,KAAK,EAAC;UAAI;8BAAC,MAA4B,C,kCAAzBzB,MAAA,CAAAU,WAAW,CAACkB,UAAU,iB;;cAC1DpC,YAAA,CAAgFgC,+BAAA;YAA1DC,KAAK,EAAC;UAAI;8BAAC,MAAwB,C,kCAArBzB,MAAA,CAAAU,WAAW,CAACmB,MAAM,iB;;cACtDrC,YAAA,CAA+EgC,+BAAA;YAAzDC,KAAK,EAAC;UAAI;8BAAC,MAAuB,C,kCAApBzB,MAAA,CAAAU,WAAW,CAACoB,KAAK,iB;;cACrDtC,YAAA,CAAmFgC,+BAAA;YAA7DC,KAAK,EAAC;UAAI;8BAAC,MAA2B,C,kCAAxBzB,MAAA,CAAAU,WAAW,CAACqB,SAAS,iB;;cACzDvC,YAAA,CAIuBgC,+BAAA;YAJDC,KAAK,EAAC;UAAM;8BAChC,MAES,CAFTjC,YAAA,CAESwC,iBAAA;cAFAC,IAAI,EAAEjC,MAAA,CAAAU,WAAW,CAACwB,WAAW;;gCACpC,MAA4C,C,kCAAzClC,MAAA,CAAAU,WAAW,CAACwB,WAAW,gC;;;;;;;;UAOpC7B,mBAAA,YAAe,EACfb,YAAA,CA6GSc,iBAAA;QA7GAC,IAAI,EAAE;MAAE;0BACf,MAAe,CAAfF,mBAAA,YAAe,EACfb,YAAA,CA6DUC,kBAAA;UA7DDJ,KAAK,EAAC,2BAA2B;UAAC8C,MAAM,EAAC;;UACrCzC,MAAM,EAAAC,QAAA,CACf,MAEMO,MAAA,QAAAA,MAAA,OAFNN,mBAAA,CAEM;YAFDP,KAAK,EAAC;UAAsB,IAC/BO,mBAAA,CAAiB,cAAX,MAAI,E;4BAQE,MAmEV,CAvEKI,MAAA,CAAAoC,cAAc,I,cAAzB9C,mBAAA,CAiDM,OAAA+C,UAAA,GAhDJzC,mBAAA,CAsCM,OAtCN0C,UAsCM,GArCJ1C,mBAAA,CAYM,OAZN2C,UAYM,GAXJ/C,YAAA,CAESwC,iBAAA;YAFAC,IAAI,EAAEjC,MAAA,CAAAoC,cAAc,CAACI,YAAY;YAAuBvB,IAAI,EAAC;;8BACpE,MAAiD,C,kCAA9CjB,MAAA,CAAAoC,cAAc,CAACI,YAAY,iC;;uCAEhC5C,mBAAA,CAOM,OAPN6C,UAOM,GANJjD,YAAA,CAKEkD,mBAAA;YAJCtB,KAAK,EAAEpB,MAAA,CAAA2C,oBAAoB;YAC3BV,IAAI,EAAEjC,MAAA,CAAAoC,cAAc,CAACI,YAAY;YACjCI,QAAQ,EAAE,KAAK;YAChB,WAAS,EAAT;0DAKNhD,mBAAA,CAsBM,OAtBNiD,UAsBM,GArBJrD,YAAA,CAKEsD,sBAAA;YAJCC,UAAU,EAAE/C,MAAA,CAAAoC,cAAc,CAACY,aAAa;YACxCC,MAAM,EAAEjD,MAAA,CAAAoC,cAAc,CAACI,YAAY;YACnC,cAAY,EAAE,EAAE;YAChBU,MAAM,EAAElD,MAAA,CAAAmD;uEAGXvD,mBAAA,CAaM,OAbNwD,WAaM,GAZJxD,mBAAA,CAGM,OAHNyD,WAGM,GAFJzD,mBAAA,CAAoE,OAApE0D,WAAoE,EAAAC,gBAAA,CAAzCvD,MAAA,CAAAoC,cAAc,CAACoB,iBAAiB,kB,0BAC3D5D,mBAAA,CAAkC;YAA7BP,KAAK,EAAC;UAAY,GAAC,MAAI,oB,GAE9BO,mBAAA,CAGM,OAHN6D,WAGM,GAFJ7D,mBAAA,CAAiE,OAAjE8D,WAAiE,EAAAH,gBAAA,CAAtCvD,MAAA,CAAAoC,cAAc,CAACuB,cAAc,kB,0BACxD/D,mBAAA,CAAiC;YAA5BP,KAAK,EAAC;UAAY,GAAC,KAAG,oB,GAE7BO,mBAAA,CAGM,OAHNgE,WAGM,GAFJhE,mBAAA,CAAiE,OAAjEiE,WAAiE,EAAAN,gBAAA,CAAtCvD,MAAA,CAAAoC,cAAc,CAACY,aAAa,IAAG,GAAC,iB,0BAC3DpD,mBAAA,CAAiC;YAA5BP,KAAK,EAAC;UAAY,GAAC,KAAG,oB,SAMnCO,mBAAA,CAOM,OAPNkE,WAOM,GANJtE,YAAA,CAAuDuE,qBAAA;YAA3C,kBAAgB,EAAC;UAAM;8BAAC,MAAM7D,MAAA,QAAAA,MAAA,O,iBAAN,QAAM,E;;;wCAC1CN,mBAAA,CAIM;YAJDP,KAAK,EAAC;UAAe,IACxBO,mBAAA,CAAoC,WAAjC,+BAA6B,GAChCA,mBAAA,CAAwC,WAArC,mCAAiC,GACpCA,mBAAA,CAAsC,WAAnC,iCAA+B,E,0CAKxCN,mBAAA,CAEM,OAFN0E,WAEM,EAFsB,eAE5B,G;;YAGF3D,mBAAA,YAAe,EACfb,YAAA,CA0CUC,kBAAA;UA1CDJ,KAAK,EAAC,kBAAkB;UAAC8C,MAAM,EAAC;;UAC5BzC,MAAM,EAAAC,QAAA,CACf,MAEMO,MAAA,QAAAA,MAAA,OAFNN,mBAAA,CAEM;YAFDP,KAAK,EAAC;UAAoB,IAC7BO,mBAAA,CAAiB,cAAX,MAAI,E;4BAId,MAkCM,C,+BAlCNN,mBAAA,CAkCM,cAjCJE,YAAA,CA4BWyE,mBAAA;YA3BRC,IAAI,EAAElE,MAAA,CAAAmE,WAAW;YAClB5C,MAAM,EAAN,EAAM;YACN6C,KAAmB,EAAnB;cAAA;YAAA;;8BAEA,MAAqD,CAArD5E,YAAA,CAAqD6E,0BAAA;cAApCpC,IAAI,EAAC,OAAO;cAACqC,KAAK,EAAC,IAAI;cAAC7C,KAAK,EAAC;gBAC/CjC,YAAA,CAA2E6E,0BAAA;cAA1DE,IAAI,EAAC,wBAAwB;cAAC9C,KAAK,EAAC,OAAO;cAAC6C,KAAK,EAAC;gBACnE9E,YAAA,CAAyE6E,0BAAA;cAAxDE,IAAI,EAAC,YAAY;cAAC9C,KAAK,EAAC,OAAO;cAAC,uBAAqB,EAArB;gBACjDjC,YAAA,CAAmE6E,0BAAA;cAAlDE,IAAI,EAAC,eAAe;cAAC9C,KAAK,EAAC,QAAQ;cAAC6C,KAAK,EAAC;gBAC3D9E,YAAA,CAA+D6E,0BAAA;cAA9CE,IAAI,EAAC,eAAe;cAAC9C,KAAK,EAAC,KAAK;cAAC6C,KAAK,EAAC;gBACxD9E,YAAA,CAMkB6E,0BAAA;cAND5C,KAAK,EAAC,MAAM;cAAC6C,KAAK,EAAC;;cACvBE,OAAO,EAAA7E,QAAA,CAGP8E,KAHc,KACvBjF,YAAA,CAESwC,iBAAA;gBAFAC,IAAI,EAAEwC,KAAK,CAACC,GAAG,CAACC,mBAAmB;;kCAC1C,MAAkD,C,kCAA/CF,KAAK,CAACC,GAAG,CAACC,mBAAmB,gC;;;;gBAItCnF,YAAA,CAIkB6E,0BAAA;cAJDE,IAAI,EAAC,iBAAiB;cAAC9C,KAAK,EAAC,MAAM;cAAC6C,KAAK,EAAC;;cAC9CE,OAAO,EAAA7E,QAAA,CAC2B8E,KADpB,K,kCACpBzE,MAAA,CAAA4E,UAAU,CAACH,KAAK,CAACC,GAAG,CAACG,eAAe,kB;;gBAG3CrF,YAAA,CAAiE6E,0BAAA;cAAhDE,IAAI,EAAC,gBAAgB;cAAC9C,KAAK,EAAC,KAAK;cAAC6C,KAAK,EAAC;gBACzD9E,YAAA,CAIkB6E,0BAAA;cAJD5C,KAAK,EAAC,IAAI;cAAC6C,KAAK,EAAC;;cACrBE,OAAO,EAAA7E,QAAA,CAC4D8E,KADrD,KACvBjF,YAAA,CAA4EM,oBAAA;gBAAjEmB,IAAI,EAAC,OAAO;gBAAElB,OAAK,EAAA+E,MAAA,IAAE9E,MAAA,CAAA+E,cAAc,CAACN,KAAK,CAACC,GAAG,CAACM,EAAE;;kCAAG,MAAE9E,MAAA,QAAAA,MAAA,O,iBAAF,IAAE,E;;;;;;;uCAK3DF,MAAA,CAAAmE,WAAW,CAACc,MAAM,WAAWjF,MAAA,CAAAkF,kBAAkB,I,cAA1D5F,mBAAA,CAEM,OAFN6F,WAEM,EAFsE,UAE5E,K,4DAjCcnF,MAAA,CAAAkF,kBAAkB,E;;;;;;iCA1G1BlF,MAAA,CAAAoF,OAAO,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}