{"version": 3, "file": "js/870.64680931.js", "mappings": "iLACOA,MAAM,4B,GAGAA,MAAM,e,SAURA,MAAM,a,GAgBcA,MAAM,iB,aAItBA,MAAM,kB,GACJA,MAAM,a,GACJA,MAAM,c,GAGRA,MAAM,a,GACJA,MAAM,c,GAGRA,MAAM,a,GACJA,MAAM,c,GAGRA,MAAM,a,GACJA,MAAM,c,GAGRA,MAAM,a,GACJA,MAAM,c,GA4ENA,MAAM,kB,GAwBVA,MAAM,c,GA4BPA,MAAM,iB,SAacA,MAAM,mB,GAC3BA,MAAM,mB,GAIHA,MAAM,kB,GAGTA,MAAM,oB,SAGuCA,MAAM,gB,GAO/CA,MAAM,gB,GACNA,MAAM,kB,SACmBA,MAAM,gB,SAO5BA,MAAM,qB,GAEXA,MAAM,gB,SAQ2BA,MAAM,wB,GAEvCA,MAAM,uB,GA2BNA,MAAM,kB,mpBAlQnBC,EAAAA,EAAAA,IAwQM,MAxQNC,EAwQM,EAvQJC,EAAAA,EAAAA,IA4EUC,EAAA,CA5EDJ,MAAM,YAAU,CACZK,QAAMC,EAAAA,EAAAA,IACf,IAOM,EAPNC,EAAAA,EAAAA,IAOM,MAPNC,EAOM,C,eANJD,EAAAA,EAAAA,IAAiC,QAA3BP,MAAM,SAAQ,UAAM,KAC1BO,EAAAA,EAAAA,IAIM,aAHJJ,EAAAA,EAAAA,IAA6CM,EAAA,CAAjCC,QAAOC,EAAAC,QAAM,C,iBAAE,IAAMC,EAAA,MAAAA,EAAA,M,QAAN,a,6BAC3BV,EAAAA,EAAAA,IAAiEM,EAAA,CAAtDK,KAAK,UAAWJ,QAAOC,EAAAI,e,kBAAe,IAAIF,EAAA,MAAAA,EAAA,M,QAAJ,W,6BACjDV,EAAAA,EAAAA,IAAmEM,EAAA,CAAxDK,KAAK,UAAWJ,QAAOC,EAAAK,iB,kBAAiB,IAAIH,EAAA,MAAAA,EAAA,M,QAAJ,W,mDAKzD,IAaM,CAbuBF,EAAAM,W,WAA7BhB,EAAAA,EAAAA,IAaM,MAbNiB,EAaM,EAZJf,EAAAA,EAAAA,IAWkBgB,EAAA,CAXDC,MAAM,OAAQC,OAAQ,EAAGC,OAAA,I,kBACxC,IAA8E,EAA9EnB,EAAAA,EAAAA,IAA8EoB,EAAA,CAAxDC,MAAM,QAAM,C,iBAAC,IAAoB,E,iBAAjBb,EAAAM,SAASG,OAAK,K,OACpDjB,EAAAA,EAAAA,IAAmFoB,EAAA,CAA7DC,MAAM,QAAM,C,iBAAC,IAAuB,E,iBAApBb,EAAAM,SAASQ,UAAW,KAAE,K,OAC5DtB,EAAAA,EAAAA,IAAmFoB,EAAA,CAA7DC,MAAM,MAAI,C,iBAAC,IAA0B,E,iBAAvBb,EAAAM,SAASS,aAAc,IAAC,K,OAC5DvB,EAAAA,EAAAA,IAAoFoB,EAAA,CAA9DC,MAAM,QAAM,C,iBAAC,IAAyB,E,iBAAtBb,EAAAM,SAASU,YAAa,IAAC,K,OAC7DxB,EAAAA,EAAAA,IAIuBoB,EAAA,CAJDC,MAAM,QAAM,C,iBAChC,IAES,EAFTrB,EAAAA,EAAAA,IAESyB,EAAA,CAFAd,KAAMH,EAAAkB,kBAAkBlB,EAAAM,SAASa,S,kBACxC,IAAwC,E,iBAArCnB,EAAAoB,kBAAkBpB,EAAAM,SAASa,SAAM,K,0BAGxC3B,EAAAA,EAAAA,IAA+FoB,EAAA,CAAzEC,MAAM,QAAM,C,iBAAC,IAAqC,E,iBAAlCb,EAAAqB,WAAWrB,EAAAM,SAASgB,aAAU,K,uDAKxEhC,EAAAA,EAAAA,IA+CM,MA/CNiC,EA+CM,CA9CiC,IAArBvB,EAAAwB,UAAUC,S,WAA1BC,EAAAA,EAAAA,IAAmEC,EAAA,C,MAA3BC,YAAY,kB,WAEpDtC,EAAAA,EAAAA,IA2CM,MAAAuC,EAAA,EA1CJjC,EAAAA,EAAAA,IAqBM,MArBNkC,EAqBM,EApBJlC,EAAAA,EAAAA,IAGM,MAHNmC,EAGM,EAFJnC,EAAAA,EAAAA,IAAoD,MAApDoC,GAAoDC,EAAAA,EAAAA,IAAzBjC,EAAAwB,UAAUC,QAAM,G,eAC3C7B,EAAAA,EAAAA,IAAiC,OAA5BP,MAAM,cAAa,OAAG,OAE7BO,EAAAA,EAAAA,IAGM,MAHNsC,EAGM,EAFJtC,EAAAA,EAAAA,IAAqD,MAArDuC,GAAqDF,EAAAA,EAAAA,IAA1BjC,EAAAoC,mBAAiB,G,eAC5CxC,EAAAA,EAAAA,IAAiC,OAA5BP,MAAM,cAAa,OAAG,OAE7BO,EAAAA,EAAAA,IAGM,MAHNyC,EAGM,EAFJzC,EAAAA,EAAAA,IAAuD,MAAvD0C,GAAuDL,EAAAA,EAAAA,IAA5BjC,EAAAuC,qBAAmB,G,eAC9C3C,EAAAA,EAAAA,IAAiC,OAA5BP,MAAM,cAAa,OAAG,OAE7BO,EAAAA,EAAAA,IAGM,MAHN4C,EAGM,EAFJ5C,EAAAA,EAAAA,IAAkD,MAAlD6C,GAAkDR,EAAAA,EAAAA,IAAvBjC,EAAA0C,gBAAc,G,eACzC9C,EAAAA,EAAAA,IAAiC,OAA5BP,MAAM,cAAa,OAAG,OAE7BO,EAAAA,EAAAA,IAGM,MAHN+C,EAGM,EAFJ/C,EAAAA,EAAAA,IAA8C,MAA9CgD,GAA8CX,EAAAA,EAAAA,IAAnBjC,EAAA6C,YAAU,G,eACrCjD,EAAAA,EAAAA,IAAgC,OAA3BP,MAAM,cAAa,MAAE,SAI9BG,EAAAA,EAAAA,IAkBWsD,EAAA,CAlBAC,KAAM/C,EAAAwB,UAAWb,OAAA,GAAOqC,MAAA,gB,kBACjC,IAAqD,EAArDxD,EAAAA,EAAAA,IAAqDyD,EAAA,CAApC9C,KAAK,QAAQ+C,MAAM,KAAKrC,MAAM,OAC/CrB,EAAAA,EAAAA,IAMkByD,EAAA,CANDpC,MAAM,KAAKqC,MAAM,O,CACrBC,SAAOxD,EAAAA,EAAAA,IAGPyD,GAHc,EACvB5D,EAAAA,EAAAA,IAESyB,EAAA,CAFAd,KAAMH,EAAAqD,mBAAmBD,EAAME,IAAInD,O,kBAC1C,IAAyC,E,iBAAtCH,EAAAuD,oBAAoBH,EAAME,IAAInD,OAAI,K,6BAI3CX,EAAAA,EAAAA,IAAqEyD,EAAA,CAApDO,KAAK,UAAU3C,MAAM,OAAO,8BAC7CrB,EAAAA,EAAAA,IAAsDyD,EAAA,CAArCO,KAAK,QAAQ3C,MAAM,KAAKqC,MAAM,QAC/C1D,EAAAA,EAAAA,IAMkByD,EAAA,CANDpC,MAAM,KAAKqC,MAAM,MAAMO,MAAM,S,CACjCN,SAAOxD,EAAAA,EAAAA,IACuDyD,GADhD,EACvB5D,EAAAA,EAAAA,IAAuEM,EAAA,CAA5D4D,KAAK,QAAS3D,QAAK4D,GAAE3D,EAAA4D,aAAaR,EAAME,M,kBAAM,IAAEpD,EAAA,MAAAA,EAAA,M,QAAF,S,gCACzDV,EAAAA,EAAAA,IAAsFM,EAAA,CAA3E4D,KAAK,QAAQvD,KAAK,UAAWJ,QAAK4D,GAAE3D,EAAA6D,aAAaT,EAAME,M,kBAAM,IAAEpD,EAAA,MAAAA,EAAA,M,QAAF,S,gCACxEV,EAAAA,EAAAA,IAAuFM,EAAA,CAA5E4D,KAAK,QAAQvD,KAAK,SAAUJ,QAAK4D,GAAE3D,EAAA8D,eAAeV,EAAME,M,kBAAM,IAAEpD,EAAA,MAAAA,EAAA,M,QAAF,S,qEA1CnEF,EAAA+D,a,OAmDlBvE,EAAAA,EAAAA,IAwGYwE,GAAA,C,WAvGDhE,EAAAiE,c,qCAAAjE,EAAAiE,cAAaN,GACrBlD,MAAOT,EAAAkE,OAAS,OAAS,OAC1BhB,MAAM,S,CA+FKiB,QAAMxE,EAAAA,EAAAA,IACf,IAGO,EAHPC,EAAAA,EAAAA,IAGO,OAHPwE,EAGO,EAFL5E,EAAAA,EAAAA,IAAwDM,EAAA,CAA5CC,QAAKG,EAAA,KAAAA,EAAA,GAAAyD,GAAE3D,EAAAiE,eAAgB,I,kBAAO,IAAE/D,EAAA,MAAAA,EAAA,M,QAAF,S,eAC1CV,EAAAA,EAAAA,IAA4DM,EAAA,CAAjDK,KAAK,UAAWJ,QAAOC,EAAAqE,Y,kBAAY,IAAEnE,EAAA,MAAAA,EAAA,M,QAAF,S,iDAhGlD,IA2FU,EA3FVV,EAAAA,EAAAA,IA2FU8E,GAAA,CA1FRC,IAAI,kBACHC,MAAOxE,EAAAyE,SACPC,MAAO1E,EAAA2E,UACR,cAAY,QACZ,iBAAe,S,kBAEf,IAMe,EANfnF,EAAAA,EAAAA,IAMeoF,EAAA,CAND/D,MAAM,OAAO2C,KAAK,Q,kBAC9B,IAIY,EAJZhE,EAAAA,EAAAA,IAIYqF,EAAA,C,WAJQ7E,EAAAyE,SAAStE,K,qCAATH,EAAAyE,SAAStE,KAAIwD,GAAEmB,YAAY,W,kBAC7C,IAAwC,EAAxCtF,EAAAA,EAAAA,IAAwCuF,EAAA,CAA7BlE,MAAM,MAAMmE,MAAM,YAC7BxF,EAAAA,EAAAA,IAA0CuF,EAAA,CAA/BlE,MAAM,MAAMmE,MAAM,cAC7BxF,EAAAA,EAAAA,IAA4CuF,EAAA,CAAjClE,MAAM,MAAMmE,MAAM,iB,gCAIjCxF,EAAAA,EAAAA,IAOeoF,EAAA,CAPD/D,MAAM,OAAO2C,KAAK,W,kBAC9B,IAKE,EALFhE,EAAAA,EAAAA,IAKEyF,EAAA,C,WAJSjF,EAAAyE,SAASS,Q,qCAATlF,EAAAyE,SAASS,QAAOvB,GACzBxD,KAAK,WACJgF,KAAM,EACPL,YAAY,W,gCAIhBtF,EAAAA,EAAAA,IAEeoF,EAAA,CAFD/D,MAAM,KAAK2C,KAAK,S,kBAC5B,IAAgE,EAAhEhE,EAAAA,EAAAA,IAAgE4F,EAAA,C,WAAtCpF,EAAAyE,SAASY,M,qCAATrF,EAAAyE,SAASY,MAAK1B,GAAG2B,IAAK,EAAIC,IAAK,K,+BAIzB,WAAlBvF,EAAAyE,SAAStE,MAAuC,aAAlBH,EAAAyE,SAAStE,O,WAAvDb,EAAAA,EAAAA,IA0CWkG,EAAAA,GAAA,CAAAC,IAAA,KAzCTjG,EAAAA,EAAAA,IAAmDkG,EAAA,CAAvC,mBAAiB,QAAM,C,iBAAC,IAAExF,EAAA,MAAAA,EAAA,M,QAAF,S,6BAEpCZ,EAAAA,EAAAA,IAgCMkG,EAAAA,GAAA,MAAAG,EAAAA,EAAAA,IA/BsB3F,EAAAyE,SAASmB,QAAO,CAAlCC,EAAQC,M,WADlBxG,EAAAA,EAAAA,IAgCM,OA9BHmG,IAAKK,EACNzG,MAAM,e,EAENG,EAAAA,EAAAA,IA0BeoF,EAAA,CAzBZ/D,MAAK,MAAQkF,OAAOC,aAAa,GAAKF,KACtCtC,KAAI,WAAasC,YACjBpB,MAAO,CAAAuB,UAAA,EAAAC,QAAA,UAAAC,QAAA,S,kBAER,IAoBM,EApBNvG,EAAAA,EAAAA,IAoBM,MApBNwG,EAoBM,EAnBJ5G,EAAAA,EAAAA,IAA2DyF,EAAA,C,WAAxCY,EAAOX,Q,yBAAPW,EAAOX,QAAOvB,EAAEmB,YAAY,W,6CAErB,aAAlB9E,EAAAyE,SAAStE,O,WADjBuB,EAAAA,EAAAA,IAIE2E,GAAA,C,iBAFSR,EAAOS,W,yBAAPT,EAAOS,WAAU3C,EAC1B9C,MAAM,Q,2DAERa,EAAAA,EAAAA,IAKgB6E,GAAA,C,iBAHLvG,EAAAyE,SAAS+B,e,qCAATxG,EAAAyE,SAAS+B,eAAc7C,GAC/B9C,MAAOiF,EACRzG,MAAM,gB,kBACP,IAAIa,EAAA,MAAAA,EAAA,M,QAAJ,W,2CAMOF,EAAAyE,SAASmB,QAAQnE,OAAS,I,WALlCC,EAAAA,EAAAA,IAME5B,EAAA,C,MALAK,KAAK,SACLsG,KAAK,SACLC,OAAA,GACC3G,QAAK4D,GAAE3D,EAAA2G,aAAab,I,gFAO7BlG,EAAAA,EAAAA,IAIM,MAJNgH,EAIM,EAHJpH,EAAAA,EAAAA,IAEYM,EAAA,CAFDK,KAAK,UAAU0G,MAAA,GAAO9G,QAAOC,EAAA8G,UAAYC,SAAU/G,EAAAyE,SAASmB,QAAQnE,QAAU,G,kBAAG,IAE5FvB,EAAA,MAAAA,EAAA,M,QAF4F,a,+DAO9D,eAAlBF,EAAAyE,SAAStE,O,WACvBuB,EAAAA,EAAAA,IAKekD,EAAA,C,MALD/D,MAAM,OAAO2C,KAAK,qB,kBAC9B,IAGiB,EAHjBhE,EAAAA,EAAAA,IAGiBwH,GAAA,C,WAHQhH,EAAAyE,SAASwC,kB,qCAATjH,EAAAyE,SAASwC,kBAAiBtD,I,kBACjD,IAAqC,EAArCnE,EAAAA,EAAAA,IAAqC+G,GAAA,CAA1B1F,OAAO,GAAI,C,iBAAE,IAAEX,EAAA,MAAAA,EAAA,M,QAAF,S,eACxBV,EAAAA,EAAAA,IAAsC+G,GAAA,CAA3B1F,OAAO,GAAK,C,iBAAE,IAAEX,EAAA,MAAAA,EAAA,M,QAAF,S,iEAK/BV,EAAAA,EAAAA,IAOeoF,EAAA,CAPD/D,MAAM,KAAK2C,KAAK,e,kBAC5B,IAKE,EALFhE,EAAAA,EAAAA,IAKEyF,EAAA,C,WAJSjF,EAAAyE,SAASyC,Y,qCAATlH,EAAAyE,SAASyC,YAAWvD,GAC7BxD,KAAK,WACJgF,KAAM,EACPL,YAAY,e,6FAcpBtF,EAAAA,EAAAA,IA+CYwE,GAAA,C,WA9CDhE,EAAAmH,kB,qCAAAnH,EAAAmH,kBAAiBxD,GAC1BlD,MAAM,OACNyC,MAAM,S,kBAEN,IAyCM,CAzCKlD,EAAAoH,kB,WAAX9H,EAAAA,EAAAA,IAyCM,MAzCN+H,EAyCM,EAxCJzH,EAAAA,EAAAA,IAKM,MALN0H,EAKM,EAJJ9H,EAAAA,EAAAA,IAESyB,EAAA,CAFAd,KAAMH,EAAAqD,mBAAmBrD,EAAAoH,gBAAgBjH,MAAOuD,KAAK,S,kBAC5D,IAA+C,E,iBAA5C1D,EAAAuD,oBAAoBvD,EAAAoH,gBAAgBjH,OAAI,K,kBAE7CP,EAAAA,EAAAA,IAAgE,OAAhE2H,GAAgEtF,EAAAA,EAAAA,IAAhCjC,EAAAoH,gBAAgB/B,OAAQ,IAAC,MAG3DzF,EAAAA,EAAAA,IAAiE,MAAjE4H,GAAiEvF,EAAAA,EAAAA,IAAhCjC,EAAAoH,gBAAgBlC,SAAO,GAGpB,eAAzBlF,EAAAoH,gBAAgBjH,O,WAA3Bb,EAAAA,EAAAA,IAaM,MAbNmI,EAaM,G,aAZJnI,EAAAA,EAAAA,IAWMkG,EAAAA,GAAA,MAAAG,EAAAA,EAAAA,IAVsB3F,EAAAoH,gBAAgBxB,QAAO,CAAzCC,EAAQC,M,WADlBxG,EAAAA,EAAAA,IAWM,OATHmG,IAAKK,EACNzG,OAAKqI,EAAAA,EAAAA,IAAA,CAAC,cAAa,kBACS7B,EAAOS,e,EAEnC1G,EAAAA,EAAAA,IAAqE,MAArE+H,GAAqE1F,EAAAA,EAAAA,IAAxC8D,OAAOC,aAAa,GAAKF,IAAK,IAC3DlG,EAAAA,EAAAA,IAAsD,MAAtDgI,GAAsD3F,EAAAA,EAAAA,IAAvB4D,EAAOX,SAAO,GAClCW,EAAOS,a,WAAlBhH,EAAAA,EAAAA,IAEM,MAFNuI,EAEM,EADJrI,EAAAA,EAAAA,IAA4BsI,GAAA,M,iBAAnB,IAAS,EAATtI,EAAAA,EAAAA,IAASuI,M,qDAMxBzI,EAAAA,EAAAA,IAOM,MAPN0I,EAOM,C,eANJpI,EAAAA,EAAAA,IAAqC,OAAhCP,MAAM,gBAAe,SAAK,KAC/BO,EAAAA,EAAAA,IAIM,MAJNqI,EAIM,EAHJzI,EAAAA,EAAAA,IAESyB,EAAA,CAFAd,KAAMH,EAAAoH,gBAAgBH,kBAAoB,UAAY,U,kBAC7D,IAAqD,E,iBAAlDjH,EAAAoH,gBAAgBH,kBAAoB,KAAO,MAAV,K,sBAM/BjH,EAAAoH,gBAAgBF,c,WAA3B5H,EAAAA,EAAAA,IAGM,MAHN4I,EAGM,C,eAFJtI,EAAAA,EAAAA,IAAwC,OAAnCP,MAAM,qBAAoB,OAAG,KAClCO,EAAAA,EAAAA,IAAwE,MAAxEuI,GAAwElG,EAAAA,EAAAA,IAApCjC,EAAAoH,gBAAgBF,aAAW,O,4DAMrE1H,EAAAA,EAAAA,IA0BYwE,GAAA,C,WAzBDhE,EAAAoI,oB,qCAAApI,EAAAoI,oBAAmBzE,GAC5BlD,MAAM,OACNyC,MAAM,S,kBAEN,IAoBY,EApBZ1D,EAAAA,EAAAA,IAoBY6I,GAAA,CAnBVhJ,MAAM,cACNiJ,KAAA,GACAC,OAAO,qDACNC,QAAS,uCACTzF,KAAI,CAAA0F,QAAazI,EAAA0I,QACjB,aAAY1I,EAAA2I,oBACZ,WAAU3I,EAAA4I,kBACV,gBAAe5I,EAAA6I,mBAChBC,OAAO,c,CAMIC,KAAGpJ,EAAAA,EAAAA,IACZ,IAEM,EAFNC,EAAAA,EAAAA,IAEM,MAFNoJ,EAEM,C,uBAFsB,uBACVxJ,EAAAA,EAAAA,IAAyEM,EAAA,CAA9DK,KAAK,UAAU8I,KAAA,GAAMlJ,QAAOC,EAAAkJ,kB,kBAAkB,IAAIhJ,EAAA,MAAAA,EAAA,M,QAAJ,W,iDAN7E,IAA4D,EAA5DV,EAAAA,EAAAA,IAA4DsI,GAAA,CAAnDzI,MAAM,mBAAiB,C,iBAAC,IAAiB,EAAjBG,EAAAA,EAAAA,IAAiB2J,M,qBAClDvJ,EAAAA,EAAAA,IAEM,OAFDP,MAAM,mBAAiB,E,QAAC,oBACbO,EAAAA,EAAAA,IAAa,UAAT,U,yMAmB5B,GACEwJ,KAAM,gBACNC,WAAY,CACVC,MAAK,QACLC,aAAYA,EAAAA,cAEdC,KAAAA,GACE,MAAMC,GAAQC,EAAAA,EAAAA,MACRC,GAASC,EAAAA,EAAAA,MACTlB,EAASe,EAAMI,OAAOC,GAGtB/F,GAAUQ,EAAAA,EAAAA,KAAI,GACdjE,GAAWiE,EAAAA,EAAAA,IAAI,MACf/C,GAAY+C,EAAAA,EAAAA,IAAI,IAChBwF,GAAkBxF,EAAAA,EAAAA,IAAI,MAGtBN,GAAgBM,EAAAA,EAAAA,KAAI,GACpB4C,GAAoB5C,EAAAA,EAAAA,KAAI,GACxB6D,GAAsB7D,EAAAA,EAAAA,KAAI,GAC1BL,GAASK,EAAAA,EAAAA,KAAI,GACb6C,GAAkB7C,EAAAA,EAAAA,IAAI,MAGtBE,GAAWuF,EAAAA,EAAAA,IAAS,CACxBF,GAAI,GACJ3J,KAAM,SACN+E,QAAS,GACTG,MAAO,EACPO,QAAS,CACP,CAAEV,QAAS,GAAIoB,YAAY,GAC3B,CAAEpB,QAAS,GAAIoB,YAAY,GAC3B,CAAEpB,QAAS,GAAIoB,YAAY,GAC3B,CAAEpB,QAAS,GAAIoB,YAAY,IAE7BE,eAAgB,EAChBS,mBAAmB,EACnBC,YAAa,GACbuB,QAASC,IAIL/D,EAAY,CAChBxE,KAAM,CACJ,CAAE8F,UAAU,EAAMC,QAAS,UAAWC,QAAS,WAEjDjB,QAAS,CACP,CAAEe,UAAU,EAAMC,QAAS,UAAWC,QAAS,SAEjDd,MAAO,CACL,CAAEY,UAAU,EAAMC,QAAS,QAASC,QAAS,YAK3C/D,GAAoB6H,EAAAA,EAAAA,IAAS,IAC1BzI,EAAUwD,MAAMkF,OAAOC,GAAgB,WAAXA,EAAEhK,MAAmBsB,QAGpDc,GAAsB0H,EAAAA,EAAAA,IAAS,IAC5BzI,EAAUwD,MAAMkF,OAAOC,GAAgB,aAAXA,EAAEhK,MAAqBsB,QAGtDiB,GAAiBuH,EAAAA,EAAAA,IAAS,IACvBzI,EAAUwD,MAAMkF,OAAOC,GAAgB,eAAXA,EAAEhK,MAAuBsB,QAGxDoB,GAAaoH,EAAAA,EAAAA,IAAS,IACnBzI,EAAUwD,MAAMoF,OAAO,CAACC,EAAKF,IAAME,EAAMF,EAAE9E,MAAO,KAI3DiF,EAAAA,EAAAA,IAAU,KACRC,IACAC,MAIF,MAAMD,EAAgBE,UACpB,IACE,MAAMC,QAAiBC,EAAAA,EAAMC,IAAI,qCAAqClC,KACtEpI,EAAS0E,MAAQ0F,EAAS3H,KAAKA,IACjC,CAAE,MAAO8H,GACPC,QAAQD,MAAM,YAAaA,GAC3BE,EAAAA,GAAUF,MAAM,WAClB,GAIIL,EAAiBC,UACrB1G,EAAQiB,OAAQ,EAChB,IACE,MAAM0F,QAAiBC,EAAAA,EAAMC,IAAI,qCAAqClC,eACtElH,EAAUwD,MAAQ0F,EAAS3H,KAAKA,IAClC,CAAE,MAAO8H,GACPC,QAAQD,MAAM,YAAaA,GAC3BE,EAAAA,GAAUF,MAAM,WAClB,CAAE,QACA9G,EAAQiB,OAAQ,CAClB,GAII5D,EAAqBD,IACzB,MAAM6J,EAAY,CAChB,MAAS,KACT,UAAa,MACb,YAAe,MACf,UAAa,OAEf,OAAOA,EAAU7J,IAAW,QAIxBD,EAAqBC,IACzB,MAAM8J,EAAU,CACd,MAAS,OACT,UAAa,UACb,YAAe,UACf,UAAa,UAEf,OAAOA,EAAQ9J,IAAW,QAItBoC,EAAuBpD,IAC3B,MAAM8K,EAAU,CACd,OAAU,MACV,SAAY,MACZ,WAAc,OAEhB,OAAOA,EAAQ9K,IAAS,QAIpBkD,EAAsBlD,IAC1B,MAAM+K,EAAS,CACb,OAAU,UACV,SAAY,UACZ,WAAc,WAEhB,OAAOA,EAAO/K,IAAS,QAInBkB,EAAc8J,IAClB,IAAKA,EAAY,MAAO,IACxB,MAAMC,EAAO,IAAIC,KAAKF,GACtB,MAAO,GAAGC,EAAKE,kBAAkBF,EAAKG,WAAa,GAAGC,WAAWC,SAAS,EAAG,QAAQL,EAAKM,UAAUF,WAAWC,SAAS,EAAG,QAIvHxL,EAASA,KACb0J,EAAOgC,KAAK,gBAIRvL,EAAgBA,KACpB8D,EAAOc,OAAQ,EACf4G,IACA3H,EAAce,OAAQ,GAIlBnB,EAAgBP,IACpBY,EAAOc,OAAQ,EACfoC,EAAgBpC,MAAQ1B,EAGxBuI,OAAOC,OAAOrH,EAAU,CACtBqF,GAAIxG,EAAIwG,GACR3J,KAAMmD,EAAInD,KACV+E,QAAS5B,EAAI4B,QACbG,MAAO/B,EAAI+B,MACX6B,YAAa5D,EAAI4D,aAAe,GAChCuB,QAASC,IAIM,eAAbpF,EAAInD,KACNsE,EAASwC,kBAAoB3D,EAAI2D,mBAEjCxC,EAASmB,QAAU,IAAItC,EAAIsC,SACV,WAAbtC,EAAInD,OACNsE,EAAS+B,eAAiBlD,EAAIsC,QAAQmG,UAAUC,GAAOA,EAAI1F,cAI/DrC,EAAce,OAAQ,GAIlBpB,EAAgBN,IACpB8D,EAAgBpC,MAAQ1B,EACxB6D,EAAkBnC,OAAQ,GAItBlB,EAAkBR,IACtB2I,EAAAA,EAAaC,QACX,aACA,KACA,CACEC,kBAAmB,KACnBC,iBAAkB,KAClBjM,KAAM,YAGPkM,KAAK5B,UACJ,UACQE,EAAAA,EAAM2B,OAAO,+CAA+ChJ,EAAIwG,MACtEiB,EAAAA,GAAUwB,QAAQ,QAClB/B,GACF,CAAE,MAAOK,GACPC,QAAQD,MAAM,QAASA,GACvBE,EAAAA,GAAUF,MAAM,OAClB,IAED2B,MAAM,KACLzB,EAAAA,GAAU0B,KAAK,YAKf3F,EAAYA,KACZrC,EAASmB,QAAQnE,OAAS,GAC5BgD,EAASmB,QAAQ+F,KAAK,CAAEzG,QAAS,GAAIoB,YAAY,KAK/CK,EAAgBb,IAChBrB,EAASmB,QAAQnE,OAAS,IAC5BgD,EAASmB,QAAQ8G,OAAO5G,EAAO,GAGT,WAAlBrB,EAAStE,MAAqBsE,EAAS+B,iBAAmBV,EAC5DrB,EAAS+B,eAAiB,EACC,WAAlB/B,EAAStE,MAAqBsE,EAAS+B,eAAiBV,GACjErB,EAAS+B,mBAMToF,EAAgBA,KACpBC,OAAOC,OAAOrH,EAAU,CACtBqF,GAAI,GACJ3J,KAAM,SACN+E,QAAS,GACTG,MAAO,EACPO,QAAS,CACP,CAAEV,QAAS,GAAIoB,YAAY,GAC3B,CAAEpB,QAAS,GAAIoB,YAAY,GAC3B,CAAEpB,QAAS,GAAIoB,YAAY,GAC3B,CAAEpB,QAAS,GAAIoB,YAAY,IAE7BE,eAAgB,EAChBS,mBAAmB,EACnBC,YAAa,GACbuB,QAASC,IAGPqB,EAAgB/E,OAClB+E,EAAgB/E,MAAM2H,eAKpBtI,EAAaoG,UACZV,EAAgB/E,aAEf+E,EAAgB/E,MAAM4H,SAASnC,UACnC,IAAIoC,EA0BF,OAAO,EAxBe,WAAlBpI,EAAStE,MACXsE,EAASmB,QAAQkH,QAAQ,CAACd,EAAKlG,KAC7BkG,EAAI1F,WAAaR,IAAUrB,EAAS+B,iBAIxC,IACMtC,EAAOc,aAEH2F,EAAAA,EAAMoC,IAAI,+CAA+CtI,EAASqF,KAAMrF,GAC9EsG,EAAAA,GAAUwB,QAAQ,kBAGZ5B,EAAAA,EAAMqC,KAAK,8CAA+CvI,GAChEsG,EAAAA,GAAUwB,QAAQ,WAGpBtI,EAAce,OAAQ,EACtBwF,GACF,CAAE,MAAOK,GACPC,QAAQD,MAAM,QAASA,GACvBE,EAAAA,GAAUF,MAAM,OAClB,KAQAxK,EAAkBA,KACtB+H,EAAoBpD,OAAQ,GAIxBkE,EAAmBA,KACvB+D,OAAOC,KAAK,uDAAwD,WAIhErE,EAAsBsE,IAC1B,MAAMC,EAAwB,6BAAdD,EAAKhN,MACS,sEAAdgN,EAAKhN,KACfkN,EAASF,EAAKzJ,KAAO,KAAO,KAAO,EASzC,OAPK0J,GACHrC,EAAAA,GAAUF,MAAM,gBAEbwC,GACHtC,EAAAA,GAAUF,MAAM,gBAGXuC,GAAWC,GAId1E,EAAuB+B,IAC3BK,EAAAA,GAAUwB,QAAQ,OAAO7B,EAAS3H,KAAKuK,YACvClF,EAAoBpD,OAAQ,EAC5BwF,KAII5B,EAAqBiC,IACzBC,QAAQD,MAAM,QAASA,GACvBE,EAAAA,GAAUF,MAAM,qBAGlB,MAAO,CACL9G,UACAzD,WACAkB,YACAyC,gBACAkD,oBACAiB,sBACAlE,SACAO,WACAE,YACAoF,kBACA3C,kBACAsB,SACAtG,oBACAG,sBACAG,iBACAG,aACAzB,oBACAF,oBACAqC,sBACAF,qBACAhC,aACApB,SACAG,gBACAyD,eACAD,eACAE,iBACAgD,YACAH,eACAtC,aACAhE,kBACA6I,mBACAL,qBACAF,sBACAC,oBAEJ,G,UCxoBF,MAAM2E,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://ms/./src/views/exams/ExamQuestions.vue", "webpack://ms/./src/views/exams/ExamQuestions.vue?4730"], "sourcesContent": ["<template>\r\n  <div class=\"exam-questions-container\">\r\n    <el-card class=\"box-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span class=\"title\">考试题目管理</span>\r\n          <div>\r\n            <el-button @click=\"goBack\">返回考试列表</el-button>\r\n            <el-button type=\"primary\" @click=\"openAddDialog\">添加题目</el-button>\r\n            <el-button type=\"success\" @click=\"importQuestions\">导入题库</el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <div class=\"exam-info\" v-if=\"examData\">\r\n        <el-descriptions title=\"考试信息\" :column=\"3\" border>\r\n          <el-descriptions-item label=\"考试名称\">{{ examData.title }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"考试时长\">{{ examData.duration }}分钟</el-descriptions-item>\r\n          <el-descriptions-item label=\"总分\">{{ examData.total_score }}分</el-descriptions-item>\r\n          <el-descriptions-item label=\"及格分数\">{{ examData.pass_score }}分</el-descriptions-item>\r\n          <el-descriptions-item label=\"考试状态\">\r\n            <el-tag :type=\"getExamStatusType(examData.status)\">\r\n              {{ getExamStatusText(examData.status) }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"创建时间\">{{ formatDate(examData.created_at) }}</el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n\r\n      <!-- 题目列表 -->\r\n      <div v-loading=\"loading\" class=\"question-list\">\r\n        <el-empty v-if=\"questions.length === 0\" description=\"暂无考试题目，请添加\" />\r\n        \r\n        <div v-else>\r\n          <div class=\"question-stats\">\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-value\">{{ questions.length }}</div>\r\n              <div class=\"stat-label\">总题数</div>\r\n            </div>\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-value\">{{ singleChoiceCount }}</div>\r\n              <div class=\"stat-label\">单选题</div>\r\n            </div>\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-value\">{{ multipleChoiceCount }}</div>\r\n              <div class=\"stat-label\">多选题</div>\r\n            </div>\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-value\">{{ trueFalseCount }}</div>\r\n              <div class=\"stat-label\">判断题</div>\r\n            </div>\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-value\">{{ totalScore }}</div>\r\n              <div class=\"stat-label\">总分</div>\r\n            </div>\r\n          </div>\r\n\r\n          <el-table :data=\"questions\" border style=\"width: 100%\">\r\n            <el-table-column type=\"index\" width=\"50\" label=\"#\" />\r\n            <el-table-column label=\"题型\" width=\"100\">\r\n              <template #default=\"scope\">\r\n                <el-tag :type=\"getQuestionTypeTag(scope.row.type)\">\r\n                  {{ getQuestionTypeText(scope.row.type) }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"content\" label=\"题目内容\" show-overflow-tooltip />\r\n            <el-table-column prop=\"score\" label=\"分值\" width=\"80\" />\r\n            <el-table-column label=\"操作\" width=\"180\" fixed=\"right\">\r\n              <template #default=\"scope\">\r\n                <el-button size=\"small\" @click=\"viewQuestion(scope.row)\">查看</el-button>\r\n                <el-button size=\"small\" type=\"primary\" @click=\"editQuestion(scope.row)\">编辑</el-button>\r\n                <el-button size=\"small\" type=\"danger\" @click=\"deleteQuestion(scope.row)\">删除</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 添加/编辑题目对话框 -->\r\n    <el-dialog\r\n      v-model=\"dialogVisible\"\r\n      :title=\"isEdit ? '编辑题目' : '添加题目'\"\r\n      width=\"700px\"\r\n    >\r\n      <el-form\r\n        ref=\"questionFormRef\"\r\n        :model=\"formData\"\r\n        :rules=\"formRules\"\r\n        label-width=\"100px\"\r\n        label-position=\"right\"\r\n      >\r\n        <el-form-item label=\"题目类型\" prop=\"type\">\r\n          <el-select v-model=\"formData.type\" placeholder=\"请选择题目类型\">\r\n            <el-option label=\"单选题\" value=\"single\" />\r\n            <el-option label=\"多选题\" value=\"multiple\" />\r\n            <el-option label=\"判断题\" value=\"true_false\" />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"题目内容\" prop=\"content\">\r\n          <el-input\r\n            v-model=\"formData.content\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入题目内容\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"分值\" prop=\"score\">\r\n          <el-input-number v-model=\"formData.score\" :min=\"1\" :max=\"100\" />\r\n        </el-form-item>\r\n\r\n        <!-- 选项 (单选题和多选题) -->\r\n        <template v-if=\"formData.type === 'single' || formData.type === 'multiple'\">\r\n          <el-divider content-position=\"left\">选项</el-divider>\r\n          \r\n          <div\r\n            v-for=\"(option, index) in formData.options\"\r\n            :key=\"index\"\r\n            class=\"option-item\"\r\n          >\r\n            <el-form-item\r\n              :label=\"`选项 ${String.fromCharCode(65 + index)}`\"\r\n              :prop=\"`options.${index}.content`\"\r\n              :rules=\"{ required: true, message: '请输入选项内容', trigger: 'blur' }\"\r\n            >\r\n              <div class=\"option-content\">\r\n                <el-input v-model=\"option.content\" placeholder=\"请输入选项内容\" />\r\n                <el-checkbox\r\n                  v-if=\"formData.type === 'multiple'\"\r\n                  v-model=\"option.is_correct\"\r\n                  label=\"正确答案\"\r\n                />\r\n                <el-radio\r\n                  v-else\r\n                  v-model=\"formData.correct_option\"\r\n                  :label=\"index\"\r\n                  class=\"option-radio\"\r\n                >正确答案</el-radio>\r\n                <el-button\r\n                  type=\"danger\"\r\n                  icon=\"Delete\"\r\n                  circle\r\n                  @click=\"removeOption(index)\"\r\n                  v-if=\"formData.options.length > 2\"\r\n                />\r\n              </div>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <div class=\"add-option\">\r\n            <el-button type=\"primary\" plain @click=\"addOption\" :disabled=\"formData.options.length >= 6\">\r\n              添加选项\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n\r\n        <!-- 判断题答案 -->\r\n        <template v-if=\"formData.type === 'true_false'\">\r\n          <el-form-item label=\"正确答案\" prop=\"true_false_answer\">\r\n            <el-radio-group v-model=\"formData.true_false_answer\">\r\n              <el-radio :label=\"true\">正确</el-radio>\r\n              <el-radio :label=\"false\">错误</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </template>\r\n\r\n        <el-form-item label=\"解析\" prop=\"explanation\">\r\n          <el-input\r\n            v-model=\"formData.explanation\"\r\n            type=\"textarea\"\r\n            :rows=\"2\"\r\n            placeholder=\"请输入题目解析（可选）\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitForm\">确认</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- 查看题目对话框 -->\r\n    <el-dialog\r\n      v-model=\"viewDialogVisible\"\r\n      title=\"题目详情\"\r\n      width=\"700px\"\r\n    >\r\n      <div v-if=\"currentQuestion\" class=\"question-detail\">\r\n        <div class=\"question-header\">\r\n          <el-tag :type=\"getQuestionTypeTag(currentQuestion.type)\" size=\"large\">\r\n            {{ getQuestionTypeText(currentQuestion.type) }}\r\n          </el-tag>\r\n          <span class=\"question-score\">{{ currentQuestion.score }}分</span>\r\n        </div>\r\n\r\n        <div class=\"question-content\">{{ currentQuestion.content }}</div>\r\n\r\n        <!-- 选项 -->\r\n        <div v-if=\"currentQuestion.type !== 'true_false'\" class=\"options-list\">\r\n          <div\r\n            v-for=\"(option, index) in currentQuestion.options\"\r\n            :key=\"index\"\r\n            class=\"option-item\"\r\n            :class=\"{ 'correct-option': option.is_correct }\"\r\n          >\r\n            <div class=\"option-label\">{{ String.fromCharCode(65 + index) }}</div>\r\n            <div class=\"option-content\">{{ option.content }}</div>\r\n            <div v-if=\"option.is_correct\" class=\"correct-mark\">\r\n              <el-icon><Check /></el-icon>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 判断题答案 -->\r\n        <div v-else class=\"true-false-answer\">\r\n          <div class=\"answer-label\">正确答案：</div>\r\n          <div class=\"answer-value\">\r\n            <el-tag :type=\"currentQuestion.true_false_answer ? 'success' : 'danger'\">\r\n              {{ currentQuestion.true_false_answer ? '正确' : '错误' }}\r\n            </el-tag>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 解析 -->\r\n        <div v-if=\"currentQuestion.explanation\" class=\"question-explanation\">\r\n          <div class=\"explanation-label\">解析：</div>\r\n          <div class=\"explanation-content\">{{ currentQuestion.explanation }}</div>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 导入题库对话框 -->\r\n    <el-dialog\r\n      v-model=\"importDialogVisible\"\r\n      title=\"导入题库\"\r\n      width=\"500px\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        drag\r\n        action=\"http://localhost:3000/api/exams/questions/import\"\r\n        :headers=\"{ 'Content-Type': 'multipart/form-data' }\"\r\n        :data=\"{ exam_id: examId }\"\r\n        :on-success=\"handleImportSuccess\"\r\n        :on-error=\"handleImportError\"\r\n        :before-upload=\"beforeImportUpload\"\r\n        accept=\".xlsx,.xls\"\r\n      >\r\n        <el-icon class=\"el-icon--upload\"><upload-filled /></el-icon>\r\n        <div class=\"el-upload__text\">\r\n          将Excel文件拖到此处，或<em>点击上传</em>\r\n        </div>\r\n        <template #tip>\r\n          <div class=\"el-upload__tip\">\r\n            请上传Excel格式的题库文件，<el-button type=\"primary\" link @click=\"downloadTemplate\">下载模板</el-button>\r\n          </div>\r\n        </template>\r\n      </el-upload>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, computed, onMounted } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { Check, UploadFilled } from '@element-plus/icons-vue'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'ExamQuestions',\r\n  components: {\r\n    Check,\r\n    UploadFilled\r\n  },\r\n  setup() {\r\n    const route = useRoute()\r\n    const router = useRouter()\r\n    const examId = route.params.id\r\n    \r\n    // 基础数据\r\n    const loading = ref(false)\r\n    const examData = ref(null)\r\n    const questions = ref([])\r\n    const questionFormRef = ref(null)\r\n    \r\n    // 对话框\r\n    const dialogVisible = ref(false)\r\n    const viewDialogVisible = ref(false)\r\n    const importDialogVisible = ref(false)\r\n    const isEdit = ref(false)\r\n    const currentQuestion = ref(null)\r\n    \r\n    // 表单数据\r\n    const formData = reactive({\r\n      id: '',\r\n      type: 'single',\r\n      content: '',\r\n      score: 5,\r\n      options: [\r\n        { content: '', is_correct: false },\r\n        { content: '', is_correct: false },\r\n        { content: '', is_correct: false },\r\n        { content: '', is_correct: false }\r\n      ],\r\n      correct_option: 0,\r\n      true_false_answer: true,\r\n      explanation: '',\r\n      exam_id: examId\r\n    })\r\n    \r\n    // 表单验证规则\r\n    const formRules = {\r\n      type: [\r\n        { required: true, message: '请选择题目类型', trigger: 'change' }\r\n      ],\r\n      content: [\r\n        { required: true, message: '请输入题目内容', trigger: 'blur' }\r\n      ],\r\n      score: [\r\n        { required: true, message: '请输入分值', trigger: 'change' }\r\n      ]\r\n    }\r\n    \r\n    // 计算属性\r\n    const singleChoiceCount = computed(() => {\r\n      return questions.value.filter(q => q.type === 'single').length\r\n    })\r\n    \r\n    const multipleChoiceCount = computed(() => {\r\n      return questions.value.filter(q => q.type === 'multiple').length\r\n    })\r\n    \r\n    const trueFalseCount = computed(() => {\r\n      return questions.value.filter(q => q.type === 'true_false').length\r\n    })\r\n    \r\n    const totalScore = computed(() => {\r\n      return questions.value.reduce((sum, q) => sum + q.score, 0)\r\n    })\r\n    \r\n    // 生命周期钩子\r\n    onMounted(() => {\r\n      fetchExamData()\r\n      fetchQuestions()\r\n    })\r\n    \r\n    // 获取考试信息\r\n    const fetchExamData = async () => {\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/exams/${examId}`)\r\n        examData.value = response.data.data\r\n      } catch (error) {\r\n        console.error('获取考试信息失败:', error)\r\n        ElMessage.error('获取考试信息失败')\r\n      }\r\n    }\r\n    \r\n    // 获取题目列表\r\n    const fetchQuestions = async () => {\r\n      loading.value = true\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/exams/${examId}/questions`)\r\n        questions.value = response.data.data\r\n      } catch (error) {\r\n        console.error('获取题目列表失败:', error)\r\n        ElMessage.error('获取题目列表失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 获取考试状态文本\r\n    const getExamStatusText = (status) => {\r\n      const statusMap = {\r\n        'draft': '草稿',\r\n        'published': '已发布',\r\n        'in_progress': '进行中',\r\n        'completed': '已结束'\r\n      }\r\n      return statusMap[status] || '未知状态'\r\n    }\r\n    \r\n    // 获取考试状态类型\r\n    const getExamStatusType = (status) => {\r\n      const typeMap = {\r\n        'draft': 'info',\r\n        'published': 'success',\r\n        'in_progress': 'warning',\r\n        'completed': 'danger'\r\n      }\r\n      return typeMap[status] || 'info'\r\n    }\r\n    \r\n    // 获取题目类型文本\r\n    const getQuestionTypeText = (type) => {\r\n      const typeMap = {\r\n        'single': '单选题',\r\n        'multiple': '多选题',\r\n        'true_false': '判断题'\r\n      }\r\n      return typeMap[type] || '未知类型'\r\n    }\r\n    \r\n    // 获取题目类型标签\r\n    const getQuestionTypeTag = (type) => {\r\n      const tagMap = {\r\n        'single': 'primary',\r\n        'multiple': 'success',\r\n        'true_false': 'warning'\r\n      }\r\n      return tagMap[type] || 'info'\r\n    }\r\n    \r\n    // 格式化日期\r\n    const formatDate = (dateString) => {\r\n      if (!dateString) return '-'\r\n      const date = new Date(dateString)\r\n      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`\r\n    }\r\n    \r\n    // 返回考试列表\r\n    const goBack = () => {\r\n      router.push('/exams/list')\r\n    }\r\n    \r\n    // 打开添加对话框\r\n    const openAddDialog = () => {\r\n      isEdit.value = false\r\n      resetFormData()\r\n      dialogVisible.value = true\r\n    }\r\n    \r\n    // 编辑题目\r\n    const editQuestion = (row) => {\r\n      isEdit.value = true\r\n      currentQuestion.value = row\r\n      \r\n      // 填充表单数据\r\n      Object.assign(formData, {\r\n        id: row.id,\r\n        type: row.type,\r\n        content: row.content,\r\n        score: row.score,\r\n        explanation: row.explanation || '',\r\n        exam_id: examId\r\n      })\r\n      \r\n      // 处理选项和答案\r\n      if (row.type === 'true_false') {\r\n        formData.true_false_answer = row.true_false_answer\r\n      } else {\r\n        formData.options = [...row.options]\r\n        if (row.type === 'single') {\r\n          formData.correct_option = row.options.findIndex(opt => opt.is_correct)\r\n        }\r\n      }\r\n      \r\n      dialogVisible.value = true\r\n    }\r\n    \r\n    // 查看题目\r\n    const viewQuestion = (row) => {\r\n      currentQuestion.value = row\r\n      viewDialogVisible.value = true\r\n    }\r\n    \r\n    // 删除题目\r\n    const deleteQuestion = (row) => {\r\n      ElMessageBox.confirm(\r\n        '确定要删除该题目吗？',\r\n        '警告',\r\n        {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n        }\r\n      )\r\n        .then(async () => {\r\n          try {\r\n            await axios.delete(`http://localhost:3000/api/exams/questions/${row.id}`)\r\n            ElMessage.success('删除成功')\r\n            fetchQuestions()\r\n          } catch (error) {\r\n            console.error('删除失败:', error)\r\n            ElMessage.error('删除失败')\r\n          }\r\n        })\r\n        .catch(() => {\r\n          ElMessage.info('已取消删除')\r\n        })\r\n    }\r\n    \r\n    // 添加选项\r\n    const addOption = () => {\r\n      if (formData.options.length < 6) {\r\n        formData.options.push({ content: '', is_correct: false })\r\n      }\r\n    }\r\n    \r\n    // 移除选项\r\n    const removeOption = (index) => {\r\n      if (formData.options.length > 2) {\r\n        formData.options.splice(index, 1)\r\n        \r\n        // 如果删除的是正确答案，重置正确答案\r\n        if (formData.type === 'single' && formData.correct_option === index) {\r\n          formData.correct_option = 0\r\n        } else if (formData.type === 'single' && formData.correct_option > index) {\r\n          formData.correct_option--\r\n        }\r\n      }\r\n    }\r\n    \r\n    // 重置表单数据\r\n    const resetFormData = () => {\r\n      Object.assign(formData, {\r\n        id: '',\r\n        type: 'single',\r\n        content: '',\r\n        score: 5,\r\n        options: [\r\n          { content: '', is_correct: false },\r\n          { content: '', is_correct: false },\r\n          { content: '', is_correct: false },\r\n          { content: '', is_correct: false }\r\n        ],\r\n        correct_option: 0,\r\n        true_false_answer: true,\r\n        explanation: '',\r\n        exam_id: examId\r\n      })\r\n      \r\n      if (questionFormRef.value) {\r\n        questionFormRef.value.resetFields()\r\n      }\r\n    }\r\n    \r\n    // 提交表单\r\n    const submitForm = async () => {\r\n      if (!questionFormRef.value) return\r\n      \r\n      await questionFormRef.value.validate(async (valid) => {\r\n        if (valid) {\r\n          // 处理单选题答案\r\n          if (formData.type === 'single') {\r\n            formData.options.forEach((opt, index) => {\r\n              opt.is_correct = index === formData.correct_option\r\n            })\r\n          }\r\n          \r\n          try {\r\n            if (isEdit.value) {\r\n              // 编辑模式\r\n              await axios.put(`http://localhost:3000/api/exams/questions/${formData.id}`, formData)\r\n              ElMessage.success('题目更新成功')\r\n            } else {\r\n              // 添加模式\r\n              await axios.post('http://localhost:3000/api/exams/questions', formData)\r\n              ElMessage.success('题目添加成功')\r\n            }\r\n            \r\n            dialogVisible.value = false\r\n            fetchQuestions()\r\n          } catch (error) {\r\n            console.error('操作失败:', error)\r\n            ElMessage.error('操作失败')\r\n          }\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    }\r\n    \r\n    // 导入题库\r\n    const importQuestions = () => {\r\n      importDialogVisible.value = true\r\n    }\r\n    \r\n    // 下载模板\r\n    const downloadTemplate = () => {\r\n      window.open('http://localhost:3000/api/exams/questions/template', '_blank')\r\n    }\r\n    \r\n    // 上传前验证\r\n    const beforeImportUpload = (file) => {\r\n      const isExcel = file.type === 'application/vnd.ms-excel' || \r\n                      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\r\n      const isLt2M = file.size / 1024 / 1024 < 2\r\n      \r\n      if (!isExcel) {\r\n        ElMessage.error('只能上传Excel文件!')\r\n      }\r\n      if (!isLt2M) {\r\n        ElMessage.error('文件大小不能超过2MB!')\r\n      }\r\n      \r\n      return isExcel && isLt2M\r\n    }\r\n    \r\n    // 导入成功\r\n    const handleImportSuccess = (response) => {\r\n      ElMessage.success(`成功导入${response.data.count}道题目`)\r\n      importDialogVisible.value = false\r\n      fetchQuestions()\r\n    }\r\n    \r\n    // 导入失败\r\n    const handleImportError = (error) => {\r\n      console.error('导入失败:', error)\r\n      ElMessage.error('导入失败，请检查文件格式是否正确')\r\n    }\r\n    \r\n    return {\r\n      loading,\r\n      examData,\r\n      questions,\r\n      dialogVisible,\r\n      viewDialogVisible,\r\n      importDialogVisible,\r\n      isEdit,\r\n      formData,\r\n      formRules,\r\n      questionFormRef,\r\n      currentQuestion,\r\n      examId,\r\n      singleChoiceCount,\r\n      multipleChoiceCount,\r\n      trueFalseCount,\r\n      totalScore,\r\n      getExamStatusText,\r\n      getExamStatusType,\r\n      getQuestionTypeText,\r\n      getQuestionTypeTag,\r\n      formatDate,\r\n      goBack,\r\n      openAddDialog,\r\n      editQuestion,\r\n      viewQuestion,\r\n      deleteQuestion,\r\n      addOption,\r\n      removeOption,\r\n      submitForm,\r\n      importQuestions,\r\n      downloadTemplate,\r\n      beforeImportUpload,\r\n      handleImportSuccess,\r\n      handleImportError\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.exam-questions-container {\r\n  padding: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.exam-info {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.question-list {\r\n  margin-top: 20px;\r\n}\r\n\r\n.question-stats {\r\n  display: flex;\r\n  margin-bottom: 20px;\r\n  background-color: #f7f7f7;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.stat-item {\r\n  flex: 1;\r\n  text-align: center;\r\n  border-right: 1px solid #eee;\r\n}\r\n\r\n.stat-item:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-top: 5px;\r\n}\r\n\r\n.option-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.option-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.option-radio {\r\n  margin-left: 10px;\r\n}\r\n\r\n.add-option {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 10px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.question-detail {\r\n  padding: 10px;\r\n}\r\n\r\n.question-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.question-score {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #f56c6c;\r\n}\r\n\r\n.question-content {\r\n  font-size: 16px;\r\n  margin-bottom: 20px;\r\n  padding: 10px;\r\n  background-color: #f7f7f7;\r\n  border-radius: 4px;\r\n}\r\n\r\n.options-list {\r\n  margin-top: 15px;\r\n}\r\n\r\n.options-list .option-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.options-list .option-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.options-list .option-item.correct-option {\r\n  background-color: #f0f9eb;\r\n}\r\n\r\n.options-list .option-label {\r\n  width: 30px;\r\n  height: 30px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-radius: 50%;\r\n  background-color: #f2f6fc;\r\n  margin-right: 10px;\r\n  font-weight: bold;\r\n}\r\n\r\n.options-list .option-content {\r\n  flex: 1;\r\n}\r\n\r\n.options-list .correct-mark {\r\n  color: #67c23a;\r\n  margin-left: 10px;\r\n}\r\n\r\n.true-false-answer {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-top: 15px;\r\n}\r\n\r\n.answer-label {\r\n  font-weight: bold;\r\n  margin-right: 10px;\r\n}\r\n\r\n.question-explanation {\r\n  margin-top: 20px;\r\n  padding: 10px;\r\n  background-color: #f7f7f7;\r\n  border-radius: 4px;\r\n}\r\n\r\n.explanation-label {\r\n  font-weight: bold;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.explanation-content {\r\n  color: #606266;\r\n  white-space: pre-line;\r\n}\r\n</style> ", "import { render } from \"./ExamQuestions.vue?vue&type=template&id=9ada6f44&scoped=true\"\nimport script from \"./ExamQuestions.vue?vue&type=script&lang=js\"\nexport * from \"./ExamQuestions.vue?vue&type=script&lang=js\"\n\nimport \"./ExamQuestions.vue?vue&type=style&index=0&id=9ada6f44&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-9ada6f44\"]])\n\nexport default __exports__"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_component_el_button", "onClick", "$setup", "goBack", "_cache", "type", "openAddDialog", "importQuestions", "examData", "_hoisted_3", "_component_el_descriptions", "title", "column", "border", "_component_el_descriptions_item", "label", "duration", "total_score", "pass_score", "_component_el_tag", "getExamStatusType", "status", "getExamStatusText", "formatDate", "created_at", "_hoisted_4", "questions", "length", "_createBlock", "_component_el_empty", "description", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_toDisplayString", "_hoisted_9", "_hoisted_10", "singleChoiceCount", "_hoisted_11", "_hoisted_12", "multipleChoiceCount", "_hoisted_13", "_hoisted_14", "trueFalseCount", "_hoisted_15", "_hoisted_16", "totalScore", "_component_el_table", "data", "style", "_component_el_table_column", "width", "default", "scope", "getQuestionTypeTag", "row", "getQuestionTypeText", "prop", "fixed", "size", "$event", "viewQuestion", "editQuestion", "deleteQuestion", "loading", "_component_el_dialog", "dialogVisible", "isEdit", "footer", "_hoisted_19", "submitForm", "_component_el_form", "ref", "model", "formData", "rules", "formRules", "_component_el_form_item", "_component_el_select", "placeholder", "_component_el_option", "value", "_component_el_input", "content", "rows", "_component_el_input_number", "score", "min", "max", "_Fragment", "key", "_component_el_divider", "_renderList", "options", "option", "index", "String", "fromCharCode", "required", "message", "trigger", "_hoisted_17", "_component_el_checkbox", "is_correct", "_component_el_radio", "correct_option", "icon", "circle", "removeOption", "_hoisted_18", "plain", "addOption", "disabled", "_component_el_radio_group", "true_false_answer", "explanation", "viewDialogVisible", "currentQuestion", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_normalizeClass", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_component_el_icon", "_component_Check", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_hoisted_31", "importDialogVisible", "_component_el_upload", "drag", "action", "headers", "exam_id", "examId", "handleImportSuccess", "handleImportError", "beforeImportUpload", "accept", "tip", "_hoisted_32", "link", "downloadTemplate", "_component_upload_filled", "name", "components", "Check", "UploadFilled", "setup", "route", "useRoute", "router", "useRouter", "params", "id", "questionFormRef", "reactive", "computed", "filter", "q", "reduce", "sum", "onMounted", "fetchExamData", "fetchQuestions", "async", "response", "axios", "get", "error", "console", "ElMessage", "statusMap", "typeMap", "tagMap", "dateString", "date", "Date", "getFullYear", "getMonth", "toString", "padStart", "getDate", "push", "resetFormData", "Object", "assign", "findIndex", "opt", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "then", "delete", "success", "catch", "info", "splice", "resetFields", "validate", "valid", "for<PERSON>ach", "put", "post", "window", "open", "file", "isExcel", "isLt2M", "count", "__exports__", "render"], "sourceRoot": ""}