const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const trainingModel = require('../models/trainingModel');

// 配置课件/视频上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../../uploads/courses');
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    // 处理文件名编码 - 确保文件扩展名正确提取
    let originalName = file.originalname;
    // 如果文件名是 Buffer，转换为字符串
    if (Buffer.isBuffer(originalName)) {
      originalName = originalName.toString('utf8');
    }
    // 处理 URL 编码的文件名
    try {
      originalName = decodeURIComponent(originalName);
    } catch (e) {
      // 如果解码失败，使用原始文件名
      console.log('无法解码文件名:', e.message);
    }
    const ext = path.extname(originalName);
    cb(null, 'training-' + uniqueSuffix + ext);
  }
});

const upload = multer({ 
  storage: storage,
  limits: { fileSize: 100 * 1024 * 1024 }, // 限制100MB
  fileFilter: function (req, file, cb) {
    // 设置文件名的编码
    file.originalname = Buffer.from(file.originalname, 'latin1').toString('utf8');
    cb(null, true);
  }
});

// 处理文件名编码的函数
function handleFilename(filename) {
  console.log('原始文件名:', filename);
  if (!filename) return null;
  
  // 如果文件名是 Buffer，转换为字符串
  if (Buffer.isBuffer(filename)) {
    filename = filename.toString('utf8');
  }
  
  // 尝试修复来自 latin1 编码的文件名
  try {
    const correctedName = Buffer.from(filename, 'latin1').toString('utf8');
    console.log('修正后的文件名:', correctedName);
    return correctedName;
  } catch (e) {
    console.log('文件名编码处理失败:', e.message);
    return filename; // 如果处理失败，返回原始文件名
  }
}

// 获取所有培训课程
router.get('/', async (req, res, next) => {
  try {
    const { page = 1, limit = 10, title, course_type } = req.query;
    const filters = { title, course_type };
    
    const result = await trainingModel.findAll(filters, page, limit);
    
    res.status(200).json({
      success: true,
      count: result.count,
      data: result.data
    });
  } catch (error) {
    next(error);
  }
});

// 获取单个培训课程
router.get('/:id', async (req, res, next) => {
  try {
    const training = await trainingModel.findById(req.params.id);
    
    if (!training) {
      return res.status(404).json({
        success: false,
        message: '未找到该培训课程'
      });
    }
    
    res.status(200).json({
      success: true,
      data: training
    });
  } catch (error) {
    next(error);
  }
});

// 创建培训课程
router.post('/', upload.single('material'), async (req, res, next) => {
  try {
    const { title, description, course_type } = req.body;
    
    // 检查必填项
    if (!title || !course_type) {
      return res.status(400).json({
        success: false,
        message: '请填写课程标题和类型'
      });
    }
    
    // 处理课件/视频路径
    let materialPath = null;
    let originalFilename = null;
    
    if (req.file) {
      materialPath = `/uploads/courses/${req.file.filename}`;
      originalFilename = req.file.originalname;
    }
    
    const trainingData = {
      title,
      description: description || null,
      course_type,
      material_path: materialPath,
      original_filename: originalFilename
    };
    
    const result = await trainingModel.create(trainingData);
    
    res.status(201).json({
      success: true,
      message: '培训课程创建成功',
      data: result
    });
  } catch (error) {
    next(error);
  }
});

// 更新培训课程
router.put('/:id', upload.single('material'), async (req, res, next) => {
  try {
    const { title, description, course_type } = req.body;
    
    // 检查必填项
    if (!title || !course_type) {
      return res.status(400).json({
        success: false,
        message: '请填写课程标题和类型'
      });
    }
    
    // 获取当前课程信息
    const currentCourse = await trainingModel.findById(req.params.id);
    
    if (!currentCourse) {
      return res.status(404).json({
        success: false,
        message: '未找到该培训课程'
      });
    }
    
    // 处理课件/视频路径
    let materialPath = currentCourse.material_path;
    let originalFilename = currentCourse.original_filename;
    
    if (req.file) {
      materialPath = `/uploads/courses/${req.file.filename}`;
      originalFilename = req.file.originalname;
      
      // 删除旧文件
      if (currentCourse.material_path) {
        const oldFilePath = path.join(__dirname, '../..', currentCourse.material_path);
        if (fs.existsSync(oldFilePath)) {
          fs.unlinkSync(oldFilePath);
        }
      }
    }
    
    const trainingData = {
      title,
      description: description || null,
      course_type,
      material_path: materialPath,
      original_filename: originalFilename
    };
    
    const result = await trainingModel.update(req.params.id, trainingData);
    
    if (!result) {
      return res.status(404).json({
        success: false,
        message: '更新失败，未找到该培训课程'
      });
    }
    
    res.status(200).json({
      success: true,
      message: '培训课程更新成功',
      data: result
    });
  } catch (error) {
    next(error);
  }
});

// 删除培训课程
router.delete('/:id', async (req, res, next) => {
  try {
    // 获取当前课程信息
    const currentCourse = await trainingModel.findById(req.params.id);
    
    if (!currentCourse) {
      return res.status(404).json({
        success: false,
        message: '未找到该培训课程'
      });
    }
    
    // 删除课件/视频文件
    if (currentCourse.material_path) {
      const filePath = path.join(__dirname, '../..', currentCourse.material_path);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
    }
    
    const result = await trainingModel.remove(req.params.id);
    
    res.status(200).json({
      success: true,
      message: '培训课程删除成功'
    });
  } catch (error) {
    next(error);
  }
});

// 按类型获取培训课程
router.get('/type/:courseType', async (req, res, next) => {
  try {
    const courseType = req.params.courseType;
    const filters = { course_type: courseType };
    
    const result = await trainingModel.findAll(filters);
    
    res.status(200).json({
      success: true,
      count: result.count,
      data: result.data
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router; 