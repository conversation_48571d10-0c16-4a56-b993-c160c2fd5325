import api from '@/utils/api';

const supervisorService = {
  /**
   * 获取督导成员列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getSupervisors(params) {
    return api.get('/api/supervisors', { params });
  },

  /**
   * 获取单个督导成员
   * @param {number} id - 督导ID
   * @returns {Promise}
   */
  getSupervisor(id) {
    return api.get(`/api/supervisors/${id}`);
  },

  /**
   * 创建督导成员
   * @param {Object} supervisorData - 督导数据
   * @returns {Promise}
   */
  createSupervisor(supervisorData) {
    return api.post('/api/supervisors', supervisorData);
  },

  /**
   * 创建督导成员（含头像上传）
   * @param {FormData} formData - 包含督导数据和头像文件的表单数据
   * @returns {Promise}
   */
  createSupervisorWithAvatar(formData) {
    return api.post('/api/supervisors', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  /**
   * 更新督导成员
   * @param {number} id - 督导ID
   * @param {Object} supervisorData - 督导数据
   * @returns {Promise}
   */
  updateSupervisor(id, supervisorData) {
    return api.put(`/api/supervisors/${id}`, supervisorData);
  },

  /**
   * 更新督导成员（含头像上传）
   * @param {number} id - 督导ID
   * @param {FormData} formData - 包含督导数据和头像文件的表单数据
   * @returns {Promise}
   */
  updateSupervisorWithAvatar(id, formData) {
    return api.put(`/api/supervisors/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  /**
   * 删除督导成员
   * @param {number} id - 督导ID
   * @returns {Promise}
   */
  deleteSupervisor(id) {
    return api.delete(`/api/supervisors/${id}`);
  }
};

export default supervisorService; 