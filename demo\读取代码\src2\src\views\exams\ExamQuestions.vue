<template>
  <div class="exam-questions-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="title">考试题目管理</span>
          <div>
            <el-button @click="goBack">返回考试列表</el-button>
            <el-button type="primary" @click="openAddDialog">添加题目</el-button>
            <el-button type="success" @click="importQuestions">导入题库</el-button>
          </div>
        </div>
      </template>

      <div class="exam-info" v-if="examData">
        <el-descriptions title="考试信息" :column="3" border>
          <el-descriptions-item label="考试名称">{{ examData.title }}</el-descriptions-item>
          <el-descriptions-item label="考试时长">{{ examData.duration }}分钟</el-descriptions-item>
          <el-descriptions-item label="总分">{{ examData.total_score }}分</el-descriptions-item>
          <el-descriptions-item label="及格分数">{{ examData.pass_score }}分</el-descriptions-item>
          <el-descriptions-item label="考试状态">
            <el-tag :type="getExamStatusType(examData.status)">
              {{ getExamStatusText(examData.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(examData.created_at) }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 题目列表 -->
      <div v-loading="loading" class="question-list">
        <el-empty v-if="questions.length === 0" description="暂无考试题目，请添加" />
        
        <div v-else>
          <div class="question-stats">
            <div class="stat-item">
              <div class="stat-value">{{ questions.length }}</div>
              <div class="stat-label">总题数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ singleChoiceCount }}</div>
              <div class="stat-label">单选题</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ multipleChoiceCount }}</div>
              <div class="stat-label">多选题</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ trueFalseCount }}</div>
              <div class="stat-label">判断题</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ totalScore }}</div>
              <div class="stat-label">总分</div>
            </div>
          </div>

          <el-table :data="questions" border style="width: 100%">
            <el-table-column type="index" width="50" label="#" />
            <el-table-column label="题型" width="100">
              <template #default="scope">
                <el-tag :type="getQuestionTypeTag(scope.row.type)">
                  {{ getQuestionTypeText(scope.row.type) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="content" label="题目内容" show-overflow-tooltip />
            <el-table-column prop="score" label="分值" width="80" />
            <el-table-column label="操作" width="180" fixed="right">
              <template #default="scope">
                <el-button size="small" @click="viewQuestion(scope.row)">查看</el-button>
                <el-button size="small" type="primary" @click="editQuestion(scope.row)">编辑</el-button>
                <el-button size="small" type="danger" @click="deleteQuestion(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>

    <!-- 添加/编辑题目对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑题目' : '添加题目'"
      width="700px"
    >
      <el-form
        ref="questionFormRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="题目类型" prop="type">
          <el-select v-model="formData.type" placeholder="请选择题目类型">
            <el-option label="单选题" value="single" />
            <el-option label="多选题" value="multiple" />
            <el-option label="判断题" value="true_false" />
          </el-select>
        </el-form-item>

        <el-form-item label="题目内容" prop="content">
          <el-input
            v-model="formData.content"
            type="textarea"
            :rows="3"
            placeholder="请输入题目内容"
          />
        </el-form-item>

        <el-form-item label="分值" prop="score">
          <el-input-number v-model="formData.score" :min="1" :max="100" />
        </el-form-item>

        <!-- 选项 (单选题和多选题) -->
        <template v-if="formData.type === 'single' || formData.type === 'multiple'">
          <el-divider content-position="left">选项</el-divider>
          
          <div
            v-for="(option, index) in formData.options"
            :key="index"
            class="option-item"
          >
            <el-form-item
              :label="`选项 ${String.fromCharCode(65 + index)}`"
              :prop="`options.${index}.content`"
              :rules="{ required: true, message: '请输入选项内容', trigger: 'blur' }"
            >
              <div class="option-content">
                <el-input v-model="option.content" placeholder="请输入选项内容" />
                <el-checkbox
                  v-if="formData.type === 'multiple'"
                  v-model="option.is_correct"
                  label="正确答案"
                />
                <el-radio
                  v-else
                  v-model="formData.correct_option"
                  :label="index"
                  class="option-radio"
                >正确答案</el-radio>
                <el-button
                  type="danger"
                  icon="Delete"
                  circle
                  @click="removeOption(index)"
                  v-if="formData.options.length > 2"
                />
              </div>
            </el-form-item>
          </div>

          <div class="add-option">
            <el-button type="primary" plain @click="addOption" :disabled="formData.options.length >= 6">
              添加选项
            </el-button>
          </div>
        </template>

        <!-- 判断题答案 -->
        <template v-if="formData.type === 'true_false'">
          <el-form-item label="正确答案" prop="true_false_answer">
            <el-radio-group v-model="formData.true_false_answer">
              <el-radio :label="true">正确</el-radio>
              <el-radio :label="false">错误</el-radio>
            </el-radio-group>
          </el-form-item>
        </template>

        <el-form-item label="解析" prop="explanation">
          <el-input
            v-model="formData.explanation"
            type="textarea"
            :rows="2"
            placeholder="请输入题目解析（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 查看题目对话框 -->
    <el-dialog
      v-model="viewDialogVisible"
      title="题目详情"
      width="700px"
    >
      <div v-if="currentQuestion" class="question-detail">
        <div class="question-header">
          <el-tag :type="getQuestionTypeTag(currentQuestion.type)" size="large">
            {{ getQuestionTypeText(currentQuestion.type) }}
          </el-tag>
          <span class="question-score">{{ currentQuestion.score }}分</span>
        </div>

        <div class="question-content">{{ currentQuestion.content }}</div>

        <!-- 选项 -->
        <div v-if="currentQuestion.type !== 'true_false'" class="options-list">
          <div
            v-for="(option, index) in currentQuestion.options"
            :key="index"
            class="option-item"
            :class="{ 'correct-option': option.is_correct }"
          >
            <div class="option-label">{{ String.fromCharCode(65 + index) }}</div>
            <div class="option-content">{{ option.content }}</div>
            <div v-if="option.is_correct" class="correct-mark">
              <el-icon><Check /></el-icon>
            </div>
          </div>
        </div>

        <!-- 判断题答案 -->
        <div v-else class="true-false-answer">
          <div class="answer-label">正确答案：</div>
          <div class="answer-value">
            <el-tag :type="currentQuestion.true_false_answer ? 'success' : 'danger'">
              {{ currentQuestion.true_false_answer ? '正确' : '错误' }}
            </el-tag>
          </div>
        </div>

        <!-- 解析 -->
        <div v-if="currentQuestion.explanation" class="question-explanation">
          <div class="explanation-label">解析：</div>
          <div class="explanation-content">{{ currentQuestion.explanation }}</div>
        </div>
      </div>
    </el-dialog>

    <!-- 导入题库对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="导入题库"
      width="500px"
    >
      <el-upload
        class="upload-demo"
        drag
        action="http://127.0.0.1:3000/api/exams/questions/import"
        :headers="{ 'Content-Type': 'multipart/form-data' }"
        :data="{ exam_id: examId }"
        :on-success="handleImportSuccess"
        :on-error="handleImportError"
        :before-upload="beforeImportUpload"
        accept=".xlsx,.xls"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          将Excel文件拖到此处，或<em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            请上传Excel格式的题库文件，<el-button type="primary" link @click="downloadTemplate">下载模板</el-button>
          </div>
        </template>
      </el-upload>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, UploadFilled } from '@element-plus/icons-vue'
import axios from 'axios'

export default {
  name: 'ExamQuestions',
  components: {
    Check,
    UploadFilled
  },
  setup() {
    const route = useRoute()
    const router = useRouter()
    const examId = route.params.id
    
    // 基础数据
    const loading = ref(false)
    const examData = ref(null)
    const questions = ref([])
    const questionFormRef = ref(null)
    
    // 对话框
    const dialogVisible = ref(false)
    const viewDialogVisible = ref(false)
    const importDialogVisible = ref(false)
    const isEdit = ref(false)
    const currentQuestion = ref(null)
    
    // 表单数据
    const formData = reactive({
      id: '',
      type: 'single',
      content: '',
      score: 5,
      options: [
        { content: '', is_correct: false },
        { content: '', is_correct: false },
        { content: '', is_correct: false },
        { content: '', is_correct: false }
      ],
      correct_option: 0,
      true_false_answer: true,
      explanation: '',
      exam_id: examId
    })
    
    // 表单验证规则
    const formRules = {
      type: [
        { required: true, message: '请选择题目类型', trigger: 'change' }
      ],
      content: [
        { required: true, message: '请输入题目内容', trigger: 'blur' }
      ],
      score: [
        { required: true, message: '请输入分值', trigger: 'change' }
      ]
    }
    
    // 计算属性
    const singleChoiceCount = computed(() => {
      return questions.value.filter(q => q.type === 'single').length
    })
    
    const multipleChoiceCount = computed(() => {
      return questions.value.filter(q => q.type === 'multiple').length
    })
    
    const trueFalseCount = computed(() => {
      return questions.value.filter(q => q.type === 'true_false').length
    })
    
    const totalScore = computed(() => {
      return questions.value.reduce((sum, q) => sum + q.score, 0)
    })
    
    // 生命周期钩子
    onMounted(() => {
      fetchExamData()
      fetchQuestions()
    })
    
    // 获取考试信息
    const fetchExamData = async () => {
      try {
        const response = await axios.get(`http://127.0.0.1:3000/api/exams/${examId}`)
        examData.value = response.data.data
      } catch (error) {
        console.error('获取考试信息失败:', error)
        ElMessage.error('获取考试信息失败')
      }
    }
    
    // 获取题目列表
    const fetchQuestions = async () => {
      loading.value = true
      try {
        const response = await axios.get(`http://127.0.0.1:3000/api/exams/${examId}/questions`)
        questions.value = response.data.data
      } catch (error) {
        console.error('获取题目列表失败:', error)
        ElMessage.error('获取题目列表失败')
      } finally {
        loading.value = false
      }
    }
    
    // 获取考试状态文本
    const getExamStatusText = (status) => {
      const statusMap = {
        'draft': '草稿',
        'published': '已发布',
        'in_progress': '进行中',
        'completed': '已结束'
      }
      return statusMap[status] || '未知状态'
    }
    
    // 获取考试状态类型
    const getExamStatusType = (status) => {
      const typeMap = {
        'draft': 'info',
        'published': 'success',
        'in_progress': 'warning',
        'completed': 'danger'
      }
      return typeMap[status] || 'info'
    }
    
    // 获取题目类型文本
    const getQuestionTypeText = (type) => {
      const typeMap = {
        'single': '单选题',
        'multiple': '多选题',
        'true_false': '判断题'
      }
      return typeMap[type] || '未知类型'
    }
    
    // 获取题目类型标签
    const getQuestionTypeTag = (type) => {
      const tagMap = {
        'single': 'primary',
        'multiple': 'success',
        'true_false': 'warning'
      }
      return tagMap[type] || 'info'
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
    }
    
    // 返回考试列表
    const goBack = () => {
      router.push('/exams/list')
    }
    
    // 打开添加对话框
    const openAddDialog = () => {
      isEdit.value = false
      resetFormData()
      dialogVisible.value = true
    }
    
    // 编辑题目
    const editQuestion = (row) => {
      isEdit.value = true
      currentQuestion.value = row
      
      // 填充表单数据
      Object.assign(formData, {
        id: row.id,
        type: row.type,
        content: row.content,
        score: row.score,
        explanation: row.explanation || '',
        exam_id: examId
      })
      
      // 处理选项和答案
      if (row.type === 'true_false') {
        formData.true_false_answer = row.true_false_answer
      } else {
        formData.options = [...row.options]
        if (row.type === 'single') {
          formData.correct_option = row.options.findIndex(opt => opt.is_correct)
        }
      }
      
      dialogVisible.value = true
    }
    
    // 查看题目
    const viewQuestion = (row) => {
      currentQuestion.value = row
      viewDialogVisible.value = true
    }
    
    // 删除题目
    const deleteQuestion = (row) => {
      ElMessageBox.confirm(
        '确定要删除该题目吗？',
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(async () => {
          try {
            await axios.delete(`http://127.0.0.1:3000/api/exams/questions/${row.id}`)
            ElMessage.success('删除成功')
            fetchQuestions()
          } catch (error) {
            console.error('删除失败:', error)
            ElMessage.error('删除失败')
          }
        })
        .catch(() => {
          ElMessage.info('已取消删除')
        })
    }
    
    // 添加选项
    const addOption = () => {
      if (formData.options.length < 6) {
        formData.options.push({ content: '', is_correct: false })
      }
    }
    
    // 移除选项
    const removeOption = (index) => {
      if (formData.options.length > 2) {
        formData.options.splice(index, 1)
        
        // 如果删除的是正确答案，重置正确答案
        if (formData.type === 'single' && formData.correct_option === index) {
          formData.correct_option = 0
        } else if (formData.type === 'single' && formData.correct_option > index) {
          formData.correct_option--
        }
      }
    }
    
    // 重置表单数据
    const resetFormData = () => {
      Object.assign(formData, {
        id: '',
        type: 'single',
        content: '',
        score: 5,
        options: [
          { content: '', is_correct: false },
          { content: '', is_correct: false },
          { content: '', is_correct: false },
          { content: '', is_correct: false }
        ],
        correct_option: 0,
        true_false_answer: true,
        explanation: '',
        exam_id: examId
      })
      
      if (questionFormRef.value) {
        questionFormRef.value.resetFields()
      }
    }
    
    // 提交表单
    const submitForm = async () => {
      if (!questionFormRef.value) return
      
      await questionFormRef.value.validate(async (valid) => {
        if (valid) {
          // 处理单选题答案
          if (formData.type === 'single') {
            formData.options.forEach((opt, index) => {
              opt.is_correct = index === formData.correct_option
            })
          }
          
          try {
            if (isEdit.value) {
              // 编辑模式
              await axios.put(`http://127.0.0.1:3000/api/exams/questions/${formData.id}`, formData)
              ElMessage.success('题目更新成功')
            } else {
              // 添加模式
              await axios.post('http://127.0.0.1:3000/api/exams/questions', formData)
              ElMessage.success('题目添加成功')
            }
            
            dialogVisible.value = false
            fetchQuestions()
          } catch (error) {
            console.error('操作失败:', error)
            ElMessage.error('操作失败')
          }
        } else {
          return false
        }
      })
    }
    
    // 导入题库
    const importQuestions = () => {
      importDialogVisible.value = true
    }
    
    // 下载模板
    const downloadTemplate = () => {
      window.open('http://127.0.0.1:3000/api/exams/questions/template', '_blank')
    }
    
    // 上传前验证
    const beforeImportUpload = (file) => {
      const isExcel = file.type === 'application/vnd.ms-excel' || 
                      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isExcel) {
        ElMessage.error('只能上传Excel文件!')
      }
      if (!isLt2M) {
        ElMessage.error('文件大小不能超过2MB!')
      }
      
      return isExcel && isLt2M
    }
    
    // 导入成功
    const handleImportSuccess = (response) => {
      ElMessage.success(`成功导入${response.data.count}道题目`)
      importDialogVisible.value = false
      fetchQuestions()
    }
    
    // 导入失败
    const handleImportError = (error) => {
      console.error('导入失败:', error)
      ElMessage.error('导入失败，请检查文件格式是否正确')
    }
    
    return {
      loading,
      examData,
      questions,
      dialogVisible,
      viewDialogVisible,
      importDialogVisible,
      isEdit,
      formData,
      formRules,
      questionFormRef,
      currentQuestion,
      examId,
      singleChoiceCount,
      multipleChoiceCount,
      trueFalseCount,
      totalScore,
      getExamStatusText,
      getExamStatusType,
      getQuestionTypeText,
      getQuestionTypeTag,
      formatDate,
      goBack,
      openAddDialog,
      editQuestion,
      viewQuestion,
      deleteQuestion,
      addOption,
      removeOption,
      submitForm,
      importQuestions,
      downloadTemplate,
      beforeImportUpload,
      handleImportSuccess,
      handleImportError
    }
  }
}
</script>

<style scoped>
.exam-questions-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.exam-info {
  margin-bottom: 20px;
}

.question-list {
  margin-top: 20px;
}

.question-stats {
  display: flex;
  margin-bottom: 20px;
  background-color: #f7f7f7;
  padding: 15px;
  border-radius: 4px;
}

.stat-item {
  flex: 1;
  text-align: center;
  border-right: 1px solid #eee;
}

.stat-item:last-child {
  border-right: none;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.option-item {
  margin-bottom: 10px;
}

.option-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.option-radio {
  margin-left: 10px;
}

.add-option {
  display: flex;
  justify-content: center;
  margin-top: 10px;
  margin-bottom: 20px;
}

.question-detail {
  padding: 10px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.question-score {
  font-size: 16px;
  font-weight: bold;
  color: #f56c6c;
}

.question-content {
  font-size: 16px;
  margin-bottom: 20px;
  padding: 10px;
  background-color: #f7f7f7;
  border-radius: 4px;
}

.options-list {
  margin-top: 15px;
}

.options-list .option-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #eee;
}

.options-list .option-item:last-child {
  border-bottom: none;
}

.options-list .option-item.correct-option {
  background-color: #f0f9eb;
}

.options-list .option-label {
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: #f2f6fc;
  margin-right: 10px;
  font-weight: bold;
}

.options-list .option-content {
  flex: 1;
}

.options-list .correct-mark {
  color: #67c23a;
  margin-left: 10px;
}

.true-false-answer {
  display: flex;
  align-items: center;
  margin-top: 15px;
}

.answer-label {
  font-weight: bold;
  margin-right: 10px;
}

.question-explanation {
  margin-top: 20px;
  padding: 10px;
  background-color: #f7f7f7;
  border-radius: 4px;
}

.explanation-label {
  font-weight: bold;
  margin-bottom: 5px;
}

.explanation-content {
  color: #606266;
  white-space: pre-line;
}
</style> 