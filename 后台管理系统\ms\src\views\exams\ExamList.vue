<template>
  <div class="exam-list-container">
    <el-card class="filter-card">
      <div class="filter-container">
        <el-form :model="filterForm" inline>
          <el-form-item label="考试标题">
            <el-input v-model="filterForm.title" placeholder="请输入考试标题" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>考试列表</span>
          <div>
            <el-button type="primary" @click="handleAddExam" v-if="canManageExams">新增考试</el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="examList" stripe border style="width: 100%" v-loading="loading">
        <el-table-column type="index" width="50" />
        <el-table-column prop="title" label="考试标题" min-width="200" />
        <el-table-column prop="description" label="考试描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="duration" label="考试时长(分钟)" width="120" />
        <el-table-column prop="pass_score" label="及格分数" width="100" />
        <el-table-column prop="total_score" label="总分" width="80" />
        <!-- Add remaining attempts column for students -->
        <el-table-column label="考试机会" width="120" v-if="isStudent">
          <template #default="scope">
            <el-tag :type="remainingAttempts[scope.row.id] > 0 ? 'success' : 'danger'" effect="plain">
              已用 {{ 2 - (remainingAttempts[scope.row.id]) }}/2 次
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="160">
          <template #default="scope">
        
            {{ formatDate(scope.row.created_at, 'YYYY-MM-DD HH:mm') }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="320" fixed="right">
          <template #default="scope">
          
            <template v-if="canManageExams">
           
              <el-button type="primary" size="small" @click="handleManageQuestions(scope.row)">试题管理</el-button>
              <el-button type="success" size="small" @click="handleViewResults(scope.row)">成绩查看</el-button>
              <el-button type="warning" size="small" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
            </template>
            <template v-else>
              <el-button 
                type="primary" 
                size="small" 
                @click="handleTakeExam(scope.row)" 
                :disabled="remainingAttempts[scope.row.id] === 0"
                :loading="loading && currentLoadingExam === scope.row.id"
              >
                {{ remainingAttempts[scope.row.id] === 0 ? '已无机会' : '参加考试' }}
              </el-button>
            
            </template>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑考试对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
    >
      <el-form :model="examForm" :rules="examRules" ref="examFormRef" label-width="100px">
        <el-form-item label="考试标题" prop="title">
          <el-input v-model="examForm.title" placeholder="请输入考试标题"></el-input>
        </el-form-item>
        
        <el-form-item label="考试描述" prop="description">
          <el-input type="textarea" v-model="examForm.description" placeholder="请输入考试描述" rows="3"></el-input>
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="考试时长" prop="duration">
              <el-input-number v-model="examForm.duration" :min="1" :max="240" placeholder="分钟" style="width: 100%"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="及格分数" prop="pass_score">
              <el-input-number v-model="examForm.pass_score" :min="1" :max="examForm.total_score" style="width: 100%"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="总分" prop="total_score">
              <el-input-number v-model="examForm.total_score" :min="1" :max="1000" style="width: 100%"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 试题管理对话框 -->
    <el-dialog
      title="试题管理"
      v-model="questionDialogVisible"
      width="850px"
      :fullscreen="false"
      :close-on-click-modal="false"
    >
      <div v-if="currentExam" class="question-dialog-content">
        <div class="question-header">
          <h3>{{ currentExam.title }}</h3>
          <div class="question-actions">
            <el-button type="primary" @click="handleAddQuestion">新增试题</el-button>
            <el-button type="success" @click="handleImportQuestions">批量导入</el-button>
          </div>
        </div>
        
        <div class="question-table-wrapper">
          <el-table 
            :data="questionList" 
            stripe 
            border 
            style="width: 100%; margin-top: 15px;" 
            v-loading="questionLoading"
            :max-height="550"
            :show-header="true"
          >
            <el-table-column type="index" width="50" />
            <el-table-column label="题目内容" min-width="300">
              <template #default="scope">
                <div v-html="formatQuestionContent(scope.row.question)"></div>
              </template>
            </el-table-column>
            <el-table-column prop="question_type" label="题型" width="100" />
            <el-table-column prop="score" label="分值" width="80" />
            <el-table-column label="操作" width="150" fixed="right">
              <template #default="scope">
                <el-button type="warning" size="small" @click="handleEditQuestion(scope.row)">编辑</el-button>
                <el-button type="danger" size="small" @click="handleDeleteQuestion(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        
        <!-- 移除分页，使用滚动条 -->
      </div>
    </el-dialog>
    
    <!-- 试题导入对话框 -->
    <el-dialog
      title="批量导入试题"
      v-model="importDialogVisible"
      width="500px"
    >
      <div class="import-dialog-content">
        <el-alert
          title="请上传Word格式的试题模板文件"
          type="info"
          :closable="false"
          show-icon
          style="margin-bottom: 20px;"
        />
        
        <el-upload
          class="upload-demo"
          drag
          action="#"
          :http-request="handleFileUpload"
          :before-upload="beforeUpload"
          :limit="1"
          :file-list="fileList"
          :auto-upload="false"
          :on-change="handleFileChange"
          accept=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            拖拽文件到此处或 <em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              仅支持 .doc/.docx 格式文件
            </div>
          </template>
        </el-upload>
        
        <div class="template-download">
          <span>没有模板？</span>
          <el-button type="primary" link @click="downloadTemplate">下载试题模板</el-button>
        </div>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUpload" :loading="uploadLoading">上传</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 试题编辑对话框 -->
    <el-dialog
      :title="questionDialogTitle"
      v-model="questionEditDialogVisible"
      width="700px"
    >
      <el-form :model="questionForm" :rules="questionRules" ref="questionFormRef" label-width="100px">
        <el-form-item label="题目类型" prop="question_type">
          <el-select v-model="questionForm.question_type" style="width: 100%">
            <el-option label="单选题" value="单选题"></el-option>
            <el-option label="多选题" value="多选题"></el-option>
            <el-option label="判断题" value="判断题"></el-option>
            <el-option label="简答题" value="简答题"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="分值" prop="score">
          <el-input-number v-model="questionForm.score" :min="1" :max="100" style="width: 100%"></el-input-number>
        </el-form-item>
        
        <el-form-item label="题目内容" prop="question">
          <el-input type="textarea" v-model="questionForm.question" placeholder="请输入题目内容" rows="4"></el-input>
        </el-form-item>
        
        <template v-if="questionForm.question_type === '单选题' || questionForm.question_type === '多选题'">
          <div class="options-header">
            <h4>选项</h4>
            <el-button type="primary" size="small" @click="addOption">添加选项</el-button>
          </div>
          
          <el-form-item 
            v-for="(option, index) in questionForm.options"
            :key="index"
            :label="'选项 ' + String.fromCharCode(65 + index)"
          >
            <div class="option-item">
              <el-input v-model="option.text" placeholder="请输入选项内容"></el-input>
              <el-checkbox v-model="option.isCorrect">正确答案</el-checkbox>
              <el-button type="danger" icon="Delete" circle size="small" @click="removeOption(index)"></el-button>
            </div>
          </el-form-item>
        </template>
        
        <template v-else-if="questionForm.question_type === '判断题'">
          <el-form-item label="正确答案" prop="correct_answer">
            <el-radio-group v-model="questionForm.correct_answer">
              <el-radio label="正确">正确</el-radio>
              <el-radio label="错误">错误</el-radio>
            </el-radio-group>
          </el-form-item>
        </template>
        
        <template v-else>
          <el-form-item label="参考答案" prop="correct_answer">
            <el-input type="textarea" v-model="questionForm.correct_answer" placeholder="请输入参考答案" rows="3"></el-input>
          </el-form-item>
        </template>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="questionEditDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitQuestionForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 成绩查看对话框 -->
    <el-dialog
      title="考试成绩"
      v-model="resultsDialogVisible"
      width="800px"
    >
      <div v-if="currentExam" class="results-dialog-content">
        <h3>{{ currentExam.title }}</h3>
        
        <el-card class="statistics-card">
          <div class="statistics-items">
            <div class="statistics-item">
              <div class="statistics-label">总人数</div>
              <div class="statistics-value">{{ examStatistics.total_students || 0 }}</div>
            </div>
            <div class="statistics-item">
              <div class="statistics-label">及格人数</div>
              <div class="statistics-value">{{ examStatistics.passed_students || 0 }}</div>
            </div>
            <div class="statistics-item">
              <div class="statistics-label">及格率</div>
              <div class="statistics-value">{{ examStatistics.pass_rate || 0 }}%</div>
            </div>
            <div class="statistics-item">
              <div class="statistics-label">平均分</div>
              <div class="statistics-value">{{ examStatistics.average_score || 0 }}</div>
            </div>
          </div>
        </el-card>
        
        <el-table :data="resultsList" stripe border style="width: 100%; margin-top: 15px;" v-loading="resultsLoading">
          <el-table-column type="index" width="50" />
          <el-table-column prop="student_name" label="学生姓名" />
          <el-table-column prop="score" label="得分" width="100" sortable>
            <template #default="scope">
              <span :class="scope.row.score >= currentExam.pass_score ? 'pass-score' : 'fail-score'">
                {{ scope.row.score }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="考试时间" width="160">
            <template #default="scope">
              {{ formatDate(scope.row.exam_date, 'YYYY-MM-DD HH:mm') }}
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.score >= currentExam.pass_score ? 'success' : 'danger'">
                {{ scope.row.score >= currentExam.pass_score ? '及格' : '不及格' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="scope">
              <el-button type="primary" size="small" @click="handleViewDetail(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import examService from '@/services/examService'
import { formatDate, formatRelativeTime } from '@/utils/dateFormat'

const store = useStore()
const router = useRouter()
const loading = ref(true)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const dialogVisible = ref(false)
const dialogTitle = ref('新增考试')
const examFormRef = ref(null)
const currentLoadingExam = ref(null) // 当前正在加载的考试ID
let userInfo = localStorage.getItem("userInfo") ? JSON.parse(localStorage.getItem("userInfo")): ''
// 用户角色
const isAdmin = userInfo?.role == 'admin'
const isTeacher = userInfo?.role == 'teacher'
const isStudent = computed(() => store.getters.isStudent)
const canManageExams = userInfo?.role == 'admin'

// 试题管理相关
const questionDialogVisible = ref(false)
const questionEditDialogVisible = ref(false)
const questionDialogTitle = ref('新增试题')
const questionFormRef = ref(null)
const currentExam = ref(null)
const questionList = ref([])
const questionLoading = ref(false)
const questionCurrentPage = ref(1)
const questionTotal = ref(0)

// 导入试题相关
const importDialogVisible = ref(false)
const fileList = ref([])
const uploadLoading = ref(false)

// 成绩查看相关
const resultsDialogVisible = ref(false)
const resultsList = ref([])
const resultsLoading = ref(false)
const examStatistics = reactive({
  total_students: 0,
  passed_students: 0,
  pass_rate: 0,
  average_score: 0
})

// Add remaining attempts tracking
const remainingAttempts = ref({});
const attemptedExams = ref({}); // 记录已参加过的考试

// 检查学生是否参加过某个考试
const hasAttemptedExam = (examId) => {
  if (!examId) return false;
  return attemptedExams.value[examId] === true;
};

// 过滤条件
const filterForm = reactive({
  title: ''
})

// 考试表单
const examForm = reactive({
  id: null,
  title: '',
  description: '',
  duration: 60,
  pass_score: 60,
  total_score: 100
})

// 验证规则
const examRules = {
  title: [
    { required: true, message: '请输入考试标题', trigger: 'blur' }
  ],
  duration: [
    { required: true, message: '请输入考试时长', trigger: 'blur' },
    { type: 'number', min: 1, message: '考试时长必须大于0', trigger: 'blur' }
  ],
  pass_score: [
    { required: true, message: '请输入及格分数', trigger: 'blur' },
    { type: 'number', min: 1, message: '及格分数必须大于0', trigger: 'blur' }
  ],
  total_score: [
    { required: true, message: '请输入总分', trigger: 'blur' },
    { type: 'number', min: 1, message: '总分必须大于0', trigger: 'blur' }
  ]
}

// 试题表单
const questionForm = reactive({
  id: null,
  question: '',
  question_type: '单选题',
  correct_answer: '',
  score: 5,
  options: [
    { text: '', isCorrect: false },
    { text: '', isCorrect: false },
    { text: '', isCorrect: false },
    { text: '', isCorrect: false }
  ]
})

// 试题验证规则
const questionRules = {
  question: [
    { required: true, message: '请输入题目内容', trigger: 'blur' }
  ],
  question_type: [
    { required: true, message: '请选择题目类型', trigger: 'change' }
  ],
  score: [
    { required: true, message: '请输入分值', trigger: 'blur' },
    { type: 'number', min: 1, message: '分值必须大于0', trigger: 'blur' }
  ]
}

// 模拟考试数据
const examList = ref([])

// 模拟试题数据
const mockQuestions = [
  {
    id: 1,
    question: '公司的上班时间是几点到几点？',
    options: [
      { text: 'A. 8:30-17:30', isCorrect: true },
      { text: 'B. 9:00-18:00', isCorrect: false },
      { text: 'C. 9:30-18:30', isCorrect: false },
      { text: 'D. 8:00-17:00', isCorrect: false }
    ],
    correct_answer: 'A',
    question_type: '单选题',
    score: 5,
    exam_id: 1
  },
  {
    id: 2,
    question: '公司允许员工在工作时间做以下哪些事情？（多选）',
    options: [
      { text: 'A. 喝水', isCorrect: true },
      { text: 'B. 短暂休息', isCorrect: true },
      { text: 'C. 玩游戏', isCorrect: false },
      { text: 'D. 睡觉', isCorrect: false }
    ],
    correct_answer: 'AB',
    question_type: '多选题',
    score: 10,
    exam_id: 1
  },
  {
    id: 3,
    question: '公司规定必须遵守考勤制度。',
    options: [],
    correct_answer: '正确',
    question_type: '判断题',
    score: 5,
    exam_id: 1
  },
  {
    id: 4,
    question: '请简述公司的请假流程。',
    options: [],
    correct_answer: '1. 提前向部门负责人口头说明\n2. 在OA系统填写请假申请\n3. 等待审批通过\n4. 销假时提交相关证明',
    question_type: '简答题',
    score: 15,
    exam_id: 1
  }
]

// 模拟成绩数据
const mockResults = [
  {
    id: 1,
    student_id: 1,
    student_name: '张三',
    exam_id: 1,
    score: 85,
    exam_date: '2023-01-20 10:30:00'
  },
  {
    id: 2,
    student_id: 2,
    student_name: '李四',
    exam_id: 1,
    score: 75,
    exam_date: '2023-01-20 10:45:00'
  },
  {
    id: 3,
    student_id: 3,
    student_name: '王五',
    exam_id: 1,
    score: 55,
    exam_date: '2023-01-20 11:00:00'
  },
  {
    id: 4,
    student_id: 4,
    student_name: '赵六',
    exam_id: 1,
    score: 92,
    exam_date: '2023-01-20 10:15:00'
  }
]

// 设置总数
total.value = examList.length

// 获取所有考试
const fetchExams = async () => {
  loading.value = true;
  try {
    const response = await examService.getExams({
      page: currentPage.value,
      limit: pageSize.value,
      title: filterForm.title
    });
    
    examList.value = response.data.data;
    total.value = response.data.total || examList.value.length;
    
    // If student, check attempts for each exam
    if (isStudent.value) {
      await checkRemainingAttempts();
    }
    
    loading.value = false;
  } catch (error) {
    console.error('获取考试列表失败', error);
    ElMessage.error('获取考试列表失败');
    loading.value = false;
  }
};

// Check remaining attempts for each exam
const checkRemainingAttempts = async () => {
  const studentId = localStorage.getItem('studentId');
  if (!studentId) {
    console.error('未找到有效的学生ID');
    ElMessage.warning('未找到有效的学生ID，请重新登录后再试');
    return;
  }
  
  try {
    console.log('正在获取所有考试的尝试次数...学生ID:', studentId);
    // 创建一个Promise数组，同时请求所有考试的尝试次数
    const attemptPromises = examList.value.map(async (exam) => {
      try {
        console.log(`正在查询考试 ${exam.id} (${exam.title}) 的尝试记录...`);
        const response = await examService.getExamResults(exam.id, { student_id: studentId });
        console.log(`考试 ${exam.id} 返回数据:`, response.data);
        
        // 确保我们有结果数据
        if (response.data && response.data.data) {
          const results = response.data.data.results || [];
          const attempts = results.length;
          const remaining = Math.max(0, 2 - attempts);
          
          console.log(`考试 ${exam.id} (${exam.title}): 已尝试 ${attempts} 次，剩余 ${remaining} 次`);
          remainingAttempts.value[exam.id] = remaining;
          attemptedExams.value[exam.id] = attempts > 0; // 只有实际参加过才标记为true
          
          // 记录每次尝试的详情，便于调试
          if (results.length > 0) {
            results.forEach((result, index) => {
              console.log(`  尝试 ${index + 1}: 得分 ${result.score}，时间 ${result.exam_date}`);
            });
          }
        } else {
          console.warn(`考试 ${exam.id} 未返回有效数据，设置默认剩余次数为2`);
          remainingAttempts.value[exam.id] = 2;
          attemptedExams.value[exam.id] = false; // 没有数据，标记为未参加
        }
      } catch (err) {
        console.error(`获取考试 ${exam.id} 尝试次数失败:`, err);
        remainingAttempts.value[exam.id] = 2;
        attemptedExams.value[exam.id] = false; // 出错，标记为未参加
      }
    });
    
    // 等待所有请求完成
    await Promise.all(attemptPromises);
    console.log('所有考试尝试次数获取完成:', remainingAttempts.value);
    console.log('已参加过的考试:', attemptedExams.value);
  } catch (error) {
    console.error('获取考试尝试次数失败:', error);
    // 设置默认值
    examList.value.forEach(exam => {
      remainingAttempts.value[exam.id] = 2;
      attemptedExams.value[exam.id] = false; // 出错，标记为未参加
    });
  }
};

// 参加考试
const handleTakeExam = (row) => {
  // 设置当前正在加载的考试ID
  currentLoadingExam.value = row.id;
  
  // 确保已经加载了尝试次数
  if (!remainingAttempts.value[row.id] && remainingAttempts.value[row.id] !== 0) {
    // 如果还没加载尝试次数信息，先加载
    const teacherId = localStorage.getItem('teacherId');
    if (!teacherId) {
      ElMessage.warning('未找到有效的教师ID，请重新登录');
      currentLoadingExam.value = null;
      return;
    }
    
    ElMessage.info('正在获取考试尝试信息，请稍候...');
    
    // 立即获取该考试的尝试次数
    examService.getExamResults(row.id, { teacher_id: teacherId })
      .then(response => {
        console.log(`考试 ${row.id} 尝试信息:`, response.data);
        
        if (response.data && response.data.data) {
          const results = response.data.data.results || [];
          const attempts = results.length;
          const remaining = Math.max(0, 2 - attempts);
          
          console.log(`考试 ${row.id} (${row.title}): 已尝试 ${attempts} 次，剩余 ${remaining} 次`);
          remainingAttempts.value[row.id] = remaining;
          attemptedExams.value[row.id] = attempts > 0;
          
          // 获取到尝试次数后，继续参加考试逻辑
          currentLoadingExam.value = null;
          continueToTakeExam(row);
        } else {
          console.warn(`考试 ${row.id} 未返回有效数据，设置默认剩余次数为2`);
          remainingAttempts.value[row.id] = 2;
          attemptedExams.value[row.id] = false;
          currentLoadingExam.value = null;
          continueToTakeExam(row);
        }
      })
      .catch(error => {
        console.error('获取考试尝试次数失败', error);
        ElMessage.error('获取考试尝试次数失败，请刷新页面重试');
        currentLoadingExam.value = null;
      });
    return;
  }
  
  currentLoadingExam.value = null;
  continueToTakeExam(row);
};

// 继续参加考试流程
const continueToTakeExam = (row) => {
  // Check if student still has attempts left
  if (remainingAttempts.value[row.id] === 0) {
    ElMessage.warning('您已达到该考试的最大尝试次数（2次）');
    return;
  }
  
  // Confirm before taking the exam
  ElMessageBox.confirm(
    `您总共有2次尝试机会，已使用 ${2 - remainingAttempts.value[row.id]} 次，还剩 ${remainingAttempts.value[row.id]} 次机会。确定要参加考试吗？`,
    '参加考试',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 记录考试ID和剩余次数到localStorage，以便考试页面使用
    localStorage.setItem('currentExamId', row.id);
    localStorage.setItem('currentExamTitle', row.title);
    localStorage.setItem('currentExamRemainingAttempts', remainingAttempts.value[row.id]);
    
    // 跳转到考试页面
    router.push(`/exams/take/${row.id}`);
  }).catch(() => {
    console.log('用户取消参加考试');
  });
};

// 查看我的成绩
const handleViewMyResult = async (row) => {
  try {
    const studentId = localStorage.getItem('studentId');
    if (!studentId) {
      ElMessage.error('未找到有效的学生ID，请重新登录');
      return;
    }
    
    console.log('查询成绩 - 学生ID:', studentId, '考试ID:', row.id);
    
    const response = await examService.getExamResults(row.id, { student_id: studentId });
    console.log('查询成绩 - 返回数据:', response.data);
    
    // 检查是否有考试记录
    if (response.data.data.results && response.data.data.results.length > 0) {
      // Get all attempts sorted by score (highest first)
      const myResults = response.data.data.results
        .filter(r => r.student_id === parseInt(studentId))
        .sort((a, b) => b.score - a.score);
      
      console.log('查询成绩 - 过滤后的结果:', myResults);
      
      if (myResults.length > 0) {
        // Format results to show all attempts
        const attemptsList = myResults.map((result, index) => {
          return `<div class="result-item ${result.score >= row.pass_score ? 'pass' : 'fail'}">
            <h4>尝试 ${index + 1}</h4>
            <p><strong>得分:</strong> ${result.score} / ${row.total_score}</p>
            <p><strong>考试时间:</strong> ${formatDate(result.exam_date, 'YYYY-MM-DD HH:mm:ss')}</p>
            <p><strong>状态:</strong> ${result.score >= row.pass_score ? '通过' : '未通过'}</p>
          </div>`;
        }).join('<hr>');
        
        ElMessageBox.alert(
          `<div class="results-container">
            <h3>您的考试成绩</h3>
            <div class="results-list">${attemptsList}</div>
            <div class="attempts-info">
              <p>总尝试次数: ${myResults.length}/2</p>
              <p>剩余次数: ${Math.max(0, 2 - myResults.length)}</p>
              <p class="view-all-results"><a href="#/exams/my-results">查看我的所有考试成绩 &raquo;</a></p>
            </div>
          </div>`,
          '考试成绩',
          {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '确定',
            customClass: 'result-dialog'
          }
        );
        return;
      }
    }
    
    ElMessage.info('您暂无该考试的成绩记录');
  } catch (error) {
    console.error('获取成绩失败', error);
    ElMessage.error('获取成绩失败: ' + (error.response?.data?.message || error.message));
  }
};

onMounted(() => {
  fetchExams()
})

// 重置过滤条件
const resetFilter = () => {
  filterForm.title = ''
  handleSearch()
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  fetchExams()
}

// 新增考试
const handleAddExam = () => {
  dialogTitle.value = '新增考试'
  dialogVisible.value = true
  // 重置表单
  examForm.id = null
  examForm.title = ''
  examForm.description = ''
  examForm.duration = 60
  examForm.pass_score = 60
  examForm.total_score = 100
}

// 编辑考试
const handleEdit = (row) => {
  dialogTitle.value = '编辑考试'
  dialogVisible.value = true
  // 填充表单数据
  Object.keys(examForm).forEach(key => {
    examForm[key] = row[key]
  })
}

// 删除考试
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除考试 ${row.title} 吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await examService.deleteExam(row.id)
      ElMessage.success(`考试 ${row.title} 已删除`)
      fetchExams() // 重新加载列表
    } catch (error) {
      console.error('删除考试失败', error)
      ElMessage.error('删除考试失败，请重试')
    }
  }).catch(() => {})
}

// 提交表单
const submitForm = async () => {
  if (!examFormRef.value) return
  
  await examFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (examForm.id) {
          // 编辑模式
          await examService.updateExam(examForm.id, examForm)
          ElMessage.success(`考试 ${examForm.title} 信息已更新`)
        } else {
          // 新增模式
          await examService.createExam(examForm)
          ElMessage.success(`考试 ${examForm.title} 添加成功`)
        }
        dialogVisible.value = false
        fetchExams() // 重新加载列表
      } catch (error) {
        console.error('保存考试失败', error)
        ElMessage.error('保存考试失败，请重试')
      }
    } else {
      return false
    }
  })
}

// 试题管理
const handleManageQuestions = (row) => {
  currentExam.value = row
  questionDialogVisible.value = true
  // 设置为第一页（即使我们不再使用分页）
  questionCurrentPage.value = 1
  
  // 加载试题数据
  loadQuestions(row.id)
  
  // 确保对话框完全打开后调整表格高度
  setTimeout(() => {
    const questionTable = document.querySelector('.question-table-wrapper .el-table')
    if (questionTable) {
      questionTable.style.height = '550px'
    }
  }, 100)
}

// 加载试题数据
const loadQuestions = async (examId) => {
  questionLoading.value = true
  
  try {
    // 不使用分页参数，一次性获取所有试题
    const response = await examService.getExamQuestions(examId)
    
    // 检查返回数据结构
    console.log('加载试题数据返回:', response.data)
    
    // 确保获取完整的数据列表
    if (response.data && response.data.data) {
      questionList.value = response.data.data
      // 依然保存总数，用于展示
      questionTotal.value = response.data.count || questionList.value.length
      console.log(`加载了 ${questionList.value.length} 道试题`)
    } else {
      questionList.value = []
      questionTotal.value = 0
      console.warn('未找到试题数据')
    }
    
    questionLoading.value = false
  } catch (error) {
    console.error('获取试题失败', error)
    ElMessage.error('获取试题失败')
    questionLoading.value = false
  }
}

// 新增试题
const handleAddQuestion = () => {
  questionDialogTitle.value = '新增试题'
  questionEditDialogVisible.value = true
  // 重置表单
  questionForm.id = null
  questionForm.question = ''
  questionForm.question_type = '单选题'
  questionForm.correct_answer = ''
  questionForm.score = 5
  questionForm.options = [
    { text: '', isCorrect: false },
    { text: '', isCorrect: false },
    { text: '', isCorrect: false },
    { text: '', isCorrect: false }
  ]
}

// 编辑试题
const handleEditQuestion = (row) => {
  questionDialogTitle.value = '编辑试题'
  questionEditDialogVisible.value = true
  
  // 填充表单数据
  questionForm.id = row.id
  questionForm.question = row.question
  questionForm.question_type = row.question_type
  questionForm.correct_answer = row.correct_answer
  questionForm.score = row.score
  
  // 根据题型处理选项
  if (row.question_type === '单选题' || row.question_type === '多选题') {
    questionForm.options = row.options ? [...row.options] : []
  } else if (row.question_type === '判断题') {
    questionForm.correct_answer = row.correct_answer
  }
}

// 删除试题
const handleDeleteQuestion = (row) => {
  ElMessageBox.confirm(`确定要删除该试题吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await examService.deleteQuestion(row.id)
      ElMessage.success('试题已删除')
      // 重新加载试题列表
      loadQuestions(currentExam.value.id)
    } catch (error) {
      console.error('删除试题失败', error)
      ElMessage.error('删除试题失败，请重试')
    }
  }).catch(() => {})
}

// 批量导入试题
const handleImportQuestions = () => {
  importDialogVisible.value = true
  fileList.value = []
}

// 文件上传前检查
const beforeUpload = (file) => {
  const isWordDoc = 
    file.type === 'application/msword' || 
    file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  
  if (!isWordDoc) {
    ElMessage.error('请上传Word格式的文件!')
    return false
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10
  
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB!')
    return false
  }
  
  return true
}

// 处理文件选择
const handleFileChange = (file, uploadFileList) => {
  // 记录最新选择的文件
  console.log('文件选择变化:', file, uploadFileList)
  fileList.value = uploadFileList
}

// 处理文件上传
const handleFileUpload = (options) => {
  const { file } = options
  // 这个函数只在auto-upload=true时调用
  // 我们设置为false，所以这里只需返回false
  return false // 阻止默认上传行为，使用我们自己的提交方式
}

// 提交上传
const submitUpload = async () => {
  console.log('当前文件列表:', fileList.value)
  if (!fileList.value || fileList.value.length === 0) {
    ElMessage.warning('请选择要上传的文件')
    return
  }
  
  uploadLoading.value = true
  
  try {
    // 创建FormData对象
    const formData = new FormData()
    // 获取原始文件对象
    const fileObject = fileList.value[0]
    const rawFile = fileObject.raw || fileObject
    console.log('上传文件:', rawFile)
    
    // 使用field name 'file' 匹配后端控制器
    formData.append('template', rawFile)
    
    // 调用实际API
    const response = await examService.importQuestionsFromWord(currentExam.value.id, formData)
    
    // 处理成功响应
    ElMessage.success(`试题导入成功，共导入${response.data.count || 0}道题目`)
    importDialogVisible.value = false
    
    // 重新加载试题列表
    loadQuestions(currentExam.value.id)
  } catch (error) {
    console.error('导入试题失败', error)
    let errorMsg = '导入试题失败'
    
    // 获取详细错误信息
    if (error.response && error.response.data && error.response.data.message) {
      errorMsg += `：${error.response.data.message}`
    }
    
    ElMessage.error(errorMsg)
  } finally {
    uploadLoading.value = false
  }
}

// 下载模板
const downloadTemplate = async () => {
  try {
    ElMessage.success('模板下载中...')
    
    // 创建下载链接
    const link = document.createElement('a')
    // 设置下载链接为后端API地址
    link.href = `${import.meta.env.VITE_API_URL || 'http://localhost:3000'}/api/exams/template/download`
    
    // 添加token到URL，以便通过授权
    const token = localStorage.getItem('token')
    if (token) {
      link.href += `?token=${token}`
    }
    
    link.download = '试题导入模板.docx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (error) {
    console.error('下载模板失败', error)
    ElMessage.error('下载模板失败，请重试')
  }
}

// 添加选项
const addOption = () => {
  questionForm.options.push({ text: '', isCorrect: false })
}

// 移除选项
const removeOption = (index) => {
  if (questionForm.options.length <= 2) {
    ElMessage.warning('至少需要2个选项')
    return
  }
  questionForm.options.splice(index, 1)
}

// 提交试题表单
const submitQuestionForm = async () => {
  if (!questionFormRef.value) return
  
  await questionFormRef.value.validate(async (valid) => {
    if (valid) {
      // 选择题验证选项
      if ((questionForm.question_type === '单选题' || questionForm.question_type === '多选题')) {
        // 验证选项内容
        const emptyOption = questionForm.options.find(opt => !opt.text.trim())
        if (emptyOption) {
          ElMessage.error('选项内容不能为空')
          return
        }
        
        // 验证是否选择了正确答案
        const hasCorrect = questionForm.options.some(opt => opt.isCorrect)
        if (!hasCorrect) {
          ElMessage.error('请至少选择一个正确答案')
          return
        }
        
        // 单选题只能有一个正确答案
        if (questionForm.question_type === '单选题') {
          const correctCount = questionForm.options.filter(opt => opt.isCorrect).length
          if (correctCount > 1) {
            ElMessage.error('单选题只能有一个正确答案')
            return
          }
        }
      }
      
      try {
        // 准备提交的数据
        const submitData = { ...questionForm }
        
        // 处理正确答案
        if (questionForm.question_type === '单选题') {
          const correctOption = questionForm.options.find(opt => opt.isCorrect)
          if (correctOption) {
            // 获取正确选项的索引，转换为A、B、C...
            const index = questionForm.options.findIndex(opt => opt.isCorrect)
            submitData.correct_answer = String.fromCharCode(65 + index) // A, B, C...
          }
        } else if (questionForm.question_type === '多选题') {
          // 将所有正确选项索引转换为字符串，如"ABC"
          const correctAnswers = questionForm.options
            .map((opt, index) => opt.isCorrect ? String.fromCharCode(65 + index) : null)
            .filter(Boolean)
            .join('')
          submitData.correct_answer = correctAnswers
        }
        
        // 提交表单
        if (questionForm.id) {
          // 编辑模式
          await examService.updateQuestion(questionForm.id, submitData)
          ElMessage.success('试题更新成功')
        } else {
          // 新增模式
          await examService.createQuestion(currentExam.value.id, submitData)
          ElMessage.success('试题添加成功')
        }
        
        questionEditDialogVisible.value = false
        // 重新加载试题列表
        loadQuestions(currentExam.value.id)
      } catch (error) {
        console.error('保存试题失败', error)
        ElMessage.error('保存试题失败，请重试')
      }
    } else {
      return false
    }
  })
}

// 格式化试题内容
const formatQuestionContent = (content) => {
  if (!content) return ''
  // 最多显示100个字符
  return content.length > 100 ? content.substring(0, 100) + '...' : content
}

// 试题不再使用分页

// 查看考试成绩
const handleViewResults = async (row) => {
  currentExam.value = row
  resultsDialogVisible.value = true
  resultsLoading.value = true
  
  try {
    const response = await examService.getExamResults(row.id)
    const data = response.data.data
    
    resultsList.value = data.results || []
    
    // 设置统计数据
    examStatistics.total_students = data.summary.total_students || 0
    examStatistics.passed_students = data.summary.passed_students || 0
    examStatistics.pass_rate = data.summary.pass_rate || 0
    examStatistics.average_score = data.summary.average_score || 0
    
    resultsLoading.value = false
  } catch (error) {
    console.error('获取成绩列表失败', error)
    ElMessage.error('获取成绩列表失败')
    resultsLoading.value = false
  }
}

// 查看考试详情
const handleViewDetail = (row) => {
  ElMessage.success(`查看 ${row.student_name} 的考试详情`)
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchExams()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchExams()
}
</script>

<style scoped>
.exam-list-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.filter-container {
  padding: 10px 0;
}

.table-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: 600;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 试题管理样式 */
.question-dialog-content {
  min-height: 300px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

/* 添加表格滚动容器样式 */
.question-table-wrapper {
  max-height: 600px; /* 增加高度以显示更多内容 */
  overflow-y: auto;
  overflow-x: hidden;
}

.question-header h3 {
  margin: 0;
  font-size: 18px;
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
  margin-bottom: 10px;
}

.options-header h4 {
  margin: 0;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* 试题导入样式 */
.import-dialog-content {
  padding: 10px 0;
}

.upload-demo {
  margin-bottom: 20px;
}

.template-download {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 20px;
  gap: 5px;
}

/* 成绩查看样式 */
.results-dialog-content h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
}

.statistics-card {
  margin-bottom: 20px;
}

.statistics-items {
  display: flex;
  justify-content: space-around;
}

.statistics-item {
  text-align: center;
  flex: 1;
}

.statistics-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.statistics-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.pass-score {
  color: #67C23A;
  font-weight: bold;
}

.fail-score {
  color: #F56C6C;
  font-weight: bold;
}

/* Style the Element Plus components to match LoginView style */
:deep(.el-button--primary) {
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
  border: none;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #66b1ff 0%, #5098fa 100%);
  border: none;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409EFF inset;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
}

/* Exam results styles */
:deep(.result-dialog) {
  min-width: 400px;
}

:deep(.results-container) {
  padding: 10px;
}

:deep(.result-item) {
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
}

:deep(.result-item.pass) {
  background-color: rgba(103, 194, 58, 0.1);
  border-left: 3px solid #67C23A;
}

:deep(.result-item.fail) {
  background-color: rgba(245, 108, 108, 0.1);
  border-left: 3px solid #F56C6C;
}

:deep(.results-list) {
  margin-bottom: 15px;
}

:deep(.attempts-info) {
  font-weight: bold;
  border-top: 1px solid #eee;
  padding-top: 10px;
}

:deep(.view-all-results) {
  margin-top: 15px;
  text-align: center;
}

:deep(.view-all-results a) {
  color: #409EFF;
  text-decoration: none;
  font-weight: bold;
}

:deep(.view-all-results a:hover) {
  text-decoration: underline;
}
</style> 