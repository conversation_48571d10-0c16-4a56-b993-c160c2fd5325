const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const examController = require('../controllers/examController');

// Configure file upload for Word documents
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/exams/');
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + path.extname(file.originalname));
  }
});

const upload = multer({ storage: storage });

// 获取所有考试
router.get('/', examController.getAllExams);

// 获取单个考试
router.get('/:id', examController.getExamById);

// 创建考试
router.post('/', examController.createExam);

// 更新考试
router.put('/:id', examController.updateExam);

// 删除考试
router.delete('/:id', examController.deleteExam);

// 获取考试的所有试题
router.get('/:examId/questions', examController.getExamQuestions);

// 添加试题
router.post('/:examId/questions', examController.createExamQuestion);

// 更新试题
router.put('/questions/:id', examController.updateExamQuestion);

// 删除试题
router.delete('/questions/:id', examController.deleteExamQuestion);

// 批量导入考试题目
router.post('/:examId/questions/import', examController.importExamQuestions);

// 从Word文档导入考试题目
router.post('/:examId/questions/import-word', examController.importQuestionsFromWord);

// 提交考试答案并自动判卷
router.post('/:id/submit', examController.submitExam);

// 获取考试成绩汇总
router.get('/:examId/results', examController.getExamResults);

// 获取教师的所有考试成绩
router.get('/teacher/:teacherId/results', examController.getTeacherResults);

// 根据考试类型获取考试列表
router.get('/type/:examType', examController.getExamsByType);

module.exports = router; 