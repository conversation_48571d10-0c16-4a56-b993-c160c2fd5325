const User = require('../models/userModel');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

// 用户注册
exports.register = async (req, res) => {
  try {
    const { username, password, role, name, email, phone, student_id } = req.body;
    
    // 验证必要字段
    if (!username || !password || !name) {
      return res.status(400).json({
        success: false,
        message: '请提供用户名、密码和姓名'
      });
    }
    
    // 检查用户名是否已存在
    const existingUser = await User.findByUsername(username);
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '该用户名已被使用'
      });
    }
    
    // 创建用户
    const userId = await User.create({
      username,
      password,
      role,
      name,
      email,
      phone,
      student_id
    });
    
    // 获取创建的用户信息（不包含密码）
    const user = await User.findById(userId);
    
    // 生成JWT令牌
    const token = jwt.sign(
      { id: user.id, username: user.username, role: user.role },
      process.env.JWT_SECRET || 'your_jwt_secret_key',
      { expiresIn: '1d' }
    );
    
    res.status(201).json({
      success: true,
      message: '用户注册成功',
      token,
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '用户注册失败',
      error: error.message
    });
  }
};

// 用户登录
exports.login = async (req, res) => {
  try {
    const { username, password } = req.body;
    
    // 验证必要字段
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '请提供用户名和密码'
      });
    }
    
    // 查找用户
    const user = await User.findByUsername(username);
    if (!user) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }
    
    // 验证密码
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }
    
    // 更新最后登录时间
    await User.updateLastLogin(user.id);
    
    // 生成JWT令牌
    const token = jwt.sign(
      { id: user.id, username: user.username, role: user.role },
      process.env.JWT_SECRET || 'your_jwt_secret_key',
      { expiresIn: '1d' }
    );
    
    // 不返回密码
    const { password: _, ...userData } = user;
    
    res.status(200).json({
      success: true,
      message: '登录成功',
      token,
      data: userData
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '登录失败',
      error: error.message
    });
  }
};

// 获取当前用户信息
exports.getCurrentUser = async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '未找到用户'
      });
    }
    
    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取用户信息失败',
      error: error.message
    });
  }
};

// 修改密码
exports.changePassword = async (req, res) => {
  try {
    const { oldPassword, newPassword } = req.body;
    
    if (!oldPassword || !newPassword) {
      return res.status(400).json({
        success: false,
        message: '请提供原密码和新密码'
      });
    }
    
    const result = await User.changePassword(req.user.id, oldPassword, newPassword);
    
    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: result.message
      });
    }
    
    res.status(200).json({
      success: true,
      message: '密码修改成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '修改密码失败',
      error: error.message
    });
  }
};

// 忘记密码请求
exports.forgotPassword = async (req, res) => {
  try {
    const { username, email } = req.body;
    
    if (!username || !email) {
      return res.status(400).json({
        success: false,
        message: '请提供用户名和邮箱'
      });
    }
    
    // 查找用户
    const user = await User.findByUsername(username);
    if (!user || user.email !== email) {
      return res.status(404).json({
        success: false,
        message: '用户名或邮箱不正确'
      });
    }
    
    // 生成密码重置令牌
    const resetToken = jwt.sign(
      { id: user.id },
      process.env.JWT_SECRET || 'your_jwt_secret_key',
      { expiresIn: '30m' }  // 令牌30分钟有效
    );
    
    // 记录令牌到数据库（实际项目中应实现这个功能）
    // 这里我们应该将令牌与用户关联起来，并设置过期时间
    await User.saveResetToken(user.id, resetToken);
    
    // 实际项目中，这里应该发送重置密码邮件
    // 由于这是一个演示项目，我们只返回成功消息
    // 在真实项目中，应该发送包含重置链接的邮件
    
    res.status(200).json({
      success: true,
      message: '密码重置链接已发送到您的邮箱',
      // 在实际生产环境中不应该返回令牌，这里只是为了演示
      resetToken
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '忘记密码请求失败',
      error: error.message
    });
  }
};

// 验证用户身份（用于忘记密码流程）
exports.verifyIdentity = async (req, res) => {
  try {
    const { username, email } = req.body;
    
    if (!username || !email) {
      return res.status(400).json({
        success: false,
        message: '请提供用户名和邮箱'
      });
    }
    
    // 查找用户
    const user = await User.findByUsername(username);
    if (!user || user.email !== email) {
      return res.status(404).json({
        success: false,
        message: '用户名或邮箱不匹配'
      });
    }
    
    // 验证成功
    res.status(200).json({
      success: true,
      message: '身份验证成功',
      userId: user.id
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '身份验证失败',
      error: error.message
    });
  }
};

// 直接重置密码（不需要token，通过用户名和邮箱验证）
exports.directResetPassword = async (req, res) => {
  try {
    const { username, email, newPassword } = req.body;
    
    if (!username || !email || !newPassword) {
      return res.status(400).json({
        success: false,
        message: '请提供用户名、邮箱和新密码'
      });
    }
    
    // 查找用户
    const user = await User.findByUsername(username);
    if (!user || user.email !== email) {
      return res.status(404).json({
        success: false,
        message: '用户名或邮箱不正确'
      });
    }
    
    // 更新用户密码
    await User.resetPassword(user.id, newPassword);
    
    res.status(200).json({
      success: true,
      message: '密码已成功重置，请使用新密码登录'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '重置密码失败',
      error: error.message
    });
  }
};

// 重置密码
exports.resetPassword = async (req, res) => {
  try {
    const { token, newPassword } = req.body;
    
    if (!token || !newPassword) {
      return res.status(400).json({
        success: false,
        message: '请提供令牌和新密码'
      });
    }
    
    try {
      // 验证令牌
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret_key');
      
      // 检查令牌是否存在于数据库中
      const user = await User.findByResetToken(token);
      if (!user) {
        return res.status(400).json({
          success: false,
          message: '密码重置令牌无效或已过期'
        });
      }
      
      // 更新用户密码
      await User.resetPassword(decoded.id, newPassword);
      
      // 清除重置令牌
      await User.clearResetToken(decoded.id);
      
      res.status(200).json({
        success: true,
        message: '密码已成功重置，请使用新密码登录'
      });
    } catch (error) {
      return res.status(400).json({
        success: false,
        message: '令牌无效或已过期',
        error: error.message
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '重置密码失败',
      error: error.message
    });
  }
};

// 授权中间件
exports.protect = async (req, res, next) => {
  try {
    let token;
    
    // 检查请求头中的授权信息
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '未授权访问，请先登录'
      });
    }
    
    try {
      // 验证令牌
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret_key');
      
      // 将用户信息添加到请求对象
      req.user = {
        id: decoded.id,
        username: decoded.username,
        role: decoded.role
      };
      
      next();
    } catch (error) {
      return res.status(401).json({
        success: false,
        message: '令牌无效或已过期，请重新登录'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '授权验证失败',
      error: error.message
    });
  }
};

// 角色验证中间件
exports.authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user || !roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: '您没有执行此操作的权限'
      });
    }
    next();
  };
}; 