<template>
  <div class="training-list-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="title">专项培训</span>
          <el-button type="primary" @click="openDialog()">添加培训课程</el-button>
        </div>
      </template>
      
      <!-- 筛选区域 -->
      <div class="filter-container">
        <div class="filter-item">
          <span class="filter-label">课程类型</span>
          <el-select v-model="filterForm.course_type" placeholder="选择课程类型" clearable class="filter-select">
            <el-option label="小讲课" value="小讲课" />
            <el-option label="教学病例讨论" value="教学病例讨论" />
            <el-option label="教学查房" value="教学查房" />
            <el-option label="其他" value="其他" />
          </el-select>
        </div>
        <div class="filter-buttons">
          <el-button type="primary" @click="handleFilter">筛选</el-button>
          <el-button @click="resetFilter">重置</el-button>
        </div>
      </div>

      <!-- 课程列表表格 -->
      <div v-loading="loading">
        <div v-if="trainingCourses.length === 0 && !loading" class="empty-data">
          暂无培训课程，请添加新课程
        </div>
        
        <el-table
          v-else
          :data="trainingCourses"
          style="width: 100%"
          border
        >
          <el-table-column prop="title" label="课程标题" min-width="180" show-overflow-tooltip />
          
          <el-table-column prop="course_type" label="课程类型" width="150" align="center">
            <template #default="scope">
              <el-tag :type="getTagType(scope.row.course_type)">{{ scope.row.course_type }}</el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="description" label="课程描述" min-width="200" show-overflow-tooltip />
          
          <el-table-column prop="created_at" label="创建时间" width="120" align="center">
            <template #default="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>
          
          <el-table-column label="课程资料" width="220" align="center">
            <template #default="scope">
              <div v-if="scope.row.material_path" class="file-actions">
                <el-button
                  type="primary"
                  link
                  @click="previewFile(scope.row)"
                  title="预览文件"
                >
                  <el-icon><Document /></el-icon> {{ scope.row.original_filename }}
                </el-button>
                
              </div>
              <span v-else>无资料</span>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="150" align="center" fixed="right">
            <template #default="scope">
              <el-button type="primary" link size="small" @click="openDialog(scope.row)">编辑</el-button>
              <el-button type="danger" link size="small" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container" v-if="trainingCourses.length > 0">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 30, 50]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 添加/编辑课程对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="formData.id ? '编辑培训课程' : '添加培训课程'"
      width="50%"
      destroy-on-close
    >
      <el-form
        ref="courseFormRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        label-position="right"
      >
        <el-form-item label="课程标题" prop="title">
          <el-input v-model="formData.title" placeholder="请输入课程标题" />
        </el-form-item>
        
        <el-form-item label="课程类型" prop="course_type">
          <el-select v-model="formData.course_type" placeholder="选择课程类型" style="width: 100%">
            <el-option label="小讲课" value="小讲课" />
            <el-option label="教学病例讨论" value="教学病例讨论" />
            <el-option label="教学查房" value="教学查房" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="课程描述" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            rows="3"
            placeholder="请输入课程描述"
          />
        </el-form-item>
        
        <el-form-item label="课件/视频">
          <el-upload
            class="material-uploader"
            drag
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange"
            :on-remove="handleRemove"
            :before-upload="beforeMaterialUpload"
            :file-list="fileList"
            :limit="1"
          >
            <el-icon class="el-icon--upload"><Upload /></el-icon>
            <div class="el-upload__text">
              拖拽文件到此处或 <em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持各种文档、PPT、视频等格式，文件大小不超过100MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Upload, More, Download } from '@element-plus/icons-vue'
import axios from 'axios'
import { API_URL } from '@/utils/api'
import * as trainingService from '@/services/trainingService'

export default {
  name: 'TrainingList',
  components: {
    Document,
    Upload,
    More,
    Download
  },
  setup() {
    // 基础数据
    const loading = ref(false)
    const submitting = ref(false)
    const trainingCourses = ref([])
    const dialogVisible = ref(false)
    const courseFormRef = ref(null)
    const total = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(10)
    const fileList = ref([])
    const uploadFile = ref(null) // 存储要上传的文件
    
    // 筛选条件
    const filterForm = reactive({
      course_type: ''
    })
    
    // 表单数据
    const formData = reactive({
      id: '',
      title: '',
      course_type: '',
      description: '',
      material_path: '',
      original_filename: ''
    })
    
    // 表单验证规则
    const formRules = {
      title: [
        { required: true, message: '请输入课程标题', trigger: 'blur' },
        { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
      ],
      course_type: [
        { required: true, message: '请选择课程类型', trigger: 'change' }
      ]
    }
    
    // 生命周期钩子
    onMounted(() => {
      fetchTrainingCourses()
    })
    
    // 获取培训课程列表
    const fetchTrainingCourses = async () => {
      loading.value = true
      try {
        let response
        const params = {
          page: currentPage.value,
          limit: pageSize.value
        }
        
        if (filterForm.course_type) {
          response = await trainingService.getTrainingsByType(filterForm.course_type, params)
        } else {
          response = await trainingService.getAllTrainings(params)
        }
        
        trainingCourses.value = response.data
        total.value = response.count
      } catch (error) {
        console.error('获取培训课程失败:', error)
        ElMessage.error('获取培训课程失败')
      } finally {
        loading.value = false
      }
    }
    
    // 筛选操作
    const handleFilter = () => {
      currentPage.value = 1
      fetchTrainingCourses()
    }
    
    // 重置筛选
    const resetFilter = () => {
      filterForm.course_type = ''
      handleFilter()
    }
    
    // 分页操作
    const handleSizeChange = (val) => {
      pageSize.value = val
      fetchTrainingCourses()
    }
    
    const handleCurrentChange = (val) => {
      currentPage.value = val
      fetchTrainingCourses()
    }
    
    // 文件上传前检查
    const beforeMaterialUpload = (file) => {
      const isLt100M = file.size / 1024 / 1024 < 100
      
      if (!isLt100M) {
        ElMessage.error('上传文件大小不能超过 100MB!')
      }
      
      return isLt100M
    }
    
    // 文件改变处理
    const handleFileChange = (file) => {
      uploadFile.value = file.raw
    }
    
    // 移除文件
    const handleRemove = () => {
      formData.material_path = ''
      formData.original_filename = ''
      fileList.value = []
      uploadFile.value = null
    }
    
    // 打开对话框
    const openDialog = (course) => {
      if (course) {
        // 编辑模式
        Object.keys(formData).forEach(key => {
          formData[key] = course[key]
        })
        
        // 如果有文件，添加到文件列表
        fileList.value = []
        if (course.material_path && course.original_filename) {
          fileList.value = [
            {
              name: decodeURIComponent(course.original_filename),
              url: `http://127.0.0.1:3000${course.material_path}`
            }
          ]
        }
      } else {
        // 新增模式
        Object.keys(formData).forEach(key => {
          formData[key] = ''
        })
        fileList.value = []
        uploadFile.value = null
      }
      
      dialogVisible.value = true
    }
    
    // 提交表单
    const submitForm = async () => {
      if (!courseFormRef.value) return
      
      await courseFormRef.value.validate(async (valid) => {
        if (valid) {
          submitting.value = true
          
          try {
            // 提交表单数据，直接将文件传递给service
            if (formData.id) {
              // 编辑
              await trainingService.updateTraining(formData.id, formData, uploadFile.value);
              ElMessage.success('课程更新成功')
            } else {
              // 新增
              await trainingService.createTraining(formData, uploadFile.value);
              ElMessage.success('课程添加成功')
            }
            
            dialogVisible.value = false
            fetchTrainingCourses()
          } catch (error) {
            console.error('操作失败:', error)
            ElMessage.error('操作失败: ' + (error.response?.data?.message || error.message))
          } finally {
            submitting.value = false
          }
        } else {
          return false
        }
      })
    }
    
    // 处理删除
    const handleDelete = (course) => {
      ElMessageBox.confirm(
        `确定要删除培训课程 "${course.title}" 吗?`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(async () => {
          try {
            await trainingService.deleteTraining(course.id)
            ElMessage.success('删除成功')
            fetchTrainingCourses()
          } catch (error) {
            console.error('删除失败:', error)
            ElMessage.error('删除失败')
          }
        })
        .catch(() => {
          ElMessage.info('已取消删除')
        })
    }
    
    // 下拉菜单命令处理 - 保留但不再使用
    const handleCommand = (command, course) => {
      if (command === 'edit') {
        openDialog(course)
      } else if (command === 'delete') {
        handleDelete(course)
      }
    }
    
    // 根据课程类型获取标签类型
    const getTagType = (type) => {
      switch (type) {
        case '小讲课':
          return 'success'
        case '教学病例讨论':
          return 'warning'
        case '教学查房':
          return 'danger'
        default:
          return 'info'
      }
    }
    
    // 日期格式化
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
    }

    // 添加预览文件方法
    const previewFile = (course) => {
      window.open(`http://127.0.0.1:3000${course.material_path}`, '_blank')
    }
    
    return {
      loading,
      submitting,
      trainingCourses,
      dialogVisible,
      courseFormRef,
      formData,
      formRules,
      filterForm,
      fileList,
      uploadFile,
      currentPage,
      pageSize,
      total,
      handleFilter,
      resetFilter,
      handleSizeChange,
      handleCurrentChange,
      openDialog,
      submitForm,
      beforeMaterialUpload,
      handleFileChange,
      handleRemove,
      handleCommand,
      handleDelete,
      getTagType,
      formatDate,
      previewFile
    }
  }
}
</script>

<style scoped>
.training-list-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.filter-form {
  margin-bottom: 20px;
}

.empty-data {
  padding: 50px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.material-uploader {
  width: 100%;
}

.material-uploader .el-upload-dragger {
  width: 100%;
}

.filter-container {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.filter-item {
  display: flex;
  align-items: center;
  margin-right: 20px;
}

.filter-label {
  font-weight: bold;
  margin-right: 10px;
  min-width: 70px;
}

.filter-select {
  width: 200px;
}

.filter-buttons {
  display: flex;
  gap: 10px;
}

.file-actions {
  display: flex;
  justify-content: center;
  gap: 8px;
}
</style> 