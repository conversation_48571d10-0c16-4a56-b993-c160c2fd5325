{"ast": null, "code": "import axios from 'axios';\n\n// API基础URL\nexport const API_URL = 'http://localhost:3000/api';\n\n// 配置axios默认值\nconst api = axios.create({\n  baseURL: 'http://localhost:3000',\n  timeout: 10000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 请求拦截器 - 添加认证头\napi.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers['Authorization'] = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// 响应拦截器 - 处理常见错误\napi.interceptors.response.use(response => {\n  return response;\n}, error => {\n  const {\n    response\n  } = error;\n  if (response) {\n    // 未授权，清除token并重定向到登录页\n    if (response.status === 401) {\n      localStorage.removeItem('token');\n      localStorage.removeItem('userId');\n      localStorage.removeItem('userRole');\n      window.location.href = '/login';\n    }\n  }\n  return Promise.reject(error);\n});\nexport default api;", "map": {"version": 3, "names": ["axios", "API_URL", "api", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "error", "Promise", "reject", "response", "status", "removeItem", "window", "location", "href"], "sources": ["D:/admin/202506/督导系统/后台管理系统/ms/src/utils/api.js"], "sourcesContent": ["import axios from 'axios'\r\n\r\n// API基础URL\r\nexport const API_URL = 'http://localhost:3000/api'\r\n\r\n// 配置axios默认值\r\nconst api = axios.create({\r\n  baseURL: 'http://localhost:3000',\r\n  timeout: 10000,\r\n  headers: {\r\n    'Content-Type': 'application/json'\r\n  }\r\n})\r\n\r\n// 请求拦截器 - 添加认证头\r\napi.interceptors.request.use(\r\n  config => {\r\n    const token = localStorage.getItem('token')\r\n    if (token) {\r\n      config.headers['Authorization'] = `Bearer ${token}`\r\n    }\r\n    return config\r\n  },\r\n  error => {\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\n// 响应拦截器 - 处理常见错误\r\napi.interceptors.response.use(\r\n  response => {\r\n    return response\r\n  },\r\n  error => {\r\n    const { response } = error\r\n    if (response) {\r\n      // 未授权，清除token并重定向到登录页\r\n      if (response.status === 401) {\r\n        localStorage.removeItem('token')\r\n        localStorage.removeItem('userId')\r\n        localStorage.removeItem('userRole')\r\n        window.location.href = '/login'\r\n      }\r\n    }\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\nexport default api "], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,OAAO,MAAMC,OAAO,GAAG,2BAA2B;;AAElD;AACA,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAE,uBAAuB;EAChCC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,GAAG,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CAC1BC,MAAM,IAAI;EACR,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUK,KAAK,EAAE;EACrD;EACA,OAAOD,MAAM;AACf,CAAC,EACDI,KAAK,IAAI;EACP,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,GAAG,CAACK,YAAY,CAACU,QAAQ,CAACR,GAAG,CAC3BQ,QAAQ,IAAI;EACV,OAAOA,QAAQ;AACjB,CAAC,EACDH,KAAK,IAAI;EACP,MAAM;IAAEG;EAAS,CAAC,GAAGH,KAAK;EAC1B,IAAIG,QAAQ,EAAE;IACZ;IACA,IAAIA,QAAQ,CAACC,MAAM,KAAK,GAAG,EAAE;MAC3BN,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;MAChCP,YAAY,CAACO,UAAU,CAAC,QAAQ,CAAC;MACjCP,YAAY,CAACO,UAAU,CAAC,UAAU,CAAC;MACnCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;IACjC;EACF;EACA,OAAOP,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAeZ,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}