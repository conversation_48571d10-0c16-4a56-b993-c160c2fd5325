"use strict";(self["webpackChunkms"]=self["webpackChunkms"]||[]).push([[955],{3955:function(e,t,a){a.r(t),a.d(t,{default:function(){return Q}});a(4114),a(8111),a(2489),a(116),a(5207),a(1701),a(3579);var l=a(6768),o=a(4232),s=a(144),r=a(1219),i=a(2933),n=a(7477),u=a(782),d=a(1387),c=a(6434),p=a(9634);const m={class:"exam-list-container"},_={class:"filter-container"},v={class:"card-header"},g={class:"pagination-container"},k={class:"dialog-footer"},b={key:0,class:"question-dialog-content"},f={class:"question-header"},h={class:"question-actions"},y={class:"question-table-wrapper"},w=["innerHTML"],x={class:"import-dialog-content"},F={class:"template-download"},C={class:"dialog-footer"},V={class:"options-header"},q={class:"option-item"},$={class:"dialog-footer"},R={key:0,class:"results-dialog-content"},W={class:"statistics-items"},L={class:"statistics-item"},E={class:"statistics-value"},K={class:"statistics-item"},A={class:"statistics-value"},I={class:"statistics-item"},U={class:"statistics-value"},S={class:"statistics-item"},M={class:"statistics-value"};var Y={__name:"ExamList",setup(e){const t=(0,u.Pj)(),a=(0,d.rd)(),Y=(0,s.KR)(!0),z=(0,s.KR)(1),D=(0,s.KR)(10),Q=(0,s.KR)(0),X=(0,s.KR)(!1),T=(0,s.KR)("新增考试"),H=(0,s.KR)(null),B=(0,s.KR)(null);let j=localStorage.getItem("userInfo")?JSON.parse(localStorage.getItem("userInfo")):"";const N=(0,l.EW)(()=>t.getters.isStudent),P="admin"==j?.role,O=(0,s.KR)(!1),J=(0,s.KR)(!1),G=(0,s.KR)("新增试题"),Z=(0,s.KR)(null),ee=(0,s.KR)(null),te=(0,s.KR)([]),ae=(0,s.KR)(!1),le=(0,s.KR)(1),oe=(0,s.KR)(0),se=(0,s.KR)(!1),re=(0,s.KR)([]),ie=(0,s.KR)(!1),ne=(0,s.KR)(!1),ue=(0,s.KR)([]),de=(0,s.KR)(!1),ce=(0,s.Kh)({total_students:0,passed_students:0,pass_rate:0,average_score:0}),pe=(0,s.KR)({}),me=(0,s.KR)({}),_e=(0,s.Kh)({title:""}),ve=(0,s.Kh)({id:null,title:"",description:"",duration:60,pass_score:60,total_score:100}),ge={title:[{required:!0,message:"请输入考试标题",trigger:"blur"}],duration:[{required:!0,message:"请输入考试时长",trigger:"blur"},{type:"number",min:1,message:"考试时长必须大于0",trigger:"blur"}],pass_score:[{required:!0,message:"请输入及格分数",trigger:"blur"},{type:"number",min:1,message:"及格分数必须大于0",trigger:"blur"}],total_score:[{required:!0,message:"请输入总分",trigger:"blur"},{type:"number",min:1,message:"总分必须大于0",trigger:"blur"}]},ke=(0,s.Kh)({id:null,question:"",question_type:"单选题",correct_answer:"",score:5,options:[{text:"",isCorrect:!1},{text:"",isCorrect:!1},{text:"",isCorrect:!1},{text:"",isCorrect:!1}]}),be={question:[{required:!0,message:"请输入题目内容",trigger:"blur"}],question_type:[{required:!0,message:"请选择题目类型",trigger:"change"}],score:[{required:!0,message:"请输入分值",trigger:"blur"},{type:"number",min:1,message:"分值必须大于0",trigger:"blur"}]},fe=(0,s.KR)([]);Q.value=fe.length;const he=async()=>{Y.value=!0;try{const e=await c.A.getExams({page:z.value,limit:D.value,title:_e.title});fe.value=e.data.data,Q.value=e.data.total||fe.value.length,N.value&&await ye(),Y.value=!1}catch(e){console.error("获取考试列表失败",e),r.nk.error("获取考试列表失败"),Y.value=!1}},ye=async()=>{const e=localStorage.getItem("studentId");if(!e)return console.error("未找到有效的学生ID"),void r.nk.warning("未找到有效的学生ID，请重新登录后再试");try{console.log("正在获取所有考试的尝试次数...学生ID:",e);const t=fe.value.map(async t=>{try{console.log(`正在查询考试 ${t.id} (${t.title}) 的尝试记录...`);const a=await c.A.getExamResults(t.id,{student_id:e});if(console.log(`考试 ${t.id} 返回数据:`,a.data),a.data&&a.data.data){const e=a.data.data.results||[],l=e.length,o=Math.max(0,2-l);console.log(`考试 ${t.id} (${t.title}): 已尝试 ${l} 次，剩余 ${o} 次`),pe.value[t.id]=o,me.value[t.id]=l>0,e.length>0&&e.forEach((e,t)=>{console.log(`  尝试 ${t+1}: 得分 ${e.score}，时间 ${e.exam_date}`)})}else console.warn(`考试 ${t.id} 未返回有效数据，设置默认剩余次数为2`),pe.value[t.id]=2,me.value[t.id]=!1}catch(a){console.error(`获取考试 ${t.id} 尝试次数失败:`,a),pe.value[t.id]=2,me.value[t.id]=!1}});await Promise.all(t),console.log("所有考试尝试次数获取完成:",pe.value),console.log("已参加过的考试:",me.value)}catch(t){console.error("获取考试尝试次数失败:",t),fe.value.forEach(e=>{pe.value[e.id]=2,me.value[e.id]=!1})}},we=e=>{if(B.value=e.id,!pe.value[e.id]&&0!==pe.value[e.id]){const t=localStorage.getItem("teacherId");return t?(r.nk.info("正在获取考试尝试信息，请稍候..."),void c.A.getExamResults(e.id,{teacher_id:t}).then(t=>{if(console.log(`考试 ${e.id} 尝试信息:`,t.data),t.data&&t.data.data){const a=t.data.data.results||[],l=a.length,o=Math.max(0,2-l);console.log(`考试 ${e.id} (${e.title}): 已尝试 ${l} 次，剩余 ${o} 次`),pe.value[e.id]=o,me.value[e.id]=l>0,B.value=null,xe(e)}else console.warn(`考试 ${e.id} 未返回有效数据，设置默认剩余次数为2`),pe.value[e.id]=2,me.value[e.id]=!1,B.value=null,xe(e)}).catch(e=>{console.error("获取考试尝试次数失败",e),r.nk.error("获取考试尝试次数失败，请刷新页面重试"),B.value=null})):(r.nk.warning("未找到有效的教师ID，请重新登录"),void(B.value=null))}B.value=null,xe(e)},xe=e=>{0!==pe.value[e.id]?i.s.confirm(`您总共有2次尝试机会，已使用 ${2-pe.value[e.id]} 次，还剩 ${pe.value[e.id]} 次机会。确定要参加考试吗？`,"参加考试",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{localStorage.setItem("currentExamId",e.id),localStorage.setItem("currentExamTitle",e.title),localStorage.setItem("currentExamRemainingAttempts",pe.value[e.id]),a.push(`/exams/take/${e.id}`)}).catch(()=>{console.log("用户取消参加考试")}):r.nk.warning("您已达到该考试的最大尝试次数（2次）")};(0,l.sV)(()=>{he()});const Fe=()=>{_e.title="",Ce()},Ce=()=>{z.value=1,he()},Ve=()=>{T.value="新增考试",X.value=!0,ve.id=null,ve.title="",ve.description="",ve.duration=60,ve.pass_score=60,ve.total_score=100},qe=e=>{T.value="编辑考试",X.value=!0,Object.keys(ve).forEach(t=>{ve[t]=e[t]})},$e=e=>{i.s.confirm(`确定要删除考试 ${e.title} 吗?`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await c.A.deleteExam(e.id),r.nk.success(`考试 ${e.title} 已删除`),he()}catch(t){console.error("删除考试失败",t),r.nk.error("删除考试失败，请重试")}}).catch(()=>{})},Re=async()=>{H.value&&await H.value.validate(async e=>{if(!e)return!1;try{ve.id?(await c.A.updateExam(ve.id,ve),r.nk.success(`考试 ${ve.title} 信息已更新`)):(await c.A.createExam(ve),r.nk.success(`考试 ${ve.title} 添加成功`)),X.value=!1,he()}catch(t){console.error("保存考试失败",t),r.nk.error("保存考试失败，请重试")}})},We=e=>{ee.value=e,O.value=!0,le.value=1,Le(e.id),setTimeout(()=>{const e=document.querySelector(".question-table-wrapper .el-table");e&&(e.style.height="550px")},100)},Le=async e=>{ae.value=!0;try{const t=await c.A.getExamQuestions(e);console.log("加载试题数据返回:",t.data),t.data&&t.data.data?(te.value=t.data.data,oe.value=t.data.count||te.value.length,console.log(`加载了 ${te.value.length} 道试题`)):(te.value=[],oe.value=0,console.warn("未找到试题数据")),ae.value=!1}catch(t){console.error("获取试题失败",t),r.nk.error("获取试题失败"),ae.value=!1}},Ee=()=>{G.value="新增试题",J.value=!0,ke.id=null,ke.question="",ke.question_type="单选题",ke.correct_answer="",ke.score=5,ke.options=[{text:"",isCorrect:!1},{text:"",isCorrect:!1},{text:"",isCorrect:!1},{text:"",isCorrect:!1}]},Ke=e=>{G.value="编辑试题",J.value=!0,ke.id=e.id,ke.question=e.question,ke.question_type=e.question_type,ke.correct_answer=e.correct_answer,ke.score=e.score,"单选题"===e.question_type||"多选题"===e.question_type?ke.options=e.options?[...e.options]:[]:"判断题"===e.question_type&&(ke.correct_answer=e.correct_answer)},Ae=e=>{i.s.confirm("确定要删除该试题吗?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await c.A.deleteQuestion(e.id),r.nk.success("试题已删除"),Le(ee.value.id)}catch(t){console.error("删除试题失败",t),r.nk.error("删除试题失败，请重试")}}).catch(()=>{})},Ie=()=>{se.value=!0,re.value=[]},Ue=e=>{const t="application/msword"===e.type||"application/vnd.openxmlformats-officedocument.wordprocessingml.document"===e.type;if(!t)return r.nk.error("请上传Word格式的文件!"),!1;const a=e.size/1024/1024<10;return!!a||(r.nk.error("文件大小不能超过10MB!"),!1)},Se=(e,t)=>{console.log("文件选择变化:",e,t),re.value=t},Me=e=>{const{file:t}=e;return!1},Ye=async()=>{if(console.log("当前文件列表:",re.value),re.value&&0!==re.value.length){ie.value=!0;try{const e=new FormData,t=re.value[0],a=t.raw||t;console.log("上传文件:",a),e.append("template",a);const l=await c.A.importQuestionsFromWord(ee.value.id,e);r.nk.success(`试题导入成功，共导入${l.data.count||0}道题目`),se.value=!1,Le(ee.value.id)}catch(e){console.error("导入试题失败",e);let t="导入试题失败";e.response&&e.response.data&&e.response.data.message&&(t+=`：${e.response.data.message}`),r.nk.error(t)}finally{ie.value=!1}}else r.nk.warning("请选择要上传的文件")},ze=async()=>{try{r.nk.success("模板下载中...");const e=document.createElement("a");e.href=`${(void 0).VITE_API_URL||"http://localhost:3000"}/api/exams/template/download`;const t=localStorage.getItem("token");t&&(e.href+=`?token=${t}`),e.download="试题导入模板.docx",document.body.appendChild(e),e.click(),document.body.removeChild(e)}catch(e){console.error("下载模板失败",e),r.nk.error("下载模板失败，请重试")}},De=()=>{ke.options.push({text:"",isCorrect:!1})},Qe=e=>{ke.options.length<=2?r.nk.warning("至少需要2个选项"):ke.options.splice(e,1)},Xe=async()=>{Z.value&&await Z.value.validate(async e=>{if(!e)return!1;if("单选题"===ke.question_type||"多选题"===ke.question_type){const e=ke.options.find(e=>!e.text.trim());if(e)return void r.nk.error("选项内容不能为空");const t=ke.options.some(e=>e.isCorrect);if(!t)return void r.nk.error("请至少选择一个正确答案");if("单选题"===ke.question_type){const e=ke.options.filter(e=>e.isCorrect).length;if(e>1)return void r.nk.error("单选题只能有一个正确答案")}}try{const e={...ke};if("单选题"===ke.question_type){const t=ke.options.find(e=>e.isCorrect);if(t){const t=ke.options.findIndex(e=>e.isCorrect);e.correct_answer=String.fromCharCode(65+t)}}else if("多选题"===ke.question_type){const t=ke.options.map((e,t)=>e.isCorrect?String.fromCharCode(65+t):null).filter(Boolean).join("");e.correct_answer=t}ke.id?(await c.A.updateQuestion(ke.id,e),r.nk.success("试题更新成功")):(await c.A.createQuestion(ee.value.id,e),r.nk.success("试题添加成功")),J.value=!1,Le(ee.value.id)}catch(t){console.error("保存试题失败",t),r.nk.error("保存试题失败，请重试")}})},Te=e=>e?e.length>100?e.substring(0,100)+"...":e:"",He=async e=>{ee.value=e,ne.value=!0,de.value=!0;try{const t=await c.A.getExamResults(e.id),a=t.data.data;ue.value=a.results||[],ce.total_students=a.summary.total_students||0,ce.passed_students=a.summary.passed_students||0,ce.pass_rate=a.summary.pass_rate||0,ce.average_score=a.summary.average_score||0,de.value=!1}catch(t){console.error("获取成绩列表失败",t),r.nk.error("获取成绩列表失败"),de.value=!1}},Be=e=>{r.nk.success(`查看 ${e.student_name} 的考试详情`)},je=e=>{D.value=e,z.value=1,he()},Ne=e=>{z.value=e,he()};return(e,t)=>{const a=(0,l.g2)("el-input"),r=(0,l.g2)("el-form-item"),i=(0,l.g2)("el-button"),u=(0,l.g2)("el-form"),d=(0,l.g2)("el-card"),c=(0,l.g2)("el-table-column"),j=(0,l.g2)("el-tag"),le=(0,l.g2)("el-table"),oe=(0,l.g2)("el-pagination"),me=(0,l.g2)("el-input-number"),he=(0,l.g2)("el-col"),ye=(0,l.g2)("el-row"),xe=(0,l.g2)("el-dialog"),Le=(0,l.g2)("el-alert"),Pe=(0,l.g2)("el-icon"),Oe=(0,l.g2)("el-upload"),Je=(0,l.g2)("el-option"),Ge=(0,l.g2)("el-select"),Ze=(0,l.g2)("el-checkbox"),et=(0,l.g2)("el-radio"),tt=(0,l.g2)("el-radio-group"),at=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",m,[(0,l.bF)(d,{class:"filter-card"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",_,[(0,l.bF)(u,{model:_e,inline:""},{default:(0,l.k6)(()=>[(0,l.bF)(r,{label:"考试标题"},{default:(0,l.k6)(()=>[(0,l.bF)(a,{modelValue:_e.title,"onUpdate:modelValue":t[0]||(t[0]=e=>_e.title=e),placeholder:"请输入考试标题",clearable:""},null,8,["modelValue"])]),_:1}),(0,l.bF)(r,null,{default:(0,l.k6)(()=>[(0,l.bF)(i,{type:"primary",onClick:Ce},{default:(0,l.k6)(()=>t[19]||(t[19]=[(0,l.eW)("搜索")])),_:1,__:[19]}),(0,l.bF)(i,{onClick:Fe},{default:(0,l.k6)(()=>t[20]||(t[20]=[(0,l.eW)("重置")])),_:1,__:[20]})]),_:1})]),_:1},8,["model"])])]),_:1}),(0,l.bF)(d,{class:"table-card"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",v,[t[22]||(t[22]=(0,l.Lk)("span",null,"考试列表",-1)),(0,l.Lk)("div",null,[P?((0,l.uX)(),(0,l.Wv)(i,{key:0,type:"primary",onClick:Ve},{default:(0,l.k6)(()=>t[21]||(t[21]=[(0,l.eW)("新增考试")])),_:1,__:[21]})):(0,l.Q3)("",!0)])])]),default:(0,l.k6)(()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(le,{data:fe.value,stripe:"",border:"",style:{width:"100%"}},{default:(0,l.k6)(()=>[(0,l.bF)(c,{type:"index",width:"50"}),(0,l.bF)(c,{prop:"title",label:"考试标题","min-width":"200"}),(0,l.bF)(c,{prop:"description",label:"考试描述","min-width":"200","show-overflow-tooltip":""}),(0,l.bF)(c,{prop:"duration",label:"考试时长(分钟)",width:"120"}),(0,l.bF)(c,{prop:"pass_score",label:"及格分数",width:"100"}),(0,l.bF)(c,{prop:"total_score",label:"总分",width:"80"}),N.value?((0,l.uX)(),(0,l.Wv)(c,{key:0,label:"考试机会",width:"120"},{default:(0,l.k6)(e=>[(0,l.bF)(j,{type:pe.value[e.row.id]>0?"success":"danger",effect:"plain"},{default:(0,l.k6)(()=>[(0,l.eW)(" 已用 "+(0,o.v_)(2-pe.value[e.row.id])+"/2 次 ",1)]),_:2},1032,["type"])]),_:1})):(0,l.Q3)("",!0),(0,l.bF)(c,{label:"创建时间",width:"160"},{default:(0,l.k6)(e=>[(0,l.eW)((0,o.v_)((0,s.R1)(p.Y)(e.row.created_at,"YYYY-MM-DD HH:mm")),1)]),_:1}),(0,l.bF)(c,{label:"操作",width:"320",fixed:"right"},{default:(0,l.k6)(e=>[P?((0,l.uX)(),(0,l.CE)(l.FK,{key:0},[(0,l.bF)(i,{type:"primary",size:"small",onClick:t=>We(e.row)},{default:(0,l.k6)(()=>t[23]||(t[23]=[(0,l.eW)("试题管理")])),_:2,__:[23]},1032,["onClick"]),(0,l.bF)(i,{type:"success",size:"small",onClick:t=>He(e.row)},{default:(0,l.k6)(()=>t[24]||(t[24]=[(0,l.eW)("成绩查看")])),_:2,__:[24]},1032,["onClick"]),(0,l.bF)(i,{type:"warning",size:"small",onClick:t=>qe(e.row)},{default:(0,l.k6)(()=>t[25]||(t[25]=[(0,l.eW)("编辑")])),_:2,__:[25]},1032,["onClick"]),(0,l.bF)(i,{type:"danger",size:"small",onClick:t=>$e(e.row)},{default:(0,l.k6)(()=>t[26]||(t[26]=[(0,l.eW)("删除")])),_:2,__:[26]},1032,["onClick"])],64)):((0,l.uX)(),(0,l.Wv)(i,{key:1,type:"primary",size:"small",onClick:t=>we(e.row),disabled:0===pe.value[e.row.id],loading:Y.value&&B.value===e.row.id},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(0===pe.value[e.row.id]?"已无机会":"参加考试"),1)]),_:2},1032,["onClick","disabled","loading"]))]),_:1})]),_:1},8,["data"])),[[at,Y.value]]),(0,l.Lk)("div",g,[(0,l.bF)(oe,{background:"",layout:"total, sizes, prev, pager, next, jumper","current-page":z.value,"page-sizes":[10,20,50,100],"page-size":D.value,total:Q.value,onSizeChange:je,onCurrentChange:Ne},null,8,["current-page","page-size","total"])])]),_:1}),(0,l.bF)(xe,{title:T.value,modelValue:X.value,"onUpdate:modelValue":t[7]||(t[7]=e=>X.value=e),width:"600px"},{footer:(0,l.k6)(()=>[(0,l.Lk)("div",k,[(0,l.bF)(i,{onClick:t[6]||(t[6]=e=>X.value=!1)},{default:(0,l.k6)(()=>t[27]||(t[27]=[(0,l.eW)("取消")])),_:1,__:[27]}),(0,l.bF)(i,{type:"primary",onClick:Re},{default:(0,l.k6)(()=>t[28]||(t[28]=[(0,l.eW)("确定")])),_:1,__:[28]})])]),default:(0,l.k6)(()=>[(0,l.bF)(u,{model:ve,rules:ge,ref_key:"examFormRef",ref:H,"label-width":"100px"},{default:(0,l.k6)(()=>[(0,l.bF)(r,{label:"考试标题",prop:"title"},{default:(0,l.k6)(()=>[(0,l.bF)(a,{modelValue:ve.title,"onUpdate:modelValue":t[1]||(t[1]=e=>ve.title=e),placeholder:"请输入考试标题"},null,8,["modelValue"])]),_:1}),(0,l.bF)(r,{label:"考试描述",prop:"description"},{default:(0,l.k6)(()=>[(0,l.bF)(a,{type:"textarea",modelValue:ve.description,"onUpdate:modelValue":t[2]||(t[2]=e=>ve.description=e),placeholder:"请输入考试描述",rows:"3"},null,8,["modelValue"])]),_:1}),(0,l.bF)(ye,{gutter:20},{default:(0,l.k6)(()=>[(0,l.bF)(he,{span:24},{default:(0,l.k6)(()=>[(0,l.bF)(r,{label:"考试时长",prop:"duration"},{default:(0,l.k6)(()=>[(0,l.bF)(me,{modelValue:ve.duration,"onUpdate:modelValue":t[3]||(t[3]=e=>ve.duration=e),min:1,max:240,placeholder:"分钟",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1}),(0,l.bF)(he,{span:24},{default:(0,l.k6)(()=>[(0,l.bF)(r,{label:"及格分数",prop:"pass_score"},{default:(0,l.k6)(()=>[(0,l.bF)(me,{modelValue:ve.pass_score,"onUpdate:modelValue":t[4]||(t[4]=e=>ve.pass_score=e),min:1,max:ve.total_score,style:{width:"100%"}},null,8,["modelValue","max"])]),_:1})]),_:1}),(0,l.bF)(he,{span:24},{default:(0,l.k6)(()=>[(0,l.bF)(r,{label:"总分",prop:"total_score"},{default:(0,l.k6)(()=>[(0,l.bF)(me,{modelValue:ve.total_score,"onUpdate:modelValue":t[5]||(t[5]=e=>ve.total_score=e),min:1,max:1e3,style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1})]),_:1})]),_:1},8,["model"])]),_:1},8,["title","modelValue"]),(0,l.bF)(xe,{title:"试题管理",modelValue:O.value,"onUpdate:modelValue":t[8]||(t[8]=e=>O.value=e),width:"850px",fullscreen:!1,"close-on-click-modal":!1},{default:(0,l.k6)(()=>[ee.value?((0,l.uX)(),(0,l.CE)("div",b,[(0,l.Lk)("div",f,[(0,l.Lk)("h3",null,(0,o.v_)(ee.value.title),1),(0,l.Lk)("div",h,[(0,l.bF)(i,{type:"primary",onClick:Ee},{default:(0,l.k6)(()=>t[29]||(t[29]=[(0,l.eW)("新增试题")])),_:1,__:[29]}),(0,l.bF)(i,{type:"success",onClick:Ie},{default:(0,l.k6)(()=>t[30]||(t[30]=[(0,l.eW)("批量导入")])),_:1,__:[30]})])]),(0,l.Lk)("div",y,[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(le,{data:te.value,stripe:"",border:"",style:{width:"100%","margin-top":"15px"},"max-height":550,"show-header":!0},{default:(0,l.k6)(()=>[(0,l.bF)(c,{type:"index",width:"50"}),(0,l.bF)(c,{label:"题目内容","min-width":"300"},{default:(0,l.k6)(e=>[(0,l.Lk)("div",{innerHTML:Te(e.row.question)},null,8,w)]),_:1}),(0,l.bF)(c,{prop:"question_type",label:"题型",width:"100"}),(0,l.bF)(c,{prop:"score",label:"分值",width:"80"}),(0,l.bF)(c,{label:"操作",width:"150",fixed:"right"},{default:(0,l.k6)(e=>[(0,l.bF)(i,{type:"warning",size:"small",onClick:t=>Ke(e.row)},{default:(0,l.k6)(()=>t[31]||(t[31]=[(0,l.eW)("编辑")])),_:2,__:[31]},1032,["onClick"]),(0,l.bF)(i,{type:"danger",size:"small",onClick:t=>Ae(e.row)},{default:(0,l.k6)(()=>t[32]||(t[32]=[(0,l.eW)("删除")])),_:2,__:[32]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[at,ae.value]])])])):(0,l.Q3)("",!0)]),_:1},8,["modelValue"]),(0,l.bF)(xe,{title:"批量导入试题",modelValue:se.value,"onUpdate:modelValue":t[10]||(t[10]=e=>se.value=e),width:"500px"},{footer:(0,l.k6)(()=>[(0,l.Lk)("div",C,[(0,l.bF)(i,{onClick:t[9]||(t[9]=e=>se.value=!1)},{default:(0,l.k6)(()=>t[37]||(t[37]=[(0,l.eW)("取消")])),_:1,__:[37]}),(0,l.bF)(i,{type:"primary",onClick:Ye,loading:ie.value},{default:(0,l.k6)(()=>t[38]||(t[38]=[(0,l.eW)("上传")])),_:1,__:[38]},8,["loading"])])]),default:(0,l.k6)(()=>[(0,l.Lk)("div",x,[(0,l.bF)(Le,{title:"请上传Word格式的试题模板文件",type:"info",closable:!1,"show-icon":"",style:{"margin-bottom":"20px"}}),(0,l.bF)(Oe,{class:"upload-demo",drag:"",action:"#","http-request":Me,"before-upload":Ue,limit:1,"file-list":re.value,"auto-upload":!1,"on-change":Se,accept:".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"},{tip:(0,l.k6)(()=>t[33]||(t[33]=[(0,l.Lk)("div",{class:"el-upload__tip"}," 仅支持 .doc/.docx 格式文件 ",-1)])),default:(0,l.k6)(()=>[(0,l.bF)(Pe,{class:"el-icon--upload"},{default:(0,l.k6)(()=>[(0,l.bF)((0,s.R1)(n.UploadFilled))]),_:1}),t[34]||(t[34]=(0,l.Lk)("div",{class:"el-upload__text"},[(0,l.eW)(" 拖拽文件到此处或 "),(0,l.Lk)("em",null,"点击上传")],-1))]),_:1,__:[34]},8,["file-list"]),(0,l.Lk)("div",F,[t[36]||(t[36]=(0,l.Lk)("span",null,"没有模板？",-1)),(0,l.bF)(i,{type:"primary",link:"",onClick:ze},{default:(0,l.k6)(()=>t[35]||(t[35]=[(0,l.eW)("下载试题模板")])),_:1,__:[35]})])])]),_:1},8,["modelValue"]),(0,l.bF)(xe,{title:G.value,modelValue:J.value,"onUpdate:modelValue":t[17]||(t[17]=e=>J.value=e),width:"700px"},{footer:(0,l.k6)(()=>[(0,l.Lk)("div",$,[(0,l.bF)(i,{onClick:t[16]||(t[16]=e=>J.value=!1)},{default:(0,l.k6)(()=>t[44]||(t[44]=[(0,l.eW)("取消")])),_:1,__:[44]}),(0,l.bF)(i,{type:"primary",onClick:Xe},{default:(0,l.k6)(()=>t[45]||(t[45]=[(0,l.eW)("确定")])),_:1,__:[45]})])]),default:(0,l.k6)(()=>[(0,l.bF)(u,{model:ke,rules:be,ref_key:"questionFormRef",ref:Z,"label-width":"100px"},{default:(0,l.k6)(()=>[(0,l.bF)(r,{label:"题目类型",prop:"question_type"},{default:(0,l.k6)(()=>[(0,l.bF)(Ge,{modelValue:ke.question_type,"onUpdate:modelValue":t[11]||(t[11]=e=>ke.question_type=e),style:{width:"100%"}},{default:(0,l.k6)(()=>[(0,l.bF)(Je,{label:"单选题",value:"单选题"}),(0,l.bF)(Je,{label:"多选题",value:"多选题"}),(0,l.bF)(Je,{label:"判断题",value:"判断题"}),(0,l.bF)(Je,{label:"简答题",value:"简答题"})]),_:1},8,["modelValue"])]),_:1}),(0,l.bF)(r,{label:"分值",prop:"score"},{default:(0,l.k6)(()=>[(0,l.bF)(me,{modelValue:ke.score,"onUpdate:modelValue":t[12]||(t[12]=e=>ke.score=e),min:1,max:100,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),(0,l.bF)(r,{label:"题目内容",prop:"question"},{default:(0,l.k6)(()=>[(0,l.bF)(a,{type:"textarea",modelValue:ke.question,"onUpdate:modelValue":t[13]||(t[13]=e=>ke.question=e),placeholder:"请输入题目内容",rows:"4"},null,8,["modelValue"])]),_:1}),"单选题"===ke.question_type||"多选题"===ke.question_type?((0,l.uX)(),(0,l.CE)(l.FK,{key:0},[(0,l.Lk)("div",V,[t[40]||(t[40]=(0,l.Lk)("h4",null,"选项",-1)),(0,l.bF)(i,{type:"primary",size:"small",onClick:De},{default:(0,l.k6)(()=>t[39]||(t[39]=[(0,l.eW)("添加选项")])),_:1,__:[39]})]),((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(ke.options,(e,o)=>((0,l.uX)(),(0,l.Wv)(r,{key:o,label:"选项 "+String.fromCharCode(65+o)},{default:(0,l.k6)(()=>[(0,l.Lk)("div",q,[(0,l.bF)(a,{modelValue:e.text,"onUpdate:modelValue":t=>e.text=t,placeholder:"请输入选项内容"},null,8,["modelValue","onUpdate:modelValue"]),(0,l.bF)(Ze,{modelValue:e.isCorrect,"onUpdate:modelValue":t=>e.isCorrect=t},{default:(0,l.k6)(()=>t[41]||(t[41]=[(0,l.eW)("正确答案")])),_:2,__:[41]},1032,["modelValue","onUpdate:modelValue"]),(0,l.bF)(i,{type:"danger",icon:"Delete",circle:"",size:"small",onClick:e=>Qe(o)},null,8,["onClick"])])]),_:2},1032,["label"]))),128))],64)):"判断题"===ke.question_type?((0,l.uX)(),(0,l.Wv)(r,{key:1,label:"正确答案",prop:"correct_answer"},{default:(0,l.k6)(()=>[(0,l.bF)(tt,{modelValue:ke.correct_answer,"onUpdate:modelValue":t[14]||(t[14]=e=>ke.correct_answer=e)},{default:(0,l.k6)(()=>[(0,l.bF)(et,{label:"正确"},{default:(0,l.k6)(()=>t[42]||(t[42]=[(0,l.eW)("正确")])),_:1,__:[42]}),(0,l.bF)(et,{label:"错误"},{default:(0,l.k6)(()=>t[43]||(t[43]=[(0,l.eW)("错误")])),_:1,__:[43]})]),_:1},8,["modelValue"])]),_:1})):((0,l.uX)(),(0,l.Wv)(r,{key:2,label:"参考答案",prop:"correct_answer"},{default:(0,l.k6)(()=>[(0,l.bF)(a,{type:"textarea",modelValue:ke.correct_answer,"onUpdate:modelValue":t[15]||(t[15]=e=>ke.correct_answer=e),placeholder:"请输入参考答案",rows:"3"},null,8,["modelValue"])]),_:1}))]),_:1},8,["model"])]),_:1},8,["title","modelValue"]),(0,l.bF)(xe,{title:"考试成绩",modelValue:ne.value,"onUpdate:modelValue":t[18]||(t[18]=e=>ne.value=e),width:"800px"},{default:(0,l.k6)(()=>[ee.value?((0,l.uX)(),(0,l.CE)("div",R,[(0,l.Lk)("h3",null,(0,o.v_)(ee.value.title),1),(0,l.bF)(d,{class:"statistics-card"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",W,[(0,l.Lk)("div",L,[t[46]||(t[46]=(0,l.Lk)("div",{class:"statistics-label"},"总人数",-1)),(0,l.Lk)("div",E,(0,o.v_)(ce.total_students||0),1)]),(0,l.Lk)("div",K,[t[47]||(t[47]=(0,l.Lk)("div",{class:"statistics-label"},"及格人数",-1)),(0,l.Lk)("div",A,(0,o.v_)(ce.passed_students||0),1)]),(0,l.Lk)("div",I,[t[48]||(t[48]=(0,l.Lk)("div",{class:"statistics-label"},"及格率",-1)),(0,l.Lk)("div",U,(0,o.v_)(ce.pass_rate||0)+"%",1)]),(0,l.Lk)("div",S,[t[49]||(t[49]=(0,l.Lk)("div",{class:"statistics-label"},"平均分",-1)),(0,l.Lk)("div",M,(0,o.v_)(ce.average_score||0),1)])])]),_:1}),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(le,{data:ue.value,stripe:"",border:"",style:{width:"100%","margin-top":"15px"}},{default:(0,l.k6)(()=>[(0,l.bF)(c,{type:"index",width:"50"}),(0,l.bF)(c,{prop:"student_name",label:"学生姓名"}),(0,l.bF)(c,{prop:"score",label:"得分",width:"100",sortable:""},{default:(0,l.k6)(e=>[(0,l.Lk)("span",{class:(0,o.C4)(e.row.score>=ee.value.pass_score?"pass-score":"fail-score")},(0,o.v_)(e.row.score),3)]),_:1}),(0,l.bF)(c,{label:"考试时间",width:"160"},{default:(0,l.k6)(e=>[(0,l.eW)((0,o.v_)((0,s.R1)(p.Y)(e.row.exam_date,"YYYY-MM-DD HH:mm")),1)]),_:1}),(0,l.bF)(c,{label:"状态",width:"100"},{default:(0,l.k6)(e=>[(0,l.bF)(j,{type:e.row.score>=ee.value.pass_score?"success":"danger"},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(e.row.score>=ee.value.pass_score?"及格":"不及格"),1)]),_:2},1032,["type"])]),_:1}),(0,l.bF)(c,{label:"操作",width:"100",fixed:"right"},{default:(0,l.k6)(e=>[(0,l.bF)(i,{type:"primary",size:"small",onClick:t=>Be(e.row)},{default:(0,l.k6)(()=>t[50]||(t[50]=[(0,l.eW)("详情")])),_:2,__:[50]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[at,de.value]])])):(0,l.Q3)("",!0)]),_:1},8,["modelValue"])])}}},z=a(1241);const D=(0,z.A)(Y,[["__scopeId","data-v-4a32efa4"]]);var Q=D},6434:function(e,t,a){var l=a(653);t.A={getExams(e){return l.A.get("/api/exams",{params:e})},getExam(e){return l.A.get(`/api/exams/${e}`)},createExam(e){return l.A.post("/api/exams",e)},updateExam(e,t){return l.A.put(`/api/exams/${e}`,t)},deleteExam(e){return l.A.delete(`/api/exams/${e}`)},getExamQuestions(e){return l.A.get(`/api/exams/${e}/questions`)},createQuestion(e,t){return l.A.post(`/api/exams/${e}/questions`,t)},updateQuestion(e,t){return l.A.put(`/api/exams/questions/${e}`,t)},deleteQuestion(e){return l.A.delete(`/api/exams/questions/${e}`)},importQuestions(e,t){return l.A.post(`/api/exams/${e}/questions/import`,t)},importQuestionsFromWord(e,t){return l.A.post(`/api/exams/${e}/questions/import-word`,t,{headers:{"Content-Type":"multipart/form-data"}})},submitExam(e){return l.A.post(`/api/exams/${e.exam_id}/submit`,e)},getExamResults(e,t){return l.A.get(`/api/exams/${e}/results`)},getStudentResults(e){return l.A.get(`/api/exams/student/${e}/results`)},getTeacherResults(e){return l.A.get(`/api/exams/teacher/${e}/results`)}}},9634:function(e,t,a){function l(e,t="YYYY-MM-DD HH:mm:ss"){if(console.log(e),!e)return"";const a=new Date(e);if(console.log(a),isNaN(a.getTime()))return"";const l={"M+":a.getMonth()+1,"D+":a.getDate(),"H+":a.getHours(),"m+":a.getMinutes(),"s+":a.getSeconds(),"q+":Math.floor((a.getMonth()+3)/3),S:a.getMilliseconds()};/(Y+)/.test(t)&&(t=t.replace(RegExp.$1,(a.getFullYear()+"").substring(4-RegExp.$1.length)));for(let o in l)new RegExp("("+o+")").test(t)&&(t=t.replace(RegExp.$1,1===RegExp.$1.length?l[o]:("00"+l[o]).substring((""+l[o]).length)));return console.log(t),t}a.d(t,{Y:function(){return l}})}}]);
//# sourceMappingURL=955.20f7dbf6.js.map