const { Exam, ExamQuestion, ExamResult } = require('../models/examModel');
const fs = require('fs');
const path = require('path');
const mammoth = require('mammoth');
const multer = require('multer');
const util = require('util');
const unlinkFile = util.promisify(fs.unlink);

// 配置文件上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, 'uploads/exams/');
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + path.extname(file.originalname));
  }
});

// 文件过滤，只允许 .docx 和 .doc 文件
const fileFilter = (req, file, cb) => {
  // 检查MIME类型
  if (file.mimetype === 'application/msword' || 
      file.mimetype === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    
    // 检查文件扩展名
    const ext = path.extname(file.originalname).toLowerCase();
    if (ext !== '.doc' && ext !== '.docx') {
      return cb(new Error('文件扩展名必须是 .doc 或 .docx'), false);
    }
    
    cb(null, true);
  } else {
    cb(new Error('仅支持 .doc 或 .docx 格式的文件'), false);
  }
};

const upload = multer({ 
  storage: storage,
  fileFilter: fileFilter,
  limits: { fileSize: 10 * 1024 * 1024 } // 10MB 限制
});

// 获取所有考试
exports.getAllExams = async (req, res) => {
  try {
    const { page, limit, title } = req.query;
    
    // Create filter object
    const filter = {};
    if (title) {
      console.log(title,  'title');
      // Just pass the title directly - the model will handle the LIKE formatting
      filter.title = title;
    }
    
    // Get exams with pagination and filters
    const exams = await Exam.findAll(filter, page, limit);
    
    // Get total count for pagination
    const total = await Exam.count(filter);
    
    res.status(200).json({
      success: true,
      data: exams,
      total
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取考试列表失败',
      error: error.message
    });
  }
};

// 获取单个考试信息
exports.getExamById = async (req, res) => {
  try {
    const exam = await Exam.findById(req.params.id);
    if (!exam) {
      return res.status(404).json({
        success: false,
        message: '未找到该考试'
      });
    }
    
    res.status(200).json({
      success: true,
      data: exam
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取考试信息失败',
      error: error.message
    });
  }
};

// 创建考试
exports.createExam = async (req, res) => {
  try {
    // 验证必要字段
    const { title, duration, pass_score, total_score } = req.body;
    if (!title || !duration || !pass_score || !total_score) {
      return res.status(400).json({
        success: false,
        message: '请提供完整的考试信息（标题、时长、及格分数、总分）'
      });
    }
    
    const examId = await Exam.create(req.body);
    
    const exam = await Exam.findById(examId);
    
    res.status(201).json({
      success: true,
      message: '考试创建成功',
      data: exam
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建考试失败',
      error: error.message
    });
  }
};

// 更新考试信息
exports.updateExam = async (req, res) => {
  try {
    const exam = await Exam.findById(req.params.id);
    if (!exam) {
      return res.status(404).json({
        success: false,
        message: '未找到该考试'
      });
    }
    
    const updated = await Exam.update(req.params.id, req.body);
    
    if (updated) {
      const updatedExam = await Exam.findById(req.params.id);
      res.status(200).json({
        success: true,
        message: '考试信息更新成功',
        data: updatedExam
      });
    } else {
      res.status(500).json({
        success: false,
        message: '考试信息更新失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新考试信息失败',
      error: error.message
    });
  }
};

// 删除考试
exports.deleteExam = async (req, res) => {
  try {
    const exam = await Exam.findById(req.params.id);
    if (!exam) {
      return res.status(404).json({
        success: false,
        message: '未找到该考试'
      });
    }
    
    const deleted = await Exam.delete(req.params.id);
    
    if (deleted) {
      res.status(200).json({
        success: true,
        message: '考试删除成功'
      });
    } else {
      res.status(500).json({
        success: false,
        message: '考试删除失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除考试失败',
      error: error.message
    });
  }
};

// 获取考试题目列表
exports.getExamQuestions = async (req, res) => {
  try {
    const examId = req.params.examId;
    
    // 检查考试是否存在
    const exam = await Exam.findById(examId);
    if (!exam) {
      return res.status(404).json({
        success: false,
        message: '未找到该考试'
      });
    }
    
    const questions = await ExamQuestion.findByExamId(examId);
    
    res.status(200).json({
      success: true,
      count: questions.length,
      data: questions
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取考试题目失败',
      error: error.message
    });
  }
};

// 创建考试题目
exports.createExamQuestion = async (req, res) => {
  try {
    const examId = req.params.examId;
    
    // 检查考试是否存在
    const exam = await Exam.findById(examId);
    if (!exam) {
      return res.status(404).json({
        success: false,
        message: '未找到该考试'
      });
    }
    
    // 验证必要字段
    const { question, correct_answer, question_type } = req.body;
    if (!question || !correct_answer || !question_type) {
      return res.status(400).json({
        success: false,
        message: '请提供完整的题目信息（题目内容、正确答案、题目类型）'
      });
    }
    
    // 为题目设置考试ID
    req.body.exam_id = examId;
    
    const questionId = await ExamQuestion.create(req.body);
    
    const createdQuestion = await ExamQuestion.findById(questionId);
    
    res.status(201).json({
      success: true,
      message: '考试题目创建成功',
      data: createdQuestion
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建考试题目失败',
      error: error.message
    });
  }
};

// 批量导入考试题目
exports.importExamQuestions = async (req, res) => {
  try {
    const examId = req.params.examId;
    
    // 检查考试是否存在
    const exam = await Exam.findById(examId);
    if (!exam) {
      return res.status(404).json({
        success: false,
        message: '未找到该考试'
      });
    }
    
    // 验证请求体是否为数组
    if (!Array.isArray(req.body)) {
      return res.status(400).json({
        success: false,
        message: '请提供题目数组'
      });
    }
    
    // 验证每个题目的必要字段
    for (const question of req.body) {
      if (!question.question || !question.correct_answer || !question.question_type) {
        return res.status(400).json({
          success: false,
          message: '每个题目必须包含题目内容、正确答案和题目类型'
        });
      }
    }
    
    // 批量导入题目
    await ExamQuestion.batchCreate(req.body, examId);
    
    // 获取导入后的题目列表
    const questions = await ExamQuestion.findByExamId(examId);
    
    res.status(201).json({
      success: true,
      message: `成功导入 ${req.body.length} 道题目`,
      count: questions.length,
      data: questions
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '批量导入考试题目失败',
      error: error.message
    });
  }
};

// 更新考试题目
exports.updateExamQuestion = async (req, res) => {
  try {
    const questionId = req.params.id;
    
    // 检查题目是否存在
    const question = await ExamQuestion.findById(questionId);
    if (!question) {
      return res.status(404).json({
        success: false,
        message: '未找到该题目'
      });
    }
    
    const updated = await ExamQuestion.update(questionId, req.body);
    
    if (updated) {
      const updatedQuestion = await ExamQuestion.findById(questionId);
      res.status(200).json({
        success: true,
        message: '题目更新成功',
        data: updatedQuestion
      });
    } else {
      res.status(500).json({
        success: false,
        message: '题目更新失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新题目失败',
      error: error.message
    });
  }
};

// 删除考试题目
exports.deleteExamQuestion = async (req, res) => {
  try {
    const questionId = req.params.id;
    
    // 检查题目是否存在
    const question = await ExamQuestion.findById(questionId);
    if (!question) {
      return res.status(404).json({
        success: false,
        message: '未找到该题目'
      });
    }
    
    const deleted = await ExamQuestion.delete(questionId);
    
    if (deleted) {
      res.status(200).json({
        success: true,
        message: '题目删除成功'
      });
    } else {
      res.status(500).json({
        success: false,
        message: '题目删除失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除题目失败',
      error: error.message
    });
  }
};

// 提交考试答案并自动判卷
exports.submitExam = async (req, res) => {
  try {
    const { teacher_id, exam_id, answers } = req.body;
    
    if (!teacher_id || !exam_id || !answers) {
      return res.status(400).json({
        success: false,
        message: '请提供教师ID、考试ID和答案'
      });
    }
    
    // 检查考试尝试次数
    const existingAttempts = await ExamResult.findByTeacherAndExam(teacher_id, exam_id);
    
    // 如果已经有2次及以上的尝试，拒绝提交
    if (existingAttempts.length >= 2) {
      return res.status(403).json({
        success: false,
        message: '您已达到该考试的最大尝试次数（2次）'
      });
    }
    
    // 获取考试题目
    const questions = await ExamQuestion.findByExamId(exam_id);
    if (questions.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到考试题目'
      });
    }
    
    // 自动判卷
    let totalScore = 0;
    let correctCount = 0;
    
    for (const answer of answers) {
      const question = questions.find(q => q.id === answer.question_id);
      
      if (question) {
        // 判断答案是否正确
        const isCorrect = question.correct_answer === answer.answer;
        answer.is_correct = isCorrect;
        
        if (isCorrect) {
          totalScore += question.score;
          correctCount++;
        }
      }
    }
    
    // 获取考试信息
    const exam = await Exam.findById(exam_id);
    
    // 保存考试结果
    const resultData = {
      teacher_id,
      exam_id,
      score: totalScore,
      answers: answers,
      attempt_number: existingAttempts.length + 1
    };
    
    const resultId = await ExamResult.create(resultData);
    const result = await ExamResult.findById(resultId);
    
    // 计算剩余尝试次数
    const remainingAttempts = Math.max(0, 2 - (existingAttempts.length + 1));
    
    res.status(200).json({
      success: true,
      message: '考试提交成功',
      data: {
        result,
        summary: {
          total_questions: questions.length,
          correct_questions: correctCount,
          score: totalScore,
          pass_score: exam.pass_score,
          is_passed: totalScore >= exam.pass_score,
          attempt_number: existingAttempts.length + 1,
          remaining_attempts: remainingAttempts
        }
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '提交考试失败',
      error: error.message
    });
  }
};

// 获取考试成绩汇总
exports.getExamResults = async (req, res) => {
  try {
    const examId = req.params.examId;
    
    // 检查考试是否存在
    const exam = await Exam.findById(examId);
    if (!exam) {
      return res.status(404).json({
        success: false,
        message: '未找到该考试'
      });
    }
    
    // 获取考试的所有成绩
    let results = await ExamResult.findByExamId(examId);
    
    // 如果指定了教师ID参数，只返回该教师的成绩
    const teacherId = req.query.teacher_id;
    if (teacherId) {
      // 过滤出教师的成绩
      results = results.filter(r => r.teacher_id === parseInt(teacherId));
    }
    
    // 统计信息
    const totalTeachers = results.length;
    const passedTeachers = results.filter(r => r.score >= exam.pass_score).length;
    const averageScore = totalTeachers > 0 ? 
      results.reduce((sum, r) => sum + r.score, 0) / totalTeachers : 0;
    
    res.status(200).json({
      success: true,
      data: {
        exam: {
          id: exam.id,
          title: exam.title,
          pass_score: exam.pass_score,
          total_score: exam.total_score
        },
        summary: {
          total_teachers: totalTeachers,
          passed_teachers: passedTeachers,
          pass_rate: totalTeachers > 0 ? (passedTeachers / totalTeachers * 100).toFixed(2) : 0,
          average_score: averageScore.toFixed(2)
        },
        results: results
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取考试成绩汇总失败',
      error: error.message
    });
  }
}; 

// 获取教师的所有考试成绩
exports.getTeacherResults = async (req, res) => {
  try {
    const teacherId = req.params.teacherId;
    
    // 验证教师ID
    if (!teacherId) {
      return res.status(400).json({
        success: false,
        message: '请提供教师ID'
      });
    }
    
    // 获取教师的所有考试结果
    const results = await ExamResult.findByTeacherId(teacherId);
    
    // 处理结果，按考试分组
    const examResults = {};
    for (const result of results) {
      if (!examResults[result.exam_id]) {
        // 获取考试信息
        const exam = await Exam.findById(result.exam_id);
        if (exam) {
          examResults[result.exam_id] = {
            exam: {
              id: exam.id,
              title: exam.title,
              pass_score: exam.pass_score,
              total_score: exam.total_score
            },
            attempts: []
          };
        }
      }
      
      if (examResults[result.exam_id]) {
        // 添加当前尝试结果
        examResults[result.exam_id].attempts.push({
          id: result.id,
          score: result.score,
          is_passed: result.score >= examResults[result.exam_id].exam.pass_score,
          exam_date: result.exam_date
        });
      }
    }
    
    // 添加统计信息
    const examIds = Object.keys(examResults);
    const passedExams = examIds.filter(id => 
      examResults[id].attempts.some(attempt => attempt.is_passed)
    ).length;
    
    const totalAttempts = results.length;
    const averageScore = totalAttempts > 0 ? 
      results.reduce((sum, r) => sum + r.score, 0) / totalAttempts : 0;
    
    res.status(200).json({
      success: true,
      data: {
        summary: {
          total_exams: examIds.length,
          passed_exams: passedExams,
          pass_rate: examIds.length > 0 ? (passedExams / examIds.length * 100).toFixed(2) : 0,
          total_attempts: totalAttempts,
          average_score: averageScore.toFixed(2)
        },
        results: examResults
      }
    });
    
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取教师考试成绩失败',
      error: error.message
    });
  }
}; 

// 根据考试类型获取考试列表
exports.getExamsByType = async (req, res) => {
  try {
    const examType = req.params.examType;
    
    const exams = await Exam.findByType(examType);
    
    res.status(200).json({
      success: true,
      count: exams.length,
      data: exams
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取考试类型列表失败',
      error: error.message
    });
  }
}; 

// 从Word文档导入考试题目
exports.importQuestionsFromWord = async (req, res) => {
  try {
    // 使用multer处理单文件上传
    const uploadMiddleware = upload.single('template');
    
    uploadMiddleware(req, res, async function(err) {
      if (err) {
        return res.status(400).json({
          success: false,
          message: err.message
        });
      }
      
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: '请上传试题模板文件'
        });
      }

      const examId = req.params.examId;
      
      // 检查考试是否存在
      const exam = await Exam.findById(examId);
      if (!exam) {
        // 删除上传的文件
        await unlinkFile(req.file.path);
        return res.status(404).json({
          success: false,
          message: '未找到该考试'
        });
      }
      
      try {
        // 检查文件是否存在及可访问
        try {
          await fs.promises.access(req.file.path, fs.constants.R_OK);
        } catch (fileErr) {
          return res.status(500).json({
            success: false,
            message: '无法访问上传的文件',
            error: fileErr.message
          });
        }

        // 检查文件大小
        const stats = await fs.promises.stat(req.file.path);
        if (stats.size === 0) {
          await unlinkFile(req.file.path);
          return res.status(400).json({
            success: false,
            message: '上传的文件为空'
          });
        }

        // 使用mammoth将docx转换为纯文本
        try {
          const result = await mammoth.extractRawText({ path: req.file.path });
          console.log(result);
          const text = result.value;
          
          if (!text || text.trim() === '') {
            await unlinkFile(req.file.path);
            return res.status(400).json({
              success: false,
              message: '文档中没有找到文本内容'
            });
          }
          
          // 删除上传的文件
          await unlinkFile(req.file.path);
          
          // 解析文本内容，提取题目
          const questions = parseQuestions(text);
          console.log(questions,'questions');
         
          
          if (questions.length === 0) {
            return res.status(400).json({
              success: false,
              message: '无法从文件中解析出有效的试题'
            });
          }
          
          // 批量导入题目
          // 处理题目类型格式，转换为数据库枚举值
          const formattedQuestions = questions.map(q => {
            const questionCopy = {...q};
            // 将英文题型转换为中文枚举值
            if (q.question_type === 'single_choice') {
              questionCopy.question_type = '单选题';
            } else if (q.question_type === 'multiple_choice') {
              questionCopy.question_type = '多选题';
            } else if (q.question_type === 'true_false') {
              questionCopy.question_type = '判断题';
            } else {
              questionCopy.question_type = '简答题';
            }
            return questionCopy;
          });
          // console.log(formattedQuestions,'formattedQuestions');
          await ExamQuestion.batchCreate(formattedQuestions, examId);
          
          // 获取导入后的题目列表
          const importedQuestions = await ExamQuestion.findByExamId(examId);
          
          res.status(201).json({
            success: true,
            message: `成功导入 ${questions.length} 道题目`,
            count: importedQuestions.length,
            data: {
              parsed: questions,
              saved: importedQuestions
            }
          });
        } catch (mammothError) {
          console.error('mammoth解析错误:', mammothError);
          
          // 尝试删除临时文件
          try {
            await unlinkFile(req.file.path);
          } catch (unlinkError) {
            console.error('删除临时文件失败:', unlinkError);
          }
          
          return res.status(400).json({
            success: false,
            message: '解析Word文档失败，请确保上传的是有效的Word文档(.doc或.docx)',
            error: mammothError.message
          });
        }
      } catch (error) {
        console.error('解析文件失败:', error);
        
        // 尝试删除临时文件
        try {
          await unlinkFile(req.file.path);
        } catch (unlinkError) {
          console.error('删除临时文件失败:', unlinkError);
        }
        
        res.status(500).json({
          success: false,
          message: '解析文件失败',
          error: error.message
        });
      }
    });
  } catch (error) {
    console.error('导入试题失败:', error);
    res.status(500).json({
      success: false,
      message: '导入试题失败',
      error: error.message
    });
  }
};

// 解析文本内容，提取题目
function parseQuestions(text) {
  const questions = [];
  
  // 分割文本为不同部分
  const parts = text.split(/(?=一、|二、|三、|四、|五、)/g);
  
  for (const part of parts) {
    if (part.includes('单选题')) {
      // 解析单选题
      const choiceQuestions = parseChoiceQuestions(part);
      questions.push(...choiceQuestions);
    } else if (part.includes('是非题')) {
      // 解析是非题
      const trueFalseQuestions = parseTrueFalseQuestions(part);
      questions.push(...trueFalseQuestions);
    }
  }
  
  return questions;
}

// 解析单选题
function parseChoiceQuestions(text) {
  const questions = [];
  
  // 先按题号分割得到每一题的内容块
  const questionBlocks = text.split(/(?=\d+[\.、][^\.、\d])/g);
  
  for (const block of questionBlocks) {
    if (!block.trim()) continue;
    
    // 匹配题号和题目文本
    const questionMatch = block.match(/^(\d+)[\.、]([^\n]+)/);
    if (!questionMatch) continue;
    
    const questionNumber = questionMatch[1];
    const questionText = questionMatch[2].trim();
    
    // 提取所有选项和答案
    const optionsAndAnswerMatch = block.substring(questionMatch[0].length);
    
    // 提取所有选项 (A-Z)
    const optionsRegex = /([A-Z])[\.\、]([^\n]+?)(?=\s*\n|\s+[A-Z][\.\、]|\s+答案[：:]|$)/g;
    const options = [];
    let optionMatch;
    
    while ((optionMatch = optionsRegex.exec(optionsAndAnswerMatch)) !== null) {
      const key = optionMatch[1]; // 例如 'A'
      const text = optionMatch[2].trim(); // 选项文本
      options.push({ key, text });
    }
    
    // 如果没有提取到选项，跳过这个题目
    if (options.length === 0) continue;
    
    // 提取答案
    const answerMatch = optionsAndAnswerMatch.match(/答案[：:][\s\n]*([A-Z])/i);
    if (!answerMatch) continue;
    
    const correctAnswer = answerMatch[1].trim();
    
    questions.push({
      question: questionText,
      options,
      correct_answer: correctAnswer,
      question_type: 'single_choice',
      score: 5 // 默认分数
    });
  }
  
  // 如果没有匹配到任何题目，尝试另一种解析方法
  if (questions.length === 0) {
    // 另一种解析逻辑：根据行内容判断题目、选项和答案
    const lines = text.split(/\n/);
    let currentQuestion = null;
    let currentOptions = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;
      
      // 匹配新题目
      const questionMatch = line.match(/^(\d+)[\.、](.+)/);
      if (questionMatch) {
        // 如果之前有未完成的题目，先保存
        if (currentQuestion && currentOptions.length > 0) {
          // 寻找该题的答案
          let correctAnswer = null;
          for (let j = i; j < Math.min(i + 5, lines.length); j++) {
            const answerMatch = lines[j].match(/答案[：:][\s\n]*([A-Z])/i);
            if (answerMatch) {
              correctAnswer = answerMatch[1].trim();
              break;
            }
          }
          
          if (correctAnswer) {
            questions.push({
              question: currentQuestion,
              options: currentOptions,
              correct_answer: correctAnswer,
              question_type: 'single_choice',
              score: 5
            });
          }
        }
        
        // 开始新题目
        currentQuestion = questionMatch[2].trim();
        currentOptions = [];
        continue;
      }
      
      // 匹配选项
      const optionMatch = line.match(/^([A-Z])[\.\、](.+)/);
      if (optionMatch && currentQuestion) {
        currentOptions.push({
          key: optionMatch[1],
          text: optionMatch[2].trim()
        });
        continue;
      }
      
      // 匹配答案并完成当前题目
      const answerMatch = line.match(/^答案[：:][\s\n]*([A-Z])/i);
      if (answerMatch && currentQuestion && currentOptions.length > 0) {
        const correctAnswer = answerMatch[1].trim();
        
        questions.push({
          question: currentQuestion,
          options: currentOptions,
          correct_answer: correctAnswer,
          question_type: 'single_choice',
          score: 5
        });
        
        // 重置
        currentQuestion = null;
        currentOptions = [];
      }
    }
  }
  
  return questions;
}

// 解析是非题
function parseTrueFalseQuestions(text) {
  const questions = [];
  
  // 匹配是非题格式：数字.问题文本 答案:对/错
  const regex = /(\d+)[\.、]([^\n]+?)(?:\s*\n|\s+)(?:答案[：:]\s*)(对|错|√|×|正确|错误|true|false)/gi;
  
  let match;
  while ((match = regex.exec(text)) !== null) {
    const questionNumber = match[1];
    const questionText = match[2].trim();
    const answerText = match[3].trim();
    
    // 标准化答案格式
    let correctAnswer;
    if (['对', '√', '正确', 'true'].includes(answerText.toLowerCase())) {
      correctAnswer = 'true';
    } else {
      correctAnswer = 'false';
    }
    
    questions.push({
      question: questionText,
      options: [
        { key: 'true', text: '正确' },
        { key: 'false', text: '错误' }
      ],
      correct_answer: correctAnswer,
      question_type: 'true_false',
      score: 5 // 默认分数
    });
  }
  
  // 如果上面的正则表达式没有匹配到任何题目，尝试另一种格式
  if (questions.length === 0) {
    // 先按题号分割
    const questionsBlocks = text.split(/\d+[\.\、]/g).slice(1);
    
    for (let i = 0; i < questionsBlocks.length; i++) {
      const block = questionsBlocks[i].trim();
      
      // 查找答案部分
      const answerMatch = block.match(/答案[：:]\s*(对|错|√|×|正确|错误|true|false)/i);
      
      if (answerMatch) {
        // 提取问题文本（答案部分之前的内容）
        const questionText = block.substring(0, answerMatch.index).trim();
        const answerText = answerMatch[1];
        
        // 标准化答案格式
        let correctAnswer;
        if (['对', '√', '正确', 'true'].includes(answerText.toLowerCase())) {
          correctAnswer = 'true';
        } else {
          correctAnswer = 'false';
        }
        
        questions.push({
          question: questionText,
          options: [
            { key: 'true', text: '正确' },
            { key: 'false', text: '错误' }
          ],
          correct_answer: correctAnswer,
          question_type: 'true_false',
          score: 5 // 默认分数
        });
      }
    }
  }
  
  return questions;
} 