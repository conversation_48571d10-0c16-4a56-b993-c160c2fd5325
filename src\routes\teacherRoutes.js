const express = require('express');
const router = express.Router();
const { pool } = require('../config/db');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const XLSX = require('xlsx');

// 配置头像上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../../uploads/photos');
    // 确保目录存在
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'teacher-' + uniqueSuffix + ext);
  }
});

const upload = multer({ storage: storage });

// 配置Excel文件上传
const excelStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../../uploads/excel');
    // 确保目录存在
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'teachers-import-' + uniqueSuffix + ext);
  }
});

const uploadExcel = multer({
  storage: excelStorage,
  fileFilter: function (req, file, cb) {
    // 只允许Excel文件
    const allowedTypes = ['.xlsx', '.xls'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('只支持Excel文件格式(.xlsx, .xls)'));
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 限制文件大小为10MB
  }
});

// 上传教师照片专用接口
router.post('/upload/photo', upload.single('photo'), (req, res) => {
  if (!req.file) {
    return res.status(400).json({
      success: false,
      message: '未上传文件'
    });
  }
  
  const photoPath = `/uploads/photos/${req.file.filename}`;
  
  res.status(200).json({
    success: true,
    message: '文件上传成功',
    path: photoPath
  });
});

// 获取所有教师 - 添加搜索和分页功能
router.get('/', async (req, res, next) => {
  try {
    const { name, department, is_employed, page = 1, limit = 10 } = req.query;
    
    // 构建查询条件
    const queryParams = [];
    let whereClause = '';
    
    if (name) {
      whereClause += whereClause ? ' AND ' : ' WHERE ';
      whereClause += 'name LIKE ?';
      queryParams.push(`%${name}%`);
    }
    
    if (department) {
      whereClause += whereClause ? ' AND ' : ' WHERE ';
      whereClause += 'department LIKE ?';
      queryParams.push(`%${department}%`);
    }
    
    if (is_employed !== undefined && is_employed !== '') {
      whereClause += whereClause ? ' AND ' : ' WHERE ';
      whereClause += 'is_employed = ?';
      queryParams.push(is_employed);
    }
    
    // 计算总数
    const [countResult] = await pool.query(
      `SELECT COUNT(*) as total FROM teachers${whereClause}`, 
      queryParams
    );
    const total = countResult[0].total;
    
    // 分页参数
    const offset = (page - 1) * limit;
    const pageLimit = parseInt(limit);
    
    // 查询数据
    const [rows] = await pool.query(
      `SELECT * FROM teachers${whereClause}
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`, 
      [...queryParams, pageLimit, offset]
    );
    
    res.status(200).json({
      success: true,
      count: total,
      data: rows,
      page: parseInt(page),
      limit: pageLimit,
      totalPages: Math.ceil(total / pageLimit)
    });
  } catch (error) {
    next(error);
  }
});

// 获取单个教师信息
router.get('/:id', async (req, res, next) => {
  try {
    const [rows] = await pool.query(`
      SELECT * FROM teachers WHERE id = ?
    `, [req.params.id]);
    
    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该教师'
      });
    }
    
    res.status(200).json({
      success: true,
      data: rows[0]
    });
  } catch (error) {
    next(error);
  }
});

// 创建教师
router.post('/', upload.single('photo'), async (req, res, next) => {
  try {
    console.log('创建教师请求体:', req.body);
    console.log('上传的文件:', req.file);
    
    const {
      name,
      gender,
      department,
      school,
      major,
      education,
      is_employed,
      employment_period,
      phone,
      photo
    } = req.body;
    
    // 检查必填项
    if (!name || !gender || !department || !school || !major || !education) {
      return res.status(400).json({
        success: false,
        message: '请填写必要的教师信息'
      });
    }
    
    // 处理头像路径
    let photoPath = null;
    if (req.file) {
      // 如果是通过文件上传的照片
      photoPath = `/uploads/photos/${req.file.filename}`;
      console.log('从文件中获取的照片路径:', photoPath);
    } else if (photo) {
      // 如果通过JSON请求体提供了photo字段
      photoPath = photo;
      console.log('从请求体获取的照片路径:', photoPath);
    }
    
    console.log('最终使用的照片路径:', photoPath);
    
    const [result] = await pool.query(`
      INSERT INTO teachers (
        name, gender, department, school, major, education,
        is_employed, employment_period, phone, photo
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      name, 
      gender, 
      department, 
      school, 
      major, 
      education,
      is_employed === 'true' || is_employed === '1' || is_employed === 1 ? 1 : 0,
      employment_period || null,
      phone || null,
      photoPath
    ]);
    
    res.status(201).json({
      success: true,
      message: '教师创建成功',
      data: {
        id: result.insertId,
        name,
        gender,
        department,
        school,
        major,
        education,
        is_employed: is_employed === 'true' || is_employed === '1' || is_employed === 1 ? 1 : 0,
        employment_period: employment_period || null,
        phone: phone || null,
        photo: photoPath
      }
    });
  } catch (error) {
    console.error('创建教师失败:', error);
    next(error);
  }
});

// 更新教师信息
router.put('/:id', upload.single('photo'), async (req, res, next) => {
  try {
    const {
      name,
      gender,
      department,
      school,
      major,
      education,
      is_employed,
      employment_period,
      phone,
      photo
    } = req.body;
    
    // 检查必填项
    if (!name || !gender || !department || !school || !major || !education) {
      return res.status(400).json({
        success: false,
        message: '请填写必要的教师信息'
      });
    }
    
    // 获取当前教师信息
    const [currentTeacher] = await pool.query(`
      SELECT * FROM teachers WHERE id = ?
    `, [req.params.id]);
    
    if (currentTeacher.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该教师'
      });
    }
    
    // 处理头像路径
    let photoPath = currentTeacher[0].photo;
    if (req.file) {
      photoPath = `/uploads/photos/${req.file.filename}`;
      
      // 删除旧头像
      if (currentTeacher[0].photo) {
        const oldPhotoPath = path.join(__dirname, '../..', currentTeacher[0].photo);
        if (fs.existsSync(oldPhotoPath)) {
          fs.unlinkSync(oldPhotoPath);
        }
      }
    } else if (photo !== undefined) {
      // 如果通过请求体传递了photo字段
      photoPath = photo;
    }
    
    await pool.query(`
      UPDATE teachers 
      SET name = ?, gender = ?, department = ?, school = ?, major = ?,
          education = ?, is_employed = ?, employment_period = ?, phone = ?,
          photo = ?, updated_at = NOW()
      WHERE id = ?
    `, [
      name, 
      gender, 
      department, 
      school, 
      major, 
      education,
      is_employed === 'true' || is_employed === '1' || is_employed === 1 ? 1 : 0,
      employment_period || null,
      phone || null,
      photoPath,
      req.params.id
    ]);
    
    res.status(200).json({
      success: true,
      message: '教师信息更新成功',
      data: {
        id: parseInt(req.params.id),
        name,
        gender,
        department,
        school,
        major,
        education,
        is_employed: is_employed === 'true' || is_employed === '1' || is_employed === 1 ? 1 : 0,
        employment_period: employment_period || null,
        phone: phone || null,
        photo: photoPath
      }
    });
  } catch (error) {
    next(error);
  }
});

// 删除教师
router.delete('/:id', async (req, res, next) => {
  try {
    // 获取当前教师信息
    const [currentTeacher] = await pool.query(`
      SELECT * FROM teachers WHERE id = ?
    `, [req.params.id]);
    
    if (currentTeacher.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该教师'
      });
    }
    
    // 删除头像文件
    if (currentTeacher[0].photo) {
      const photoPath = path.join(__dirname, '../..', currentTeacher[0].photo);
      if (fs.existsSync(photoPath)) {
        fs.unlinkSync(photoPath);
      }
    }
    
    await pool.query('DELETE FROM teachers WHERE id = ?', [req.params.id]);
    
    res.status(200).json({
      success: true,
      message: '教师删除成功'
    });
  } catch (error) {
    next(error);
  }
});

// 批量导入教师 - Excel文件上传
router.post('/import/excel', uploadExcel.single('excel'), async (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请上传Excel文件'
      });
    }

    const filePath = req.file.path;

    try {
      // 读取Excel文件
      const workbook = XLSX.readFile(filePath);
      const sheetName = workbook.SheetNames[0]; // 读取第一个工作表
      const worksheet = workbook.Sheets[sheetName];

      // 将工作表转换为JSON数组
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      if (jsonData.length < 2) {
        return res.status(400).json({
          success: false,
          message: 'Excel文件内容为空或格式不正确'
        });
      }

      // 获取表头和数据
      const headers = jsonData[0];
      const dataRows = jsonData.slice(1);

      // 验证必要的列是否存在
      const requiredColumns = ['姓名', '性别', '科室', '学校', '专业', '学历'];
      const missingColumns = requiredColumns.filter(col => !headers.includes(col));

      if (missingColumns.length > 0) {
        return res.status(400).json({
          success: false,
          message: `Excel文件缺少必要的列: ${missingColumns.join(', ')}`
        });
      }

      // 获取列索引
      const columnIndexes = {
        name: headers.indexOf('姓名'),
        gender: headers.indexOf('性别'),
        department: headers.indexOf('科室'),
        school: headers.indexOf('学校'),
        major: headers.indexOf('专业'),
        education: headers.indexOf('学历'),
        is_employed: headers.indexOf('是否在聘'),
        employment_period: headers.indexOf('聘期'),
        phone: headers.indexOf('联系方式')
      };

      const successRecords = [];
      const failedRecords = [];

      // 开始事务
      const connection = await pool.getConnection();
      await connection.beginTransaction();

      try {
        for (let i = 0; i < dataRows.length; i++) {
          const row = dataRows[i];
          const rowNumber = i + 2; // Excel行号（从第2行开始）

          try {
            // 提取数据
            const name = row[columnIndexes.name];
            const gender = row[columnIndexes.gender];
            const department = row[columnIndexes.department];
            const school = row[columnIndexes.school];
            const major = row[columnIndexes.major];
            const education = row[columnIndexes.education];
            const is_employed = row[columnIndexes.is_employed];
            const employment_period = row[columnIndexes.employment_period];
            const phone = row[columnIndexes.phone];

            // 验证必填字段
            if (!name || !gender || !department || !school || !major || !education) {
              failedRecords.push({
                row: rowNumber,
                data: row,
                error: '缺少必填字段'
              });
              continue;
            }

            // 验证性别
            if (!['男', '女'].includes(gender)) {
              failedRecords.push({
                row: rowNumber,
                data: row,
                error: '性别必须是"男"或"女"'
              });
              continue;
            }

            // 处理是否在聘字段
            let isEmployedValue = 1; // 默认在聘
            if (is_employed !== undefined && is_employed !== null && is_employed !== '') {
              const employedStr = String(is_employed).toLowerCase();
              if (['否', 'false', '0', 'no'].includes(employedStr)) {
                isEmployedValue = 0;
              }
            }

            // 插入数据库
            await connection.query(`
              INSERT INTO teachers (
                name, gender, department, school, major, education,
                is_employed, employment_period, phone
              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            `, [
              name,
              gender,
              department,
              school,
              major,
              education,
              isEmployedValue,
              employment_period || null,
              phone || null
            ]);

            successRecords.push({
              row: rowNumber,
              name: name
            });

          } catch (error) {
            failedRecords.push({
              row: rowNumber,
              data: row,
              error: error.message
            });
          }
        }

        // 提交事务
        await connection.commit();
        connection.release();

        // 删除临时文件
        fs.unlinkSync(filePath);

        res.status(200).json({
          success: true,
          message: `导入完成！成功导入 ${successRecords.length} 条记录，失败 ${failedRecords.length} 条记录`,
          data: {
            total: dataRows.length,
            success: successRecords.length,
            failed: failedRecords.length,
            successRecords: successRecords,
            failedRecords: failedRecords
          }
        });

      } catch (error) {
        // 回滚事务
        await connection.rollback();
        connection.release();
        throw error;
      }

    } catch (error) {
      // 删除临时文件
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
      throw error;
    }

  } catch (error) {
    console.error('批量导入教师失败:', error);
    next(error);
  }
});

// 下载教师导入模板
router.get('/import/template', (req, res) => {
  try {
    // 创建模板数据
    const templateData = [
      ['姓名', '性别', '科室', '学校', '专业', '学历', '是否在聘', '聘期', '联系方式'],
      ['张三', '男', '内科', '某某医学院', '临床医学', '本科', '是', '2023-01至2025-12', '13800138000'],
      ['李四', '女', '外科', '某某大学', '外科学', '硕士', '是', '2022-06至2024-06', '13900139000']
    ];

    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet(templateData);

    // 设置列宽
    worksheet['!cols'] = [
      { wch: 10 }, // 姓名
      { wch: 6 },  // 性别
      { wch: 15 }, // 科室
      { wch: 20 }, // 学校
      { wch: 15 }, // 专业
      { wch: 10 }, // 学历
      { wch: 10 }, // 是否在聘
      { wch: 20 }, // 聘期
      { wch: 15 }  // 联系方式
    ];

    XLSX.utils.book_append_sheet(workbook, worksheet, '教师信息');

    // 生成Excel文件
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename="teacher_import_template.xlsx"');
    res.send(buffer);

  } catch (error) {
    console.error('下载模板失败:', error);
    res.status(500).json({
      success: false,
      message: '下载模板失败'
    });
  }
});

module.exports = router;