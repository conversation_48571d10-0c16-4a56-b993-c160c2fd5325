{"version": 3, "file": "js/155.1e8b8045.js", "mappings": "iLACOA,MAAM,4B,GAGAA,MAAM,e,GACHA,MAAM,S,8TAJpBC,EAAAA,EAAAA,IA+HM,MA/HNC,EA+HM,EA9HJC,EAAAA,EAAAA,IA6HUC,EAAA,CA7HDJ,MAAM,YAAU,CACZK,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNC,EAAAA,EAAAA,IAGM,MAHNC,EAGM,EAFJD,EAAAA,EAAAA,IAA6D,OAA7DE,GAA6DC,EAAAA,EAAAA,IAAtCC,EAAAC,OAAS,SAAW,UAAd,IAC7BT,EAAAA,EAAAA,IAA2CU,EAAA,CAA/BC,QAAOH,EAAAI,QAAM,C,iBAAE,IAAIC,EAAA,MAAAA,EAAA,M,QAAJ,W,iDAI/B,IAoHM,E,qBApHNf,EAAAA,EAAAA,IAoHM,aAnHJE,EAAAA,EAAAA,IAkHUc,EAAA,CAjHRC,IAAI,oBACHC,MAAOR,EAAAS,SACPC,MAAOV,EAAAW,UACR,cAAY,QACZ,iBAAe,S,kBAGf,IAEe,EAFfnB,EAAAA,EAAAA,IAEeoB,EAAA,CAFDC,MAAM,QAAQC,KAAK,0B,kBAC/B,IAA6E,EAA7EtB,EAAAA,EAAAA,IAA6EuB,EAAA,C,WAA1Df,EAAAS,SAASO,uB,qCAAThB,EAAAS,SAASO,uBAAsBC,GAAEC,YAAY,Y,gCAIlE1B,EAAAA,EAAAA,IAEeoB,EAAA,CAFDC,MAAM,QAAQC,KAAK,c,kBAC/B,IAAiE,EAAjEtB,EAAAA,EAAAA,IAAiEuB,EAAA,C,WAA9Cf,EAAAS,SAASU,W,qCAATnB,EAAAS,SAASU,WAAUF,GAAEC,YAAY,Y,gCAItD1B,EAAAA,EAAAA,IAEeoB,EAAA,CAFDC,MAAM,SAASC,KAAK,iB,kBAChC,IAAwE,EAAxEtB,EAAAA,EAAAA,IAAwEuB,EAAA,C,WAArDf,EAAAS,SAASW,c,qCAATpB,EAAAS,SAASW,cAAaH,GAAEC,YAAY,gB,gCAIzD1B,EAAAA,EAAAA,IAceoB,EAAA,CAdDC,MAAM,OAAOC,KAAK,c,kBAC9B,IAYY,EAZZtB,EAAAA,EAAAA,IAYY6B,EAAA,C,WAXDrB,EAAAS,SAASa,W,qCAATtB,EAAAS,SAASa,WAAUL,GAC5BC,YAAY,UACZK,WAAA,GACCC,QAASxB,EAAAyB,gB,kBAGR,IAA8B,G,aADhCnC,EAAAA,EAAAA,IAKEoC,EAAAA,GAAA,MAAAC,EAAAA,EAAAA,IAJe3B,EAAA4B,eAARC,K,WADTC,EAAAA,EAAAA,IAKEC,EAAA,CAHCC,IAAKH,EAAKI,GACVpB,MAAK,GAAKgB,EAAKK,MAAQ,UAAUL,EAAKM,YAAc,SACpDC,MAAOP,EAAKI,I,8EAMnBzC,EAAAA,EAAAA,IAEeoB,EAAA,CAFDC,MAAM,SAASC,KAAK,iB,kBAChC,IAAqE,EAArEtB,EAAAA,EAAAA,IAAqEuB,EAAA,C,WAAlDf,EAAAS,SAAS4B,c,qCAATrC,EAAAS,SAAS4B,cAAapB,GAAEC,YAAY,a,gCAIzD1B,EAAAA,EAAAA,IAEeoB,EAAA,CAFDC,MAAM,OAAOC,KAAK,gB,kBAC9B,IAAkE,EAAlEtB,EAAAA,EAAAA,IAAkEuB,EAAA,C,WAA/Cf,EAAAS,SAAS6B,a,qCAATtC,EAAAS,SAAS6B,aAAYrB,GAAEC,YAAY,W,gCAIxD1B,EAAAA,EAAAA,IAMeoB,EAAA,CANDC,MAAM,OAAOC,KAAK,gB,kBAC9B,IAIY,EAJZtB,EAAAA,EAAAA,IAIY6B,EAAA,C,WAJQrB,EAAAS,SAAS8B,a,qCAATvC,EAAAS,SAAS8B,aAAYtB,GAAEC,YAAY,W,kBACrD,IAAqC,EAArC1B,EAAAA,EAAAA,IAAqCuC,EAAA,CAA1BlB,MAAM,MAAMuB,MAAM,SAC7B5C,EAAAA,EAAAA,IAAqCuC,EAAA,CAA1BlB,MAAM,MAAMuB,MAAM,SAC7B5C,EAAAA,EAAAA,IAAyCuC,EAAA,CAA9BlB,MAAM,QAAQuB,MAAM,Y,gCAKnC5C,EAAAA,EAAAA,IAUeoB,EAAA,CAVDC,MAAM,KAAKC,KAAK,iB,kBAC5B,IAOE,EAPFtB,EAAAA,EAAAA,IAOEgD,EAAA,C,WANSxC,EAAAS,SAASgC,c,qCAATzC,EAAAS,SAASgC,cAAaxB,GAC9ByB,IAAK,EACLC,IAAK,IACLC,UAAW,EACXC,KAAM,EACP,oBAAkB,S,sCAEpBjD,EAAAA,EAAAA,IAAwC,QAAlCP,MAAM,cAAa,YAAQ,M,eAInCG,EAAAA,EAAAA,IAOeoB,EAAA,CAPDC,MAAM,KAAKC,KAAK,c,kBAC5B,IAKE,EALFtB,EAAAA,EAAAA,IAKEuB,EAAA,C,WAJSf,EAAAS,SAASqC,W,qCAAT9C,EAAAS,SAASqC,WAAU7B,GAC5B8B,KAAK,WACJC,KAAM,EACP9B,YAAY,W,gCAKhB1B,EAAAA,EAAAA,IAOeoB,EAAA,CAPDC,MAAM,KAAKC,KAAK,gB,kBAC5B,IAKE,EALFtB,EAAAA,EAAAA,IAKEuB,EAAA,C,WAJSf,EAAAS,SAASwC,a,qCAATjD,EAAAS,SAASwC,aAAYhC,GAC9B8B,KAAK,WACJC,KAAM,EACP9B,YAAY,a,gCAKhB1B,EAAAA,EAAAA,IAOeoB,EAAA,CAPDC,MAAM,OAAOC,KAAK,2B,kBAC9B,IAKE,EALFtB,EAAAA,EAAAA,IAKEuB,EAAA,C,WAJSf,EAAAS,SAASyC,wB,uCAATlD,EAAAS,SAASyC,wBAAuBjC,GACzC8B,KAAK,WACJC,KAAM,EACP9B,YAAY,W,gCAKhB1B,EAAAA,EAAAA,IAKeoB,EAAA,CALDC,MAAM,OAAOC,KAAK,uB,kBAC9B,IAGiB,EAHjBtB,EAAAA,EAAAA,IAGiB2D,EAAA,C,WAHQnD,EAAAS,SAAS2C,oB,uCAATpD,EAAAS,SAAS2C,oBAAmBnC,I,kBACnD,IAAkC,EAAlCzB,EAAAA,EAAAA,IAAkC6D,EAAA,CAAvBxC,MAAO,GAAC,C,iBAAE,IAAER,EAAA,MAAAA,EAAA,M,QAAF,S,eACrBb,EAAAA,EAAAA,IAAmC6D,EAAA,CAAxBxC,MAAO,GAAC,C,iBAAE,IAAGR,EAAA,MAAAA,EAAA,M,QAAH,U,gDAKzBb,EAAAA,EAAAA,IAGeoB,EAAA,M,iBAFb,IAA4D,EAA5DpB,EAAAA,EAAAA,IAA4DU,EAAA,CAAjD6C,KAAK,UAAW5C,QAAOH,EAAAsD,Y,kBAAY,IAAEjD,EAAA,MAAAA,EAAA,M,QAAF,S,6BAC9Cb,EAAAA,EAAAA,IAA4CU,EAAA,CAAhCC,QAAOH,EAAAuD,WAAS,C,iBAAE,IAAElD,EAAA,MAAAA,EAAA,M,QAAF,S,uEAjHpBL,EAAAwB,a,kFA+HtB,GACEU,KAAM,gBACNsB,KAAAA,GACE,MAAMC,GAAQC,EAAAA,EAAAA,MACRC,GAASC,EAAAA,EAAAA,MACTC,GAAoBtD,EAAAA,EAAAA,IAAI,MAGxBiB,GAAUjB,EAAAA,EAAAA,KAAI,GACdkB,GAAiBlB,EAAAA,EAAAA,KAAI,GACrBqB,GAAiBrB,EAAAA,EAAAA,IAAI,IACrBuD,GAAcvD,EAAAA,EAAAA,IAAI,IAGlBwD,EAAeN,EAAMO,MAAM/B,GAC3BhC,GAASgE,EAAAA,EAAAA,IAAS,MAAQF,GAG1BtD,GAAWyD,EAAAA,EAAAA,IAAS,CACxBlD,uBAAwB,GACxBG,WAAY,GACZC,cAAe,GACfE,WAAY,GACZe,cAAe,GACfC,aAAc,GACdC,aAAc,MACdE,cAAe,EACfK,WAAY,GACZG,aAAc,GACdC,wBAAyB,GACzBE,oBAAqB,EACrBe,aAAc,IAIVxD,EAAY,CAChBK,uBAAwB,CACtB,CAAEoD,UAAU,EAAMC,QAAS,WAAYC,QAAS,SAElDnD,WAAY,CACV,CAAEiD,UAAU,EAAMC,QAAS,WAAYC,QAAS,SAElDlD,cAAe,CACb,CAAEgD,UAAU,EAAMC,QAAS,YAAaC,QAAS,SAEnDhD,WAAY,CACV,CAAE8C,UAAU,EAAMC,QAAS,UAAWC,QAAS,WAEjDjC,cAAe,CACb,CAAE+B,UAAU,EAAMC,QAAS,YAAaC,QAAS,SAEnDhC,aAAc,CACZ,CAAE8B,UAAU,EAAMC,QAAS,UAAWC,QAAS,SAEjD/B,aAAc,CACZ,CAAE6B,UAAU,EAAMC,QAAS,UAAWC,QAAS,WAEjD7B,cAAe,CACb,CAAE2B,UAAU,EAAMC,QAAS,SAAUC,QAAS,aAKlDC,EAAAA,EAAAA,IAAM,IAAM9D,EAASa,WAAakD,IAChC,GAAIA,EAAU,CACZ,MAAMC,EAAkBX,EAAY1B,MAAMsC,KAAKC,GAAWA,EAAQ1C,KAAOuC,GACrEC,GAAmBA,EAAgBG,QACrCnE,EAAS4B,cAAgBoC,EAAgBG,MAE7C,IAIF,MAAMC,EAAgBC,UACpBrD,EAAeW,OAAQ,EACvB,IACE,MAAM2C,QAAiBC,EAAAA,EAAMC,IAAI,wCAC7BF,EAASG,MAAQH,EAASG,KAAKA,MACjCpB,EAAY1B,MAAQ2C,EAASG,KAAKA,KAClCtD,EAAeQ,MAAQ2C,EAASG,KAAKA,OAErCtD,EAAeQ,MAAQ,GACvB+C,QAAQC,MAAM,gBAElB,CAAE,MAAOA,GACPD,QAAQC,MAAM,YAAaA,GAC3BC,EAAAA,GAAUD,MAAM,WAClB,CAAE,QACA3D,EAAeW,OAAQ,CACzB,GAIIkD,EAAwBR,UAC5BtD,EAAQY,OAAQ,EAChB,IACE,MAAM2C,QAAiBC,EAAAA,EAAMC,IAAI,2CAA2ClB,KACtEmB,EAAOH,EAASG,KAAKA,KAG3BK,OAAOC,KAAK/E,GAAUgF,QAAQzD,IACxBA,KAAOkD,IACTzE,EAASuB,GAAOkD,EAAKlD,KAG3B,CAAE,MAAOoD,GACPD,QAAQC,MAAM,YAAaA,GAC3BC,EAAAA,GAAUD,MAAM,WAClB,CAAE,QACA5D,EAAQY,OAAQ,CAClB,GAIIkB,EAAawB,UACZjB,EAAkBzB,aAEjByB,EAAkBzB,MAAMsD,SAASZ,UACrC,IAAIa,EAsBF,OAAO,EArBPnE,EAAQY,OAAQ,EAChB,IACMnC,EAAOmC,aAEH4C,EAAAA,EAAMY,IAAI,2CAA2C7B,IAAgBtD,GAC3E4E,EAAAA,GAAUQ,QAAQ,oBAGZb,EAAAA,EAAMc,KAAK,0CAA2CrF,GAC5D4E,EAAAA,GAAUQ,QAAQ,aAIpBlC,EAAOoC,KAAK,oBACd,CAAE,MAAOX,GACPD,QAAQC,MAAM,QAASA,GACvBC,EAAAA,GAAUD,MAAM,OAClB,CAAE,QACA5D,EAAQY,OAAQ,CAClB,KAQAmB,EAAYA,KACZM,EAAkBzB,OACpByB,EAAkBzB,MAAM4D,eAKtB5F,EAASA,KACbuD,EAAOoC,KAAK,sBAcd,OAVAE,EAAAA,EAAAA,IAAUnB,gBAEFD,IAGF5E,EAAOmC,aACHkD,MAIH,CACL9D,UACAC,iBACAG,iBACAiC,oBACApD,WACAE,YACAV,SACAqD,aACAC,YACAnD,SAEJ,G,UCtTF,MAAM8F,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://ms/./src/views/evaluations/AddEvaluation.vue", "webpack://ms/./src/views/evaluations/AddEvaluation.vue?9552"], "sourcesContent": ["<template>\r\n  <div class=\"add-evaluation-container\">\r\n    <el-card class=\"box-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span class=\"title\">{{ isEdit ? '编辑督导评价' : '添加督导评价' }}</span>\r\n          <el-button @click=\"goBack\">返回列表</el-button>\r\n        </div>\r\n      </template>\r\n\r\n      <div v-loading=\"loading\">\r\n        <el-form\r\n          ref=\"evaluationFormRef\"\r\n          :model=\"formData\"\r\n          :rules=\"formRules\"\r\n          label-width=\"120px\"\r\n          label-position=\"right\"\r\n        >\r\n          <!-- 督导教研室 -->\r\n          <el-form-item label=\"督导教研室\" prop=\"supervising_department\">\r\n            <el-input v-model=\"formData.supervising_department\" placeholder=\"请输入督导教研室\" />\r\n          </el-form-item>\r\n\r\n          <!-- 病例/主题 -->\r\n          <el-form-item label=\"病例/主题\" prop=\"case_topic\">\r\n            <el-input v-model=\"formData.case_topic\" placeholder=\"请输入病例或主题\" />\r\n          </el-form-item>\r\n\r\n          <!-- 教学活动形式 -->\r\n          <el-form-item label=\"教学活动形式\" prop=\"teaching_form\">\r\n            <el-input v-model=\"formData.teaching_form\" placeholder=\"例如：小讲课、教学查房等\" />\r\n          </el-form-item>\r\n\r\n          <!-- 带教老师 -->\r\n          <el-form-item label=\"带教老师\" prop=\"teacher_id\">\r\n            <el-select \r\n              v-model=\"formData.teacher_id\" \r\n              placeholder=\"请选择带教老师\" \r\n              filterable \r\n              :loading=\"teacherLoading\"\r\n            >\r\n              <el-option \r\n                v-for=\"item in teacherOptions\" \r\n                :key=\"item.id\" \r\n                :label=\"`${item.name || '未命名'} (${item.department || '无部门'})`\" \r\n                :value=\"item.id\" \r\n              />\r\n            </el-select>\r\n          </el-form-item>\r\n\r\n          <!-- 带教老师职称 -->\r\n          <el-form-item label=\"带教老师职称\" prop=\"teacher_title\">\r\n            <el-input v-model=\"formData.teacher_title\" placeholder=\"请输入带教老师职称\" />\r\n          </el-form-item>\r\n\r\n          <!-- 学员姓名 -->\r\n          <el-form-item label=\"学员姓名\" prop=\"student_name\">\r\n            <el-input v-model=\"formData.student_name\" placeholder=\"请输入学员姓名\" />\r\n          </el-form-item>\r\n\r\n          <!-- 学员类别 -->\r\n          <el-form-item label=\"学员类别\" prop=\"student_type\">\r\n            <el-select v-model=\"formData.student_type\" placeholder=\"请选择学员类别\">\r\n              <el-option label=\"实习生\" value=\"实习生\" />\r\n              <el-option label=\"进修生\" value=\"进修生\" />\r\n              <el-option label=\"低年资轮转\" value=\"低年资轮转\" />\r\n            </el-select>\r\n          </el-form-item>\r\n\r\n          <!-- 平均分 -->\r\n          <el-form-item label=\"评分\" prop=\"average_score\">\r\n            <el-input-number \r\n              v-model=\"formData.average_score\" \r\n              :min=\"0\" \r\n              :max=\"100\" \r\n              :precision=\"1\" \r\n              :step=\"1\" \r\n              controls-position=\"right\" \r\n            />\r\n            <span class=\"score-hint\">（0-100分）</span>\r\n          </el-form-item>\r\n\r\n          <!-- 亮点 -->\r\n          <el-form-item label=\"亮点\" prop=\"highlights\">\r\n            <el-input \r\n              v-model=\"formData.highlights\" \r\n              type=\"textarea\" \r\n              :rows=\"3\" \r\n              placeholder=\"请输入教学亮点\" \r\n            />\r\n          </el-form-item>\r\n\r\n          <!-- 不足 -->\r\n          <el-form-item label=\"不足\" prop=\"shortcomings\">\r\n            <el-input \r\n              v-model=\"formData.shortcomings\" \r\n              type=\"textarea\" \r\n              :rows=\"3\" \r\n              placeholder=\"请输入教学不足之处\" \r\n            />\r\n          </el-form-item>\r\n\r\n          <!-- 改进建议 -->\r\n          <el-form-item label=\"改进建议\" prop=\"improvement_suggestions\">\r\n            <el-input \r\n              v-model=\"formData.improvement_suggestions\" \r\n              type=\"textarea\" \r\n              :rows=\"3\" \r\n              placeholder=\"请输入改进建议\" \r\n            />\r\n          </el-form-item>\r\n\r\n          <!-- 能力认定 -->\r\n          <el-form-item label=\"能力认定\" prop=\"competency_approved\">\r\n            <el-radio-group v-model=\"formData.competency_approved\">\r\n              <el-radio :label=\"1\">同意</el-radio>\r\n              <el-radio :label=\"0\">不同意</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n\r\n          <!-- 操作按钮 -->\r\n          <el-form-item>\r\n            <el-button type=\"primary\" @click=\"submitForm\">提交</el-button>\r\n            <el-button @click=\"resetForm\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, computed, onMounted, watch } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'AddEvaluation',\r\n  setup() {\r\n    const route = useRoute()\r\n    const router = useRouter()\r\n    const evaluationFormRef = ref(null)\r\n    \r\n    // 基础数据\r\n    const loading = ref(false)\r\n    const teacherLoading = ref(false)\r\n    const teacherOptions = ref([])\r\n    const allTeachers = ref([])\r\n    \r\n    // 是否为编辑模式\r\n    const evaluationId = route.query.id\r\n    const isEdit = computed(() => !!evaluationId)\r\n    \r\n    // 表单数据\r\n    const formData = reactive({\r\n      supervising_department: '',\r\n      case_topic: '',\r\n      teaching_form: '',\r\n      teacher_id: '',\r\n      teacher_title: '',\r\n      student_name: '',\r\n      student_type: '实习生',\r\n      average_score: 7.0,\r\n      highlights: '',\r\n      shortcomings: '',\r\n      improvement_suggestions: '',\r\n      competency_approved: 1,\r\n      evaluator_id: 1 // 默认使用当前登录用户的ID，这里暂时写死\r\n    })\r\n    \r\n    // 表单验证规则\r\n    const formRules = {\r\n      supervising_department: [\r\n        { required: true, message: '请输入督导教研室', trigger: 'blur' }\r\n      ],\r\n      case_topic: [\r\n        { required: true, message: '请输入病例/主题', trigger: 'blur' }\r\n      ],\r\n      teaching_form: [\r\n        { required: true, message: '请输入教学活动形式', trigger: 'blur' }\r\n      ],\r\n      teacher_id: [\r\n        { required: true, message: '请选择带教老师', trigger: 'change' }\r\n      ],\r\n      teacher_title: [\r\n        { required: true, message: '请输入带教老师职称', trigger: 'blur' }\r\n      ],\r\n      student_name: [\r\n        { required: true, message: '请输入学员姓名', trigger: 'blur' }\r\n      ],\r\n      student_type: [\r\n        { required: true, message: '请选择学员类别', trigger: 'change' }\r\n      ],\r\n      average_score: [\r\n        { required: true, message: '请输入平均分', trigger: 'change' }\r\n      ]\r\n    }\r\n\r\n    // 监听教师ID变化自动填充职称\r\n    watch(() => formData.teacher_id, (newValue) => {\r\n      if (newValue) {\r\n        const selectedTeacher = allTeachers.value.find(teacher => teacher.id === newValue)\r\n        if (selectedTeacher && selectedTeacher.title) {\r\n          formData.teacher_title = selectedTeacher.title\r\n        }\r\n      }\r\n    })\r\n    \r\n    // 获取教师列表\r\n    const fetchTeachers = async () => {\r\n      teacherLoading.value = true\r\n      try {\r\n        const response = await axios.get('http://localhost:3000/api/teachers')\r\n        if (response.data && response.data.data) {\r\n          allTeachers.value = response.data.data\r\n          teacherOptions.value = response.data.data\r\n        } else {\r\n          teacherOptions.value = []\r\n          console.error('获取教师列表返回格式有误')\r\n        }\r\n      } catch (error) {\r\n        console.error('获取教师列表失败:', error)\r\n        ElMessage.error('获取教师列表失败')\r\n      } finally {\r\n        teacherLoading.value = false\r\n      }\r\n    }\r\n    \r\n    // 获取评价详情\r\n    const fetchEvaluationDetail = async () => {\r\n      loading.value = true\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/evaluations/${evaluationId}`)\r\n        const data = response.data.data\r\n        \r\n        // 填充表单数据\r\n        Object.keys(formData).forEach(key => {\r\n          if (key in data) {\r\n            formData[key] = data[key]\r\n          }\r\n        })\r\n      } catch (error) {\r\n        console.error('获取评价详情失败:', error)\r\n        ElMessage.error('获取评价详情失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 提交表单\r\n    const submitForm = async () => {\r\n      if (!evaluationFormRef.value) return\r\n      \r\n      await evaluationFormRef.value.validate(async (valid) => {\r\n        if (valid) {\r\n          loading.value = true\r\n          try {\r\n            if (isEdit.value) {\r\n              // 编辑模式\r\n              await axios.put(`http://localhost:3000/api/evaluations/${evaluationId}`, formData)\r\n              ElMessage.success('督导评价更新成功')\r\n            } else {\r\n              // 添加模式\r\n              await axios.post('http://localhost:3000/api/evaluations', formData)\r\n              ElMessage.success('督导评价添加成功')\r\n            }\r\n            \r\n            // 跳转回列表页\r\n            router.push('/evaluations/list')\r\n          } catch (error) {\r\n            console.error('操作失败:', error)\r\n            ElMessage.error('操作失败')\r\n          } finally {\r\n            loading.value = false\r\n          }\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    }\r\n    \r\n    // 重置表单\r\n    const resetForm = () => {\r\n      if (evaluationFormRef.value) {\r\n        evaluationFormRef.value.resetFields()\r\n      }\r\n    }\r\n    \r\n    // 返回列表\r\n    const goBack = () => {\r\n      router.push('/evaluations/list')\r\n    }\r\n    \r\n    // 生命周期钩子\r\n    onMounted(async () => {\r\n      // 初始化教师下拉列表\r\n      await fetchTeachers()\r\n      \r\n      // 如果是编辑模式，加载评价数据\r\n      if (isEdit.value) {\r\n        await fetchEvaluationDetail()\r\n      }\r\n    })\r\n    \r\n    return {\r\n      loading,\r\n      teacherLoading,\r\n      teacherOptions,\r\n      evaluationFormRef,\r\n      formData,\r\n      formRules,\r\n      isEdit,\r\n      submitForm,\r\n      resetForm,\r\n      goBack\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.add-evaluation-container {\r\n  padding: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.score-hint {\r\n  margin-left: 10px;\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n</style> ", "import { render } from \"./AddEvaluation.vue?vue&type=template&id=84544d94&scoped=true\"\nimport script from \"./AddEvaluation.vue?vue&type=script&lang=js\"\nexport * from \"./AddEvaluation.vue?vue&type=script&lang=js\"\n\nimport \"./AddEvaluation.vue?vue&type=style&index=0&id=84544d94&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-84544d94\"]])\n\nexport default __exports__"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "$setup", "isEdit", "_component_el_button", "onClick", "goBack", "_cache", "_component_el_form", "ref", "model", "formData", "rules", "formRules", "_component_el_form_item", "label", "prop", "_component_el_input", "supervising_department", "$event", "placeholder", "case_topic", "teaching_form", "_component_el_select", "teacher_id", "filterable", "loading", "teacherLoading", "_Fragment", "_renderList", "teacherOptions", "item", "_createBlock", "_component_el_option", "key", "id", "name", "department", "value", "teacher_title", "student_name", "student_type", "_component_el_input_number", "average_score", "min", "max", "precision", "step", "highlights", "type", "rows", "shortcomings", "improvement_suggestions", "_component_el_radio_group", "competency_approved", "_component_el_radio", "submitForm", "resetForm", "setup", "route", "useRoute", "router", "useRouter", "evaluationFormRef", "allTeachers", "evaluationId", "query", "computed", "reactive", "evaluator_id", "required", "message", "trigger", "watch", "newValue", "<PERSON><PERSON><PERSON><PERSON>", "find", "teacher", "title", "fetchTeachers", "async", "response", "axios", "get", "data", "console", "error", "ElMessage", "fetchEvaluationDetail", "Object", "keys", "for<PERSON>ach", "validate", "valid", "put", "success", "post", "push", "resetFields", "onMounted", "__exports__", "render"], "sourceRoot": ""}