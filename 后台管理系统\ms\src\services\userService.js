import api from '@/utils/api';

const userService = {
  /**
   * 获取用户列表
   * @param {Object} params - 查询参数
   * @returns {Promise}
   */
  getUsers(params) {
    return api.get('/api/users', { params });
  },

  /**
   * 获取单个用户
   * @param {number} id - 用户ID
   * @returns {Promise}
   */
  getUser(id) {
    return api.get(`/api/users/${id}`);
  },

  /**
   * 创建用户
   * @param {Object} userData - 用户数据
   * @returns {Promise}
   */
  createUser(userData) {
    return api.post('/api/users', userData);
  },

  /**
   * 更新用户
   * @param {number} id - 用户ID
   * @param {Object} userData - 用户数据
   * @returns {Promise}
   */
  updateUser(id, userData) {
    return api.put(`/api/users/${id}`, userData); 
  },

  /**
   * 删除用户
   * @param {number} id - 用户ID
   * @returns {Promise}
   */
  deleteUser(id) {
    return api.delete(`/api/users/${id}`);
  },

  /**
   * 批量删除用户
   * @param {Array} ids - 用户ID数组
   * @returns {Promise}
   */
  batchDeleteUsers(ids) {
    return api.delete('/api/users/batch', { data: { ids } });
  },

  /**
   * 修改用户状态
   * @param {number} id - 用户ID
   * @param {number} status - 状态值（0禁用，1启用）
   * @returns {Promise}
   */
  updateUserStatus(id, status) {
    return api.put(`/api/users/${id}/status`, { status });
  },

  /**
   * 下载用户导入模板
   * @returns {Promise}
   */
  downloadImportTemplate() {
    return api.get('/api/users/import/template', {
      responseType: 'blob'
    });
  },

  /**
   * 批量导入用户
   * @param {File} file - Excel文件
   * @returns {Promise}
   */
  importUsers(file) {
    const formData = new FormData();
    formData.append('excel', file);

    return api.post('/api/users/import/excel', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }
};

export default userService; 