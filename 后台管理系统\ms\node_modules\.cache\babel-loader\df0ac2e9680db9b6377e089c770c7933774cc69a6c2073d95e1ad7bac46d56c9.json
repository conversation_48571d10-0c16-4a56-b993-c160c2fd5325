{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nimport { ref, reactive, onMounted, watch } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { Plus, Delete, Refresh, Upload } from '@element-plus/icons-vue';\nimport userService from '@/services/userService';\nimport teacherService from '@/services/teacherService';\nexport default {\n  __name: 'UserList',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const loading = ref(false);\n    const teacherListLoading = ref(false);\n    const currentPage = ref(1);\n    const pageSize = ref(10);\n    const total = ref(0);\n    const multipleSelection = ref([]);\n    const dialogVisible = ref(false);\n    const dialogType = ref('add'); // 'add' or 'edit'\n    const userFormRef = ref(null);\n    const teacherList = ref([]); // 教师列表\n\n    // 导入相关变量\n    const importDialogVisible = ref(false);\n    const uploading = ref(false);\n    const uploadRef = ref(null);\n    const fileList = ref([]);\n    const importResult = ref(null);\n    const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';\n\n    // 上传请求头\n    const uploadHeaders = {\n      'Authorization': `Bearer ${localStorage.getItem('token')}`\n    };\n\n    // 搜索表单\n    const searchForm = reactive({\n      username: '',\n      phone: '',\n      status: ''\n    });\n\n    // 用户表单\n    const userForm = reactive({\n      id: '',\n      username: '',\n      name: '',\n      password: '',\n      phone: '',\n      email: '',\n      role: 'teacher',\n      teacher_id: '',\n      status: 1\n    });\n\n    // 获取教师列表\n    const fetchTeacherList = async () => {\n      teacherListLoading.value = true;\n      try {\n        const response = await teacherService.getTeachers();\n        teacherList.value = response.data.data;\n      } catch (error) {\n        console.error('获取教师列表失败:', error);\n        ElMessage.error('获取教师列表失败');\n      } finally {\n        teacherListLoading.value = false;\n      }\n    };\n\n    // 当角色选择为教师时，加载教师列表\n    watch(() => userForm.role, newRole => {\n      if (newRole === 'teacher' && teacherList.value.length === 0) {\n        fetchTeacherList();\n      }\n    });\n\n    // 当对话框打开时，如果角色是教师且教师列表为空，则获取教师列表\n    watch(() => dialogVisible.value, newVal => {\n      if (newVal && userForm.role === 'teacher' && teacherList.value.length === 0) {\n        fetchTeacherList();\n      }\n    });\n\n    // 表单校验规则\n    const userFormRules = {\n      username: [{\n        required: true,\n        message: '请输入用户名',\n        trigger: 'blur'\n      }, {\n        min: 3,\n        max: 20,\n        message: '长度在 3 到 20 个字符',\n        trigger: 'blur'\n      }],\n      name: [{\n        required: true,\n        message: '请输入姓名',\n        trigger: 'blur'\n      }],\n      password: [{\n        required: true,\n        message: '请输入密码',\n        trigger: 'blur'\n      }, {\n        min: 6,\n        max: 20,\n        message: '长度在 6 到 20 个字符',\n        trigger: 'blur'\n      }],\n      phone: [{\n        required: true,\n        message: '请输入手机号',\n        trigger: 'blur'\n      }, {\n        pattern: /^1[3-9]\\d{9}$/,\n        message: '请输入正确的手机号',\n        trigger: 'blur'\n      }],\n      email: [{\n        required: true,\n        message: '请输入邮箱',\n        trigger: 'blur'\n      }, {\n        type: 'email',\n        message: '请输入正确的邮箱地址',\n        trigger: 'blur'\n      }],\n      role: [{\n        required: true,\n        message: '请选择角色',\n        trigger: 'change'\n      }],\n      teacher_id: [{\n        validator: (rule, value, callback) => {\n          if (userForm.role === 'teacher' && !value) {\n            callback(new Error('请选择关联的教师'));\n          } else {\n            callback();\n          }\n        },\n        trigger: 'change'\n      }]\n    };\n\n    // 用户数据\n    const userList = ref([]);\n    onMounted(() => {\n      fetchData();\n    });\n\n    // 格式化日期\n    const formatDate = dateString => {\n      if (!dateString) return '';\n      const date = new Date(dateString);\n      const year = date.getFullYear();\n      const month = String(date.getMonth() + 1).padStart(2, '0');\n      const day = String(date.getDate()).padStart(2, '0');\n      const hours = String(date.getHours()).padStart(2, '0');\n      const minutes = String(date.getMinutes()).padStart(2, '0');\n      return `${year}-${month}-${day} ${hours}:${minutes}`;\n    };\n\n    // 获取数据\n    const fetchData = async () => {\n      loading.value = true;\n      try {\n        const response = await userService.getUsers({\n          page: currentPage.value,\n          limit: pageSize.value,\n          username: searchForm.username || undefined,\n          phone: searchForm.phone || undefined,\n          status: searchForm.status || undefined\n        });\n        userList.value = response.data.data;\n        total.value = response.data.count || 0;\n      } catch (error) {\n        console.error('获取用户列表失败:', error);\n        ElMessage.error('获取用户列表失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 查询\n    const handleSearch = () => {\n      currentPage.value = 1;\n      fetchData();\n    };\n\n    // 重置表单\n    const resetForm = () => {\n      Object.keys(searchForm).forEach(key => {\n        searchForm[key] = '';\n      });\n      handleSearch();\n    };\n\n    // 刷新表格\n    const refreshTable = () => {\n      fetchData();\n    };\n\n    // 多选变化\n    const handleSelectionChange = selection => {\n      multipleSelection.value = selection;\n    };\n\n    // 新增用户\n    const handleAdd = () => {\n      dialogType.value = 'add';\n      resetUserForm();\n      dialogVisible.value = true;\n    };\n\n    // 编辑用户\n    const handleEdit = row => {\n      dialogType.value = 'edit';\n      resetUserForm();\n      Object.keys(userForm).forEach(key => {\n        if (key !== 'password') {\n          userForm[key] = row[key];\n        }\n      });\n      dialogVisible.value = true;\n    };\n\n    // 重置用户表单\n    const resetUserForm = () => {\n      if (userFormRef.value) {\n        userFormRef.value.resetFields();\n      }\n      Object.assign(userForm, {\n        id: '',\n        username: '',\n        name: '',\n        password: '',\n        phone: '',\n        email: '',\n        role: 'teacher',\n        teacher_id: '',\n        status: 1\n      });\n    };\n\n    // 提交表单\n    const submitForm = async () => {\n      if (!userFormRef.value) return;\n      await userFormRef.value.validate(async valid => {\n        if (valid) {\n          try {\n            if (dialogType.value === 'add') {\n              // 新增用户\n              if (userForm.role !== 'teacher') {\n                delete userForm.teacher_id;\n              }\n              await userService.createUser(userForm);\n              ElMessage.success('新增用户成功');\n            } else {\n              // 编辑用户\n              await userService.updateUser(userForm.id, userForm);\n              ElMessage.success('编辑用户成功');\n            }\n            dialogVisible.value = false;\n            fetchData();\n          } catch (error) {\n            console.error('保存用户失败:', error);\n            ElMessage.error('保存用户失败: ' + (error.response?.data?.message || error.message));\n          }\n        } else {\n          return false;\n        }\n      });\n    };\n\n    // 删除用户\n    const handleDelete = row => {\n      ElMessageBox.confirm(`确定要删除用户 ${row.username} 吗?`, '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async () => {\n        try {\n          await userService.deleteUser(row.id);\n          ElMessage.success(`用户 ${row.username} 已删除`);\n          fetchData();\n        } catch (error) {\n          console.error('删除用户失败:', error);\n          ElMessage.error('删除用户失败: ' + (error.response?.data?.message || error.message));\n        }\n      }).catch(() => {});\n    };\n\n    // 批量删除\n    const handleBatchDelete = () => {\n      if (multipleSelection.value.length === 0) {\n        ElMessage.warning('请至少选择一条记录');\n        return;\n      }\n      const names = multipleSelection.value.map(item => item.username).join('、');\n      const ids = multipleSelection.value.map(item => item.id);\n      ElMessageBox.confirm(`确定要删除选中的 ${multipleSelection.value.length} 条记录吗?`, '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async () => {\n        try {\n          await userService.batchDeleteUsers(ids);\n          ElMessage.success('批量删除成功');\n          fetchData();\n        } catch (error) {\n          console.error('批量删除失败:', error);\n          ElMessage.error('批量删除失败: ' + (error.response?.data?.message || error.message));\n        }\n      }).catch(() => {});\n    };\n\n    // 修改状态\n    const handleStatusChange = async (val, row) => {\n      try {\n        await userService.updateUserStatus(row.id, val);\n        const status = val === 1 ? '启用' : '禁用';\n        ElMessage.success(`已${status}用户 ${row.username}`);\n      } catch (error) {\n        console.error('修改状态失败:', error);\n        ElMessage.error('修改状态失败: ' + (error.response?.data?.message || error.message));\n        // 回滚状态\n        row.status = val === 1 ? 0 : 1;\n      }\n    };\n\n    // 分页大小变化\n    const handleSizeChange = size => {\n      pageSize.value = size;\n      fetchData();\n    };\n\n    // 页码变化\n    const handleCurrentChange = page => {\n      currentPage.value = page;\n      fetchData();\n    };\n\n    // 下载导入模板\n    const downloadTemplate = async () => {\n      try {\n        const response = await fetch(`${baseUrl}/api/users/import/template`, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          }\n        });\n        if (!response.ok) {\n          throw new Error('下载失败');\n        }\n        const blob = await response.blob();\n        const url = window.URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = 'user_import_template.xlsx';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        window.URL.revokeObjectURL(url);\n        ElMessage.success('模板下载成功');\n      } catch (error) {\n        console.error('下载模板失败:', error);\n        ElMessage.error('下载模板失败');\n      }\n    };\n\n    // 打开导入对话框\n    const openImportDialog = () => {\n      importDialogVisible.value = true;\n      fileList.value = [];\n      importResult.value = null;\n    };\n\n    // 文件选择变化处理\n    const handleFileChange = (file, fileListParam) => {\n      console.log('文件选择变化:', file, fileListParam);\n      fileList.value = fileListParam;\n    };\n\n    // Excel文件上传前验证\n    const beforeExcelUpload = file => {\n      const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || file.type === 'application/vnd.ms-excel';\n      const isLt10M = file.size / 1024 / 1024 < 10;\n      if (!isExcel) {\n        ElMessage.error('只能上传Excel文件!');\n        return false;\n      }\n      if (!isLt10M) {\n        ElMessage.error('文件大小不能超过10MB!');\n        return false;\n      }\n      return true;\n    };\n\n    // 手动上传\n    const handleUpload = () => {\n      if (fileList.value.length === 0) {\n        ElMessage.error('请先选择要上传的文件');\n        return;\n      }\n      uploading.value = true;\n      uploadRef.value.submit();\n    };\n\n    // 上传成功回调\n    const handleImportSuccess = response => {\n      uploading.value = false;\n      console.log('导入成功:', response);\n      if (response.success) {\n        importResult.value = response.data;\n        ElMessage.success(response.message);\n        // 刷新用户列表\n        fetchData();\n      } else {\n        ElMessage.error(response.message || '导入失败');\n      }\n    };\n\n    // 上传失败回调\n    const handleImportError = error => {\n      uploading.value = false;\n      console.error('导入失败:', error);\n      ElMessage.error('导入失败，请检查文件格式');\n    };\n    const __returned__ = {\n      loading,\n      teacherListLoading,\n      currentPage,\n      pageSize,\n      total,\n      multipleSelection,\n      dialogVisible,\n      dialogType,\n      userFormRef,\n      teacherList,\n      importDialogVisible,\n      uploading,\n      uploadRef,\n      fileList,\n      importResult,\n      baseUrl,\n      uploadHeaders,\n      searchForm,\n      userForm,\n      fetchTeacherList,\n      userFormRules,\n      userList,\n      formatDate,\n      fetchData,\n      handleSearch,\n      resetForm,\n      refreshTable,\n      handleSelectionChange,\n      handleAdd,\n      handleEdit,\n      resetUserForm,\n      submitForm,\n      handleDelete,\n      handleBatchDelete,\n      handleStatusChange,\n      handleSizeChange,\n      handleCurrentChange,\n      downloadTemplate,\n      openImportDialog,\n      handleFileChange,\n      beforeExcelUpload,\n      handleUpload,\n      handleImportSuccess,\n      handleImportError,\n      ref,\n      reactive,\n      onMounted,\n      watch,\n      get ElMessage() {\n        return ElMessage;\n      },\n      get ElMessageBox() {\n        return ElMessageBox;\n      },\n      get Plus() {\n        return Plus;\n      },\n      get Delete() {\n        return Delete;\n      },\n      get Refresh() {\n        return Refresh;\n      },\n      get Upload() {\n        return Upload;\n      },\n      get userService() {\n        return userService;\n      },\n      get teacherService() {\n        return teacherService;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "watch", "ElMessage", "ElMessageBox", "Plus", "Delete", "Refresh", "Upload", "userService", "teacherService", "loading", "teacherListL<PERSON>ding", "currentPage", "pageSize", "total", "multipleSelection", "dialogVisible", "dialogType", "userFormRef", "teacherList", "importDialogVisible", "uploading", "uploadRef", "fileList", "importResult", "baseUrl", "import", "meta", "env", "VITE_API_BASE_URL", "uploadHeaders", "localStorage", "getItem", "searchForm", "username", "phone", "status", "userForm", "id", "name", "password", "email", "role", "teacher_id", "fetchTeacherList", "value", "response", "getTeachers", "data", "error", "console", "newRole", "length", "newVal", "userFormRules", "required", "message", "trigger", "min", "max", "pattern", "type", "validator", "rule", "callback", "Error", "userList", "fetchData", "formatDate", "dateString", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "getUsers", "page", "limit", "undefined", "count", "handleSearch", "resetForm", "Object", "keys", "for<PERSON>ach", "key", "refreshTable", "handleSelectionChange", "selection", "handleAdd", "resetUserForm", "handleEdit", "row", "resetFields", "assign", "submitForm", "validate", "valid", "createUser", "success", "updateUser", "handleDelete", "confirm", "confirmButtonText", "cancelButtonText", "then", "deleteUser", "catch", "handleBatchDelete", "warning", "names", "map", "item", "join", "ids", "batchDeleteUsers", "handleStatusChange", "val", "updateUserStatus", "handleSizeChange", "size", "handleCurrentChange", "downloadTemplate", "fetch", "method", "headers", "ok", "blob", "url", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "openImportDialog", "handleFileChange", "file", "fileListParam", "log", "beforeExcelUpload", "isExcel", "isLt10M", "handleUpload", "submit", "handleImportSuccess", "handleImportError"], "sources": ["D:/admin/202506/督导系统/后台管理系统/ms/src/views/users/UserList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"user-list-container\">\r\n    <!-- 搜索和操作区域 -->\r\n    <el-card class=\"search-card\">\r\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\r\n        <el-form-item label=\"用户名\">\r\n          <el-input v-model=\"searchForm.username\" placeholder=\"请输入用户名\" clearable></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\">\r\n          <el-input v-model=\"searchForm.phone\" placeholder=\"请输入手机号\" clearable></el-input>\r\n        </el-form-item>\r\n      \r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n          <el-button @click=\"resetForm\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </el-card>\r\n    \r\n    <!-- 表格区域 -->\r\n    <el-card class=\"table-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span>用户列表</span>\r\n          <div>\r\n            <el-button type=\"success\" @click=\"downloadTemplate\">下载模板</el-button>\r\n            <el-button type=\"danger\" @click=\"openImportDialog\">\r\n              <el-icon><Upload /></el-icon> 一键导入\r\n            </el-button>\r\n            <el-button type=\"primary\" @click=\"handleAdd\">\r\n              <el-icon><Plus /></el-icon> 新增用户\r\n            </el-button>\r\n            <el-button @click=\"refreshTable\">\r\n              <el-icon><Refresh /></el-icon> 刷新\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n      \r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"userList\"\r\n        @selection-change=\"handleSelectionChange\"\r\n        style=\"width: 100%\"\r\n        border\r\n      >\r\n        <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n        <el-table-column prop=\"id\" label=\"ID\" width=\"80\" align=\"center\" />\r\n        <el-table-column prop=\"username\" label=\"用户名\" />\r\n        <el-table-column prop=\"name\" label=\"姓名\" />\r\n        <el-table-column prop=\"phone\" label=\"手机号\" />\r\n        <el-table-column prop=\"email\" label=\"邮箱\" />\r\n        <el-table-column prop=\"role\" label=\"角色\" width=\"100\">\r\n          <template #default=\"scope\">\r\n            <el-tag v-if=\"scope.row.role === 'admin'\" type=\"danger\">管理员</el-tag>\r\n            <el-tag v-else-if=\"scope.row.role === 'teacher'\" type=\"warning\">教师</el-tag>\r\n            <!-- 教学管理员、带教老师、教研室主任、教研室副主任、教学秘书 -->\r\n             <el-tag v-else-if=\"scope.row.role === 'teaching_admin'\" type=\"warning\">教学管理员</el-tag>\r\n             <el-tag v-else-if=\"scope.row.role === 'teaching_teacher'\" type=\"warning\">带教老师</el-tag>\r\n             <el-tag v-else-if=\"scope.row.role === 'department_head'\" type=\"warning\">教研室主任</el-tag>\r\n             <el-tag v-else-if=\"scope.row.role === 'department_deputy_head'\" type=\"warning\">教研室副主任</el-tag>\r\n             <el-tag v-else-if=\"scope.row.role === 'teaching_secretary'\" type=\"warning\">教学秘书</el-tag>\r\n            <el-tag v-else type=\"info\">{{ scope.row.role }}</el-tag>\r\n          </template>\r\n        </el-table-column>\r\n       \r\n        \r\n       \r\n       \r\n        <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"160\">\r\n          <template #default=\"scope\">\r\n            {{ formatDate(scope.row.created_at) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column fixed=\"right\" label=\"操作\" width=\"180\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <el-button size=\"small\" @click=\"handleEdit(scope.row)\">编辑</el-button>\r\n            <el-button \r\n              size=\"small\" \r\n              type=\"danger\" \r\n              @click=\"handleDelete(scope.row)\"\r\n            >删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      \r\n      <!-- 分页 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          v-model:current-page=\"currentPage\"\r\n          v-model:page-size=\"pageSize\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          :total=\"total\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n        />\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 用户表单对话框 -->\r\n    <el-dialog\r\n      v-model=\"dialogVisible\"\r\n      :title=\"dialogType === 'add' ? '新增用户' : '编辑用户'\"\r\n      width=\"600px\"\r\n    >\r\n      <el-form\r\n        :model=\"userForm\"\r\n        :rules=\"userFormRules\"\r\n        ref=\"userFormRef\"\r\n        label-width=\"100px\"\r\n      >\r\n        <el-form-item label=\"用户名\" prop=\"username\">\r\n          <el-input v-model=\"userForm.username\" placeholder=\"请输入用户名\" :disabled=\"dialogType === 'edit'\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"姓名\" prop=\"name\">\r\n          <el-input v-model=\"userForm.name\" placeholder=\"请输入姓名\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item v-if=\"dialogType === 'add'\" label=\"密码\" prop=\"password\">\r\n          <el-input v-model=\"userForm.password\" type=\"password\" placeholder=\"请输入密码\" show-password></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"手机号\" prop=\"phone\">\r\n          <el-input v-model=\"userForm.phone\" placeholder=\"请输入手机号\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"邮箱\" prop=\"email\" >\r\n          <el-input v-model=\"userForm.email\" placeholder=\"请输入邮箱\"></el-input>\r\n        </el-form-item>\r\n        <el-form-item label=\"角色\" prop=\"role\" v-if=\"dialogType === 'add'\">\r\n          <el-select v-model=\"userForm.role\" placeholder=\"请选择角色\">\r\n            <el-option label=\"管理员\" value=\"admin\"></el-option>\r\n            <el-option label=\"教师\" value=\"teacher\"></el-option>\r\n            <el-option label=\"教学管理员\" value=\"teaching_admin\"></el-option>\r\n            <el-option label=\"带教老师\" value=\"teaching_teacher\"></el-option>\r\n            <el-option label=\"教研室主任\" value=\"department_head\"></el-option>\r\n            <el-option label=\"教研室副主任\" value=\"department_deputy_head\"></el-option>\r\n            <el-option label=\"教学秘书\" value=\"teaching_secretary\"></el-option>\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <!-- 当角色是教师时，显示教师选择器 -->\r\n        <el-form-item v-if=\"userForm.role === 'teacher' && dialogType === 'add'\" label=\"关联教师\" prop=\"teacher_id\">\r\n          <el-select \r\n            v-model=\"userForm.teacher_id\" \r\n            placeholder=\"请选择关联的教师\" \r\n            filterable\r\n            :loading=\"teacherListLoading\"\r\n          >\r\n            <el-option\r\n              v-for=\"item in teacherList\"\r\n              :key=\"item.id\"\r\n              :label=\"`${item.name} - ${item.department}`\"\r\n              :value=\"item.id\"\r\n            >\r\n              <div class=\"teacher-option\">\r\n                <span>{{ item.name }}</span>\r\n                <span class=\"teacher-dept\">{{ item.department }}</span>\r\n              </div>\r\n            </el-option>\r\n          </el-select>\r\n          <div class=\"form-tip\">将用户账号关联到现有教师</div>\r\n        </el-form-item>\r\n\r\n        <!-- 当角色不是教师时，隐藏教师ID字段 -->\r\n        <el-form-item v-if=\"userForm.role !== 'teacher'\" label=\"教师ID\" prop=\"teacher_id\">\r\n          <el-input v-model=\"userForm.teacher_id\" placeholder=\"请输入教师ID（选填）\"></el-input>\r\n        </el-form-item>\r\n      </el-form>\r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitForm\">确定</el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- Excel导入对话框 -->\r\n    <el-dialog\r\n      v-model=\"importDialogVisible\"\r\n      title=\"批量导入用户\"\r\n      width=\"50%\"\r\n      destroy-on-close\r\n    >\r\n      <div class=\"import-content\">\r\n        <el-alert\r\n          title=\"导入说明\"\r\n          type=\"info\"\r\n          :closable=\"false\"\r\n          style=\"margin-bottom: 20px\"\r\n        >\r\n          <template #default>\r\n            <div>\r\n              <p>1. 请先下载Excel模板，按照模板格式填写用户信息</p>\r\n              <p>2. 必填字段：用户名、密码、角色、姓名</p>\r\n              <p>3. 角色只能填写\"admin\"或\"teacher\"</p>\r\n              <p>4. 状态可填写：启用/禁用、true/false、1/0</p>\r\n              <p>5. 支持.xlsx和.xls格式，文件大小不超过10MB</p>\r\n              <p>6. 注意：不包含关联教师功能，如需关联请在导入后手动编辑</p>\r\n            </div>\r\n          </template>\r\n        </el-alert>\r\n\r\n        <el-upload\r\n          ref=\"uploadRef\"\r\n          class=\"upload-demo\"\r\n          :action=\"`${baseUrl}/api/users/import/excel`\"\r\n          :headers=\"uploadHeaders\"\r\n          :before-upload=\"beforeExcelUpload\"\r\n          :on-success=\"handleImportSuccess\"\r\n          :on-error=\"handleImportError\"\r\n          :on-change=\"handleFileChange\"\r\n          :file-list=\"fileList\"\r\n          :auto-upload=\"false\"\r\n          accept=\".xlsx,.xls\"\r\n          :limit=\"1\"\r\n          name=\"excel\"\r\n        >\r\n          <el-button type=\"primary\">选择Excel文件</el-button>\r\n          <template #tip>\r\n            <div class=\"el-upload__tip\">\r\n              只能上传xlsx/xls文件，且不超过10MB\r\n            </div>\r\n          </template>\r\n        </el-upload>\r\n\r\n        <!-- 导入结果显示 -->\r\n        <div v-if=\"importResult\" class=\"import-result\" style=\"margin-top: 20px\">\r\n          <el-alert\r\n            :title=\"`导入完成！成功 ${importResult.success} 条，失败 ${importResult.failed} 条`\"\r\n            :type=\"importResult.failed > 0 ? 'warning' : 'success'\"\r\n            :closable=\"false\"\r\n          />\r\n\r\n          <!-- 失败记录详情 -->\r\n          <div v-if=\"importResult.failedRecords && importResult.failedRecords.length > 0\" style=\"margin-top: 15px\">\r\n            <el-collapse>\r\n              <el-collapse-item title=\"查看失败记录\" name=\"failed\">\r\n                <el-table :data=\"importResult.failedRecords\" border size=\"small\">\r\n                  <el-table-column prop=\"row\" label=\"行号\" width=\"80\" />\r\n                  <el-table-column prop=\"error\" label=\"错误原因\" />\r\n                </el-table>\r\n              </el-collapse-item>\r\n            </el-collapse>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"importDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"handleUpload\" :loading=\"uploading\">开始导入</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, onMounted, watch } from 'vue'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { Plus, Delete, Refresh, Upload } from '@element-plus/icons-vue'\r\nimport userService from '@/services/userService'\r\nimport teacherService from '@/services/teacherService'\r\n\r\nconst loading = ref(false)\r\nconst teacherListLoading = ref(false)\r\nconst currentPage = ref(1)\r\nconst pageSize = ref(10)\r\nconst total = ref(0)\r\nconst multipleSelection = ref([])\r\nconst dialogVisible = ref(false)\r\nconst dialogType = ref('add') // 'add' or 'edit'\r\nconst userFormRef = ref(null)\r\nconst teacherList = ref([]) // 教师列表\r\n\r\n// 导入相关变量\r\nconst importDialogVisible = ref(false)\r\nconst uploading = ref(false)\r\nconst uploadRef = ref(null)\r\nconst fileList = ref([])\r\nconst importResult = ref(null)\r\nconst baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'\r\n\r\n// 上传请求头\r\nconst uploadHeaders = {\r\n  'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n}\r\n\r\n// 搜索表单\r\nconst searchForm = reactive({\r\n  username: '',\r\n  phone: '',\r\n  status: ''\r\n})\r\n\r\n// 用户表单\r\nconst userForm = reactive({\r\n  id: '',\r\n  username: '',\r\n  name: '',\r\n  password: '',\r\n  phone: '',\r\n  email: '',\r\n  role: 'teacher',\r\n  teacher_id: '',\r\n  status: 1\r\n})\r\n\r\n// 获取教师列表\r\nconst fetchTeacherList = async () => {\r\n  teacherListLoading.value = true\r\n  try {\r\n    const response = await teacherService.getTeachers()\r\n    teacherList.value = response.data.data\r\n  } catch (error) {\r\n    console.error('获取教师列表失败:', error)\r\n    ElMessage.error('获取教师列表失败')\r\n  } finally {\r\n    teacherListLoading.value = false\r\n  }\r\n}\r\n\r\n// 当角色选择为教师时，加载教师列表\r\nwatch(() => userForm.role, (newRole) => {\r\n  if (newRole === 'teacher' && teacherList.value.length === 0) {\r\n    fetchTeacherList()\r\n  }\r\n})\r\n\r\n// 当对话框打开时，如果角色是教师且教师列表为空，则获取教师列表\r\nwatch(() => dialogVisible.value, (newVal) => {\r\n  if (newVal && userForm.role === 'teacher' && teacherList.value.length === 0) {\r\n    fetchTeacherList()\r\n  }\r\n})\r\n\r\n// 表单校验规则\r\nconst userFormRules = {\r\n  username: [\r\n    { required: true, message: '请输入用户名', trigger: 'blur' },\r\n    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  name: [\r\n    { required: true, message: '请输入姓名', trigger: 'blur' }\r\n  ],\r\n  password: [\r\n    { required: true, message: '请输入密码', trigger: 'blur' },\r\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\r\n  ],\r\n  phone: [\r\n    { required: true, message: '请输入手机号', trigger: 'blur' },\r\n    { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\r\n  ],\r\n  email: [\r\n    { required: true, message: '请输入邮箱', trigger: 'blur' },\r\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\r\n  ],\r\n  role: [\r\n    { required: true, message: '请选择角色', trigger: 'change' }\r\n  ],\r\n  teacher_id: [\r\n    { \r\n      validator: (rule, value, callback) => {\r\n        if (userForm.role === 'teacher' && !value) {\r\n          callback(new Error('请选择关联的教师'));\r\n        } else {\r\n          callback();\r\n        }\r\n      }, \r\n      trigger: 'change' \r\n    }\r\n  ]\r\n}\r\n\r\n// 用户数据\r\nconst userList = ref([])\r\n\r\nonMounted(() => {\r\n  fetchData()\r\n})\r\n\r\n// 格式化日期\r\nconst formatDate = (dateString) => {\r\n  if (!dateString) return ''\r\n  const date = new Date(dateString)\r\n  const year = date.getFullYear()\r\n  const month = String(date.getMonth() + 1).padStart(2, '0')\r\n  const day = String(date.getDate()).padStart(2, '0')\r\n  const hours = String(date.getHours()).padStart(2, '0')\r\n  const minutes = String(date.getMinutes()).padStart(2, '0')\r\n  return `${year}-${month}-${day} ${hours}:${minutes}`\r\n}\r\n\r\n// 获取数据\r\nconst fetchData = async () => {\r\n  loading.value = true\r\n  try {\r\n    const response = await userService.getUsers({\r\n      page: currentPage.value,\r\n      limit: pageSize.value,\r\n      username: searchForm.username || undefined,\r\n      phone: searchForm.phone || undefined,\r\n      status: searchForm.status || undefined\r\n    })\r\n    userList.value = response.data.data\r\n    total.value = response.data.count || 0\r\n  } catch (error) {\r\n    console.error('获取用户列表失败:', error)\r\n    ElMessage.error('获取用户列表失败')\r\n  } finally {\r\n    loading.value = false\r\n  }\r\n}\r\n\r\n// 查询\r\nconst handleSearch = () => {\r\n  currentPage.value = 1\r\n  fetchData()\r\n}\r\n\r\n// 重置表单\r\nconst resetForm = () => {\r\n  Object.keys(searchForm).forEach(key => {\r\n    searchForm[key] = ''\r\n  })\r\n  handleSearch()\r\n}\r\n\r\n// 刷新表格\r\nconst refreshTable = () => {\r\n  fetchData()\r\n}\r\n\r\n// 多选变化\r\nconst handleSelectionChange = (selection) => {\r\n  multipleSelection.value = selection\r\n}\r\n\r\n// 新增用户\r\nconst handleAdd = () => {\r\n  dialogType.value = 'add'\r\n  resetUserForm()\r\n  dialogVisible.value = true\r\n}\r\n\r\n// 编辑用户\r\nconst handleEdit = (row) => {\r\n  dialogType.value = 'edit'\r\n  resetUserForm()\r\n  Object.keys(userForm).forEach(key => {\r\n    if (key !== 'password') {\r\n      userForm[key] = row[key]\r\n    }\r\n  })\r\n  dialogVisible.value = true\r\n}\r\n\r\n// 重置用户表单\r\nconst resetUserForm = () => {\r\n  if (userFormRef.value) {\r\n    userFormRef.value.resetFields()\r\n  }\r\n  Object.assign(userForm, {\r\n    id: '',\r\n    username: '',\r\n    name: '',\r\n    password: '',\r\n    phone: '',\r\n    email: '',\r\n    role: 'teacher',\r\n    teacher_id: '',\r\n    status: 1\r\n  })\r\n}\r\n\r\n// 提交表单\r\nconst submitForm = async () => {\r\n  if (!userFormRef.value) return\r\n  \r\n  await userFormRef.value.validate(async (valid) => {\r\n    if (valid) {\r\n      try {\r\n        if (dialogType.value === 'add') {\r\n          // 新增用户\r\n          if (userForm.role !== 'teacher') {\r\n           delete userForm.teacher_id\r\n          }\r\n          await userService.createUser(userForm)\r\n          ElMessage.success('新增用户成功')\r\n        } else {\r\n          // 编辑用户\r\n          await userService.updateUser(userForm.id, userForm)\r\n          ElMessage.success('编辑用户成功')\r\n        }\r\n        dialogVisible.value = false\r\n        fetchData()\r\n      } catch (error) {\r\n        console.error('保存用户失败:', error)\r\n        ElMessage.error('保存用户失败: ' + (error.response?.data?.message || error.message))\r\n      }\r\n    } else {\r\n      return false\r\n    }\r\n  })\r\n}\r\n\r\n// 删除用户\r\nconst handleDelete = (row) => {\r\n  ElMessageBox.confirm(`确定要删除用户 ${row.username} 吗?`, '警告', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    try {\r\n      await userService.deleteUser(row.id)\r\n      ElMessage.success(`用户 ${row.username} 已删除`)\r\n      fetchData()\r\n    } catch (error) {\r\n      console.error('删除用户失败:', error)\r\n      ElMessage.error('删除用户失败: ' + (error.response?.data?.message || error.message))\r\n    }\r\n  }).catch(() => {})\r\n}\r\n\r\n// 批量删除\r\nconst handleBatchDelete = () => {\r\n  if (multipleSelection.value.length === 0) {\r\n    ElMessage.warning('请至少选择一条记录')\r\n    return\r\n  }\r\n  \r\n  const names = multipleSelection.value.map(item => item.username).join('、')\r\n  const ids = multipleSelection.value.map(item => item.id)\r\n  \r\n  ElMessageBox.confirm(`确定要删除选中的 ${multipleSelection.value.length} 条记录吗?`, '警告', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(async () => {\r\n    try {\r\n      await userService.batchDeleteUsers(ids)\r\n      ElMessage.success('批量删除成功')\r\n      fetchData()\r\n    } catch (error) {\r\n      console.error('批量删除失败:', error)\r\n      ElMessage.error('批量删除失败: ' + (error.response?.data?.message || error.message))\r\n    }\r\n  }).catch(() => {})\r\n}\r\n\r\n// 修改状态\r\nconst handleStatusChange = async (val, row) => {\r\n  try {\r\n    await userService.updateUserStatus(row.id, val);\r\n    const status = val === 1 ? '启用' : '禁用';\r\n    ElMessage.success(`已${status}用户 ${row.username}`);\r\n  } catch (error) {\r\n    console.error('修改状态失败:', error);\r\n    ElMessage.error('修改状态失败: ' + (error.response?.data?.message || error.message));\r\n    // 回滚状态\r\n    row.status = val === 1 ? 0 : 1;\r\n  }\r\n};\r\n\r\n// 分页大小变化\r\nconst handleSizeChange = (size) => {\r\n  pageSize.value = size\r\n  fetchData()\r\n}\r\n\r\n// 页码变化\r\nconst handleCurrentChange = (page) => {\r\n  currentPage.value = page\r\n  fetchData()\r\n}\r\n\r\n// 下载导入模板\r\nconst downloadTemplate = async () => {\r\n  try {\r\n    const response = await fetch(`${baseUrl}/api/users/import/template`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Authorization': `Bearer ${localStorage.getItem('token')}`\r\n      }\r\n    })\r\n\r\n    if (!response.ok) {\r\n      throw new Error('下载失败')\r\n    }\r\n\r\n    const blob = await response.blob()\r\n    const url = window.URL.createObjectURL(blob)\r\n    const link = document.createElement('a')\r\n    link.href = url\r\n    link.download = 'user_import_template.xlsx'\r\n    document.body.appendChild(link)\r\n    link.click()\r\n    document.body.removeChild(link)\r\n    window.URL.revokeObjectURL(url)\r\n\r\n    ElMessage.success('模板下载成功')\r\n  } catch (error) {\r\n    console.error('下载模板失败:', error)\r\n    ElMessage.error('下载模板失败')\r\n  }\r\n}\r\n\r\n// 打开导入对话框\r\nconst openImportDialog = () => {\r\n  importDialogVisible.value = true\r\n  fileList.value = []\r\n  importResult.value = null\r\n}\r\n\r\n// 文件选择变化处理\r\nconst handleFileChange = (file, fileListParam) => {\r\n  console.log('文件选择变化:', file, fileListParam)\r\n  fileList.value = fileListParam\r\n}\r\n\r\n// Excel文件上传前验证\r\nconst beforeExcelUpload = (file) => {\r\n  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\r\n                 file.type === 'application/vnd.ms-excel'\r\n  const isLt10M = file.size / 1024 / 1024 < 10\r\n\r\n  if (!isExcel) {\r\n    ElMessage.error('只能上传Excel文件!')\r\n    return false\r\n  }\r\n\r\n  if (!isLt10M) {\r\n    ElMessage.error('文件大小不能超过10MB!')\r\n    return false\r\n  }\r\n\r\n  return true\r\n}\r\n\r\n// 手动上传\r\nconst handleUpload = () => {\r\n  if (fileList.value.length === 0) {\r\n    ElMessage.error('请先选择要上传的文件')\r\n    return\r\n  }\r\n\r\n  uploading.value = true\r\n  uploadRef.value.submit()\r\n}\r\n\r\n// 上传成功回调\r\nconst handleImportSuccess = (response) => {\r\n  uploading.value = false\r\n  console.log('导入成功:', response)\r\n\r\n  if (response.success) {\r\n    importResult.value = response.data\r\n    ElMessage.success(response.message)\r\n    // 刷新用户列表\r\n    fetchData()\r\n  } else {\r\n    ElMessage.error(response.message || '导入失败')\r\n  }\r\n}\r\n\r\n// 上传失败回调\r\nconst handleImportError = (error) => {\r\n  uploading.value = false\r\n  console.error('导入失败:', error)\r\n  ElMessage.error('导入失败，请检查文件格式')\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.user-list-container {\r\n  padding: 10px;\r\n}\r\n\r\n.search-card {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.search-form {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: flex-end;\r\n}\r\n\r\n.teacher-option {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.teacher-dept {\r\n  color: #909399;\r\n  font-size: 0.9em;\r\n}\r\n\r\n.form-tip {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-top: 5px;\r\n}\r\n</style> "], "mappings": ";;;;;;AAiQA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,QAAQ,KAAK;AACrD,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,SAASC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAEC,MAAM,QAAQ,yBAAyB;AACvE,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,cAAc,MAAM,2BAA2B;;;;;;;IAEtD,MAAMC,OAAO,GAAGZ,GAAG,CAAC,KAAK,CAAC;IAC1B,MAAMa,kBAAkB,GAAGb,GAAG,CAAC,KAAK,CAAC;IACrC,MAAMc,WAAW,GAAGd,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAMe,QAAQ,GAAGf,GAAG,CAAC,EAAE,CAAC;IACxB,MAAMgB,KAAK,GAAGhB,GAAG,CAAC,CAAC,CAAC;IACpB,MAAMiB,iBAAiB,GAAGjB,GAAG,CAAC,EAAE,CAAC;IACjC,MAAMkB,aAAa,GAAGlB,GAAG,CAAC,KAAK,CAAC;IAChC,MAAMmB,UAAU,GAAGnB,GAAG,CAAC,KAAK,CAAC,EAAC;IAC9B,MAAMoB,WAAW,GAAGpB,GAAG,CAAC,IAAI,CAAC;IAC7B,MAAMqB,WAAW,GAAGrB,GAAG,CAAC,EAAE,CAAC,EAAC;;IAE5B;IACA,MAAMsB,mBAAmB,GAAGtB,GAAG,CAAC,KAAK,CAAC;IACtC,MAAMuB,SAAS,GAAGvB,GAAG,CAAC,KAAK,CAAC;IAC5B,MAAMwB,SAAS,GAAGxB,GAAG,CAAC,IAAI,CAAC;IAC3B,MAAMyB,QAAQ,GAAGzB,GAAG,CAAC,EAAE,CAAC;IACxB,MAAM0B,YAAY,GAAG1B,GAAG,CAAC,IAAI,CAAC;IAC9B,MAAM2B,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;;IAE5E;IACA,MAAMC,aAAa,GAAG;MACpB,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC1D,CAAC;;IAED;IACA,MAAMC,UAAU,GAAGlC,QAAQ,CAAC;MAC1BmC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,MAAM,EAAE;IACV,CAAC,CAAC;;IAEF;IACA,MAAMC,QAAQ,GAAGtC,QAAQ,CAAC;MACxBuC,EAAE,EAAE,EAAE;MACNJ,QAAQ,EAAE,EAAE;MACZK,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE,EAAE;MACZL,KAAK,EAAE,EAAE;MACTM,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,SAAS;MACfC,UAAU,EAAE,EAAE;MACdP,MAAM,EAAE;IACV,CAAC,CAAC;;IAEF;IACA,MAAMQ,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnCjC,kBAAkB,CAACkC,KAAK,GAAG,IAAI;MAC/B,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMrC,cAAc,CAACsC,WAAW,CAAC,CAAC;QACnD5B,WAAW,CAAC0B,KAAK,GAAGC,QAAQ,CAACE,IAAI,CAACA,IAAI;MACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC/C,SAAS,CAAC+C,KAAK,CAAC,UAAU,CAAC;MAC7B,CAAC,SAAS;QACRtC,kBAAkB,CAACkC,KAAK,GAAG,KAAK;MAClC;IACF,CAAC;;IAED;IACA5C,KAAK,CAAC,MAAMoC,QAAQ,CAACK,IAAI,EAAGS,OAAO,IAAK;MACtC,IAAIA,OAAO,KAAK,SAAS,IAAIhC,WAAW,CAAC0B,KAAK,CAACO,MAAM,KAAK,CAAC,EAAE;QAC3DR,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;;IAEF;IACA3C,KAAK,CAAC,MAAMe,aAAa,CAAC6B,KAAK,EAAGQ,MAAM,IAAK;MAC3C,IAAIA,MAAM,IAAIhB,QAAQ,CAACK,IAAI,KAAK,SAAS,IAAIvB,WAAW,CAAC0B,KAAK,CAACO,MAAM,KAAK,CAAC,EAAE;QAC3ER,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,CAAC;;IAEF;IACA,MAAMU,aAAa,GAAG;MACpBpB,QAAQ,EAAE,CACR;QAAEqB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,EACtD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,gBAAgB;QAAEC,OAAO,EAAE;MAAO,CAAC,CAChE;MACDlB,IAAI,EAAE,CACJ;QAAEgB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,CACtD;MACDjB,QAAQ,EAAE,CACR;QAAEe,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,gBAAgB;QAAEC,OAAO,EAAE;MAAO,CAAC,CAChE;MACDtB,KAAK,EAAE,CACL;QAAEoB,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,EACtD;QAAEG,OAAO,EAAE,eAAe;QAAEJ,OAAO,EAAE,WAAW;QAAEC,OAAO,EAAE;MAAO,CAAC,CACpE;MACDhB,KAAK,EAAE,CACL;QAAEc,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEI,IAAI,EAAE,OAAO;QAAEL,OAAO,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAO,CAAC,CAC1D;MACDf,IAAI,EAAE,CACJ;QAAEa,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS,CAAC,CACxD;MACDd,UAAU,EAAE,CACV;QACEmB,SAAS,EAAEA,CAACC,IAAI,EAAElB,KAAK,EAAEmB,QAAQ,KAAK;UACpC,IAAI3B,QAAQ,CAACK,IAAI,KAAK,SAAS,IAAI,CAACG,KAAK,EAAE;YACzCmB,QAAQ,CAAC,IAAIC,KAAK,CAAC,UAAU,CAAC,CAAC;UACjC,CAAC,MAAM;YACLD,QAAQ,CAAC,CAAC;UACZ;QACF,CAAC;QACDP,OAAO,EAAE;MACX,CAAC;IAEL,CAAC;;IAED;IACA,MAAMS,QAAQ,GAAGpE,GAAG,CAAC,EAAE,CAAC;IAExBE,SAAS,CAAC,MAAM;MACdmE,SAAS,CAAC,CAAC;IACb,CAAC,CAAC;;IAEF;IACA,MAAMC,UAAU,GAAIC,UAAU,IAAK;MACjC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;MAC1B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;MACjC,MAAMG,IAAI,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;MAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACM,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACL,IAAI,CAACS,OAAO,CAAC,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACnD,MAAMG,KAAK,GAAGL,MAAM,CAACL,IAAI,CAACW,QAAQ,CAAC,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACtD,MAAMK,OAAO,GAAGP,MAAM,CAACL,IAAI,CAACa,UAAU,CAAC,CAAC,CAAC,CAACN,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC1D,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,IAAIE,KAAK,IAAIE,OAAO,EAAE;IACtD,CAAC;;IAED;IACA,MAAMf,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BzD,OAAO,CAACmC,KAAK,GAAG,IAAI;MACpB,IAAI;QACF,MAAMC,QAAQ,GAAG,MAAMtC,WAAW,CAAC4E,QAAQ,CAAC;UAC1CC,IAAI,EAAEzE,WAAW,CAACiC,KAAK;UACvByC,KAAK,EAAEzE,QAAQ,CAACgC,KAAK;UACrBX,QAAQ,EAAED,UAAU,CAACC,QAAQ,IAAIqD,SAAS;UAC1CpD,KAAK,EAAEF,UAAU,CAACE,KAAK,IAAIoD,SAAS;UACpCnD,MAAM,EAAEH,UAAU,CAACG,MAAM,IAAImD;QAC/B,CAAC,CAAC;QACFrB,QAAQ,CAACrB,KAAK,GAAGC,QAAQ,CAACE,IAAI,CAACA,IAAI;QACnClC,KAAK,CAAC+B,KAAK,GAAGC,QAAQ,CAACE,IAAI,CAACwC,KAAK,IAAI,CAAC;MACxC,CAAC,CAAC,OAAOvC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC/C,SAAS,CAAC+C,KAAK,CAAC,UAAU,CAAC;MAC7B,CAAC,SAAS;QACRvC,OAAO,CAACmC,KAAK,GAAG,KAAK;MACvB;IACF,CAAC;;IAED;IACA,MAAM4C,YAAY,GAAGA,CAAA,KAAM;MACzB7E,WAAW,CAACiC,KAAK,GAAG,CAAC;MACrBsB,SAAS,CAAC,CAAC;IACb,CAAC;;IAED;IACA,MAAMuB,SAAS,GAAGA,CAAA,KAAM;MACtBC,MAAM,CAACC,IAAI,CAAC3D,UAAU,CAAC,CAAC4D,OAAO,CAACC,GAAG,IAAI;QACrC7D,UAAU,CAAC6D,GAAG,CAAC,GAAG,EAAE;MACtB,CAAC,CAAC;MACFL,YAAY,CAAC,CAAC;IAChB,CAAC;;IAED;IACA,MAAMM,YAAY,GAAGA,CAAA,KAAM;MACzB5B,SAAS,CAAC,CAAC;IACb,CAAC;;IAED;IACA,MAAM6B,qBAAqB,GAAIC,SAAS,IAAK;MAC3ClF,iBAAiB,CAAC8B,KAAK,GAAGoD,SAAS;IACrC,CAAC;;IAED;IACA,MAAMC,SAAS,GAAGA,CAAA,KAAM;MACtBjF,UAAU,CAAC4B,KAAK,GAAG,KAAK;MACxBsD,aAAa,CAAC,CAAC;MACfnF,aAAa,CAAC6B,KAAK,GAAG,IAAI;IAC5B,CAAC;;IAED;IACA,MAAMuD,UAAU,GAAIC,GAAG,IAAK;MAC1BpF,UAAU,CAAC4B,KAAK,GAAG,MAAM;MACzBsD,aAAa,CAAC,CAAC;MACfR,MAAM,CAACC,IAAI,CAACvD,QAAQ,CAAC,CAACwD,OAAO,CAACC,GAAG,IAAI;QACnC,IAAIA,GAAG,KAAK,UAAU,EAAE;UACtBzD,QAAQ,CAACyD,GAAG,CAAC,GAAGO,GAAG,CAACP,GAAG,CAAC;QAC1B;MACF,CAAC,CAAC;MACF9E,aAAa,CAAC6B,KAAK,GAAG,IAAI;IAC5B,CAAC;;IAED;IACA,MAAMsD,aAAa,GAAGA,CAAA,KAAM;MAC1B,IAAIjF,WAAW,CAAC2B,KAAK,EAAE;QACrB3B,WAAW,CAAC2B,KAAK,CAACyD,WAAW,CAAC,CAAC;MACjC;MACAX,MAAM,CAACY,MAAM,CAAClE,QAAQ,EAAE;QACtBC,EAAE,EAAE,EAAE;QACNJ,QAAQ,EAAE,EAAE;QACZK,IAAI,EAAE,EAAE;QACRC,QAAQ,EAAE,EAAE;QACZL,KAAK,EAAE,EAAE;QACTM,KAAK,EAAE,EAAE;QACTC,IAAI,EAAE,SAAS;QACfC,UAAU,EAAE,EAAE;QACdP,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMoE,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI,CAACtF,WAAW,CAAC2B,KAAK,EAAE;MAExB,MAAM3B,WAAW,CAAC2B,KAAK,CAAC4D,QAAQ,CAAC,MAAOC,KAAK,IAAK;QAChD,IAAIA,KAAK,EAAE;UACT,IAAI;YACF,IAAIzF,UAAU,CAAC4B,KAAK,KAAK,KAAK,EAAE;cAC9B;cACA,IAAIR,QAAQ,CAACK,IAAI,KAAK,SAAS,EAAE;gBAChC,OAAOL,QAAQ,CAACM,UAAU;cAC3B;cACA,MAAMnC,WAAW,CAACmG,UAAU,CAACtE,QAAQ,CAAC;cACtCnC,SAAS,CAAC0G,OAAO,CAAC,QAAQ,CAAC;YAC7B,CAAC,MAAM;cACL;cACA,MAAMpG,WAAW,CAACqG,UAAU,CAACxE,QAAQ,CAACC,EAAE,EAAED,QAAQ,CAAC;cACnDnC,SAAS,CAAC0G,OAAO,CAAC,QAAQ,CAAC;YAC7B;YACA5F,aAAa,CAAC6B,KAAK,GAAG,KAAK;YAC3BsB,SAAS,CAAC,CAAC;UACb,CAAC,CAAC,OAAOlB,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;YAC/B/C,SAAS,CAAC+C,KAAK,CAAC,UAAU,IAAIA,KAAK,CAACH,QAAQ,EAAEE,IAAI,EAAEQ,OAAO,IAAIP,KAAK,CAACO,OAAO,CAAC,CAAC;UAChF;QACF,CAAC,MAAM;UACL,OAAO,KAAK;QACd;MACF,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMsD,YAAY,GAAIT,GAAG,IAAK;MAC5BlG,YAAY,CAAC4G,OAAO,CAAC,WAAWV,GAAG,CAACnE,QAAQ,KAAK,EAAE,IAAI,EAAE;QACvD8E,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBpD,IAAI,EAAE;MACR,CAAC,CAAC,CAACqD,IAAI,CAAC,YAAY;QAClB,IAAI;UACF,MAAM1G,WAAW,CAAC2G,UAAU,CAACd,GAAG,CAAC/D,EAAE,CAAC;UACpCpC,SAAS,CAAC0G,OAAO,CAAC,MAAMP,GAAG,CAACnE,QAAQ,MAAM,CAAC;UAC3CiC,SAAS,CAAC,CAAC;QACb,CAAC,CAAC,OAAOlB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/B/C,SAAS,CAAC+C,KAAK,CAAC,UAAU,IAAIA,KAAK,CAACH,QAAQ,EAAEE,IAAI,EAAEQ,OAAO,IAAIP,KAAK,CAACO,OAAO,CAAC,CAAC;QAChF;MACF,CAAC,CAAC,CAAC4D,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED;IACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,IAAItG,iBAAiB,CAAC8B,KAAK,CAACO,MAAM,KAAK,CAAC,EAAE;QACxClD,SAAS,CAACoH,OAAO,CAAC,WAAW,CAAC;QAC9B;MACF;MAEA,MAAMC,KAAK,GAAGxG,iBAAiB,CAAC8B,KAAK,CAAC2E,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACvF,QAAQ,CAAC,CAACwF,IAAI,CAAC,GAAG,CAAC;MAC1E,MAAMC,GAAG,GAAG5G,iBAAiB,CAAC8B,KAAK,CAAC2E,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACnF,EAAE,CAAC;MAExDnC,YAAY,CAAC4G,OAAO,CAAC,YAAYhG,iBAAiB,CAAC8B,KAAK,CAACO,MAAM,QAAQ,EAAE,IAAI,EAAE;QAC7E4D,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBpD,IAAI,EAAE;MACR,CAAC,CAAC,CAACqD,IAAI,CAAC,YAAY;QAClB,IAAI;UACF,MAAM1G,WAAW,CAACoH,gBAAgB,CAACD,GAAG,CAAC;UACvCzH,SAAS,CAAC0G,OAAO,CAAC,QAAQ,CAAC;UAC3BzC,SAAS,CAAC,CAAC;QACb,CAAC,CAAC,OAAOlB,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;UAC/B/C,SAAS,CAAC+C,KAAK,CAAC,UAAU,IAAIA,KAAK,CAACH,QAAQ,EAAEE,IAAI,EAAEQ,OAAO,IAAIP,KAAK,CAACO,OAAO,CAAC,CAAC;QAChF;MACF,CAAC,CAAC,CAAC4D,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;IACpB,CAAC;;IAED;IACA,MAAMS,kBAAkB,GAAG,MAAAA,CAAOC,GAAG,EAAEzB,GAAG,KAAK;MAC7C,IAAI;QACF,MAAM7F,WAAW,CAACuH,gBAAgB,CAAC1B,GAAG,CAAC/D,EAAE,EAAEwF,GAAG,CAAC;QAC/C,MAAM1F,MAAM,GAAG0F,GAAG,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI;QACtC5H,SAAS,CAAC0G,OAAO,CAAC,IAAIxE,MAAM,MAAMiE,GAAG,CAACnE,QAAQ,EAAE,CAAC;MACnD,CAAC,CAAC,OAAOe,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B/C,SAAS,CAAC+C,KAAK,CAAC,UAAU,IAAIA,KAAK,CAACH,QAAQ,EAAEE,IAAI,EAAEQ,OAAO,IAAIP,KAAK,CAACO,OAAO,CAAC,CAAC;QAC9E;QACA6C,GAAG,CAACjE,MAAM,GAAG0F,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;MAChC;IACF,CAAC;;IAED;IACA,MAAME,gBAAgB,GAAIC,IAAI,IAAK;MACjCpH,QAAQ,CAACgC,KAAK,GAAGoF,IAAI;MACrB9D,SAAS,CAAC,CAAC;IACb,CAAC;;IAED;IACA,MAAM+D,mBAAmB,GAAI7C,IAAI,IAAK;MACpCzE,WAAW,CAACiC,KAAK,GAAGwC,IAAI;MACxBlB,SAAS,CAAC,CAAC;IACb,CAAC;;IAED;IACA,MAAMgE,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAMrF,QAAQ,GAAG,MAAMsF,KAAK,CAAC,GAAG3G,OAAO,4BAA4B,EAAE;UACnE4G,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUvG,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D;QACF,CAAC,CAAC;QAEF,IAAI,CAACc,QAAQ,CAACyF,EAAE,EAAE;UAChB,MAAM,IAAItE,KAAK,CAAC,MAAM,CAAC;QACzB;QAEA,MAAMuE,IAAI,GAAG,MAAM1F,QAAQ,CAAC0F,IAAI,CAAC,CAAC;QAClC,MAAMC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAACJ,IAAI,CAAC;QAC5C,MAAMK,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGP,GAAG;QACfI,IAAI,CAACI,QAAQ,GAAG,2BAA2B;QAC3CH,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;QAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;QACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;QAC/BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACb,GAAG,CAAC;QAE/BvI,SAAS,CAAC0G,OAAO,CAAC,QAAQ,CAAC;MAC7B,CAAC,CAAC,OAAO3D,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B/C,SAAS,CAAC+C,KAAK,CAAC,QAAQ,CAAC;MAC3B;IACF,CAAC;;IAED;IACA,MAAMsG,gBAAgB,GAAGA,CAAA,KAAM;MAC7BnI,mBAAmB,CAACyB,KAAK,GAAG,IAAI;MAChCtB,QAAQ,CAACsB,KAAK,GAAG,EAAE;MACnBrB,YAAY,CAACqB,KAAK,GAAG,IAAI;IAC3B,CAAC;;IAED;IACA,MAAM2G,gBAAgB,GAAGA,CAACC,IAAI,EAAEC,aAAa,KAAK;MAChDxG,OAAO,CAACyG,GAAG,CAAC,SAAS,EAAEF,IAAI,EAAEC,aAAa,CAAC;MAC3CnI,QAAQ,CAACsB,KAAK,GAAG6G,aAAa;IAChC,CAAC;;IAED;IACA,MAAME,iBAAiB,GAAIH,IAAI,IAAK;MAClC,MAAMI,OAAO,GAAGJ,IAAI,CAAC5F,IAAI,KAAK,mEAAmE,IAClF4F,IAAI,CAAC5F,IAAI,KAAK,0BAA0B;MACvD,MAAMiG,OAAO,GAAGL,IAAI,CAACxB,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE;MAE5C,IAAI,CAAC4B,OAAO,EAAE;QACZ3J,SAAS,CAAC+C,KAAK,CAAC,cAAc,CAAC;QAC/B,OAAO,KAAK;MACd;MAEA,IAAI,CAAC6G,OAAO,EAAE;QACZ5J,SAAS,CAAC+C,KAAK,CAAC,eAAe,CAAC;QAChC,OAAO,KAAK;MACd;MAEA,OAAO,IAAI;IACb,CAAC;;IAED;IACA,MAAM8G,YAAY,GAAGA,CAAA,KAAM;MACzB,IAAIxI,QAAQ,CAACsB,KAAK,CAACO,MAAM,KAAK,CAAC,EAAE;QAC/BlD,SAAS,CAAC+C,KAAK,CAAC,YAAY,CAAC;QAC7B;MACF;MAEA5B,SAAS,CAACwB,KAAK,GAAG,IAAI;MACtBvB,SAAS,CAACuB,KAAK,CAACmH,MAAM,CAAC,CAAC;IAC1B,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAInH,QAAQ,IAAK;MACxCzB,SAAS,CAACwB,KAAK,GAAG,KAAK;MACvBK,OAAO,CAACyG,GAAG,CAAC,OAAO,EAAE7G,QAAQ,CAAC;MAE9B,IAAIA,QAAQ,CAAC8D,OAAO,EAAE;QACpBpF,YAAY,CAACqB,KAAK,GAAGC,QAAQ,CAACE,IAAI;QAClC9C,SAAS,CAAC0G,OAAO,CAAC9D,QAAQ,CAACU,OAAO,CAAC;QACnC;QACAW,SAAS,CAAC,CAAC;MACb,CAAC,MAAM;QACLjE,SAAS,CAAC+C,KAAK,CAACH,QAAQ,CAACU,OAAO,IAAI,MAAM,CAAC;MAC7C;IACF,CAAC;;IAED;IACA,MAAM0G,iBAAiB,GAAIjH,KAAK,IAAK;MACnC5B,SAAS,CAACwB,KAAK,GAAG,KAAK;MACvBK,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7B/C,SAAS,CAAC+C,KAAK,CAAC,cAAc,CAAC;IACjC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}