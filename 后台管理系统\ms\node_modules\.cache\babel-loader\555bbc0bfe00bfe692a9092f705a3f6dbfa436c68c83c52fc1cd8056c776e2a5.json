{"ast": null, "code": "import { createApp } from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport store from './store';\nimport api from './utils/api';\nimport axios from 'axios';\n\n// Import Element Plus\nimport ElementPlus from 'element-plus';\nimport 'element-plus/dist/index.css';\n// Import icons\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue';\n\n// 配置全局axios默认值\naxios.defaults.baseURL = 'http://localhost:3000';\nconst app = createApp(App);\n\n// 全局API实例\napp.config.globalProperties.$api = api;\napp.config.globalProperties.$axios = axios;\n\n// Register all icons globally\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component);\n}\nconst debounce = (fn, delay) => {\n  let timer;\n  return (...args) => {\n    if (timer) {\n      clearTimeout(timer);\n    }\n    timer = setTimeout(() => {\n      fn(...args);\n    }, delay);\n  };\n};\nconst _ResizeObserver = window.ResizeObserver;\nwindow.ResizeObserver = class ResizeObserver extends _ResizeObserver {\n  constructor(callback) {\n    callback = debounce(callback, 200);\n    super(callback);\n  }\n};\napp.use(store).use(router).use(ElementPlus).mount('#app');", "map": {"version": 3, "names": ["createApp", "App", "router", "store", "api", "axios", "ElementPlus", "ElementPlusIconsVue", "defaults", "baseURL", "app", "config", "globalProperties", "$api", "$axios", "key", "component", "Object", "entries", "debounce", "fn", "delay", "timer", "args", "clearTimeout", "setTimeout", "_ResizeObserver", "window", "ResizeObserver", "constructor", "callback", "use", "mount"], "sources": ["D:/admin/202506/督导系统/后台管理系统/ms/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\r\nimport App from './App.vue'\r\nimport router from './router'\r\nimport store from './store'\r\nimport api from './utils/api'\r\nimport axios from 'axios'\r\n\r\n// Import Element Plus\r\nimport ElementPlus from 'element-plus'\r\nimport 'element-plus/dist/index.css'\r\n// Import icons\r\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue'\r\n\r\n// 配置全局axios默认值\r\naxios.defaults.baseURL = 'http://localhost:3000'\r\n\r\nconst app = createApp(App)\r\n\r\n// 全局API实例\r\napp.config.globalProperties.$api = api\r\napp.config.globalProperties.$axios = axios\r\n\r\n// Register all icons globally\r\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\r\n  app.component(key, component)\r\n}\r\nconst debounce = (fn, delay) => {\r\n  let timer\r\n   return (...args) => {\r\n     if (timer) {\r\n       clearTimeout(timer)\r\n     }\r\n     timer = setTimeout(() => {\r\n       fn(...args)\r\n     }, delay)\r\n   }\r\n}\r\nconst _ResizeObserver = window.ResizeObserver;\r\nwindow.ResizeObserver = class ResizeObserver extends _ResizeObserver{\r\n   constructor(callback) {\r\n     callback = debounce(callback, 200);\r\n     super(callback);\r\n   }\r\n}\r\napp.use(store).use(router).use(ElementPlus).mount('#app')\r\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,GAAG,MAAM,aAAa;AAC7B,OAAOC,KAAK,MAAM,OAAO;;AAEzB;AACA,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAO,6BAA6B;AACpC;AACA,OAAO,KAAKC,mBAAmB,MAAM,yBAAyB;;AAE9D;AACAF,KAAK,CAACG,QAAQ,CAACC,OAAO,GAAG,uBAAuB;AAEhD,MAAMC,GAAG,GAAGV,SAAS,CAACC,GAAG,CAAC;;AAE1B;AACAS,GAAG,CAACC,MAAM,CAACC,gBAAgB,CAACC,IAAI,GAAGT,GAAG;AACtCM,GAAG,CAACC,MAAM,CAACC,gBAAgB,CAACE,MAAM,GAAGT,KAAK;;AAE1C;AACA,KAAK,MAAM,CAACU,GAAG,EAAEC,SAAS,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACX,mBAAmB,CAAC,EAAE;EAClEG,GAAG,CAACM,SAAS,CAACD,GAAG,EAAEC,SAAS,CAAC;AAC/B;AACA,MAAMG,QAAQ,GAAGA,CAACC,EAAE,EAAEC,KAAK,KAAK;EAC9B,IAAIC,KAAK;EACR,OAAO,CAAC,GAAGC,IAAI,KAAK;IAClB,IAAID,KAAK,EAAE;MACTE,YAAY,CAACF,KAAK,CAAC;IACrB;IACAA,KAAK,GAAGG,UAAU,CAAC,MAAM;MACvBL,EAAE,CAAC,GAAGG,IAAI,CAAC;IACb,CAAC,EAAEF,KAAK,CAAC;EACX,CAAC;AACJ,CAAC;AACD,MAAMK,eAAe,GAAGC,MAAM,CAACC,cAAc;AAC7CD,MAAM,CAACC,cAAc,GAAG,MAAMA,cAAc,SAASF,eAAe;EACjEG,WAAWA,CAACC,QAAQ,EAAE;IACpBA,QAAQ,GAAGX,QAAQ,CAACW,QAAQ,EAAE,GAAG,CAAC;IAClC,KAAK,CAACA,QAAQ,CAAC;EACjB;AACH,CAAC;AACDpB,GAAG,CAACqB,GAAG,CAAC5B,KAAK,CAAC,CAAC4B,GAAG,CAAC7B,MAAM,CAAC,CAAC6B,GAAG,CAACzB,WAAW,CAAC,CAAC0B,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}