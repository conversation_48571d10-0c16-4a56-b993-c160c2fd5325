"use strict";(self["webpackChunkms"]=self["webpackChunkms"]||[]).push([[385],{3766:function(e,a,l){l.r(a),l.d(a,{default:function(){return F}});var o=l(6768),r=l(4232);const t={class:"teacher-list-container"},d={class:"card-header"},n={class:"pagination-container"},s=["src"],u={class:"dialog-footer"};function p(e,a,l,p,i,m){const c=(0,o.g2)("el-button"),b=(0,o.g2)("el-input"),h=(0,o.g2)("el-form-item"),g=(0,o.g2)("el-option"),f=(0,o.g2)("el-select"),k=(0,o.g2)("el-form"),_=(0,o.g2)("el-table-column"),F=(0,o.g2)("el-tag"),v=(0,o.g2)("el-image"),y=(0,o.g2)("el-avatar"),V=(0,o.g2)("el-table"),w=(0,o.g2)("el-pagination"),C=(0,o.g2)("el-card"),D=(0,o.g2)("el-radio"),U=(0,o.g2)("el-radio-group"),P=(0,o.g2)("el-switch"),W=(0,o.g2)("Plus"),z=(0,o.g2)("el-icon"),$=(0,o.g2)("el-upload"),R=(0,o.g2)("el-dialog"),S=(0,o.gN)("loading");return(0,o.uX)(),(0,o.CE)("div",t,[(0,o.bF)(C,{class:"box-card"},{header:(0,o.k6)(()=>[(0,o.Lk)("div",d,[a[19]||(a[19]=(0,o.Lk)("span",{class:"title"},"教师管理",-1)),(0,o.Lk)("div",null,[(0,o.bF)(c,{type:"danger"},{default:(0,o.k6)(()=>a[17]||(a[17]=[(0,o.eW)("一键导入")])),_:1,__:[17]}),(0,o.bF)(c,{type:"primary",onClick:a[0]||(a[0]=e=>p.openDialog())},{default:(0,o.k6)(()=>a[18]||(a[18]=[(0,o.eW)("添加教师")])),_:1,__:[18]})])])]),default:(0,o.k6)(()=>[(0,o.bF)(k,{inline:!0,model:p.searchForm,class:"search-form"},{default:(0,o.k6)(()=>[(0,o.bF)(h,{label:"姓名"},{default:(0,o.k6)(()=>[(0,o.bF)(b,{modelValue:p.searchForm.name,"onUpdate:modelValue":a[1]||(a[1]=e=>p.searchForm.name=e),placeholder:"教师姓名",clearable:""},null,8,["modelValue"])]),_:1}),(0,o.bF)(h,{label:"科室"},{default:(0,o.k6)(()=>[(0,o.bF)(b,{modelValue:p.searchForm.department,"onUpdate:modelValue":a[2]||(a[2]=e=>p.searchForm.department=e),placeholder:"所属科室",clearable:""},null,8,["modelValue"])]),_:1}),(0,o.bF)(h,{label:"在聘状态"},{default:(0,o.k6)(()=>[(0,o.bF)(f,{modelValue:p.searchForm.is_employed,"onUpdate:modelValue":a[3]||(a[3]=e=>p.searchForm.is_employed=e),placeholder:"是否在聘",clearable:"",style:{width:"120px"}},{default:(0,o.k6)(()=>[(0,o.bF)(g,{label:"在聘",value:1}),(0,o.bF)(g,{label:"不在聘",value:0})]),_:1},8,["modelValue"])]),_:1}),(0,o.bF)(h,null,{default:(0,o.k6)(()=>[(0,o.bF)(c,{type:"primary",onClick:p.handleSearch},{default:(0,o.k6)(()=>a[20]||(a[20]=[(0,o.eW)("查询")])),_:1,__:[20]},8,["onClick"]),(0,o.bF)(c,{onClick:p.resetSearch},{default:(0,o.k6)(()=>a[21]||(a[21]=[(0,o.eW)("重置")])),_:1,__:[21]},8,["onClick"])]),_:1})]),_:1},8,["model"]),(0,o.bo)(((0,o.uX)(),(0,o.Wv)(V,{data:p.teacherList,border:"",style:{width:"100%"}},{default:(0,o.k6)(()=>[(0,o.bF)(_,{prop:"id",label:"ID"}),(0,o.bF)(_,{prop:"name",label:"姓名"}),(0,o.bF)(_,{prop:"gender",label:"性别"}),(0,o.bF)(_,{prop:"department",label:"科室"}),(0,o.bF)(_,{prop:"school",label:"学校"}),(0,o.bF)(_,{prop:"major",label:"专业"}),(0,o.bF)(_,{prop:"education",label:"学历"}),(0,o.bF)(_,{label:"在聘状态"},{default:(0,o.k6)(e=>[(0,o.bF)(F,{type:e.row.is_employed?"success":"danger"},{default:(0,o.k6)(()=>[(0,o.eW)((0,r.v_)(e.row.is_employed?"在聘":"不在聘"),1)]),_:2},1032,["type"])]),_:1}),(0,o.bF)(_,{prop:"employment_period",label:"聘期","min-width":"120"}),(0,o.bF)(_,{label:"照片",width:"80"},{default:(0,o.k6)(e=>[e.row.photo?((0,o.uX)(),(0,o.Wv)(v,{key:0,src:`${p.baseUrl}${e.row.photo}`,"preview-src-list":[`${p.baseUrl}${e.row.photo}`],fit:"cover",style:{width:"50px",height:"50px"}},null,8,["src","preview-src-list"])):((0,o.uX)(),(0,o.Wv)(y,{key:1,size:50,icon:"UserFilled"}))]),_:1}),(0,o.bF)(_,{label:"操作",fixed:"right",width:"200"},{default:(0,o.k6)(e=>[(0,o.bF)(c,{size:"small",onClick:a=>p.viewDetails(e.row.id)},{default:(0,o.k6)(()=>a[22]||(a[22]=[(0,o.eW)("详情")])),_:2,__:[22]},1032,["onClick"]),(0,o.bF)(c,{size:"small",type:"primary",onClick:a=>p.openDialog(e.row)},{default:(0,o.k6)(()=>a[23]||(a[23]=[(0,o.eW)("编辑")])),_:2,__:[23]},1032,["onClick"]),(0,o.bF)(c,{size:"small",type:"danger",onClick:a=>p.handleDelete(e.row)},{default:(0,o.k6)(()=>a[24]||(a[24]=[(0,o.eW)("删除")])),_:2,__:[24]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[S,p.loading]]),(0,o.Lk)("div",n,[(0,o.bF)(w,{"current-page":p.currentPage,"onUpdate:currentPage":a[4]||(a[4]=e=>p.currentPage=e),"page-size":p.pageSize,"onUpdate:pageSize":a[5]||(a[5]=e=>p.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:p.total,onSizeChange:p.handleSizeChange,onCurrentChange:p.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1}),(0,o.bF)(R,{modelValue:p.dialogVisible,"onUpdate:modelValue":a[16]||(a[16]=e=>p.dialogVisible=e),title:p.formData.id?"编辑教师":"添加教师",width:"50%","destroy-on-close":""},{footer:(0,o.k6)(()=>[(0,o.Lk)("span",u,[(0,o.bF)(c,{onClick:a[15]||(a[15]=e=>p.dialogVisible=!1)},{default:(0,o.k6)(()=>a[28]||(a[28]=[(0,o.eW)("取消")])),_:1,__:[28]}),(0,o.bF)(c,{type:"primary",onClick:p.submitForm},{default:(0,o.k6)(()=>a[29]||(a[29]=[(0,o.eW)("确定")])),_:1,__:[29]},8,["onClick"])])]),default:(0,o.k6)(()=>[(0,o.bF)(k,{ref:"teacherFormRef",model:p.formData,rules:p.formRules,"label-width":"80px","label-position":"right"},{default:(0,o.k6)(()=>[(0,o.bF)(h,{label:"姓名",prop:"name"},{default:(0,o.k6)(()=>[(0,o.bF)(b,{modelValue:p.formData.name,"onUpdate:modelValue":a[6]||(a[6]=e=>p.formData.name=e),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1}),(0,o.bF)(h,{label:"性别",prop:"gender"},{default:(0,o.k6)(()=>[(0,o.bF)(U,{modelValue:p.formData.gender,"onUpdate:modelValue":a[7]||(a[7]=e=>p.formData.gender=e)},{default:(0,o.k6)(()=>[(0,o.bF)(D,{label:"男"},{default:(0,o.k6)(()=>a[25]||(a[25]=[(0,o.eW)("男")])),_:1,__:[25]}),(0,o.bF)(D,{label:"女"},{default:(0,o.k6)(()=>a[26]||(a[26]=[(0,o.eW)("女")])),_:1,__:[26]})]),_:1},8,["modelValue"])]),_:1}),(0,o.bF)(h,{label:"科室",prop:"department"},{default:(0,o.k6)(()=>[(0,o.bF)(b,{modelValue:p.formData.department,"onUpdate:modelValue":a[8]||(a[8]=e=>p.formData.department=e),placeholder:"请输入科室"},null,8,["modelValue"])]),_:1}),(0,o.bF)(h,{label:"学校",prop:"school"},{default:(0,o.k6)(()=>[(0,o.bF)(b,{modelValue:p.formData.school,"onUpdate:modelValue":a[9]||(a[9]=e=>p.formData.school=e),placeholder:"请输入学校"},null,8,["modelValue"])]),_:1}),(0,o.bF)(h,{label:"专业",prop:"major"},{default:(0,o.k6)(()=>[(0,o.bF)(b,{modelValue:p.formData.major,"onUpdate:modelValue":a[10]||(a[10]=e=>p.formData.major=e),placeholder:"请输入专业"},null,8,["modelValue"])]),_:1}),(0,o.bF)(h,{label:"学历",prop:"education"},{default:(0,o.k6)(()=>[(0,o.bF)(f,{modelValue:p.formData.education,"onUpdate:modelValue":a[11]||(a[11]=e=>p.formData.education=e),placeholder:"请选择学历",style:{width:"100%"}},{default:(0,o.k6)(()=>[(0,o.bF)(g,{label:"博士",value:"博士"}),(0,o.bF)(g,{label:"硕士",value:"硕士"}),(0,o.bF)(g,{label:"本科",value:"本科"}),(0,o.bF)(g,{label:"专科",value:"专科"}),(0,o.bF)(g,{label:"其他",value:"其他"})]),_:1},8,["modelValue"])]),_:1}),(0,o.bF)(h,{label:"在聘状态",prop:"is_employed"},{default:(0,o.k6)(()=>[(0,o.bF)(P,{modelValue:p.formData.is_employed,"onUpdate:modelValue":a[12]||(a[12]=e=>p.formData.is_employed=e),"active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1}),1===p.formData.is_employed?((0,o.uX)(),(0,o.Wv)(h,{key:0,label:"聘期",prop:"employment_period"},{default:(0,o.k6)(()=>[(0,o.bF)(b,{modelValue:p.formData.employment_period,"onUpdate:modelValue":a[13]||(a[13]=e=>p.formData.employment_period=e),placeholder:"例如：2023年6月-2026年5月"},null,8,["modelValue"])]),_:1})):(0,o.Q3)("",!0),(0,o.bF)(h,{label:"联系方式",prop:"phone"},{default:(0,o.k6)(()=>[(0,o.bF)(b,{modelValue:p.formData.phone,"onUpdate:modelValue":a[14]||(a[14]=e=>p.formData.phone=e),placeholder:"请输入联系方式"},null,8,["modelValue"])]),_:1}),(0,o.bF)(h,{label:"照片",prop:"photo"},{default:(0,o.k6)(()=>[(0,o.bF)($,{class:"avatar-uploader",action:`${p.baseUrl}/api/teachers/upload/photo`,headers:p.uploadHeaders,name:"photo","show-file-list":!1,"before-upload":p.beforePhotoUpload,"on-success":p.handlePhotoSuccess,"on-error":p.handlePhotoError},{default:(0,o.k6)(()=>[p.photoPreview?((0,o.uX)(),(0,o.CE)("img",{key:0,src:p.photoPreview,class:"avatar"},null,8,s)):((0,o.uX)(),(0,o.Wv)(z,{key:1,class:"avatar-uploader-icon"},{default:(0,o.k6)(()=>[(0,o.bF)(W)]),_:1}))]),_:1},8,["action","headers","before-upload","on-success","on-error"]),a[27]||(a[27]=(0,o.Lk)("div",{class:"upload-tip"},"点击上传照片，JPG/PNG格式，小于2MB",-1))]),_:1,__:[27]})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"])])}l(4114),l(8111),l(5207),l(4603),l(7566),l(8721);var i=l(144),m=l(1219),c=l(2933),b=l(4373),h=l(7477),g=l(1387),f={name:"TeacherList",components:{Plus:h.Plus},setup(){const e=(0,g.rd)(),a="http://localhost:3000",l={},r=(0,i.KR)(!1),t=(0,i.KR)(!1),d=(0,i.KR)(null),n=(0,i.KR)([]),s=(0,i.KR)(0),u=(0,i.KR)(1),p=(0,i.KR)(10),h=(0,i.KR)(""),f=(0,i.Kh)({name:"",department:"",is_employed:""}),k=(0,i.Kh)({id:"",name:"",gender:"男",department:"",school:"",major:"",education:"",is_employed:1,employment_period:"",phone:"",photo:""}),_=(0,i.Kh)({name:[{required:!0,message:"请输入姓名",trigger:"blur"},{min:2,max:10,message:"长度在 2 到 10 个字符",trigger:"blur"}],gender:[{required:!0,message:"请选择性别",trigger:"change"}],department:[{required:!0,message:"请输入科室",trigger:"blur"}],school:[{required:!0,message:"请输入学校",trigger:"blur"}],major:[{required:!0,message:"请输入专业",trigger:"blur"}],education:[{required:!0,message:"请选择学历",trigger:"change"}]});(0,o.wB)(()=>k.photo,e=>{e?e.startsWith("http")?h.value=e:h.value=`${a}${e}`:h.value=""},{immediate:!0}),(0,o.sV)(()=>{F()});const F=async()=>{r.value=!0;try{const e=new URLSearchParams;f.name&&e.append("name",f.name),f.department&&e.append("department",f.department),""!==f.is_employed&&e.append("is_employed",f.is_employed),e.append("page",u.value),e.append("limit",p.value);const l=await b.A.get(`${a}/api/teachers`,{params:e});n.value=l.data.data,s.value=l.data.count}catch(e){console.error("获取教师列表失败:",e),m.nk.error("获取教师列表失败")}finally{r.value=!1}},v=()=>{u.value=1,F()},y=()=>{Object.keys(f).forEach(e=>{f[e]=""}),u.value=1,F()},V=e=>{p.value=e,F()},w=e=>{u.value=e,F()},C=e=>{e?(Object.keys(k).forEach(a=>{k[a]=e[a]}),e.photo&&(h.value=`${a}${e.photo}`)):(Object.keys(k).forEach(e=>{k[e]="gender"===e?"男":"is_employed"===e?1:""}),h.value=""),t.value=!0},D=e=>{const a="image/jpeg"===e.type||"image/png"===e.type,l=e.size/1024/1024<2;return a?l?(h.value=URL.createObjectURL(e),!0):(m.nk.error("上传头像图片大小不能超过 2MB!"),!1):(m.nk.error("上传头像图片只能是 JPG 或 PNG 格式!"),!1)},U=e=>{console.log("照片上传成功响应:",e),e.success?(k.photo=e.path,console.log("设置照片路径:",k.photo),m.nk.success("照片上传成功")):m.nk.error(e.message||"照片上传失败")},P=e=>{console.error("照片上传失败:",e),m.nk.error("照片上传失败，请检查网络连接")},W=async()=>{d.value&&await d.value.validate(async e=>{if(!e)return!1;try{console.log("提交表单数据:",k);const e=new FormData;Object.keys(k).forEach(a=>{null!==k[a]&&void 0!==k[a]&&""!==k[a]&&e.append(a,k[a])});const l={headers:{"Content-Type":"multipart/form-data"}};k.id?(await b.A.put(`${a}/api/teachers/${k.id}`,e,l),m.nk.success("教师信息更新成功")):(await b.A.post(`${a}/api/teachers`,e,l),m.nk.success("教师添加成功")),t.value=!1,F()}catch(l){console.error("操作失败:",l),m.nk.error(l.response?.data?.message||"操作失败")}})},z=e=>{c.s.confirm(`确定要删除教师 ${e.name} 吗?`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await b.A.delete(`${a}/api/teachers/${e.id}`),m.nk.success("删除成功"),1===n.value.length&&u.value>1&&u.value--,F()}catch(l){console.error("删除失败:",l),m.nk.error(l.response?.data?.message||"删除失败")}}).catch(()=>{m.nk.info("已取消删除")})},$=a=>{e.push(`/teachers/detail/${a}`)};return{baseUrl:a,uploadHeaders:l,loading:r,teacherList:n,searchForm:f,formData:k,formRules:_,dialogVisible:t,teacherFormRef:d,currentPage:u,pageSize:p,total:s,photoPreview:h,handleSearch:v,resetSearch:y,handleSizeChange:V,handleCurrentChange:w,openDialog:C,submitForm:W,beforePhotoUpload:D,handlePhotoSuccess:U,handlePhotoError:P,handleDelete:z,viewDetails:$}}},k=l(1241);const _=(0,k.A)(f,[["render",p],["__scopeId","data-v-120e8a5c"]]);var F=_}}]);
//# sourceMappingURL=385.9f77b7ee.js.map