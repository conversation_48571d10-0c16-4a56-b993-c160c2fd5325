"use strict";(self["webpackChunkms"]=self["webpackChunkms"]||[]).push([[678],{678:function(e,a,t){t.r(a),t.d(a,{default:function(){return W}});var l=t(6768),r=t(4232);const o={class:"teacher-detail-container"},i={class:"card-header"},c={class:"teacher-avatar"},n={key:1},u={class:"certification-status"},s={class:"certification-tag"},d={class:"certification-stats"},v={class:"tab-content"},p={key:1,class:"empty-data"},_={class:"tab-content"},b={key:1,class:"empty-data"};function m(e,a,t,m,k,f){const h=(0,l.g2)("el-button"),y=(0,l.g2)("el-image"),g=(0,l.g2)("el-avatar"),F=(0,l.g2)("el-descriptions-item"),w=(0,l.g2)("el-tag"),W=(0,l.g2)("el-descriptions"),D=(0,l.g2)("el-progress"),x=(0,l.g2)("el-card"),E=(0,l.g2)("el-col"),L=(0,l.g2)("el-table-column"),C=(0,l.g2)("el-table"),X=(0,l.g2)("el-tab-pane"),R=(0,l.g2)("el-tabs"),T=(0,l.g2)("el-row"),$=(0,l.g2)("el-divider"),K=(0,l.g2)("el-dialog"),V=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",o,[(0,l.bF)(x,{class:"box-card"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",i,[a[3]||(a[3]=(0,l.Lk)("span",{class:"title"},"教师详情",-1)),(0,l.Lk)("div",null,[(0,l.bF)(h,{onClick:m.goBack},{default:(0,l.k6)(()=>a[2]||(a[2]=[(0,l.eW)("返回列表")])),_:1,__:[2]},8,["onClick"])])])]),default:(0,l.k6)(()=>[(0,l.bF)(T,{gutter:20},{default:(0,l.k6)(()=>[(0,l.bo)(((0,l.uX)(),(0,l.Wv)(E,{span:8},{default:(0,l.k6)(()=>[(0,l.Lk)("div",c,[m.teacherData.photo?((0,l.uX)(),(0,l.Wv)(y,{key:0,src:`${m.baseUrl}${m.teacherData.photo}`,fit:"cover",class:"avatar-image","preview-src-list":[`${m.baseUrl}${m.teacherData.photo}`],"preview-teleported":!0},null,8,["src","preview-src-list"])):((0,l.uX)(),(0,l.Wv)(g,{key:1,size:150,icon:"UserFilled"}))]),(0,l.bF)(W,{title:"基本信息",direction:"vertical",column:1,border:""},{default:(0,l.k6)(()=>[(0,l.bF)(F,{label:"姓名"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.teacherData.name||"--"),1)]),_:1}),(0,l.bF)(F,{label:"性别"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.teacherData.gender||"--"),1)]),_:1}),(0,l.bF)(F,{label:"科室"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.teacherData.department||"--"),1)]),_:1}),(0,l.bF)(F,{label:"学校"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.teacherData.school||"--"),1)]),_:1}),(0,l.bF)(F,{label:"专业"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.teacherData.major||"--"),1)]),_:1}),(0,l.bF)(F,{label:"学历"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.teacherData.education||"--"),1)]),_:1}),(0,l.bF)(F,{label:"在聘状态"},{default:(0,l.k6)(()=>[void 0!==m.teacherData.is_employed?((0,l.uX)(),(0,l.Wv)(w,{key:0,type:m.teacherData.is_employed?"success":"danger"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.teacherData.is_employed?"在聘":"不在聘"),1)]),_:1},8,["type"])):((0,l.uX)(),(0,l.CE)("span",n,"--"))]),_:1}),m.teacherData.is_employed&&m.teacherData.employment_period?((0,l.uX)(),(0,l.Wv)(F,{key:0,label:"聘期"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.teacherData.employment_period),1)]),_:1})):(0,l.Q3)("",!0),m.teacherData.phone?((0,l.uX)(),(0,l.Wv)(F,{key:1,label:"联系方式"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.teacherData.phone),1)]),_:1})):(0,l.Q3)("",!0)]),_:1}),m.competencyData?((0,l.uX)(),(0,l.Wv)(x,{key:0,class:"certification-card"},{header:(0,l.k6)(()=>a[4]||(a[4]=[(0,l.Lk)("div",{class:"card-header"},[(0,l.Lk)("span",null,"能力认证状态")],-1)])),default:(0,l.k6)(()=>[(0,l.Lk)("div",u,[(0,l.Lk)("div",s,[(0,l.bF)(w,{type:m.competencyData.is_certified?"success":"info",size:"large"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.competencyData.is_certified?"已认证":"未认证"),1)]),_:1},8,["type"])]),(0,l.bF)(D,{percentage:m.competencyData.approval_rate||0,status:m.competencyData.is_certified?"success":"","stroke-width":18,format:m.percentFormat},null,8,["percentage","status","format"]),(0,l.Lk)("div",d,[(0,l.Lk)("div",null,"总评价数: "+(0,r.v_)(m.competencyData.total_evaluations||0),1),(0,l.Lk)("div",null,"认可数: "+(0,r.v_)(m.competencyData.approved_count||0),1)]),a[5]||(a[5]=(0,l.Lk)("div",{class:"certification-note"},[(0,l.Lk)("i",{class:"el-icon-info"}),(0,l.eW)(" 注：需要80%以上的督导评价认可才能获得认证 ")],-1))])]),_:1})):(0,l.Q3)("",!0)]),_:1})),[[V,m.loading]]),(0,l.bF)(E,{span:16},{default:(0,l.k6)(()=>[(0,l.bF)(R,{modelValue:m.activeTab,"onUpdate:modelValue":a[0]||(a[0]=e=>m.activeTab=e),onTabClick:m.handleTabClick},{default:(0,l.k6)(()=>[(0,l.bF)(X,{label:"考试成绩",name:"exams"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",v,[(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",null,[m.examResults.length>0?((0,l.uX)(),(0,l.Wv)(C,{data:m.examResults,border:"",style:{width:"100%"},key:"exam-table"},{default:(0,l.k6)(()=>[(0,l.bF)(L,{prop:"exam_title",label:"考试名称"}),(0,l.bF)(L,{prop:"exam_type",label:"考试类型",width:"100"},{default:(0,l.k6)(e=>[(0,l.bF)(w,{type:"资格认定考试"===e.row.exam_type?"danger":"primary"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(e.row.exam_type),1)]),_:2},1032,["type"])]),_:1}),(0,l.bF)(L,{prop:"score",label:"分数",width:"80"}),(0,l.bF)(L,{label:"是否及格",width:"100"},{default:(0,l.k6)(e=>[(0,l.bF)(w,{type:e.row.score>=e.row.pass_score?"success":"danger"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(e.row.score>=e.row.pass_score?"及格":"不及格"),1)]),_:2},1032,["type"])]),_:1}),(0,l.bF)(L,{prop:"exam_date",label:"考试时间",width:"180"},{default:(0,l.k6)(e=>[(0,l.eW)((0,r.v_)(m.formatDate(e.row.exam_date)),1)]),_:1})]),_:1},8,["data"])):m.examLoading?(0,l.Q3)("",!0):((0,l.uX)(),(0,l.CE)("div",p," 暂无考试记录 "))])),[[V,m.examLoading]])])]),_:1}),(0,l.bF)(X,{label:"教学评价",name:"evaluations"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",_,[(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",null,[m.evaluations.length>0?((0,l.uX)(),(0,l.Wv)(C,{data:m.evaluations,border:"",style:{width:"100%"},key:"evaluation-table"},{default:(0,l.k6)(()=>[(0,l.bF)(L,{prop:"supervising_department",label:"督导教研室",width:"120"}),(0,l.bF)(L,{prop:"case_topic",label:"病例/主题"}),(0,l.bF)(L,{prop:"teaching_form",label:"教学活动形式",width:"120"}),(0,l.bF)(L,{prop:"student_name",label:"学员姓名",width:"100"}),(0,l.bF)(L,{prop:"student_type",label:"学员类别",width:"120"}),(0,l.bF)(L,{prop:"average_score",label:"平均分",width:"80"}),(0,l.bF)(L,{label:"能力认定",width:"100"},{default:(0,l.k6)(e=>[(0,l.bF)(w,{type:e.row.competency_approved?"success":"danger"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(e.row.competency_approved?"同意":"不同意"),1)]),_:2},1032,["type"])]),_:1}),(0,l.bF)(L,{prop:"evaluation_date",label:"评价时间",width:"180"},{default:(0,l.k6)(e=>[(0,l.eW)((0,r.v_)(m.formatDate(e.row.evaluation_date)),1)]),_:1}),(0,l.bF)(L,{label:"操作",width:"80",fixed:"right"},{default:(0,l.k6)(e=>[(0,l.bF)(h,{size:"small",onClick:a=>m.viewEvaluation(e.row)},{default:(0,l.k6)(()=>a[6]||(a[6]=[(0,l.eW)("详情")])),_:2,__:[6]},1032,["onClick"])]),_:1})]),_:1},8,["data"])):m.evaluationLoading?(0,l.Q3)("",!0):((0,l.uX)(),(0,l.CE)("div",b," 暂无教学评价记录 "))])),[[V,m.evaluationLoading]])])]),_:1})]),_:1},8,["modelValue","onTabClick"])]),_:1})]),_:1})]),_:1}),(0,l.bF)(K,{modelValue:m.evaluationDialogVisible,"onUpdate:modelValue":a[1]||(a[1]=e=>m.evaluationDialogVisible=e),title:"评价详情",width:"60%","append-to-body":!0,"destroy-on-close":!0},{default:(0,l.k6)(()=>[(0,l.bF)(W,{title:"基本信息",column:2,border:""},{default:(0,l.k6)(()=>[(0,l.bF)(F,{label:"督导教研室"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.currentEvaluation.supervising_department),1)]),_:1}),(0,l.bF)(F,{label:"病例/主题"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.currentEvaluation.case_topic),1)]),_:1}),(0,l.bF)(F,{label:"教学活动形式"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.currentEvaluation.teaching_form),1)]),_:1}),(0,l.bF)(F,{label:"带教老师职称"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.currentEvaluation.teacher_title),1)]),_:1}),(0,l.bF)(F,{label:"学员姓名"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.currentEvaluation.student_name),1)]),_:1}),(0,l.bF)(F,{label:"学员类别"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.currentEvaluation.student_type),1)]),_:1}),(0,l.bF)(F,{label:"评估人"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.currentEvaluation.evaluator_name),1)]),_:1}),(0,l.bF)(F,{label:"评价时间"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.formatDate(m.currentEvaluation.evaluation_date)),1)]),_:1})]),_:1}),(0,l.bF)($),(0,l.bF)(W,{title:"评价内容",column:1,border:""},{default:(0,l.k6)(()=>[(0,l.bF)(F,{label:"平均分"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.currentEvaluation.average_score),1)]),_:1}),(0,l.bF)(F,{label:"亮点"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.currentEvaluation.highlights||"无"),1)]),_:1}),(0,l.bF)(F,{label:"不足"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.currentEvaluation.shortcomings||"无"),1)]),_:1}),(0,l.bF)(F,{label:"改进建议"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.currentEvaluation.improvement_suggestions||"无"),1)]),_:1}),(0,l.bF)(F,{label:"能力认定"},{default:(0,l.k6)(()=>[(0,l.bF)(w,{type:m.currentEvaluation.competency_approved?"success":"danger"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(m.currentEvaluation.competency_approved?"同意":"不同意"),1)]),_:1},8,["type"])]),_:1})]),_:1})]),_:1},8,["modelValue"])])}t(4114);var k=t(144),f=t(1387),h=t(1219),y=t(4373),g={name:"TeacherDetail",setup(){const e=(0,f.lq)(),a=(0,f.rd)(),t=e.params.id,r="http://localhost:3000",o=(0,k.KR)(!1),i=(0,k.KR)(!1),c=(0,k.KR)(!1),n=(0,k.KR)({}),u=(0,k.KR)([]),s=(0,k.KR)([]),d=(0,k.KR)(null),v=(0,k.KR)("exams"),p=(0,k.KR)({teacher:!1,exams:!1,evaluations:!1,competency:!1}),_=(0,k.KR)(!1),b=(0,k.KR)({});(0,l.sV)(()=>{m().then(()=>{setTimeout(()=>{"exams"===v.value?(g(),setTimeout(()=>{w()},100),setTimeout(()=>{F()},200)):(F(),setTimeout(()=>{w()},100),setTimeout(()=>{g()},200))},100)})});const m=async()=>{o.value=!0;try{const e=await y.A.get(`${r}/api/teachers/${t}`);n.value=e.data.data||{},p.value.teacher=!0}catch(e){console.error("获取教师信息失败:",e),h.nk.error("获取教师信息失败")}finally{o.value=!1}},g=async()=>{if("exams"===v.value||!p.value.exams){i.value=!0;try{const e=await y.A.get(`${r}/api/exams/teacher/${t}/results`);await(0,l.dY)(),u.value=Array.isArray(e.data.data)?e.data.data:[],p.value.exams=!0}catch(e){console.error("获取考试成绩失败:",e),h.nk.error("获取考试成绩失败"),u.value=[]}finally{i.value=!1}}},F=async()=>{if("evaluations"===v.value||!p.value.evaluations){c.value=!0;try{const e=await y.A.get(`${r}/api/evaluations/teacher/${t}`);await(0,l.dY)(),s.value=Array.isArray(e.data.data)?e.data.data:[],p.value.evaluations=!0}catch(e){console.error("获取教学评价失败:",e),h.nk.error("获取教学评价失败"),s.value=[]}finally{c.value=!1}}},w=async()=>{if(!p.value.competency)try{const e=await y.A.get(`${r}/api/competency/teacher/${t}`);await(0,l.dY)(),d.value=e.data.data||null,p.value.competency=!0}catch(e){console.error("获取能力认证状态失败:",e)}},W=e=>{"exams"!==e.props.name||p.value.exams?"evaluations"!==e.props.name||p.value.evaluations||F():g()},D=()=>{a.push("/teachers/list")},x=()=>{a.push(`/teachers/edit/${t}`)},E=e=>{b.value=e,(0,l.dY)(()=>{_.value=!0})},L=e=>{if(!e)return"--";const a=new Date(e);return a.toLocaleString()},C=e=>`${e}%`;return{baseUrl:r,teacherData:n,examResults:u,evaluations:s,competencyData:d,loading:o,examLoading:i,evaluationLoading:c,activeTab:v,evaluationDialogVisible:_,currentEvaluation:b,goBack:D,editTeacher:x,viewEvaluation:E,formatDate:L,percentFormat:C,handleTabClick:W}}},F=t(1241);const w=(0,F.A)(g,[["render",m],["__scopeId","data-v-db6c88c8"]]);var W=w}}]);
//# sourceMappingURL=678.ddbe3235.js.map