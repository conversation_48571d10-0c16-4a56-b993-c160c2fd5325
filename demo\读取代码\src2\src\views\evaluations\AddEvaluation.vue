<template>
  <div class="add-evaluation-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="title">{{ isEdit ? '编辑督导评价' : '添加督导评价' }}</span>
          <el-button @click="goBack">返回列表</el-button>
        </div>
      </template>

      <div v-loading="loading">
        <el-form
          ref="evaluationFormRef"
          :model="formData"
          :rules="formRules"
          label-width="120px"
          label-position="right"
        >
          <!-- 督导教研室 -->
          <el-form-item label="督导教研室" prop="supervising_department">
            <el-input v-model="formData.supervising_department" placeholder="请输入督导教研室" />
          </el-form-item>

          <!-- 病例/主题 -->
          <el-form-item label="病例/主题" prop="case_topic">
            <el-input v-model="formData.case_topic" placeholder="请输入病例或主题" />
          </el-form-item>

          <!-- 教学活动形式 -->
          <el-form-item label="教学活动形式" prop="teaching_form">
            <el-input v-model="formData.teaching_form" placeholder="例如：小讲课、教学查房等" />
          </el-form-item>

          <!-- 带教老师 -->
          <el-form-item label="带教老师" prop="teacher_id">
            <el-select 
              v-model="formData.teacher_id" 
              placeholder="请选择带教老师" 
              filterable 
              :loading="teacherLoading"
            >
              <el-option 
                v-for="item in teacherOptions" 
                :key="item.id" 
                :label="`${item.name || '未命名'} (${item.department || '无部门'})`" 
                :value="item.id" 
              />
            </el-select>
          </el-form-item>

          <!-- 带教老师职称 -->
          <el-form-item label="带教老师职称" prop="teacher_title">
            <el-input v-model="formData.teacher_title" placeholder="请输入带教老师职称" />
          </el-form-item>

          <!-- 学员姓名 -->
          <el-form-item label="学员姓名" prop="student_name">
            <el-input v-model="formData.student_name" placeholder="请输入学员姓名" />
          </el-form-item>

          <!-- 学员类别 -->
          <el-form-item label="学员类别" prop="student_type">
            <el-select v-model="formData.student_type" placeholder="请选择学员类别">
              <el-option label="实习生" value="实习生" />
              <el-option label="进修生" value="进修生" />
              <el-option label="低年资轮转" value="低年资轮转" />
            </el-select>
          </el-form-item>

          <!-- 平均分 -->
          <el-form-item label="平均分" prop="average_score">
            <el-input-number 
              v-model="formData.average_score" 
              :min="0" 
              :max="10" 
              :precision="1" 
              :step="0.1" 
              controls-position="right" 
            />
            <span class="score-hint">（0-10分）</span>
          </el-form-item>

          <!-- 亮点 -->
          <el-form-item label="亮点" prop="highlights">
            <el-input 
              v-model="formData.highlights" 
              type="textarea" 
              :rows="3" 
              placeholder="请输入教学亮点" 
            />
          </el-form-item>

          <!-- 不足 -->
          <el-form-item label="不足" prop="shortcomings">
            <el-input 
              v-model="formData.shortcomings" 
              type="textarea" 
              :rows="3" 
              placeholder="请输入教学不足之处" 
            />
          </el-form-item>

          <!-- 改进建议 -->
          <el-form-item label="改进建议" prop="improvement_suggestions">
            <el-input 
              v-model="formData.improvement_suggestions" 
              type="textarea" 
              :rows="3" 
              placeholder="请输入改进建议" 
            />
          </el-form-item>

          <!-- 能力认定 -->
          <el-form-item label="能力认定" prop="competency_approved">
            <el-radio-group v-model="formData.competency_approved">
              <el-radio :label="1">同意</el-radio>
              <el-radio :label="0">不同意</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 操作按钮 -->
          <el-form-item>
            <el-button type="primary" @click="submitForm">提交</el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import axios from 'axios'

export default {
  name: 'AddEvaluation',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const evaluationFormRef = ref(null)
    
    // 基础数据
    const loading = ref(false)
    const teacherLoading = ref(false)
    const teacherOptions = ref([])
    const allTeachers = ref([])
    
    // 是否为编辑模式
    const evaluationId = route.query.id
    const isEdit = computed(() => !!evaluationId)
    
    // 表单数据
    const formData = reactive({
      supervising_department: '',
      case_topic: '',
      teaching_form: '',
      teacher_id: '',
      teacher_title: '',
      student_name: '',
      student_type: '实习生',
      average_score: 7.0,
      highlights: '',
      shortcomings: '',
      improvement_suggestions: '',
      competency_approved: 1,
      evaluator_id: 1 // 默认使用当前登录用户的ID，这里暂时写死
    })
    
    // 表单验证规则
    const formRules = {
      supervising_department: [
        { required: true, message: '请输入督导教研室', trigger: 'blur' }
      ],
      case_topic: [
        { required: true, message: '请输入病例/主题', trigger: 'blur' }
      ],
      teaching_form: [
        { required: true, message: '请输入教学活动形式', trigger: 'blur' }
      ],
      teacher_id: [
        { required: true, message: '请选择带教老师', trigger: 'change' }
      ],
      teacher_title: [
        { required: true, message: '请输入带教老师职称', trigger: 'blur' }
      ],
      student_name: [
        { required: true, message: '请输入学员姓名', trigger: 'blur' }
      ],
      student_type: [
        { required: true, message: '请选择学员类别', trigger: 'change' }
      ],
      average_score: [
        { required: true, message: '请输入平均分', trigger: 'change' }
      ]
    }

    // 监听教师ID变化自动填充职称
    watch(() => formData.teacher_id, (newValue) => {
      if (newValue) {
        const selectedTeacher = allTeachers.value.find(teacher => teacher.id === newValue)
        if (selectedTeacher && selectedTeacher.title) {
          formData.teacher_title = selectedTeacher.title
        }
      }
    })
    
    // 获取教师列表
    const fetchTeachers = async () => {
      teacherLoading.value = true
      try {
        const response = await axios.get('http://127.0.0.1:3000/api/teachers')
        if (response.data && response.data.data) {
          allTeachers.value = response.data.data
          teacherOptions.value = response.data.data
        } else {
          teacherOptions.value = []
          console.error('获取教师列表返回格式有误')
        }
      } catch (error) {
        console.error('获取教师列表失败:', error)
        ElMessage.error('获取教师列表失败')
      } finally {
        teacherLoading.value = false
      }
    }
    
    // 获取评价详情
    const fetchEvaluationDetail = async () => {
      loading.value = true
      try {
        const response = await axios.get(`http://127.0.0.1:3000/api/evaluations/${evaluationId}`)
        const data = response.data.data
        
        // 填充表单数据
        Object.keys(formData).forEach(key => {
          if (key in data) {
            formData[key] = data[key]
          }
        })
      } catch (error) {
        console.error('获取评价详情失败:', error)
        ElMessage.error('获取评价详情失败')
      } finally {
        loading.value = false
      }
    }
    
    // 提交表单
    const submitForm = async () => {
      if (!evaluationFormRef.value) return
      
      await evaluationFormRef.value.validate(async (valid) => {
        if (valid) {
          loading.value = true
          try {
            if (isEdit.value) {
              // 编辑模式
              await axios.put(`http://127.0.0.1:3000/api/evaluations/${evaluationId}`, formData)
              ElMessage.success('督导评价更新成功')
            } else {
              // 添加模式
              await axios.post('http://127.0.0.1:3000/api/evaluations', formData)
              ElMessage.success('督导评价添加成功')
            }
            
            // 跳转回列表页
            router.push('/evaluations/list')
          } catch (error) {
            console.error('操作失败:', error)
            ElMessage.error('操作失败')
          } finally {
            loading.value = false
          }
        } else {
          return false
        }
      })
    }
    
    // 重置表单
    const resetForm = () => {
      if (evaluationFormRef.value) {
        evaluationFormRef.value.resetFields()
      }
    }
    
    // 返回列表
    const goBack = () => {
      router.push('/evaluations/list')
    }
    
    // 生命周期钩子
    onMounted(async () => {
      // 初始化教师下拉列表
      await fetchTeachers()
      
      // 如果是编辑模式，加载评价数据
      if (isEdit.value) {
        await fetchEvaluationDetail()
      }
    })
    
    return {
      loading,
      teacherLoading,
      teacherOptions,
      evaluationFormRef,
      formData,
      formRules,
      isEdit,
      submitForm,
      resetForm,
      goBack
    }
  }
}
</script>

<style scoped>
.add-evaluation-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.score-hint {
  margin-left: 10px;
  color: #909399;
  font-size: 12px;
}
</style> 