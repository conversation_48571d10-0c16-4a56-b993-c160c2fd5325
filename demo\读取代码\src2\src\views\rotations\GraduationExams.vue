<template>
  <div class="graduation-exams-container">
    <!-- 搜索区域卡片 -->
    <el-card class="filter-card">
      <div class="filter-container">
        <el-form :model="filterForm" inline>
          <el-form-item label="学生姓名">
            <el-input v-model="filterForm.name" placeholder="请输入学生姓名" clearable></el-input>
          </el-form-item>
          <el-form-item label="考核时间">
            <el-date-picker
              v-model="filterForm.examDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              clearable>
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    
    <!-- 内容区域卡片 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>结业考核记录</span>
          <div>
            <el-button type="primary" @click="handleAddExam">新增考核记录</el-button>
            <el-button type="success" @click="handleExport">导出数据</el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="examList" stripe border style="width: 100%" v-loading="loading">
        <el-table-column type="index" width="50" />
        <el-table-column prop="student_name" label="学生姓名" min-width="100" />
        <el-table-column prop="exam_date" label="考核日期" width="120" />
        <el-table-column prop="theory_score" label="理论成绩" width="100">
          <template #default="scope">
            <span :class="getScoreClass(scope.row.theory_score)">{{ scope.row.theory_score }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="practice_score" label="操作成绩" width="100">
          <template #default="scope">
            <span :class="getScoreClass(scope.row.practice_score)">{{ scope.row.practice_score }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="final_score" label="最终成绩" width="100" sortable>
          <template #default="scope">
            <span :class="getScoreClass(scope.row.final_score)">{{ scope.row.final_score }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.final_score >= 60 ? 'success' : 'danger'">
              {{ scope.row.final_score >= 60 ? '通过' : '未通过' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="notes" label="备注" min-width="200" show-overflow-tooltip />
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑考核记录对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
    >
      <el-form :model="examForm" :rules="examRules" ref="examFormRef" label-width="100px">
        <el-form-item label="学生" prop="student_id">
          <el-select v-model="examForm.student_id" filterable placeholder="请选择学生" style="width: 100%">
            <el-option
              v-for="student in studentList"
              :key="student.id"
              :label="student.name"
              :value="student.id"
            >
              <div style="display: flex; justify-content: space-between; align-items: center">
                <span>{{ student.name }}</span>
                <span style="color: #999; font-size: 12px">{{ student.school }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="理论成绩" prop="theory_score">
              <el-input-number 
                v-model="examForm.theory_score" 
                :min="0" 
                :max="100" 
                :precision="1"
                :step="0.5"
                style="width: 100%"
                @change="calculateFinalScore">
              </el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作成绩" prop="practice_score">
              <el-input-number 
                v-model="examForm.practice_score" 
                :min="0" 
                :max="100" 
                :precision="1"
                :step="0.5"
                style="width: 100%"
                @change="calculateFinalScore">
              </el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="最终成绩" prop="final_score">
          <el-input-number 
            v-model="examForm.final_score" 
            :min="0" 
            :max="100" 
            :precision="1"
            :step="0.5"
            style="width: 100%"
            disabled>
          </el-input-number>
          <div class="form-help">最终成绩为理论成绩和操作成绩的平均值</div>
        </el-form-item>
        
        <el-form-item label="考核日期" prop="exam_date">
          <el-date-picker
            v-model="examForm.exam_date"
            type="date"
            placeholder="选择考核日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input 
            type="textarea" 
            v-model="examForm.notes" 
            placeholder="请输入备注信息"
            rows="3">
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const loading = ref(true)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const dialogVisible = ref(false)
const dialogTitle = ref('新增结业考核')
const examFormRef = ref(null)

// 过滤条件
const filterForm = reactive({
  name: '',
  examDate: []
})

// 考核表单
const examForm = reactive({
  id: null,
  student_id: '',
  student_name: '',
  theory_score: 0,
  practice_score: 0,
  final_score: 0,
  exam_date: new Date().toISOString().split('T')[0],
  notes: ''
})

// 表单校验规则
const examRules = {
  student_id: [{ required: true, message: '请选择学生', trigger: 'change' }],
  theory_score: [{ required: true, message: '请输入理论成绩', trigger: 'blur' }],
  practice_score: [{ required: true, message: '请输入操作成绩', trigger: 'blur' }],
  exam_date: [{ required: true, message: '请选择考核日期', trigger: 'change' }]
}

// 模拟数据
const examList = ref([
  {
    id: 1,
    student_id: 1,
    student_name: '张三',
    theory_score: 85.5,
    practice_score: 90,
    final_score: 87.8,
    exam_date: '2023-05-15',
    notes: '表现优秀，各方面能力均衡发展'
  },
  {
    id: 2,
    student_id: 2,
    student_name: '李四',
    theory_score: 75,
    practice_score: 82.5,
    final_score: 78.8,
    exam_date: '2023-05-15',
    notes: '理论知识掌握较好，实际操作一般'
  },
  {
    id: 3,
    student_id: 3,
    student_name: '王五',
    theory_score: 68,
    practice_score: 55,
    final_score: 61.5,
    exam_date: '2023-05-16',
    notes: '理论基础较弱，实际操作能力一般'
  },
  {
    id: 4,
    student_id: 4,
    student_name: '赵六',
    theory_score: 45,
    practice_score: 50,
    final_score: 47.5,
    exam_date: '2023-05-16',
    notes: '需要加强基础知识学习，提高实践能力'
  }
])

// 学生列表
const studentList = ref([
  { id: 1, name: '张三', school: '北京大学' },
  { id: 2, name: '李四', school: '清华大学' },
  { id: 3, name: '王五', school: '复旦大学' },
  { id: 4, name: '赵六', school: '上海交通大学' },
  { id: 5, name: '钱七', school: '浙江大学' }
])

onMounted(() => {
  // 模拟API请求延迟
  setTimeout(() => {
    loading.value = false
    total.value = examList.value.length
  }, 500)
})

// 根据成绩返回不同的CSS类名
const getScoreClass = (score) => {
  if (score >= 80) return 'score-excellent'
  if (score >= 60) return 'score-pass'
  return 'score-fail'
}

// 处理搜索
const handleSearch = () => {
  loading.value = true
  // 实际项目中这里应该调用API进行搜索
  setTimeout(() => {
    loading.value = false
    // 模拟筛选结果
    const filteredData = examList.value.filter(item => {
      const nameMatch = !filterForm.name || item.student_name.includes(filterForm.name)
      let dateMatch = true
      
      if (filterForm.examDate && filterForm.examDate.length === 2) {
        const examDate = new Date(item.exam_date)
        const startDate = new Date(filterForm.examDate[0])
        const endDate = new Date(filterForm.examDate[1])
        dateMatch = examDate >= startDate && examDate <= endDate
      }
      
      return nameMatch && dateMatch
    })
    
    total.value = filteredData.length
    examList.value = filteredData
  }, 300)
}

// 重置过滤条件
const resetFilter = () => {
  filterForm.name = ''
  filterForm.examDate = []
  handleSearch()
}

// 处理分页
const handleSizeChange = (val) => {
  pageSize.value = val
  handleSearch()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  handleSearch()
}

// 计算最终成绩
const calculateFinalScore = () => {
  const theory = examForm.theory_score || 0
  const practice = examForm.practice_score || 0
  examForm.final_score = parseFloat(((theory + practice) / 2).toFixed(1))
}

// 添加考核记录
const handleAddExam = () => {
  dialogTitle.value = '新增结业考核'
  dialogVisible.value = true
  
  if (examFormRef.value) {
    examFormRef.value.resetFields()
  }
  
  Object.assign(examForm, {
    id: null,
    student_id: '',
    student_name: '',
    theory_score: 0,
    practice_score: 0,
    final_score: 0,
    exam_date: new Date().toISOString().split('T')[0],
    notes: ''
  })
}

// 编辑考核记录
const handleEdit = (row) => {
  dialogTitle.value = '编辑结业考核'
  dialogVisible.value = true
  
  Object.assign(examForm, {
    id: row.id,
    student_id: row.student_id,
    student_name: row.student_name,
    theory_score: row.theory_score,
    practice_score: row.practice_score,
    final_score: row.final_score,
    exam_date: row.exam_date,
    notes: row.notes
  })
}

// 删除考核记录
const handleDelete = (row) => {
  ElMessageBox.confirm(`确认删除学生 ${row.student_name} 的考核记录吗？`, '警告', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 实际项目中这里应该调用删除API
    const index = examList.value.findIndex(item => item.id === row.id)
    if (index !== -1) {
      examList.value.splice(index, 1)
      total.value = examList.value.length
      ElMessage.success('删除成功')
    }
  }).catch(() => {})
}

// 导出数据
const handleExport = () => {
  ElMessage.success('数据导出成功')
}

// 提交表单
const submitForm = () => {
  examFormRef.value.validate((valid) => {
    if (valid) {
      // 在实际项目中，这里应该调用API保存数据
      if (examForm.id) {
        // 编辑现有记录
        const index = examList.value.findIndex(item => item.id === examForm.id)
        if (index !== -1) {
          const studentObj = studentList.value.find(s => s.id === examForm.student_id)
          examForm.student_name = studentObj ? studentObj.name : ''
          
          examList.value[index] = { ...examForm }
          ElMessage.success('更新成功')
        }
      } else {
        // 添加新记录
        const studentObj = studentList.value.find(s => s.id === examForm.student_id)
        const newRecord = {
          ...examForm,
          id: examList.value.length + 1,
          student_name: studentObj ? studentObj.name : ''
        }
        
        examList.value.unshift(newRecord)
        total.value = examList.value.length
        ElMessage.success('添加成功')
      }
      
      dialogVisible.value = false
    } else {
      return false
    }
  })
}
</script>

<style scoped>
.graduation-exams-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.filter-container {
  padding: 10px 0;
}

.table-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: 600;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.score-excellent {
  color: #67C23A;
  font-weight: bold;
}

.score-pass {
  color: #409EFF;
}

.score-fail {
  color: #F56C6C;
}

.form-help {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

/* Style the Element Plus components to match RotationList style */
:deep(.el-button--primary) {
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
  border: none;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #66b1ff 0%, #5098fa 100%);
  border: none;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409EFF inset;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
}

:deep(.el-tabs__active-bar) {
  background-color: #409EFF;
}

:deep(.el-tabs__item.is-active) {
  color: #409EFF;
}
</style> 