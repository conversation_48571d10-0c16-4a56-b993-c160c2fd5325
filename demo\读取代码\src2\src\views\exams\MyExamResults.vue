<template>
  <div class="my-results-container">
    <el-card class="results-card">
      <template #header>
        <div class="card-header">
          <span>我的考试成绩</span>
        </div>
      </template>
      
      <div class="results-content" v-loading="loading">
        <!-- 成绩统计 -->
        <el-card class="statistics-card" v-if="hasResults">
          <div class="statistics-items">
            <div class="statistics-item">
              <div class="statistics-label">考试总数</div>
              <div class="statistics-value">{{ resultsData.summary.total_exams || 0 }}</div>
            </div>
            <div class="statistics-item">
              <div class="statistics-label">通过考试</div>
              <div class="statistics-value">{{ resultsData.summary.passed_exams || 0 }}</div>
            </div>
            <div class="statistics-item">
              <div class="statistics-label">通过率</div>
              <div class="statistics-value">{{ resultsData.summary.pass_rate || 0 }}%</div>
            </div>
            <div class="statistics-item">
              <div class="statistics-label">平均分</div>
              <div class="statistics-value">{{ resultsData.summary.average_score || 0 }}</div>
            </div>
          </div>
        </el-card>
        
        <!-- 空数据提示 -->
        <div v-if="!loading && !hasResults" class="no-results">
          <el-empty description="暂无考试记录" />
        </div>
        
        <!-- 考试成绩列表 -->
        <div class="exams-list" v-if="hasResults">
          <el-collapse>
            <el-collapse-item v-for="(examData, examId) in resultsData.results" :key="examId">
              <template #title>
                <div class="exam-header">
                  <span class="exam-title">{{ examData.exam.title }}</span>
                  <div class="exam-summary">
                    <el-tag 
                      :type="examData.attempts.some(a => a.is_passed) ? 'success' : 'danger'"
                    >
                      {{ examData.attempts.some(a => a.is_passed) ? '已通过' : '未通过' }}
                    </el-tag>
                    <span class="attempt-count">{{ examData.attempts.length }}/2 次</span>
                  </div>
                </div>
              </template>
              
              <!-- 该考试的所有尝试 -->
              <div class="attempt-list">
                <el-card 
                  v-for="(attempt, index) in sortedAttempts(examData.attempts)" 
                  :key="attempt.id" 
                  class="attempt-card"
                  :class="{ 'passed': attempt.is_passed }"
                >
                  <div class="attempt-header">
                    <span class="attempt-title">第 {{ index + 1 }} 次尝试</span>
                    <span class="attempt-date">{{ formatDate(attempt.exam_date, 'YYYY-MM-DD HH:mm:ss') }}</span>
                  </div>
                  
                  <div class="attempt-details">
                    <div class="score-display">
                      <span class="score-value">{{ attempt.score }}</span>
                      <span class="score-total">/ {{ examData.exam.total_score }}</span>
                    </div>
                    <div class="pass-status">
                      <el-tag 
                        :type="attempt.is_passed ? 'success' : 'danger'"
                        effect="plain"
                      >
                        {{ attempt.is_passed ? '通过' : '未通过' }}
                      </el-tag>
                    </div>
                  </div>
                  
                  <div class="pass-info">
                    及格线：{{ examData.exam.pass_score }}分
                  </div>
                </el-card>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import examService from '@/services/examService'
import { formatDate } from '@/utils/dateFormat'

const loading = ref(true)
const resultsData = ref({
  summary: {
    total_exams: 0,
    passed_exams: 0,
    pass_rate: 0,
    total_attempts: 0,
    average_score: 0
  },
  results: {}
})

// 是否有成绩数据
const hasResults = computed(() => {
  return Object.keys(resultsData.value.results).length > 0
})

// 排序尝试记录，按分数从高到低
const sortedAttempts = (attempts) => {
  return [...attempts].sort((a, b) => b.score - a.score)
}

// 获取学生的所有考试成绩
const fetchMyResults = async () => {
  loading.value = true
  try {
    const teacherId = localStorage.getItem('teacherId')
    if (!teacherId) {
      throw new Error('未找到有效的教师ID，请重新登录')
    }
    
    console.log('获取所有成绩 - 教师ID:', teacherId)
    
    const response = await examService.getTeacherResults(teacherId)
    resultsData.value = response.data.data
    
    loading.value = false
  } catch (error) {
    console.error('获取考试成绩失败', error)
    ElMessage.error('获取考试成绩失败: ' + (error.response?.data?.message || error.message))
    loading.value = false
  }
}

onMounted(() => {
  fetchMyResults()
})
</script>

<style scoped>
.my-results-container {
  padding: 20px;
}

.results-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 18px;
  font-weight: 600;
}

.results-content {
  min-height: 300px;
}

.statistics-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.statistics-items {
  display: flex;
  justify-content: space-around;
}

.statistics-item {
  text-align: center;
  flex: 1;
}

.statistics-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.statistics-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.no-results {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.exams-list {
  margin-top: 20px;
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.exam-title {
  font-size: 16px;
  font-weight: 600;
}

.exam-summary {
  display: flex;
  align-items: center;
  gap: 10px;
}

.attempt-count {
  font-size: 14px;
  color: #909399;
}

.attempt-list {
  margin-top: 10px;
}

.attempt-card {
  margin-bottom: 15px;
  border-radius: 8px;
  transition: all 0.3s;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.attempt-card.passed {
  border-left: 4px solid #67C23A;
}

.attempt-card:not(.passed) {
  border-left: 4px solid #F56C6C;
}

.attempt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.attempt-title {
  font-weight: 600;
}

.attempt-date {
  font-size: 14px;
  color: #909399;
}

.attempt-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.score-display {
  display: flex;
  align-items: baseline;
}

.score-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.score-total {
  font-size: 16px;
  color: #909399;
  margin-left: 5px;
}

.pass-info {
  font-size: 14px;
  color: #909399;
}

/* Style the Element Plus components to match LoginView style */
:deep(.el-button--primary) {
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
  border: none;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #66b1ff 0%, #5098fa 100%);
  border: none;
}
</style> 