<template>
  <div class="supervision-team-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="title">督导小组管理</span>
          <el-button type="primary" @click="openAddDialog">添加督导成员</el-button>
        </div>
      </template>

      <!-- 搜索区域 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="姓名">
          <el-input v-model="searchForm.name" placeholder="督导姓名" clearable />
        </el-form-item>
        <el-form-item label="科室">
          <el-input v-model="searchForm.department" placeholder="所属科室" clearable />
        </el-form-item>
      
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="supervisorList"
        border
        style="width: 100%"
      >
        <el-table-column type="index" width="50" label="#" />
        <el-table-column prop="name" label="姓名" width="100" />
        <el-table-column prop="gender" label="性别" width="80" />
        <el-table-column prop="department" label="科室" width="120" />
        <el-table-column label="照片" width="100">
          <template #default="scope">
            <el-image
              v-if="scope.row.avatar"
              :src="scope.row.avatar ? `${baseUrl}${scope.row.avatar}` : ''"
              :preview-src-list="[`${baseUrl}${scope.row.avatar}`]"
              fit="cover"
              style="width: 50px; height: 50px"
            />
            <el-avatar v-else :size="50" icon="UserFilled" />
          </template>
        </el-table-column>
        <el-table-column prop="title" label="职称" width="120" />
        <el-table-column prop="specialty" label="专长" />
        <el-table-column prop="phone" label="联系电话" width="120" />
        <el-table-column prop="email" label="邮箱" width="180" />
        <el-table-column label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status ? 'success' : 'info'">
              {{ scope.row.status ? '在职' : '离职' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑督导成员对话框 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="isEdit ? '编辑督导成员' : '添加督导成员'"
      width="600px"
    >
      <el-form
        ref="supervisorFormRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
        label-position="right"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="formData.name" placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="性别" prop="gender">
              <el-radio-group v-model="formData.gender">
                <el-radio label="男">男</el-radio>
                <el-radio label="女">女</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="科室" prop="department">
              <el-input v-model="formData.department" placeholder="请输入科室" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="职称" prop="title">
              <el-input v-model="formData.title" placeholder="请输入职称" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="专长" prop="specialty">
          <el-input v-model="formData.specialty" placeholder="请输入专长领域" />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input v-model="formData.phone" placeholder="请输入联系电话" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input v-model="formData.email" placeholder="请输入邮箱" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="formData.status">
            <el-radio :label="1">在职</el-radio>
            <el-radio :label="0">离职</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="头像" prop="avatarFile">
          <el-upload
            class="avatar-uploader"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleAvatarChange"
            :before-upload="beforeAvatarUpload"
            name="avatar"
            action="#"
          >
            <img v-if="avatarUrl" :src="avatarUrl" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import supervisorService from '@/services/supervisorService'
import { API_URL } from '@/utils/api'

export default {
  name: 'SupervisionTeam',
  components: {
    Plus
  },
  setup() {
    // 基础数据
    const loading = ref(false)
    const supervisorList = ref([])
    const total = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(10)
    const baseUrl = ref(API_URL.replace('/api', ''))
    
    // 搜索表单
    const searchForm = reactive({
      name: '',
      department: '',
      status: ''
    })
    
    // 对话框相关
    const dialogVisible = ref(false)
    const isEdit = ref(false)
    const supervisorFormRef = ref(null)
    
    // 新增的状态变量
    const avatarFile = ref(null)
    const avatarUrl = ref('')
    
    // 表单数据
    const formData = reactive({
      id: '',
      name: '',
      gender: '男',
      department: '',
      title: '',
      specialty: '',
      phone: '',
      email: '',
      status: 1,
      avatar: ''
    })
    
    // 表单验证规则
    const formRules = {
      name: [
        { required: true, message: '请输入姓名', trigger: 'blur' }
      ],
      gender: [
        { required: true, message: '请选择性别', trigger: 'change' }
      ],
      department: [
        { required: true, message: '请输入科室', trigger: 'blur' }
      ],
      title: [
        { required: true, message: '请输入职称', trigger: 'blur' }
      ],
      phone: [
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
      ],
      email: [
        { pattern: /^[\w.-]+@[\w.-]+\.\w+$/, message: '请输入正确的邮箱地址', trigger: 'blur' }
      ]
    }
    
    // 生命周期钩子
    onMounted(() => {
      fetchSupervisors()
    })
    
    // 获取督导列表
    const fetchSupervisors = async () => {
      loading.value = true
      try {
        // 构建查询参数
        const params = {
          page: currentPage.value,
          limit: pageSize.value
        }
        
        // 添加搜索条件
        if (searchForm.name) params.name = searchForm.name
        if (searchForm.department) params.department = searchForm.department
        if (searchForm.status !== '') params.status = searchForm.status
        
        const response = await supervisorService.getSupervisors(params)
        supervisorList.value = response.data.data
        total.value = response.data.count
      } catch (error) {
        console.error('获取督导列表失败:', error)
        ElMessage.error('获取督导列表失败')
      } finally {
        loading.value = false
      }
    }
    
    // 搜索操作
    const handleSearch = () => {
      currentPage.value = 1
      fetchSupervisors()
    }
    
    // 重置搜索
    const resetSearch = () => {
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
      })
      currentPage.value = 1
      fetchSupervisors()
    }
    
    // 分页操作
    const handleSizeChange = (val) => {
      pageSize.value = val
      fetchSupervisors()
    }
    
    const handleCurrentChange = (val) => {
      currentPage.value = val
      fetchSupervisors()
    }
    
    // 打开添加对话框
    const openAddDialog = () => {
      isEdit.value = false
      resetFormData()
      dialogVisible.value = true
    }
    
    // 编辑督导
    const handleEdit = (row) => {
      isEdit.value = true
      Object.keys(formData).forEach(key => {
        if (key in row) {
          formData[key] = row[key]
        }
      })
      
      // 如果有头像，显示已有的头像
      if (row.avatar) {
        avatarUrl.value = `${baseUrl.value}${row.avatar}`
        avatarFile.value = null
      } else {
        avatarUrl.value = ''
        avatarFile.value = null
      }
      
      dialogVisible.value = true
    }
    
    // 删除督导
    const handleDelete = (row) => {
      ElMessageBox.confirm(
        `确定要删除督导成员"${row.name}"吗?`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(async () => {
          try {
            await supervisorService.deleteSupervisor(row.id)
            ElMessage.success('删除成功')
            fetchSupervisors()
          } catch (error) {
            console.error('删除失败:', error)
            ElMessage.error('删除失败')
          }
        })
        .catch(() => {
          ElMessage.info('已取消删除')
        })
    }
    
    // 头像上传前的验证
    const beforeAvatarUpload = (file) => {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isJPG) {
        ElMessage.error('头像只能是 JPG 或 PNG 格式!')
      }
      if (!isLt2M) {
        ElMessage.error('头像大小不能超过 2MB!')
      }
      
      return isJPG && isLt2M
    }
    
    // 头像选择的回调
    const handleAvatarChange = (file) => {
      if (file) {
        avatarFile.value = file.raw
        avatarUrl.value = URL.createObjectURL(file.raw)
      }
    }
    
    // 重置表单数据
    const resetFormData = () => {
      Object.assign(formData, {
        id: '',
        name: '',
        gender: '男',
        department: '',
        title: '',
        specialty: '',
        phone: '',
        email: '',
        status: 1,
        avatar: ''
      })
      
      avatarFile.value = null
      avatarUrl.value = ''
      
      if (supervisorFormRef.value) {
        supervisorFormRef.value.resetFields()
      }
    }
    
    // 提交表单
    const submitForm = async () => {
      if (!supervisorFormRef.value) return
      
      await supervisorFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            // 准备表单数据
            const formSubmitData = new FormData()
            
            // 添加所有文本字段
            Object.keys(formData).forEach(key => {
              if (key !== 'avatar') { // 不包括avatar字段，因为我们将单独处理
                formSubmitData.append(key, formData[key])
              }
            })
            
            // 添加头像文件（如果有新选择的文件）
            if (avatarFile.value) {
              formSubmitData.append('avatar', avatarFile.value)
            } else if (formData.avatar) {
              // 如果没有新选择的文件，但有现有的头像路径
              formSubmitData.append('avatar', formData.avatar)
            }
            
            if (isEdit.value) {
              // 编辑模式
              await supervisorService.updateSupervisorWithAvatar(formData.id, formSubmitData)
              ElMessage.success('督导信息更新成功')
            } else {
              // 添加模式
              await supervisorService.createSupervisorWithAvatar(formSubmitData)
              ElMessage.success('督导成员添加成功')
            }
            
            dialogVisible.value = false
            fetchSupervisors()
          } catch (error) {
            console.error('操作失败:', error)
            ElMessage.error('操作失败')
          }
        } else {
          return false
        }
      })
    }
    
    return {
      loading,
      supervisorList,
      searchForm,
      currentPage,
      pageSize,
      total,
      baseUrl,
      dialogVisible,
      isEdit,
      formData,
      formRules,
      supervisorFormRef,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      openAddDialog,
      handleEdit,
      handleDelete,
      beforeAvatarUpload,
      handleAvatarChange,
      submitForm,
      avatarUrl,
      avatarFile
    }
  }
}
</script>

<style scoped>
.supervision-team-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.avatar-uploader {
  display: flex;
  justify-content: center;
}

.avatar-uploader .avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  text-align: center;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style> 