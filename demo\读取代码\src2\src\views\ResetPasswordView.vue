<template>
  <div class="reset-password-container">
    <div class="reset-password-card">
      <div class="logo-wrapper">
        <div class="logo-icon">
          <i class="el-icon-key"></i>
        </div>
        <div class="logo-text">
          重置密码
        </div>
      </div>
      
      <el-form :model="resetForm" :rules="resetRules" ref="resetFormRef" class="reset-form">
        <p class="form-subtitle">请输入新密码</p>
        
        <el-form-item prop="newPassword">
          <el-input 
            v-model="resetForm.newPassword" 
            type="password" 
            placeholder="新密码" 
            :prefix-icon="Lock"
            show-password>
          </el-input>
        </el-form-item>
        
        <el-form-item prop="confirmPassword">
          <el-input 
            v-model="resetForm.confirmPassword" 
            type="password" 
            placeholder="确认新密码" 
            :prefix-icon="Lock"
            show-password>
          </el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleReset" class="reset-button">
            重置密码
          </el-button>
        </el-form-item>
        
        <div class="login-link">
          <span>记住密码了？</span>
          <router-link to="/login">返回登录</router-link>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Lock } from '@element-plus/icons-vue'
import axios from 'axios'

const route = useRoute()
const router = useRouter()
const resetFormRef = ref(null)
const loading = ref(false)
const token = ref('')

const API_URL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api'

// 表单数据
const resetForm = reactive({
  newPassword: '',
  confirmPassword: ''
})

// 自定义验证规则
const validatePass2 = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入密码'))
  } else if (value !== resetForm.newPassword) {
    callback(new Error('两次输入密码不一致!'))
  } else {
    callback()
  }
}

// 表单验证规则
const resetRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认密码', trigger: 'blur' },
    { validator: validatePass2, trigger: 'blur' }
  ]
}

// 处理重置密码
const handleReset = async () => {
  if (!resetFormRef.value) return
  
  try {
    await resetFormRef.value.validate(async (valid) => {
      if (valid) {
        loading.value = true
        
        try {
          const response = await axios.post(`${API_URL}/auth/reset-password`, {
            token: token.value,
            newPassword: resetForm.newPassword
          })
          
          ElMessage.success('密码重置成功，请使用新密码登录')
          router.push('/login')
        } catch (error) {
          console.error('重置密码失败:', error)
          ElMessage.error(error.response?.data?.message || '重置密码失败，请检查链接是否有效')
        } finally {
          loading.value = false
        }
      }
    })
  } catch (error) {
    loading.value = false
    ElMessage.error('表单验证失败')
  }
}

// 组件加载时从URL参数中获取token
onMounted(() => {
  token.value = route.query.token
  
  if (!token.value) {
    ElMessage.error('无效的重置链接，请重新获取')
    router.push('/login')
  }
})
</script>

<style scoped>
.reset-password-container {
  height: 100vh;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(124, 181, 239);
}

.reset-password-card {
  width: 400px;
  background-color: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.1);
}

.logo-wrapper {
  display: flex;
  align-items: center;
  margin-bottom: 30px;
  justify-content: center;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background-color: #409EFF;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 24px;
  color: white;
}

.logo-text {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.form-subtitle {
  font-size: 14px;
  color: #999;
  margin-bottom: 30px;
  text-align: center;
}

.reset-form :deep(.el-input__wrapper) {
  padding: 0 15px;
  height: 50px;
  box-shadow: 0 0 0 1px #e4e7ed inset;
}

.reset-form :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409EFF inset;
}

.reset-button {
  width: 100%;
  height: 50px;
  border-radius: 6px;
  font-size: 16px;
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
  border: none;
  margin-bottom: 20px;
}

.login-link {
  text-align: center;
  font-size: 14px;
  color: #666;
}

.login-link a {
  color: #409EFF;
  text-decoration: none;
  margin-left: 5px;
}
</style> 