<template>
  <div class="rotation-list-container">
    <el-card class="filter-card">
      <div class="filter-container">
        <el-form :model="filterForm" inline>
          <el-form-item label="学生姓名">
            <el-input v-model="filterForm.name" placeholder="请输入学生姓名" clearable></el-input>
          </el-form-item>
          <el-form-item label="科室/专业组">
            <el-input v-model="filterForm.department" placeholder="请输入科室/专业组" clearable></el-input>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="filterForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              clearable>
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>轮转记录</span>
          <div>
            <el-button type="primary" @click="handleAddRotation">新增轮转记录</el-button>
            <el-button type="success" @click="handleExport">导出数据</el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="rotationList" stripe border style="width: 100%" v-loading="loading">
        <el-table-column type="index" width="50" />
        <el-table-column prop="student_name" label="学生姓名" min-width="100" />
        <el-table-column prop="department" label="科室/专业组" min-width="120" />
        <el-table-column label="轮转时间" min-width="180">
          <template #default="scope">
            {{ scope.row.start_date }} 至 {{ scope.row.end_date }}
          </template>
        </el-table-column>
        <el-table-column label="入科考核" min-width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.entry_exam_score" type="success" effect="plain">
              {{ scope.row.entry_exam_score }}分
            </el-tag>
            <span v-else>未考核</span>
          </template>
        </el-table-column>
        <el-table-column label="出科考核" min-width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.exit_exam_score" :type="getExamScoreType(scope.row.exit_exam_score)">
              {{ scope.row.exit_exam_score }}分
            </el-tag>
            <span v-else>未考核</span>
          </template>
        </el-table-column>
        <el-table-column prop="department_rating" label="教研室评价" min-width="100">
          <template #default="scope">
            <el-rate v-model="scope.row.department_rating" disabled text-color="#ff9900"></el-rate>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="220" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="success" size="small" @click="handleExamScore(scope.row)">考核打分</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑轮转记录对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="650px"
    >
      <el-form :model="rotationForm" :rules="rotationRules" ref="rotationFormRef" label-width="120px">
        <el-form-item label="学生" prop="student_id">
          <el-select v-model="rotationForm.student_id" filterable placeholder="请选择学生" style="width: 100%">
            <el-option
              v-for="student in studentList"
              :key="student.id"
              :label="student.name"
              :value="student.id"
            >
              <div style="display: flex; justify-content: space-between; align-items: center">
                <span>{{ student.name }}</span>
                <span style="color: #999; font-size: 12px">{{ student.school }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="科室/专业组" prop="department">
          <el-input v-model="rotationForm.department" placeholder="请输入科室或专业组名称"></el-input>
        </el-form-item>
        
        <el-form-item label="轮转时间" prop="dateRange">
          <el-date-picker
            v-model="rotationForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        
        <el-form-item label="入科考核内容">
          <el-input 
            type="textarea" 
            v-model="rotationForm.entry_exam_content" 
            placeholder="请输入入科考核内容"
            rows="3">
          </el-input>
        </el-form-item>
        
        <el-form-item label="入科考核成绩">
          <el-input-number 
            v-model="rotationForm.entry_exam_score" 
            :min="0" 
            :max="100" 
            :precision="1"
            :step="0.5"
            placeholder="分数">
          </el-input-number>
        </el-form-item>
        
        <el-form-item label="出科考核内容">
          <el-input 
            type="textarea" 
            v-model="rotationForm.exit_exam_content" 
            placeholder="请输入出科考核内容"
            rows="3">
          </el-input>
        </el-form-item>
        
        <el-form-item label="出科考核成绩">
          <el-input-number 
            v-model="rotationForm.exit_exam_score" 
            :min="0" 
            :max="100" 
            :precision="1"
            :step="0.5"
            placeholder="分数">
          </el-input-number>
        </el-form-item>
        
        <el-form-item label="教研室评价">
          <el-rate v-model="rotationForm.department_rating" :max="5"></el-rate>
        </el-form-item>
        
        <el-form-item label="备注">
          <el-input 
            type="textarea" 
            v-model="rotationForm.notes" 
            placeholder="请输入备注信息"
            rows="3">
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
    
    <!-- 考核打分对话框 -->
    <el-dialog
      title="轮转考核打分"
      v-model="examDialogVisible"
      width="600px"
    >
      <div v-if="currentRotation" class="exam-dialog-content">
        <h3>{{ currentRotation.student_name }} - {{ currentRotation.department }}</h3>
        <p>轮转时间：{{ currentRotation.start_date }} 至 {{ currentRotation.end_date }}</p>
        
        <el-tabs v-model="activeExamTab">
          <el-tab-pane label="入科考核" name="entry">
            <el-form :model="examScoreForm" label-width="100px">
              <el-form-item label="考核内容">
                <el-input 
                  type="textarea" 
                  v-model="examScoreForm.entry_exam_content" 
                  placeholder="请输入入科考核内容"
                  rows="4">
                </el-input>
              </el-form-item>
              
              <el-form-item label="考核分数">
                <el-input-number 
                  v-model="examScoreForm.entry_exam_score" 
                  :min="0" 
                  :max="100" 
                  :precision="1"
                  :step="0.5"
                  placeholder="分数">
                </el-input-number>
              </el-form-item>
            </el-form>
          </el-tab-pane>
          
          <el-tab-pane label="出科考核" name="exit">
            <el-form :model="examScoreForm" label-width="100px">
              <el-form-item label="考核内容">
                <el-input 
                  type="textarea" 
                  v-model="examScoreForm.exit_exam_content" 
                  placeholder="请输入出科考核内容"
                  rows="4">
                </el-input>
              </el-form-item>
              
              <el-form-item label="考核分数">
                <el-input-number 
                  v-model="examScoreForm.exit_exam_score" 
                  :min="0" 
                  :max="100" 
                  :precision="1"
                  :step="0.5"
                  placeholder="分数">
                </el-input-number>
              </el-form-item>
              
              <el-form-item label="教研室评价">
                <el-rate v-model="examScoreForm.department_rating" :max="5"></el-rate>
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="examDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitExamScore">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const loading = ref(true)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const dialogVisible = ref(false)
const dialogTitle = ref('新增轮转记录')
const rotationFormRef = ref(null)
const examDialogVisible = ref(false)
const currentRotation = ref(null)
const activeExamTab = ref('entry')

// 过滤条件
const filterForm = reactive({
  name: '',
  department: '',
  dateRange: []
})

// 轮转记录表单
const rotationForm = reactive({
  id: null,
  student_id: null,
  department: '',
  dateRange: [],
  entry_exam_content: '',
  entry_exam_score: null,
  exit_exam_content: '',
  exit_exam_score: null,
  department_rating: 0,
  notes: ''
})

// 考核打分表单
const examScoreForm = reactive({
  entry_exam_content: '',
  entry_exam_score: null,
  exit_exam_content: '',
  exit_exam_score: null,
  department_rating: 0
})

// 验证规则
const rotationRules = {
  student_id: [
    { required: true, message: '请选择学生', trigger: 'change' }
  ],
  department: [
    { required: true, message: '请输入科室/专业组', trigger: 'blur' }
  ],
  dateRange: [
    { required: true, message: '请选择轮转时间', trigger: 'change' }
  ]
}

// 模拟学生数据
const studentList = [
  {
    id: 1,
    name: '张三',
    school: '北京大学'
  },
  {
    id: 2,
    name: '李四',
    school: '清华大学'
  },
  {
    id: 3,
    name: '王五',
    school: '中国人民大学'
  },
  {
    id: 4,
    name: '赵六',
    school: '北京师范大学'
  }
]

// 模拟轮转记录数据
const rotationList = reactive([
  {
    id: 1,
    student_id: 1,
    student_name: '张三',
    department: '内科',
    start_date: '2023-01-01',
    end_date: '2023-03-01',
    entry_exam_content: '内科基础知识测试',
    entry_exam_score: 85,
    exit_exam_content: '内科病例分析、临床技能考核',
    exit_exam_score: 90,
    department_rating: 5,
    notes: '表现优秀，能够主动学习'
  },
  {
    id: 2,
    student_id: 2,
    student_name: '李四',
    department: '外科',
    start_date: '2023-01-01',
    end_date: '2023-03-01',
    entry_exam_content: '外科基础知识测试',
    entry_exam_score: 78,
    exit_exam_content: '外科手术配合、无菌操作考核',
    exit_exam_score: 75,
    department_rating: 3,
    notes: '基本达到要求，需加强手术配合训练'
  },
  {
    id: 3,
    student_id: 3,
    student_name: '王五',
    department: '妇产科',
    start_date: '2023-01-15',
    end_date: '2023-03-15',
    entry_exam_content: '妇产科基础知识测试',
    entry_exam_score: 90,
    exit_exam_content: '',
    exit_exam_score: null,
    department_rating: 4,
    notes: '学习态度认真，临床技能掌握较好'
  },
  {
    id: 4,
    student_id: 4,
    student_name: '赵六',
    department: '儿科',
    start_date: '2023-02-01',
    end_date: '2023-04-01',
    entry_exam_content: '',
    entry_exam_score: null,
    exit_exam_content: '',
    exit_exam_score: null,
    department_rating: 0,
    notes: '轮转刚开始'
  }
])

// 设置总数
total.value = rotationList.length

onMounted(() => {
  // 模拟加载数据
  setTimeout(() => {
    loading.value = false
  }, 1000)
})

// 重置过滤条件
const resetFilter = () => {
  filterForm.name = ''
  filterForm.department = ''
  filterForm.dateRange = []
  handleSearch()
}

// 搜索
const handleSearch = () => {
  loading.value = true
  // 模拟搜索
  setTimeout(() => {
    loading.value = false
    // 此处应该是实际的搜索API调用
    ElMessage.success('搜索完成')
  }, 500)
}

// 新增轮转记录
const handleAddRotation = () => {
  dialogTitle.value = '新增轮转记录'
  dialogVisible.value = true
  // 重置表单
  rotationForm.id = null
  rotationForm.student_id = null
  rotationForm.department = ''
  rotationForm.dateRange = []
  rotationForm.entry_exam_content = ''
  rotationForm.entry_exam_score = null
  rotationForm.exit_exam_content = ''
  rotationForm.exit_exam_score = null
  rotationForm.department_rating = 0
  rotationForm.notes = ''
}

// 编辑轮转记录
const handleEdit = (row) => {
  dialogTitle.value = '编辑轮转记录'
  dialogVisible.value = true
  
  // 填充表单数据
  rotationForm.id = row.id
  rotationForm.student_id = row.student_id
  rotationForm.department = row.department
  rotationForm.dateRange = [row.start_date, row.end_date]
  rotationForm.entry_exam_content = row.entry_exam_content
  rotationForm.entry_exam_score = row.entry_exam_score
  rotationForm.exit_exam_content = row.exit_exam_content
  rotationForm.exit_exam_score = row.exit_exam_score
  rotationForm.department_rating = row.department_rating
  rotationForm.notes = row.notes
}

// 删除轮转记录
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除 ${row.student_name} 在 ${row.department} 的轮转记录吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 此处应该是实际的删除API调用
    ElMessage.success('轮转记录已删除')
  }).catch(() => {})
}

// 导出数据
const handleExport = () => {
  ElMessage.success('数据导出功能待实现')
}

// 考核打分
const handleExamScore = (row) => {
  currentRotation.value = row
  examDialogVisible.value = true
  
  // 填充打分表单
  examScoreForm.entry_exam_content = row.entry_exam_content || ''
  examScoreForm.entry_exam_score = row.entry_exam_score
  examScoreForm.exit_exam_content = row.exit_exam_content || ''
  examScoreForm.exit_exam_score = row.exit_exam_score
  examScoreForm.department_rating = row.department_rating || 0
  
  // 如果已有出科考核分数，默认显示出科考核标签页
  activeExamTab.value = row.exit_exam_score ? 'exit' : 'entry'
}

// 提交表单
const submitForm = async () => {
  if (!rotationFormRef.value) return
  
  await rotationFormRef.value.validate((valid) => {
    if (valid) {
      if (!rotationForm.dateRange || rotationForm.dateRange.length !== 2) {
        ElMessage.error('请选择正确的轮转时间范围')
        return
      }
      
      // 提交表单
      if (rotationForm.id) {
        // 编辑模式
        ElMessage.success('轮转记录更新成功')
      } else {
        // 新增模式
        ElMessage.success('轮转记录添加成功')
      }
      dialogVisible.value = false
    } else {
      return false
    }
  })
}

// 提交考核打分
const submitExamScore = () => {
  if (!currentRotation.value) return
  
  // 更新数据
  if (activeExamTab.value === 'entry') {
    ElMessage.success('入科考核打分保存成功')
  } else {
    ElMessage.success('出科考核打分保存成功')
  }
  
  examDialogVisible.value = false
}

// 获取考核分数类型
const getExamScoreType = (score) => {
  if (!score) return ''
  if (score >= 90) return 'success'
  if (score >= 60) return 'warning'
  return 'danger'
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  // 重新加载数据
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  // 重新加载数据
}
</script>

<style scoped>
.rotation-list-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.filter-container {
  padding: 10px 0;
}

.table-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: 600;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.exam-dialog-content h3 {
  margin-top: 0;
  margin-bottom: 5px;
  font-size: 18px;
}

.exam-dialog-content p {
  margin-top: 0;
  margin-bottom: 20px;
  color: #606266;
}

/* Style the Element Plus components to match LoginView style */
:deep(.el-button--primary) {
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
  border: none;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #66b1ff 0%, #5098fa 100%);
  border: none;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409EFF inset;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
}

:deep(.el-tabs__active-bar) {
  background-color: #409EFF;
}

:deep(.el-tabs__item.is-active) {
  color: #409EFF;
}
</style> 