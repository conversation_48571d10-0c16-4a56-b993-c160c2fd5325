<template>
  <div class="user-list-container">
    <!-- 搜索和操作区域 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="用户名">
          <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable></el-input>
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable></el-input>
        </el-form-item>
      
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 表格区域 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>用户列表</span>
          <div>
            <el-button type="success" @click="downloadTemplate">下载模板</el-button>
            <el-button type="danger" @click="openImportDialog">
              <el-icon><Upload /></el-icon> 一键导入
            </el-button>
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon> 新增用户
            </el-button>
            <el-button @click="refreshTable">
              <el-icon><Refresh /></el-icon> 刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="userList"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        border
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="username" label="用户名" />
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="phone" label="手机号" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="role" label="角色" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.role === 'admin'" type="danger">管理员</el-tag>
            <el-tag v-else-if="scope.row.role === 'teacher'" type="warning">教师</el-tag>
            <!-- 教学管理员、带教老师、教研室主任、教研室副主任、教学秘书 -->
             <el-tag v-else-if="scope.row.role === 'teaching_admin'" type="warning">教学管理员</el-tag>
             <el-tag v-else-if="scope.row.role === 'teaching_teacher'" type="warning">带教老师</el-tag>
             <el-tag v-else-if="scope.row.role === 'department_head'" type="warning">教研室主任</el-tag>
             <el-tag v-else-if="scope.row.role === 'department_deputy_head'" type="warning">教研室副主任</el-tag>
             <el-tag v-else-if="scope.row.role === 'teaching_secretary'" type="warning">教学秘书</el-tag>
            <el-tag v-else type="info">{{ scope.row.role }}</el-tag>
          </template>
        </el-table-column>
       
        
       
       
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="180" align="center">
          <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 用户表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增用户' : '编辑用户'"
      width="600px"
    >
      <el-form
        :model="userForm"
        :rules="userFormRules"
        ref="userFormRef"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" :disabled="dialogType === 'edit'"></el-input>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="userForm.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item v-if="dialogType === 'add'" label="密码" prop="password">
          <el-input v-model="userForm.password" type="password" placeholder="请输入密码" show-password></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="userForm.phone" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email" >
          <el-input v-model="userForm.email" placeholder="请输入邮箱"></el-input>
        </el-form-item>
        <el-form-item label="角色" prop="role" v-if="dialogType === 'add'">
          <el-select v-model="userForm.role" placeholder="请选择角色">
            <el-option label="管理员" value="admin"></el-option>
            <el-option label="教师" value="teacher"></el-option>
            <el-option label="教学管理员" value="teaching_admin"></el-option>
            <el-option label="带教老师" value="teaching_teacher"></el-option>
            <el-option label="教研室主任" value="department_head"></el-option>
            <el-option label="教研室副主任" value="department_deputy_head"></el-option>
            <el-option label="教学秘书" value="teaching_secretary"></el-option>
          </el-select>
        </el-form-item>

        <!-- 当角色是教师时，显示教师选择器 -->
        <el-form-item v-if="userForm.role === 'teacher' && dialogType === 'add'" label="关联教师" prop="teacher_id">
          <el-select 
            v-model="userForm.teacher_id" 
            placeholder="请选择关联的教师" 
            filterable
            :loading="teacherListLoading"
          >
            <el-option
              v-for="item in teacherList"
              :key="item.id"
              :label="`${item.name} - ${item.department}`"
              :value="item.id"
            >
              <div class="teacher-option">
                <span>{{ item.name }}</span>
                <span class="teacher-dept">{{ item.department }}</span>
              </div>
            </el-option>
          </el-select>
          <div class="form-tip">将用户账号关联到现有教师</div>
        </el-form-item>

        <!-- 当角色不是教师时，隐藏教师ID字段 -->
        <el-form-item v-if="userForm.role !== 'teacher'" label="教师ID" prop="teacher_id">
          <el-input v-model="userForm.teacher_id" placeholder="请输入教师ID（选填）"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- Excel导入对话框 -->
    <el-dialog
      v-model="importDialogVisible"
      title="批量导入用户"
      width="50%"
      destroy-on-close
    >
      <div class="import-content">
        <el-alert
          title="导入说明"
          type="info"
          :closable="false"
          style="margin-bottom: 20px"
        >
          <template #default>
            <div>
              <p>1. 请先下载Excel模板，按照模板格式填写用户信息</p>
              <p>2. 必填字段：用户名、密码、角色、姓名</p>
              <p>3. 角色只能填写"admin"或"teacher"</p>
              <p>4. 状态可填写：启用/禁用、true/false、1/0</p>
              <p>5. 支持.xlsx和.xls格式，文件大小不超过10MB</p>
              <p>6. 注意：不包含关联教师功能，如需关联请在导入后手动编辑</p>
            </div>
          </template>
        </el-alert>

        <el-upload
          ref="uploadRef"
          class="upload-demo"
          :action="`${baseUrl}/api/users/import/excel`"
          :headers="uploadHeaders"
          :before-upload="beforeExcelUpload"
          :on-success="handleImportSuccess"
          :on-error="handleImportError"
          :on-change="handleFileChange"
          :file-list="fileList"
          :auto-upload="false"
          accept=".xlsx,.xls"
          :limit="1"
          name="excel"
        >
          <el-button type="primary">选择Excel文件</el-button>
          <template #tip>
            <div class="el-upload__tip">
              只能上传xlsx/xls文件，且不超过10MB
            </div>
          </template>
        </el-upload>

        <!-- 导入结果显示 -->
        <div v-if="importResult" class="import-result" style="margin-top: 20px">
          <el-alert
            :title="`导入完成！成功 ${importResult.success} 条，失败 ${importResult.failed} 条`"
            :type="importResult.failed > 0 ? 'warning' : 'success'"
            :closable="false"
          />

          <!-- 失败记录详情 -->
          <div v-if="importResult.failedRecords && importResult.failedRecords.length > 0" style="margin-top: 15px">
            <el-collapse>
              <el-collapse-item title="查看失败记录" name="failed">
                <el-table :data="importResult.failedRecords" border size="small">
                  <el-table-column prop="row" label="行号" width="80" />
                  <el-table-column prop="error" label="错误原因" />
                </el-table>
              </el-collapse-item>
            </el-collapse>
          </div>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleUpload" :loading="uploading">开始导入</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, Refresh, Upload } from '@element-plus/icons-vue'
import userService from '@/services/userService'
import teacherService from '@/services/teacherService'

const loading = ref(false)
const teacherListLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const multipleSelection = ref([])
const dialogVisible = ref(false)
const dialogType = ref('add') // 'add' or 'edit'
const userFormRef = ref(null)
const teacherList = ref([]) // 教师列表

// 导入相关变量
const importDialogVisible = ref(false)
const uploading = ref(false)
const uploadRef = ref(null)
const fileList = ref([])
const importResult = ref(null)
const baseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000'

// 上传请求头
const uploadHeaders = {
  'Authorization': `Bearer ${localStorage.getItem('token')}`
}

// 搜索表单
const searchForm = reactive({
  username: '',
  phone: '',
  status: ''
})

// 用户表单
const userForm = reactive({
  id: '',
  username: '',
  name: '',
  password: '',
  phone: '',
  email: '',
  role: 'teacher',
  teacher_id: '',
  status: 1
})

// 获取教师列表
const fetchTeacherList = async () => {
  teacherListLoading.value = true
  try {
    const response = await teacherService.getTeachers()
    teacherList.value = response.data.data
  } catch (error) {
    console.error('获取教师列表失败:', error)
    ElMessage.error('获取教师列表失败')
  } finally {
    teacherListLoading.value = false
  }
}

// 当角色选择为教师时，加载教师列表
watch(() => userForm.role, (newRole) => {
  if (newRole === 'teacher' && teacherList.value.length === 0) {
    fetchTeacherList()
  }
})

// 当对话框打开时，如果角色是教师且教师列表为空，则获取教师列表
watch(() => dialogVisible.value, (newVal) => {
  if (newVal && userForm.role === 'teacher' && teacherList.value.length === 0) {
    fetchTeacherList()
  }
})

// 表单校验规则
const userFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  teacher_id: [
    { 
      validator: (rule, value, callback) => {
        if (userForm.role === 'teacher' && !value) {
          callback(new Error('请选择关联的教师'));
        } else {
          callback();
        }
      }, 
      trigger: 'change' 
    }
  ]
}

// 用户数据
const userList = ref([])

onMounted(() => {
  fetchData()
})

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const response = await userService.getUsers({
      page: currentPage.value,
      limit: pageSize.value,
      username: searchForm.username || undefined,
      phone: searchForm.phone || undefined,
      status: searchForm.status || undefined
    })
    userList.value = response.data.data
    total.value = response.data.count || 0
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 查询
const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

// 重置表单
const resetForm = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleSearch()
}

// 刷新表格
const refreshTable = () => {
  fetchData()
}

// 多选变化
const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

// 新增用户
const handleAdd = () => {
  dialogType.value = 'add'
  resetUserForm()
  dialogVisible.value = true
}

// 编辑用户
const handleEdit = (row) => {
  dialogType.value = 'edit'
  resetUserForm()
  Object.keys(userForm).forEach(key => {
    if (key !== 'password') {
      userForm[key] = row[key]
    }
  })
  dialogVisible.value = true
}

// 重置用户表单
const resetUserForm = () => {
  if (userFormRef.value) {
    userFormRef.value.resetFields()
  }
  Object.assign(userForm, {
    id: '',
    username: '',
    name: '',
    password: '',
    phone: '',
    email: '',
    role: 'teacher',
    teacher_id: '',
    status: 1
  })
}

// 提交表单
const submitForm = async () => {
  if (!userFormRef.value) return
  
  await userFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogType.value === 'add') {
          // 新增用户
          if (userForm.role !== 'teacher') {
           delete userForm.teacher_id
          }
          await userService.createUser(userForm)
          ElMessage.success('新增用户成功')
        } else {
          // 编辑用户
          await userService.updateUser(userForm.id, userForm)
          ElMessage.success('编辑用户成功')
        }
        dialogVisible.value = false
        fetchData()
      } catch (error) {
        console.error('保存用户失败:', error)
        ElMessage.error('保存用户失败: ' + (error.response?.data?.message || error.message))
      }
    } else {
      return false
    }
  })
}

// 删除用户
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除用户 ${row.username} 吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await userService.deleteUser(row.id)
      ElMessage.success(`用户 ${row.username} 已删除`)
      fetchData()
    } catch (error) {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败: ' + (error.response?.data?.message || error.message))
    }
  }).catch(() => {})
}

// 批量删除
const handleBatchDelete = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请至少选择一条记录')
    return
  }
  
  const names = multipleSelection.value.map(item => item.username).join('、')
  const ids = multipleSelection.value.map(item => item.id)
  
  ElMessageBox.confirm(`确定要删除选中的 ${multipleSelection.value.length} 条记录吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await userService.batchDeleteUsers(ids)
      ElMessage.success('批量删除成功')
      fetchData()
    } catch (error) {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败: ' + (error.response?.data?.message || error.message))
    }
  }).catch(() => {})
}

// 修改状态
const handleStatusChange = async (val, row) => {
  try {
    await userService.updateUserStatus(row.id, val);
    const status = val === 1 ? '启用' : '禁用';
    ElMessage.success(`已${status}用户 ${row.username}`);
  } catch (error) {
    console.error('修改状态失败:', error);
    ElMessage.error('修改状态失败: ' + (error.response?.data?.message || error.message));
    // 回滚状态
    row.status = val === 1 ? 0 : 1;
  }
};

// 分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchData()
}

// 页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchData()
}

// 下载导入模板
const downloadTemplate = async () => {
  try {
    const response = await fetch(`${baseUrl}/api/users/import/template`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })

    if (!response.ok) {
      throw new Error('下载失败')
    }

    const blob = await response.blob()
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = 'user_import_template.xlsx'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    ElMessage.error('下载模板失败')
  }
}

// 打开导入对话框
const openImportDialog = () => {
  importDialogVisible.value = true
  fileList.value = []
  importResult.value = null
}

// 文件选择变化处理
const handleFileChange = (file, fileListParam) => {
  console.log('文件选择变化:', file, fileListParam)
  fileList.value = fileListParam
}

// Excel文件上传前验证
const beforeExcelUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                 file.type === 'application/vnd.ms-excel'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isExcel) {
    ElMessage.error('只能上传Excel文件!')
    return false
  }

  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB!')
    return false
  }

  return true
}

// 手动上传
const handleUpload = () => {
  if (fileList.value.length === 0) {
    ElMessage.error('请先选择要上传的文件')
    return
  }

  uploading.value = true
  uploadRef.value.submit()
}

// 上传成功回调
const handleImportSuccess = (response) => {
  uploading.value = false
  console.log('导入成功:', response)

  if (response.success) {
    importResult.value = response.data
    ElMessage.success(response.message)
    // 刷新用户列表
    fetchData()
  } else {
    ElMessage.error(response.message || '导入失败')
  }
}

// 上传失败回调
const handleImportError = (error) => {
  uploading.value = false
  console.error('导入失败:', error)
  ElMessage.error('导入失败，请检查文件格式')
}
</script>

<style scoped>
.user-list-container {
  padding: 10px;
}

.search-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.teacher-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.teacher-dept {
  color: #909399;
  font-size: 0.9em;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}

.import-content {
  padding: 20px 0;
}

.import-result {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
  background-color: #f9f9f9;
}

.upload-demo {
  text-align: center;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  padding: 40px;
  transition: border-color 0.3s;
}

.upload-demo:hover {
  border-color: #409eff;
}
</style> 