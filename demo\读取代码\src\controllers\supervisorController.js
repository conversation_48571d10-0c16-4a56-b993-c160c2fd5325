const supervisorModel = require('../models/supervisorModel');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 配置头像上传存储
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../../uploads/photos');
    // 确保目录存在
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const ext = path.extname(file.originalname);
    const randomName = Math.round(Math.random() * 1E9);
    cb(null, `supervisor-${Date.now()}-${randomName}${ext}`);
  }
});

// 文件过滤器
const fileFilter = function (req, file, cb) {
  // 只允许图片文件
  if (file.mimetype.startsWith('image/')) {
    cb(null, true);
  } else {
    cb(new Error('只允许上传图片文件'), false);
  }
};

// 创建上传中间件
const upload = multer({
  storage: storage,
  limits: { fileSize: 2 * 1024 * 1024 }, // 2MB限制
  fileFilter: fileFilter
}).single('avatar');

// 获取所有督导成员，支持分页和搜索
exports.getAllSupervisors = async (req, res) => {
  try {
    const { name, department, status, page = 1, limit = 10 } = req.query;
    
    // 构建查询条件
    const filters = {};
    if (name) filters.name = name;
    if (department) filters.department = department;
    if (status !== undefined && status !== '') filters.status = Number(status);
    
    // 获取数据
    const { count, data } = await supervisorModel.findAll(filters, page, limit);
    
    res.status(200).json({
      success: true,
      count,
      data
    });
  } catch (error) {
    console.error('获取督导成员列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取督导成员列表失败',
      error: error.message
    });
  }
};

// 根据ID获取督导成员
exports.getSupervisorById = async (req, res) => {
  try {
    const supervisor = await supervisorModel.findById(req.params.id);
    
    if (!supervisor) {
      return res.status(404).json({
        success: false,
        message: '未找到该督导成员'
      });
    }
    
    res.status(200).json({
      success: true,
      data: supervisor
    });
  } catch (error) {
    console.error('获取督导成员详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取督导成员详情失败',
      error: error.message
    });
  }
};

// 创建新督导成员
exports.createSupervisor = async (req, res) => {
  upload(req, res, async function(err) {
    if (err instanceof multer.MulterError) {
      return res.status(400).json({
        success: false,
        message: '文件上传错误',
        error: err.message
      });
    } else if (err) {
      return res.status(500).json({
        success: false,
        message: '服务器错误',
        error: err.message
      });
    }
    
    try {
      // 准备督导数据
      const supervisorData = {
        name: req.body.name,
        gender: req.body.gender,
        department: req.body.department,
        title: req.body.title,
        specialty: req.body.specialty,
        phone: req.body.phone,
        email: req.body.email,
        status: req.body.status ? parseInt(req.body.status) : 1
      };
      
      // 如果有头像文件，添加头像路径
      if (req.file) {
        supervisorData.avatar = `/uploads/photos/${req.file.filename}`;
      } else if (req.body.avatar && typeof req.body.avatar === 'string' && req.body.avatar.startsWith('/uploads')) {
        // 如果没有新上传的文件，但有avatar路径（编辑时保留原头像）
        supervisorData.avatar = req.body.avatar;
      }
      
      const supervisor = await supervisorModel.create(supervisorData);
      
      res.status(201).json({
        success: true,
        message: '督导成员创建成功',
        data: supervisor
      });
    } catch (error) {
      console.error('创建督导成员失败:', error);
      // 如果有上传的文件但创建失败，删除已上传的文件
      if (req.file) {
        try {
          fs.unlinkSync(req.file.path);
        } catch (unlinkError) {
          console.error('删除上传文件失败:', unlinkError);
        }
      }
      
      res.status(400).json({
        success: false,
        message: '创建督导成员失败',
        error: error.message
      });
    }
  });
};

// 更新督导成员
exports.updateSupervisor = async (req, res) => {
  upload(req, res, async function(err) {
    if (err instanceof multer.MulterError) {
      return res.status(400).json({
        success: false,
        message: '文件上传错误',
        error: err.message
      });
    } else if (err) {
      return res.status(500).json({
        success: false,
        message: '服务器错误',
        error: err.message
      });
    }
    
    try {
      const { id } = req.params;
      
      // 获取现有的督导数据
      const existingSupervisor = await supervisorModel.findById(id);
      
      if (!existingSupervisor) {
        return res.status(404).json({
          success: false,
          message: '未找到该督导成员'
        });
      }
      
      // 准备更新的数据
      const updateData = {
        name: req.body.name,
        gender: req.body.gender,
        department: req.body.department,
        title: req.body.title,
        specialty: req.body.specialty,
        phone: req.body.phone,
        email: req.body.email,
        status: req.body.status ? parseInt(req.body.status) : existingSupervisor.status
      };
      
      // 处理头像
      if (req.file) {
        // 有新上传的头像
        updateData.avatar = `/uploads/photos/${req.file.filename}`;
        
        // 删除旧头像
        if (existingSupervisor.avatar && existingSupervisor.avatar.startsWith('/uploads/')) {
          const oldAvatarPath = path.join(__dirname, '../../', existingSupervisor.avatar);
          try {
            if (fs.existsSync(oldAvatarPath)) {
              fs.unlinkSync(oldAvatarPath);
            }
          } catch (unlinkError) {
            console.error('删除旧头像失败:', unlinkError);
          }
        }
      } else if (req.body.avatar && typeof req.body.avatar === 'string') {
        // 没有新上传的头像，但前端传来了头像路径（可能是保留原头像）
        updateData.avatar = req.body.avatar;
      }
      
      const updatedSupervisor = await supervisorModel.update(id, updateData);
      
      res.status(200).json({
        success: true,
        message: '督导成员更新成功',
        data: updatedSupervisor
      });
    } catch (error) {
      console.error('更新督导成员失败:', error);
      // 如果有上传的文件但更新失败，删除已上传的文件
      if (req.file) {
        try {
          fs.unlinkSync(req.file.path);
        } catch (unlinkError) {
          console.error('删除上传文件失败:', unlinkError);
        }
      }
      
      res.status(400).json({
        success: false,
        message: '更新督导成员失败',
        error: error.message
      });
    }
  });
};

// 删除督导成员
exports.deleteSupervisor = async (req, res) => {
  try {
    // 获取督导信息，以便删除头像
    const supervisor = await supervisorModel.findById(req.params.id);
    
    if (!supervisor) {
      return res.status(404).json({
        success: false,
        message: '未找到该督导成员'
      });
    }
    
    // 删除督导记录
    const success = await supervisorModel.remove(req.params.id);
    
    if (success && supervisor.avatar && supervisor.avatar.startsWith('/uploads/')) {
      // 删除头像文件
      const avatarPath = path.join(__dirname, '../../', supervisor.avatar);
      try {
        if (fs.existsSync(avatarPath)) {
          fs.unlinkSync(avatarPath);
        }
      } catch (unlinkError) {
        console.error('删除头像文件失败:', unlinkError);
      }
    }
    
    res.status(200).json({
      success: true,
      message: '督导成员删除成功'
    });
  } catch (error) {
    console.error('删除督导成员失败:', error);
    res.status(500).json({
      success: false,
      message: '删除督导成员失败',
      error: error.message
    });
  }
}; 