<template>
  <div class="user-list-container">
    <!-- 搜索和操作区域 -->
    <el-card class="search-card">
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="用户名">
          <el-input v-model="searchForm.username" placeholder="请输入用户名" clearable></el-input>
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="searchForm.phone" placeholder="请输入手机号" clearable></el-input>
        </el-form-item>
      
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 表格区域 -->
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>用户列表</span>
          <div>
            <el-button type="primary" @click="handleAdd">
              <el-icon><Plus /></el-icon> 新增用户
            </el-button>
         
            <el-button @click="refreshTable">
              <el-icon><Refresh /></el-icon> 刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <el-table
        v-loading="loading"
        :data="userList"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        border
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="username" label="用户名" />
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="phone" label="手机号" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="role" label="角色" width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.role === 'admin'" type="danger">管理员</el-tag>
            <el-tag v-else-if="scope.row.role === 'teacher'" type="warning">教师</el-tag>
            <el-tag v-else type="info">{{ scope.row.role }}</el-tag>
          </template>
        </el-table-column>
       
        
       
       
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="180" align="center">
          <template #default="scope">
            <el-button size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDelete(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 用户表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'add' ? '新增用户' : '编辑用户'"
      width="600px"
    >
      <el-form
        :model="userForm"
        :rules="userFormRules"
        ref="userFormRef"
        label-width="100px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" placeholder="请输入用户名" :disabled="dialogType === 'edit'"></el-input>
        </el-form-item>
        <el-form-item label="姓名" prop="name">
          <el-input v-model="userForm.name" placeholder="请输入姓名"></el-input>
        </el-form-item>
        <el-form-item v-if="dialogType === 'add'" label="密码" prop="password">
          <el-input v-model="userForm.password" type="password" placeholder="请输入密码" show-password></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="userForm.phone" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" placeholder="请输入邮箱"></el-input>
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色">
            <el-option label="管理员" value="admin"></el-option>
            <el-option label="教师" value="teacher"></el-option>
          </el-select>
        </el-form-item>

        <!-- 当角色是教师时，显示教师选择器 -->
        <el-form-item v-if="userForm.role === 'teacher'" label="关联教师" prop="teacher_id">
          <el-select 
            v-model="userForm.teacher_id" 
            placeholder="请选择关联的教师" 
            filterable
            :loading="teacherListLoading"
          >
            <el-option
              v-for="item in teacherList"
              :key="item.id"
              :label="`${item.name} - ${item.department}`"
              :value="item.id"
            >
              <div class="teacher-option">
                <span>{{ item.name }}</span>
                <span class="teacher-dept">{{ item.department }}</span>
              </div>
            </el-option>
          </el-select>
          <div class="form-tip">将用户账号关联到现有教师</div>
        </el-form-item>

        <!-- 当角色不是教师时，隐藏教师ID字段 -->
        <el-form-item v-if="userForm.role !== 'teacher'" label="教师ID" prop="teacher_id">
          <el-input v-model="userForm.teacher_id" placeholder="请输入教师ID（选填）"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, Refresh } from '@element-plus/icons-vue'
import userService from '@/services/userService'
import teacherService from '@/services/teacherService'

const loading = ref(false)
const teacherListLoading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const multipleSelection = ref([])
const dialogVisible = ref(false)
const dialogType = ref('add') // 'add' or 'edit'
const userFormRef = ref(null)
const teacherList = ref([]) // 教师列表

// 搜索表单
const searchForm = reactive({
  username: '',
  phone: '',
  status: ''
})

// 用户表单
const userForm = reactive({
  id: '',
  username: '',
  name: '',
  password: '',
  phone: '',
  email: '',
  role: 'teacher',
  teacher_id: '',
  status: 1
})

// 获取教师列表
const fetchTeacherList = async () => {
  teacherListLoading.value = true
  try {
    const response = await teacherService.getTeachers()
    teacherList.value = response.data.data
  } catch (error) {
    console.error('获取教师列表失败:', error)
    ElMessage.error('获取教师列表失败')
  } finally {
    teacherListLoading.value = false
  }
}

// 当角色选择为教师时，加载教师列表
watch(() => userForm.role, (newRole) => {
  if (newRole === 'teacher' && teacherList.value.length === 0) {
    fetchTeacherList()
  }
})

// 当对话框打开时，如果角色是教师且教师列表为空，则获取教师列表
watch(() => dialogVisible.value, (newVal) => {
  if (newVal && userForm.role === 'teacher' && teacherList.value.length === 0) {
    fetchTeacherList()
  }
})

// 表单校验规则
const userFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入姓名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ],
  role: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ],
  teacher_id: [
    { 
      validator: (rule, value, callback) => {
        if (userForm.role === 'teacher' && !value) {
          callback(new Error('请选择关联的教师'));
        } else {
          callback();
        }
      }, 
      trigger: 'change' 
    }
  ]
}

// 用户数据
const userList = ref([])

onMounted(() => {
  fetchData()
})

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 获取数据
const fetchData = async () => {
  loading.value = true
  try {
    const response = await userService.getUsers({
      page: currentPage.value,
      limit: pageSize.value,
      username: searchForm.username || undefined,
      phone: searchForm.phone || undefined,
      status: searchForm.status || undefined
    })
    userList.value = response.data.data
    total.value = response.data.count || 0
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 查询
const handleSearch = () => {
  currentPage.value = 1
  fetchData()
}

// 重置表单
const resetForm = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = ''
  })
  handleSearch()
}

// 刷新表格
const refreshTable = () => {
  fetchData()
}

// 多选变化
const handleSelectionChange = (selection) => {
  multipleSelection.value = selection
}

// 新增用户
const handleAdd = () => {
  dialogType.value = 'add'
  resetUserForm()
  dialogVisible.value = true
}

// 编辑用户
const handleEdit = (row) => {
  dialogType.value = 'edit'
  resetUserForm()
  Object.keys(userForm).forEach(key => {
    if (key !== 'password') {
      userForm[key] = row[key]
    }
  })
  dialogVisible.value = true
}

// 重置用户表单
const resetUserForm = () => {
  if (userFormRef.value) {
    userFormRef.value.resetFields()
  }
  Object.assign(userForm, {
    id: '',
    username: '',
    name: '',
    password: '',
    phone: '',
    email: '',
    role: 'teacher',
    teacher_id: '',
    status: 1
  })
}

// 提交表单
const submitForm = async () => {
  if (!userFormRef.value) return
  
  await userFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogType.value === 'add') {
          // 新增用户
          if (userForm.role === 'admin') {
           delete userForm.teacher_id
          }
          await userService.createUser(userForm)
          ElMessage.success('新增用户成功')
        } else {
          // 编辑用户
          await userService.updateUser(userForm.id, userForm)
          ElMessage.success('编辑用户成功')
        }
        dialogVisible.value = false
        fetchData()
      } catch (error) {
        console.error('保存用户失败:', error)
        ElMessage.error('保存用户失败: ' + (error.response?.data?.message || error.message))
      }
    } else {
      return false
    }
  })
}

// 删除用户
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除用户 ${row.username} 吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await userService.deleteUser(row.id)
      ElMessage.success(`用户 ${row.username} 已删除`)
      fetchData()
    } catch (error) {
      console.error('删除用户失败:', error)
      ElMessage.error('删除用户失败: ' + (error.response?.data?.message || error.message))
    }
  }).catch(() => {})
}

// 批量删除
const handleBatchDelete = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请至少选择一条记录')
    return
  }
  
  const names = multipleSelection.value.map(item => item.username).join('、')
  const ids = multipleSelection.value.map(item => item.id)
  
  ElMessageBox.confirm(`确定要删除选中的 ${multipleSelection.value.length} 条记录吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await userService.batchDeleteUsers(ids)
      ElMessage.success('批量删除成功')
      fetchData()
    } catch (error) {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败: ' + (error.response?.data?.message || error.message))
    }
  }).catch(() => {})
}

// 修改状态
const handleStatusChange = async (val, row) => {
  try {
    await userService.updateUserStatus(row.id, val);
    const status = val === 1 ? '启用' : '禁用';
    ElMessage.success(`已${status}用户 ${row.username}`);
  } catch (error) {
    console.error('修改状态失败:', error);
    ElMessage.error('修改状态失败: ' + (error.response?.data?.message || error.message));
    // 回滚状态
    row.status = val === 1 ? 0 : 1;
  }
};

// 分页大小变化
const handleSizeChange = (size) => {
  pageSize.value = size
  fetchData()
}

// 页码变化
const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchData()
}
</script>

<style scoped>
.user-list-container {
  padding: 10px;
}

.search-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  display: flex;
  flex-wrap: wrap;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.teacher-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.teacher-dept {
  color: #909399;
  font-size: 0.9em;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
</style> 