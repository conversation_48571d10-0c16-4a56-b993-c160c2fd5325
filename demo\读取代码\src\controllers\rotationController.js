const { Rotation, GraduationExam } = require('../models/rotationModel');
const Student = require('../models/studentModel');

// ===== 实习轮转控制器 =====

// 获取学生的轮转记录
exports.getStudentRotations = async (req, res) => {
  try {
    const studentId = req.params.studentId;
    
    // 验证学生是否存在
    const student = await Student.findById(studentId);
    if (!student) {
      return res.status(404).json({
        success: false,
        message: '未找到该学生'
      });
    }
    
    const rotations = await Rotation.findByStudentId(studentId);
    
    res.status(200).json({
      success: true,
      count: rotations.length,
      data: rotations
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取轮转记录失败',
      error: error.message
    });
  }
};

// 获取单个轮转记录
exports.getRotationById = async (req, res) => {
  try {
    const rotationId = req.params.id;
    const rotation = await Rotation.findById(rotationId);
    
    if (!rotation) {
      return res.status(404).json({
        success: false,
        message: '未找到该轮转记录'
      });
    }
    
    res.status(200).json({
      success: true,
      data: rotation
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取轮转记录失败',
      error: error.message
    });
  }
};

// 创建轮转记录
exports.createRotation = async (req, res) => {
  try {
    const { student_id, department, start_date, end_date } = req.body;
    
    // 验证必要字段
    if (!student_id || !department || !start_date || !end_date) {
      return res.status(400).json({
        success: false,
        message: '请提供学生ID、科室/专业组、开始日期和结束日期'
      });
    }
    
    // 验证学生是否存在
    const student = await Student.findById(student_id);
    if (!student) {
      return res.status(404).json({
        success: false,
        message: '未找到该学生'
      });
    }
    
    const rotationId = await Rotation.create(req.body);
    const rotation = await Rotation.findById(rotationId);
    
    res.status(201).json({
      success: true,
      message: '轮转记录创建成功',
      data: rotation
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建轮转记录失败',
      error: error.message
    });
  }
};

// 更新轮转记录
exports.updateRotation = async (req, res) => {
  try {
    const rotationId = req.params.id;
    const rotation = await Rotation.findById(rotationId);
    
    if (!rotation) {
      return res.status(404).json({
        success: false,
        message: '未找到该轮转记录'
      });
    }
    
    const updated = await Rotation.update(rotationId, req.body);
    
    if (updated) {
      const updatedRotation = await Rotation.findById(rotationId);
      res.status(200).json({
        success: true,
        message: '轮转记录更新成功',
        data: updatedRotation
      });
    } else {
      res.status(500).json({
        success: false,
        message: '轮转记录更新失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新轮转记录失败',
      error: error.message
    });
  }
};

// 删除轮转记录
exports.deleteRotation = async (req, res) => {
  try {
    const rotationId = req.params.id;
    const rotation = await Rotation.findById(rotationId);
    
    if (!rotation) {
      return res.status(404).json({
        success: false,
        message: '未找到该轮转记录'
      });
    }
    
    const deleted = await Rotation.delete(rotationId);
    
    if (deleted) {
      res.status(200).json({
        success: true,
        message: '轮转记录删除成功'
      });
    } else {
      res.status(500).json({
        success: false,
        message: '轮转记录删除失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除轮转记录失败',
      error: error.message
    });
  }
};

// 获取特定科室的所有轮转记录
exports.getDepartmentRotations = async (req, res) => {
  try {
    const department = req.params.department;
    
    if (!department) {
      return res.status(400).json({
        success: false,
        message: '请提供科室/专业组名称'
      });
    }
    
    const rotations = await Rotation.findByDepartment(department);
    
    res.status(200).json({
      success: true,
      count: rotations.length,
      data: rotations
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取科室轮转记录失败',
      error: error.message
    });
  }
};

// ===== 结业考核控制器 =====

// 获取所有结业考核记录
exports.getAllGraduationExams = async (req, res) => {
  try {
    const exams = await GraduationExam.findAll();
    
    res.status(200).json({
      success: true,
      count: exams.length,
      data: exams
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取结业考核记录失败',
      error: error.message
    });
  }
};

// 获取学生的结业考核记录
exports.getStudentGraduationExam = async (req, res) => {
  try {
    const studentId = req.params.studentId;
    
    // 验证学生是否存在
    const student = await Student.findById(studentId);
    if (!student) {
      return res.status(404).json({
        success: false,
        message: '未找到该学生'
      });
    }
    
    const exam = await GraduationExam.findByStudentId(studentId);
    
    if (!exam) {
      return res.status(404).json({
        success: false,
        message: '该学生尚未参加结业考核'
      });
    }
    
    res.status(200).json({
      success: true,
      data: exam
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取结业考核记录失败',
      error: error.message
    });
  }
};

// 创建结业考核记录
exports.createGraduationExam = async (req, res) => {
  try {
    const { student_id, theory_score, practice_score, exam_date } = req.body;
    
    // 验证必要字段
    if (!student_id || !theory_score || !practice_score || !exam_date) {
      return res.status(400).json({
        success: false,
        message: '请提供学生ID、理论考核成绩、操作考核成绩和考核日期'
      });
    }
    
    // 验证学生是否存在
    const student = await Student.findById(student_id);
    if (!student) {
      return res.status(404).json({
        success: false,
        message: '未找到该学生'
      });
    }
    
    // 检查是否已存在结业考核记录
    const existingExam = await GraduationExam.findByStudentId(student_id);
    if (existingExam) {
      return res.status(400).json({
        success: false,
        message: '该学生已有结业考核记录，请使用更新功能'
      });
    }
    
    // 计算最终成绩（理论成绩和操作成绩的平均值）
    const final_score = (parseFloat(theory_score) + parseFloat(practice_score)) / 2;
    
    const examData = {
      ...req.body,
      final_score
    };
    
    const examId = await GraduationExam.create(examData);
    const exam = await GraduationExam.findById(examId);
    
    res.status(201).json({
      success: true,
      message: '结业考核记录创建成功',
      data: exam
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建结业考核记录失败',
      error: error.message
    });
  }
};

// 更新结业考核记录
exports.updateGraduationExam = async (req, res) => {
  try {
    const examId = req.params.id;
    const exam = await GraduationExam.findById(examId);
    
    if (!exam) {
      return res.status(404).json({
        success: false,
        message: '未找到该结业考核记录'
      });
    }
    
    // 如果更新了理论成绩或操作成绩，重新计算最终成绩
    let final_score = exam.final_score;
    if (req.body.theory_score !== undefined || req.body.practice_score !== undefined) {
      const theory_score = req.body.theory_score !== undefined ? parseFloat(req.body.theory_score) : parseFloat(exam.theory_score);
      const practice_score = req.body.practice_score !== undefined ? parseFloat(req.body.practice_score) : parseFloat(exam.practice_score);
      final_score = (theory_score + practice_score) / 2;
    }
    
    const examData = {
      ...req.body,
      final_score
    };
    
    const updated = await GraduationExam.update(examId, examData);
    
    if (updated) {
      const updatedExam = await GraduationExam.findById(examId);
      res.status(200).json({
        success: true,
        message: '结业考核记录更新成功',
        data: updatedExam
      });
    } else {
      res.status(500).json({
        success: false,
        message: '结业考核记录更新失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新结业考核记录失败',
      error: error.message
    });
  }
};

// 删除结业考核记录
exports.deleteGraduationExam = async (req, res) => {
  try {
    const examId = req.params.id;
    const exam = await GraduationExam.findById(examId);
    
    if (!exam) {
      return res.status(404).json({
        success: false,
        message: '未找到该结业考核记录'
      });
    }
    
    const deleted = await GraduationExam.delete(examId);
    
    if (deleted) {
      res.status(200).json({
        success: true,
        message: '结业考核记录删除成功'
      });
    } else {
      res.status(500).json({
        success: false,
        message: '结业考核记录删除失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除结业考核记录失败',
      error: error.message
    });
  }
}; 