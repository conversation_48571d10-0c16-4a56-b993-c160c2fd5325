const express = require('express');
const router = express.Router();
const { pool } = require('../config/db');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 配置头像上传
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../../uploads/photos');
    // 确保目录存在
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'teacher-' + uniqueSuffix + ext);
  }
});

const upload = multer({ storage: storage });

// 上传教师照片专用接口
router.post('/upload/photo', upload.single('photo'), (req, res) => {
  if (!req.file) {
    return res.status(400).json({
      success: false,
      message: '未上传文件'
    });
  }
  
  const photoPath = `/uploads/photos/${req.file.filename}`;
  
  res.status(200).json({
    success: true,
    message: '文件上传成功',
    path: photoPath
  });
});

// 获取所有教师 - 添加搜索和分页功能
router.get('/', async (req, res, next) => {
  try {
    const { name, department, is_employed, page = 1, limit = 10 } = req.query;
    
    // 构建查询条件
    const queryParams = [];
    let whereClause = '';
    
    if (name) {
      whereClause += whereClause ? ' AND ' : ' WHERE ';
      whereClause += 'name LIKE ?';
      queryParams.push(`%${name}%`);
    }
    
    if (department) {
      whereClause += whereClause ? ' AND ' : ' WHERE ';
      whereClause += 'department LIKE ?';
      queryParams.push(`%${department}%`);
    }
    
    if (is_employed !== undefined && is_employed !== '') {
      whereClause += whereClause ? ' AND ' : ' WHERE ';
      whereClause += 'is_employed = ?';
      queryParams.push(is_employed);
    }
    
    // 计算总数
    const [countResult] = await pool.query(
      `SELECT COUNT(*) as total FROM teachers${whereClause}`, 
      queryParams
    );
    const total = countResult[0].total;
    
    // 分页参数
    const offset = (page - 1) * limit;
    const pageLimit = parseInt(limit);
    
    // 查询数据
    const [rows] = await pool.query(
      `SELECT * FROM teachers${whereClause}
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`, 
      [...queryParams, pageLimit, offset]
    );
    
    res.status(200).json({
      success: true,
      count: total,
      data: rows,
      page: parseInt(page),
      limit: pageLimit,
      totalPages: Math.ceil(total / pageLimit)
    });
  } catch (error) {
    next(error);
  }
});

// 获取单个教师信息
router.get('/:id', async (req, res, next) => {
  try {
    const [rows] = await pool.query(`
      SELECT * FROM teachers WHERE id = ?
    `, [req.params.id]);
    
    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该教师'
      });
    }
    
    res.status(200).json({
      success: true,
      data: rows[0]
    });
  } catch (error) {
    next(error);
  }
});

// 创建教师
router.post('/', upload.single('photo'), async (req, res, next) => {
  try {
    console.log('创建教师请求体:', req.body);
    console.log('上传的文件:', req.file);
    
    const {
      name,
      gender,
      department,
      school,
      major,
      education,
      is_employed,
      employment_period,
      phone,
      photo
    } = req.body;
    
    // 检查必填项
    if (!name || !gender || !department || !school || !major || !education) {
      return res.status(400).json({
        success: false,
        message: '请填写必要的教师信息'
      });
    }
    
    // 处理头像路径
    let photoPath = null;
    if (req.file) {
      // 如果是通过文件上传的照片
      photoPath = `/uploads/photos/${req.file.filename}`;
      console.log('从文件中获取的照片路径:', photoPath);
    } else if (photo) {
      // 如果通过JSON请求体提供了photo字段
      photoPath = photo;
      console.log('从请求体获取的照片路径:', photoPath);
    }
    
    console.log('最终使用的照片路径:', photoPath);
    
    const [result] = await pool.query(`
      INSERT INTO teachers (
        name, gender, department, school, major, education,
        is_employed, employment_period, phone, photo
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      name, 
      gender, 
      department, 
      school, 
      major, 
      education,
      is_employed === 'true' || is_employed === '1' || is_employed === 1 ? 1 : 0,
      employment_period || null,
      phone || null,
      photoPath
    ]);
    
    res.status(201).json({
      success: true,
      message: '教师创建成功',
      data: {
        id: result.insertId,
        name,
        gender,
        department,
        school,
        major,
        education,
        is_employed: is_employed === 'true' || is_employed === '1' || is_employed === 1 ? 1 : 0,
        employment_period: employment_period || null,
        phone: phone || null,
        photo: photoPath
      }
    });
  } catch (error) {
    console.error('创建教师失败:', error);
    next(error);
  }
});

// 更新教师信息
router.put('/:id', upload.single('photo'), async (req, res, next) => {
  try {
    const {
      name,
      gender,
      department,
      school,
      major,
      education,
      is_employed,
      employment_period,
      phone,
      photo
    } = req.body;
    
    // 检查必填项
    if (!name || !gender || !department || !school || !major || !education) {
      return res.status(400).json({
        success: false,
        message: '请填写必要的教师信息'
      });
    }
    
    // 获取当前教师信息
    const [currentTeacher] = await pool.query(`
      SELECT * FROM teachers WHERE id = ?
    `, [req.params.id]);
    
    if (currentTeacher.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该教师'
      });
    }
    
    // 处理头像路径
    let photoPath = currentTeacher[0].photo;
    if (req.file) {
      photoPath = `/uploads/photos/${req.file.filename}`;
      
      // 删除旧头像
      if (currentTeacher[0].photo) {
        const oldPhotoPath = path.join(__dirname, '../..', currentTeacher[0].photo);
        if (fs.existsSync(oldPhotoPath)) {
          fs.unlinkSync(oldPhotoPath);
        }
      }
    } else if (photo !== undefined) {
      // 如果通过请求体传递了photo字段
      photoPath = photo;
    }
    
    await pool.query(`
      UPDATE teachers 
      SET name = ?, gender = ?, department = ?, school = ?, major = ?,
          education = ?, is_employed = ?, employment_period = ?, phone = ?,
          photo = ?, updated_at = NOW()
      WHERE id = ?
    `, [
      name, 
      gender, 
      department, 
      school, 
      major, 
      education,
      is_employed === 'true' || is_employed === '1' || is_employed === 1 ? 1 : 0,
      employment_period || null,
      phone || null,
      photoPath,
      req.params.id
    ]);
    
    res.status(200).json({
      success: true,
      message: '教师信息更新成功',
      data: {
        id: parseInt(req.params.id),
        name,
        gender,
        department,
        school,
        major,
        education,
        is_employed: is_employed === 'true' || is_employed === '1' || is_employed === 1 ? 1 : 0,
        employment_period: employment_period || null,
        phone: phone || null,
        photo: photoPath
      }
    });
  } catch (error) {
    next(error);
  }
});

// 删除教师
router.delete('/:id', async (req, res, next) => {
  try {
    // 获取当前教师信息
    const [currentTeacher] = await pool.query(`
      SELECT * FROM teachers WHERE id = ?
    `, [req.params.id]);
    
    if (currentTeacher.length === 0) {
      return res.status(404).json({
        success: false,
        message: '未找到该教师'
      });
    }
    
    // 删除头像文件
    if (currentTeacher[0].photo) {
      const photoPath = path.join(__dirname, '../..', currentTeacher[0].photo);
      if (fs.existsSync(photoPath)) {
        fs.unlinkSync(photoPath);
      }
    }
    
    await pool.query('DELETE FROM teachers WHERE id = ?', [req.params.id]);
    
    res.status(200).json({
      success: true,
      message: '教师删除成功'
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router; 