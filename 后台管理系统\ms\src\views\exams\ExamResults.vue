<template>
  <div class="exam-results-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="title">考试成绩</span>
          <div>
            <el-button @click="goBack">返回考试列表</el-button>
            <el-button type="primary" @click="exportResults">导出成绩</el-button>
          </div>
        </div>
      </template>

      <div class="exam-info" v-if="examData">
        <el-descriptions title="考试信息" :column="3" border>
          <el-descriptions-item label="考试名称">{{ examData.title }}</el-descriptions-item>
          <el-descriptions-item label="考试时长">{{ examData.duration }}分钟</el-descriptions-item>
          <el-descriptions-item label="总分">{{ examData.total_score }}分</el-descriptions-item>
          <el-descriptions-item label="及格分数">{{ examData.pass_score }}分</el-descriptions-item>
          <el-descriptions-item label="考试状态">
            <el-tag :type="getExamStatusType(examData.status)">
              {{ getExamStatusText(examData.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(examData.created_at) }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 成绩统计 -->
      <div class="results-stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ totalParticipants }}</div>
              <div class="stat-label">参考人数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ passCount }}</div>
              <div class="stat-label">通过人数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ passRate }}%</div>
              <div class="stat-label">通过率</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-value">{{ averageScore }}</div>
              <div class="stat-label">平均分</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 搜索区域 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="姓名">
          <el-input v-model="searchForm.name" placeholder="教师姓名" clearable />
        </el-form-item>
        <el-form-item label="科室">
          <el-input v-model="searchForm.department" placeholder="所属科室" clearable />
        </el-form-item>
        <el-form-item label="成绩状态">
          <el-select v-model="searchForm.status" placeholder="成绩状态" clearable>
            <el-option label="通过" :value="'pass'" />
            <el-option label="不通过" :value="'fail'" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 成绩列表 -->
      <div v-loading="loading">
        <el-empty v-if="results.length === 0" description="暂无考试成绩" />
        
        <el-table
          v-else
          :data="results"
          border
          style="width: 100%"
        >
          <el-table-column type="index" width="50" label="#" />
          <el-table-column prop="teacher_name" label="姓名" width="100" />
          <el-table-column prop="department" label="科室" width="120" />
          <el-table-column prop="score" label="分数" width="80">
            <template #default="scope">
              <span :class="{ 'pass-score': isPass(scope.row), 'fail-score': !isPass(scope.row) }">
                {{ scope.row.score }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template #default="scope">
              <el-tag :type="isPass(scope.row) ? 'success' : 'danger'">
                {{ isPass(scope.row) ? '通过' : '不通过' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="start_time" label="开始时间" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.start_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="end_time" label="结束时间" width="180">
            <template #default="scope">
              {{ formatDateTime(scope.row.end_time) }}
            </template>
          </el-table-column>
          <el-table-column prop="duration" label="用时" width="120">
            <template #default="scope">
              {{ formatDuration(scope.row.start_time, scope.row.end_time) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" fixed="right">
            <template #default="scope">
              <el-button size="small" @click="viewDetail(scope.row)">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </el-card>

    <!-- 成绩详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="成绩详情"
      width="800px"
    >
      <div v-if="currentResult" class="result-detail">
        <el-descriptions title="考生信息" :column="3" border>
          <el-descriptions-item label="姓名">{{ currentResult.teacher_name }}</el-descriptions-item>
          <el-descriptions-item label="科室">{{ currentResult.department }}</el-descriptions-item>
          <el-descriptions-item label="分数">
            <span :class="{ 'pass-score': isPass(currentResult), 'fail-score': !isPass(currentResult) }">
              {{ currentResult.score }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="isPass(currentResult) ? 'success' : 'danger'">
              {{ isPass(currentResult) ? '通过' : '不通过' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatDateTime(currentResult.start_time) }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{ formatDateTime(currentResult.end_time) }}</el-descriptions-item>
        </el-descriptions>

        <el-divider content-position="center">答题详情</el-divider>

        <el-collapse v-if="currentResult.answers && currentResult.answers.length > 0">
          <el-collapse-item 
            v-for="(answer, index) in currentResult.answers" 
            :key="index"
            :name="index"
          >
            <template #title>
              <div class="answer-item-title">
                <span>第 {{ index + 1 }} 题</span>
                <el-tag :type="answer.is_correct ? 'success' : 'danger'" size="small">
                  {{ answer.is_correct ? '正确' : '错误' }}
                </el-tag>
                <span class="question-score">{{ answer.score }} 分</span>
              </div>
            </template>
            
            <div class="question-content">{{ answer.question_content }}</div>
            
            <!-- 选择题选项 -->
            <div v-if="answer.question_type !== 'true_false'" class="options-list">
              <div
                v-for="(option, optIndex) in answer.options"
                :key="optIndex"
                class="option-item"
                :class="{
                  'selected-option': isOptionSelected(answer, option),
                  'correct-option': option.is_correct,
                  'wrong-option': isWrongOption(answer, option)
                }"
              >
                <div class="option-label">{{ String.fromCharCode(65 + optIndex) }}</div>
                <div class="option-content">{{ option.content }}</div>
                <div v-if="option.is_correct" class="option-mark correct-mark">
                  <el-icon><Check /></el-icon>
                </div>
                <div v-else-if="isOptionSelected(answer, option)" class="option-mark wrong-mark">
                  <el-icon><Close /></el-icon>
                </div>
              </div>
            </div>
            
            <!-- 判断题答案 -->
            <div v-else class="true-false-answer">
              <div class="answer-row">
                <div class="answer-label">正确答案：</div>
                <div class="answer-value">
                  <el-tag :type="answer.correct_answer ? 'success' : 'danger'">
                    {{ answer.correct_answer ? '正确' : '错误' }}
                  </el-tag>
                </div>
              </div>
              <div class="answer-row">
                <div class="answer-label">考生答案：</div>
                <div class="answer-value">
                  <el-tag :type="answer.user_answer === answer.correct_answer ? 'success' : 'danger'">
                    {{ answer.user_answer ? '正确' : '错误' }}
                  </el-tag>
                </div>
              </div>
            </div>
            
            <!-- 解析 -->
            <div v-if="answer.explanation" class="question-explanation">
              <div class="explanation-label">解析：</div>
              <div class="explanation-content">{{ answer.explanation }}</div>
            </div>
          </el-collapse-item>
        </el-collapse>
        
        <el-empty v-else description="暂无答题详情" />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Check, Close } from '@element-plus/icons-vue'
import axios from 'axios'

export default {
  name: 'ExamResults',
  components: {
    Check,
    Close
  },
  setup() {
      let token = localStorage.getItem('token')
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
    }
    const route = useRoute()
    const router = useRouter()
    const examId = route.params.id
    
    // 基础数据
    const loading = ref(false)
    const examData = ref(null)
    const results = ref([])
    const total = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(10)
    
    // 详情对话框
    const detailDialogVisible = ref(false)
    const currentResult = ref(null)
    
    // 搜索表单
    const searchForm = reactive({
      name: '',
      department: '',
      status: ''
    })
    
    // 计算属性
    const totalParticipants = computed(() => {
      return results.value.length
    })
    
    const passCount = computed(() => {
      return results.value.filter(result => isPass(result)).length
    })
    
    const passRate = computed(() => {
      if (totalParticipants.value === 0) return 0
      return Math.round((passCount.value / totalParticipants.value) * 100)
    })
    
    const averageScore = computed(() => {
      if (totalParticipants.value === 0) return 0
      const totalScore = results.value.reduce((sum, result) => sum + result.score, 0)
      return (totalScore / totalParticipants.value).toFixed(1)
    })
    
    // 生命周期钩子
    onMounted(() => {
      fetchExamData()
      fetchResults()
    })
    
    // 获取考试信息
    const fetchExamData = async () => {
      try {
        const response = await axios.get(`http://localhost:3000/api/exams/${examId}`)
        examData.value = response.data.data
      } catch (error) {
        console.error('获取考试信息失败:', error)
        ElMessage.error('获取考试信息失败')
      }
    }
    
    // 获取考试成绩
    const fetchResults = async () => {
      loading.value = true
      try {
        const response = await axios.get(`http://localhost:3000/api/exams/${examId}/results`)
        results.value = response.data.data
        total.value = response.data.count
      } catch (error) {
        console.error('获取考试成绩失败:', error)
        ElMessage.error('获取考试成绩失败')
      } finally {
        loading.value = false
      }
    }
    
    // 获取考试状态文本
    const getExamStatusText = (status) => {
      const statusMap = {
        'draft': '草稿',
        'published': '已发布',
        'in_progress': '进行中',
        'completed': '已结束'
      }
      return statusMap[status] || '未知状态'
    }
    
    // 获取考试状态类型
    const getExamStatusType = (status) => {
      const typeMap = {
        'draft': 'info',
        'published': 'success',
        'in_progress': 'warning',
        'completed': 'danger'
      }
      return typeMap[status] || 'info'
    }
    
    // 判断是否通过
    const isPass = (result) => {
      if (!examData.value || !result) return false
      return result.score >= examData.value.pass_score
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
    }
    
    // 格式化日期时间
    const formatDateTime = (dateString) => {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return `${formatDate(dateString)} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    }
    
    // 格式化时长
    const formatDuration = (startTime, endTime) => {
      if (!startTime || !endTime) return '-'
      
      const start = new Date(startTime)
      const end = new Date(endTime)
      const diffMs = end - start
      
      const minutes = Math.floor(diffMs / 60000)
      const seconds = Math.floor((diffMs % 60000) / 1000)
      
      return `${minutes}分${seconds}秒`
    }
    
    // 返回考试列表
    const goBack = () => {
      router.push('/exams/list')
    }
    
    // 搜索操作
    const handleSearch = () => {
      currentPage.value = 1
      fetchResults()
    }
    
    // 重置搜索
    const resetSearch = () => {
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
      })
      currentPage.value = 1
      fetchResults()
    }
    
    // 分页操作
    const handleSizeChange = (val) => {
      pageSize.value = val
      fetchResults()
    }
    
    const handleCurrentChange = (val) => {
      currentPage.value = val
      fetchResults()
    }
    
    // 查看详情
    const viewDetail = async (row) => {
      try {
        const response = await axios.get(`http://localhost:3000/api/exams/results/${row.id}`)
        currentResult.value = response.data.data
        detailDialogVisible.value = true
      } catch (error) {
        console.error('获取成绩详情失败:', error)
        ElMessage.error('获取成绩详情失败')
      }
    }
    
    // 导出成绩
    const exportResults = () => {
      window.open(`http://localhost:3000/api/exams/${examId}/results/export`, '_blank')
    }
    
    // 判断选项是否被选中
    const isOptionSelected = (answer, option) => {
      if (!answer.user_answers) return false
      return answer.user_answers.includes(option.id)
    }
    
    // 判断是否为错误选择
    const isWrongOption = (answer, option) => {
      return isOptionSelected(answer, option) && !option.is_correct
    }
    
    return {
      loading,
      examData,
      results,
      total,
      currentPage,
      pageSize,
      searchForm,
      detailDialogVisible,
      currentResult,
      totalParticipants,
      passCount,
      passRate,
      averageScore,
      getExamStatusText,
      getExamStatusType,
      isPass,
      formatDate,
      formatDateTime,
      formatDuration,
      goBack,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      viewDetail,
      exportResults,
      isOptionSelected,
      isWrongOption
    }
  }
}
</script>

<style scoped>
.exam-results-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.exam-info {
  margin-bottom: 20px;
}

.results-stats {
  margin: 20px 0;
}

.stat-card {
  background-color: #f7f7f7;
  padding: 20px;
  border-radius: 4px;
  text-align: center;
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.pass-score {
  color: #67c23a;
  font-weight: bold;
}

.fail-score {
  color: #f56c6c;
  font-weight: bold;
}

.result-detail {
  padding: 10px;
}

.answer-item-title {
  display: flex;
  align-items: center;
  gap: 10px;
}

.question-score {
  margin-left: auto;
  color: #f56c6c;
  font-weight: bold;
}

.question-content {
  font-size: 16px;
  margin: 15px 0;
  padding: 10px;
  background-color: #f7f7f7;
  border-radius: 4px;
}

.options-list {
  margin-top: 15px;
}

.options-list .option-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 5px;
}

.options-list .option-item:last-child {
  border-bottom: none;
}

.options-list .option-item.correct-option {
  background-color: #f0f9eb;
}

.options-list .option-item.wrong-option {
  background-color: #fef0f0;
}

.options-list .option-item.selected-option {
  border: 1px solid #dcdfe6;
}

.options-list .option-label {
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background-color: #f2f6fc;
  margin-right: 10px;
  font-weight: bold;
}

.options-list .option-content {
  flex: 1;
}

.options-list .option-mark {
  margin-left: 10px;
}

.options-list .correct-mark {
  color: #67c23a;
}

.options-list .wrong-mark {
  color: #f56c6c;
}

.true-false-answer {
  margin: 15px 0;
  padding: 10px;
  background-color: #f7f7f7;
  border-radius: 4px;
}

.answer-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.answer-row:last-child {
  margin-bottom: 0;
}

.answer-label {
  font-weight: bold;
  margin-right: 10px;
  width: 80px;
}

.question-explanation {
  margin-top: 20px;
  padding: 10px;
  background-color: #f7f7f7;
  border-radius: 4px;
}

.explanation-label {
  font-weight: bold;
  margin-bottom: 5px;
}

.explanation-content {
  color: #606266;
  white-space: pre-line;
}
</style> 