{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { ElMessage } from 'element-plus';\nimport axios from 'axios';\nexport default {\n  name: 'CompetencyList',\n  setup() {\n    const router = useRouter();\n    let token = localStorage.getItem('token');\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    }\n    // 基础数据\n    const loading = ref(false);\n    const teacherList = ref([]);\n    const competencyDataMap = ref({});\n    const total = ref(0);\n    const currentPage = ref(1);\n    const pageSize = ref(10);\n\n    // 搜索表单\n    const searchForm = reactive({\n      name: '',\n      department: '',\n      isCertified: ''\n    });\n\n    // 生命周期钩子\n    onMounted(() => {\n      fetchTeachers();\n    });\n\n    // 获取教师列表\n    const fetchTeachers = async () => {\n      loading.value = true;\n      try {\n        // 添加搜索参数\n        const params = {\n          page: currentPage.value,\n          limit: pageSize.value\n        };\n        if (searchForm.name) params.name = searchForm.name;\n        if (searchForm.department) params.department = searchForm.department;\n        const response = await axios.get('http://localhost:3000/api/teachers', {\n          params\n        });\n        teacherList.value = response.data.data;\n        total.value = response.data.count;\n\n        // 获取每个教师的能力认定数据\n        await fetchAllTeachersCompetency();\n      } catch (error) {\n        console.error('获取教师列表失败:', error);\n        ElMessage.error('获取教师列表失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 获取所有教师的能力认定数据\n    const fetchAllTeachersCompetency = async () => {\n      try {\n        const promises = teacherList.value.map(teacher => axios.get(`http://localhost:3000/api/evaluations/competency/teacher/${teacher.id}`).then(response => {\n          competencyDataMap.value[teacher.id] = response.data.data;\n        }).catch(error => {\n          console.error(`获取教师${teacher.id}能力认定数据失败:`, error);\n        }));\n        await Promise.all(promises);\n      } catch (error) {\n        console.error('获取教师能力认定数据失败:', error);\n      }\n    };\n\n    // 获取教师能力认定数据\n    const getCompetencyData = (teacherId, field) => {\n      if (competencyDataMap.value[teacherId] && field in competencyDataMap.value[teacherId]) {\n        return competencyDataMap.value[teacherId][field];\n      }\n      return null;\n    };\n\n    // 获取认证状态样式\n    const getCompetencyStatus = teacherId => {\n      const isCert = isCertified(teacherId);\n      return isCert ? 'success' : '';\n    };\n\n    // 是否已认证\n    const isCertified = teacherId => {\n      return getCompetencyData(teacherId, 'is_certified') === true;\n    };\n\n    // 格式化百分比\n    const percentFormat = percentage => {\n      return `${percentage}%`;\n    };\n\n    // 搜索操作\n    const handleSearch = () => {\n      currentPage.value = 1;\n      fetchTeachers();\n    };\n\n    // 重置搜索\n    const resetSearch = () => {\n      Object.keys(searchForm).forEach(key => {\n        searchForm[key] = '';\n      });\n      currentPage.value = 1;\n      fetchTeachers();\n    };\n\n    // 分页操作\n    const handleSizeChange = val => {\n      pageSize.value = val;\n      fetchTeachers();\n    };\n    const handleCurrentChange = val => {\n      currentPage.value = val;\n      fetchTeachers();\n    };\n\n    // 查看详情\n    const viewDetails = id => {\n      router.push(`/competency/detail/${id}`);\n    };\n\n    // 查看教师评价\n    const viewEvaluations = id => {\n      router.push(`/teachers/detail/${id}`);\n    };\n    return {\n      loading,\n      teacherList,\n      competencyDataMap,\n      searchForm,\n      currentPage,\n      pageSize,\n      total,\n      handleSearch,\n      resetSearch,\n      handleSizeChange,\n      handleCurrentChange,\n      viewDetails,\n      viewEvaluations,\n      getCompetencyData,\n      getCompetencyStatus,\n      isCertified,\n      percentFormat\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "useRouter", "ElMessage", "axios", "name", "setup", "router", "token", "localStorage", "getItem", "defaults", "headers", "common", "loading", "teacherList", "competencyDataMap", "total", "currentPage", "pageSize", "searchForm", "department", "isCertified", "fetchTeachers", "value", "params", "page", "limit", "response", "get", "data", "count", "fetchAllTeachersCompetency", "error", "console", "promises", "map", "teacher", "id", "then", "catch", "Promise", "all", "getCompetencyData", "teacherId", "field", "getCompetencyStatus", "is<PERSON>ert", "percentFormat", "percentage", "handleSearch", "resetSearch", "Object", "keys", "for<PERSON>ach", "key", "handleSizeChange", "val", "handleCurrentChange", "viewDetails", "push", "viewEvaluations"], "sources": ["D:\\admin\\202506\\督导系统\\后台管理系统\\ms\\src\\views\\competency\\CompetencyList.vue"], "sourcesContent": ["<template>\r\n  <div class=\"competency-list-container\">\r\n    <el-card class=\"box-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span class=\"title\">教师能力认定</span>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- 搜索区域 -->\r\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\r\n        <el-form-item label=\"教师姓名\">\r\n          <el-input v-model=\"searchForm.name\" placeholder=\"教师姓名\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"科室\">\r\n          <el-input v-model=\"searchForm.department\" placeholder=\"所属科室\" clearable />\r\n        </el-form-item>\r\n        \r\n        \r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n          <el-button @click=\"resetSearch\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <!-- 表格区域 -->\r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"teacherList\"\r\n        border\r\n        style=\"width: 100%\"\r\n      >\r\n        <el-table-column prop=\"id\" label=\"ID\"  />\r\n        <el-table-column prop=\"name\" label=\"教师姓名\"  />\r\n        <el-table-column prop=\"gender\" label=\"性别\"  />\r\n        <el-table-column prop=\"department\" label=\"科室\"  />\r\n        <el-table-column label=\"照片\" width=\"100\">\r\n          <template #default=\"scope\">\r\n            <el-image\r\n              v-if=\"scope.row.photo\"\r\n              :src=\"`http://localhost:3000${scope.row.photo}`\"\r\n              :preview-src-list=\"[`http://localhost:3000${scope.row.photo}`]\"\r\n              fit=\"cover\"\r\n              style=\"width: 50px; height: 50px\"\r\n            />\r\n            <el-avatar v-else :size=\"50\" icon=\"UserFilled\" />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"评价数量\" width=\"100\">\r\n          <template #default=\"scope\">\r\n            {{ getCompetencyData(scope.row.id, 'total_evaluations') || 0 }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"认可数量\" width=\"100\">\r\n          <template #default=\"scope\">\r\n            {{ getCompetencyData(scope.row.id, 'approved_count') || 0 }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"认可率\" width=\"180\">\r\n          <template #default=\"scope\">\r\n            <el-progress \r\n              :percentage=\"getCompetencyData(scope.row.id, 'approval_rate') || 0\" \r\n              :format=\"percentFormat\"\r\n              :status=\"getCompetencyStatus(scope.row.id)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"认证状态\" width=\"100\">\r\n          <template #default=\"scope\">\r\n            <el-tag :type=\"isCertified(scope.row.id) ? 'success' : 'info'\">\r\n              {{ isCertified(scope.row.id) ? '已认证' : '未认证' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"180\" fixed=\"right\">\r\n          <template #default=\"scope\">\r\n            <el-button size=\"small\" @click=\"viewDetails(scope.row.id)\">详情</el-button>\r\n            <el-button \r\n              size=\"small\" \r\n              type=\"primary\"\r\n              @click=\"viewEvaluations(scope.row.id)\"\r\n            >\r\n              查看评价\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          v-model:current-page=\"currentPage\"\r\n          v-model:page-size=\"pageSize\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n        />\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, computed, onMounted } from 'vue'\r\nimport { useRouter } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'CompetencyList',\r\n  setup() {\r\n    const router = useRouter()\r\n      let token = localStorage.getItem('token')\r\n    if (token) {\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`\r\n    }\r\n    // 基础数据\r\n    const loading = ref(false)\r\n    const teacherList = ref([])\r\n    const competencyDataMap = ref({})\r\n    const total = ref(0)\r\n    const currentPage = ref(1)\r\n    const pageSize = ref(10)\r\n    \r\n    // 搜索表单\r\n    const searchForm = reactive({\r\n      name: '',\r\n      department: '',\r\n      isCertified: ''\r\n    })\r\n    \r\n    // 生命周期钩子\r\n    onMounted(() => {\r\n      fetchTeachers()\r\n    })\r\n    \r\n    // 获取教师列表\r\n    const fetchTeachers = async () => {\r\n      loading.value = true\r\n      try {\r\n        // 添加搜索参数\r\n        const params = {\r\n          page: currentPage.value,\r\n          limit: pageSize.value\r\n        }\r\n        \r\n        if (searchForm.name) params.name = searchForm.name\r\n        if (searchForm.department) params.department = searchForm.department\r\n        \r\n        const response = await axios.get('http://localhost:3000/api/teachers', { params })\r\n        teacherList.value = response.data.data\r\n        total.value = response.data.count\r\n        \r\n        // 获取每个教师的能力认定数据\r\n        await fetchAllTeachersCompetency()\r\n      } catch (error) {\r\n        console.error('获取教师列表失败:', error)\r\n        ElMessage.error('获取教师列表失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 获取所有教师的能力认定数据\r\n    const fetchAllTeachersCompetency = async () => {\r\n      try {\r\n        const promises = teacherList.value.map(teacher => \r\n          axios.get(`http://localhost:3000/api/evaluations/competency/teacher/${teacher.id}`)\r\n            .then(response => {\r\n              competencyDataMap.value[teacher.id] = response.data.data\r\n            })\r\n            .catch(error => {\r\n              console.error(`获取教师${teacher.id}能力认定数据失败:`, error)\r\n            })\r\n        )\r\n        \r\n        await Promise.all(promises)\r\n      } catch (error) {\r\n        console.error('获取教师能力认定数据失败:', error)\r\n      }\r\n    }\r\n    \r\n    // 获取教师能力认定数据\r\n    const getCompetencyData = (teacherId, field) => {\r\n      if (competencyDataMap.value[teacherId] && field in competencyDataMap.value[teacherId]) {\r\n        return competencyDataMap.value[teacherId][field]\r\n      }\r\n      return null\r\n    }\r\n    \r\n    // 获取认证状态样式\r\n    const getCompetencyStatus = (teacherId) => {\r\n      const isCert = isCertified(teacherId)\r\n      return isCert ? 'success' : ''\r\n    }\r\n    \r\n    // 是否已认证\r\n    const isCertified = (teacherId) => {\r\n      return getCompetencyData(teacherId, 'is_certified') === true\r\n    }\r\n    \r\n    // 格式化百分比\r\n    const percentFormat = (percentage) => {\r\n      return `${percentage}%`\r\n    }\r\n    \r\n    // 搜索操作\r\n    const handleSearch = () => {\r\n      currentPage.value = 1\r\n      fetchTeachers()\r\n    }\r\n    \r\n    // 重置搜索\r\n    const resetSearch = () => {\r\n      Object.keys(searchForm).forEach(key => {\r\n        searchForm[key] = ''\r\n      })\r\n      currentPage.value = 1\r\n      fetchTeachers()\r\n    }\r\n    \r\n    // 分页操作\r\n    const handleSizeChange = (val) => {\r\n      pageSize.value = val\r\n      fetchTeachers()\r\n    }\r\n    \r\n    const handleCurrentChange = (val) => {\r\n      currentPage.value = val\r\n      fetchTeachers()\r\n    }\r\n    \r\n    // 查看详情\r\n    const viewDetails = (id) => {\r\n      router.push(`/competency/detail/${id}`)\r\n    }\r\n    \r\n    // 查看教师评价\r\n    const viewEvaluations = (id) => {\r\n      router.push(`/teachers/detail/${id}`)\r\n    }\r\n    \r\n    return {\r\n      loading,\r\n      teacherList,\r\n      competencyDataMap,\r\n      searchForm,\r\n      currentPage,\r\n      pageSize,\r\n      total,\r\n      handleSearch,\r\n      resetSearch,\r\n      handleSizeChange,\r\n      handleCurrentChange,\r\n      viewDetails,\r\n      viewEvaluations,\r\n      getCompetencyData,\r\n      getCompetencyStatus,\r\n      isCertified,\r\n      percentFormat\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.competency-list-container {\r\n  padding: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.search-form {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n</style> "], "mappings": ";;;;AAyGA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAI;AACvD,SAASC,SAAQ,QAAS,YAAW;AACrC,SAASC,SAAQ,QAAS,cAAa;AACvC,OAAOC,KAAI,MAAO,OAAM;AAExB,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,KAAKA,CAAA,EAAG;IACN,MAAMC,MAAK,GAAIL,SAAS,CAAC;IACvB,IAAIM,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,OAAO;IAC1C,IAAIF,KAAK,EAAE;MACTJ,KAAK,CAACO,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,IAAI,UAAUL,KAAK,EAAC;IACnE;IACA;IACA,MAAMM,OAAM,GAAIhB,GAAG,CAAC,KAAK;IACzB,MAAMiB,WAAU,GAAIjB,GAAG,CAAC,EAAE;IAC1B,MAAMkB,iBAAgB,GAAIlB,GAAG,CAAC,CAAC,CAAC;IAChC,MAAMmB,KAAI,GAAInB,GAAG,CAAC,CAAC;IACnB,MAAMoB,WAAU,GAAIpB,GAAG,CAAC,CAAC;IACzB,MAAMqB,QAAO,GAAIrB,GAAG,CAAC,EAAE;;IAEvB;IACA,MAAMsB,UAAS,GAAIrB,QAAQ,CAAC;MAC1BM,IAAI,EAAE,EAAE;MACRgB,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE;IACf,CAAC;;IAED;IACArB,SAAS,CAAC,MAAM;MACdsB,aAAa,CAAC;IAChB,CAAC;;IAED;IACA,MAAMA,aAAY,GAAI,MAAAA,CAAA,KAAY;MAChCT,OAAO,CAACU,KAAI,GAAI,IAAG;MACnB,IAAI;QACF;QACA,MAAMC,MAAK,GAAI;UACbC,IAAI,EAAER,WAAW,CAACM,KAAK;UACvBG,KAAK,EAAER,QAAQ,CAACK;QAClB;QAEA,IAAIJ,UAAU,CAACf,IAAI,EAAEoB,MAAM,CAACpB,IAAG,GAAIe,UAAU,CAACf,IAAG;QACjD,IAAIe,UAAU,CAACC,UAAU,EAAEI,MAAM,CAACJ,UAAS,GAAID,UAAU,CAACC,UAAS;QAEnE,MAAMO,QAAO,GAAI,MAAMxB,KAAK,CAACyB,GAAG,CAAC,oCAAoC,EAAE;UAAEJ;QAAO,CAAC;QACjFV,WAAW,CAACS,KAAI,GAAII,QAAQ,CAACE,IAAI,CAACA,IAAG;QACrCb,KAAK,CAACO,KAAI,GAAII,QAAQ,CAACE,IAAI,CAACC,KAAI;;QAEhC;QACA,MAAMC,0BAA0B,CAAC;MACnC,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC9B,SAAS,CAAC8B,KAAK,CAAC,UAAU;MAC5B,UAAU;QACRnB,OAAO,CAACU,KAAI,GAAI,KAAI;MACtB;IACF;;IAEA;IACA,MAAMQ,0BAAyB,GAAI,MAAAA,CAAA,KAAY;MAC7C,IAAI;QACF,MAAMG,QAAO,GAAIpB,WAAW,CAACS,KAAK,CAACY,GAAG,CAACC,OAAM,IAC3CjC,KAAK,CAACyB,GAAG,CAAC,4DAA4DQ,OAAO,CAACC,EAAE,EAAE,EAC/EC,IAAI,CAACX,QAAO,IAAK;UAChBZ,iBAAiB,CAACQ,KAAK,CAACa,OAAO,CAACC,EAAE,IAAIV,QAAQ,CAACE,IAAI,CAACA,IAAG;QACzD,CAAC,EACAU,KAAK,CAACP,KAAI,IAAK;UACdC,OAAO,CAACD,KAAK,CAAC,OAAOI,OAAO,CAACC,EAAE,WAAW,EAAEL,KAAK;QACnD,CAAC,CACL;QAEA,MAAMQ,OAAO,CAACC,GAAG,CAACP,QAAQ;MAC5B,EAAE,OAAOF,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK;MACtC;IACF;;IAEA;IACA,MAAMU,iBAAgB,GAAIA,CAACC,SAAS,EAAEC,KAAK,KAAK;MAC9C,IAAI7B,iBAAiB,CAACQ,KAAK,CAACoB,SAAS,KAAKC,KAAI,IAAK7B,iBAAiB,CAACQ,KAAK,CAACoB,SAAS,CAAC,EAAE;QACrF,OAAO5B,iBAAiB,CAACQ,KAAK,CAACoB,SAAS,CAAC,CAACC,KAAK;MACjD;MACA,OAAO,IAAG;IACZ;;IAEA;IACA,MAAMC,mBAAkB,GAAKF,SAAS,IAAK;MACzC,MAAMG,MAAK,GAAIzB,WAAW,CAACsB,SAAS;MACpC,OAAOG,MAAK,GAAI,SAAQ,GAAI,EAAC;IAC/B;;IAEA;IACA,MAAMzB,WAAU,GAAKsB,SAAS,IAAK;MACjC,OAAOD,iBAAiB,CAACC,SAAS,EAAE,cAAc,MAAM,IAAG;IAC7D;;IAEA;IACA,MAAMI,aAAY,GAAKC,UAAU,IAAK;MACpC,OAAO,GAAGA,UAAU,GAAE;IACxB;;IAEA;IACA,MAAMC,YAAW,GAAIA,CAAA,KAAM;MACzBhC,WAAW,CAACM,KAAI,GAAI;MACpBD,aAAa,CAAC;IAChB;;IAEA;IACA,MAAM4B,WAAU,GAAIA,CAAA,KAAM;MACxBC,MAAM,CAACC,IAAI,CAACjC,UAAU,CAAC,CAACkC,OAAO,CAACC,GAAE,IAAK;QACrCnC,UAAU,CAACmC,GAAG,IAAI,EAAC;MACrB,CAAC;MACDrC,WAAW,CAACM,KAAI,GAAI;MACpBD,aAAa,CAAC;IAChB;;IAEA;IACA,MAAMiC,gBAAe,GAAKC,GAAG,IAAK;MAChCtC,QAAQ,CAACK,KAAI,GAAIiC,GAAE;MACnBlC,aAAa,CAAC;IAChB;IAEA,MAAMmC,mBAAkB,GAAKD,GAAG,IAAK;MACnCvC,WAAW,CAACM,KAAI,GAAIiC,GAAE;MACtBlC,aAAa,CAAC;IAChB;;IAEA;IACA,MAAMoC,WAAU,GAAKrB,EAAE,IAAK;MAC1B/B,MAAM,CAACqD,IAAI,CAAC,sBAAsBtB,EAAE,EAAE;IACxC;;IAEA;IACA,MAAMuB,eAAc,GAAKvB,EAAE,IAAK;MAC9B/B,MAAM,CAACqD,IAAI,CAAC,oBAAoBtB,EAAE,EAAE;IACtC;IAEA,OAAO;MACLxB,OAAO;MACPC,WAAW;MACXC,iBAAiB;MACjBI,UAAU;MACVF,WAAW;MACXC,QAAQ;MACRF,KAAK;MACLiC,YAAY;MACZC,WAAW;MACXK,gBAAgB;MAChBE,mBAAmB;MACnBC,WAAW;MACXE,eAAe;MACflB,iBAAiB;MACjBG,mBAAmB;MACnBxB,WAAW;MACX0B;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}