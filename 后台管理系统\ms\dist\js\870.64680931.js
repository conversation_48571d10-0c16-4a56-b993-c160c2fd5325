"use strict";(self["webpackChunkms"]=self["webpackChunkms"]||[]).push([[870],{6870:function(e,t,a){a.r(t),a.d(t,{default:function(){return N}});var l=a(6768),o=a(4232);const s={class:"exam-questions-container"},n={class:"card-header"},i={key:0,class:"exam-info"},r={class:"question-list"},u={key:1},c={class:"question-stats"},d={class:"stat-item"},p={class:"stat-value"},m={class:"stat-item"},_={class:"stat-value"},k={class:"stat-item"},f={class:"stat-value"},b={class:"stat-item"},g={class:"stat-value"},v={class:"stat-item"},y={class:"stat-value"},h={class:"option-content"},x={class:"add-option"},F={class:"dialog-footer"},C={key:0,class:"question-detail"},w={class:"question-header"},V={class:"question-score"},D={class:"question-content"},L={key:0,class:"options-list"},Q={class:"option-label"},W={class:"option-content"},E={key:0,class:"correct-mark"},q={key:1,class:"true-false-answer"},T={class:"answer-value"},X={key:2,class:"question-explanation"},U={class:"explanation-content"},S={class:"el-upload__tip"};function K(e,t,a,K,R,I){const $=(0,l.g2)("el-button"),A=(0,l.g2)("el-descriptions-item"),O=(0,l.g2)("el-tag"),z=(0,l.g2)("el-descriptions"),B=(0,l.g2)("el-empty"),j=(0,l.g2)("el-table-column"),M=(0,l.g2)("el-table"),N=(0,l.g2)("el-card"),Y=(0,l.g2)("el-option"),G=(0,l.g2)("el-select"),H=(0,l.g2)("el-form-item"),J=(0,l.g2)("el-input"),P=(0,l.g2)("el-input-number"),Z=(0,l.g2)("el-divider"),ee=(0,l.g2)("el-checkbox"),te=(0,l.g2)("el-radio"),ae=(0,l.g2)("el-radio-group"),le=(0,l.g2)("el-form"),oe=(0,l.g2)("el-dialog"),se=(0,l.g2)("Check"),ne=(0,l.g2)("el-icon"),ie=(0,l.g2)("upload-filled"),re=(0,l.g2)("el-upload"),ue=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",s,[(0,l.bF)(N,{class:"box-card"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",n,[t[13]||(t[13]=(0,l.Lk)("span",{class:"title"},"考试题目管理",-1)),(0,l.Lk)("div",null,[(0,l.bF)($,{onClick:K.goBack},{default:(0,l.k6)(()=>t[10]||(t[10]=[(0,l.eW)("返回考试列表")])),_:1,__:[10]},8,["onClick"]),(0,l.bF)($,{type:"primary",onClick:K.openAddDialog},{default:(0,l.k6)(()=>t[11]||(t[11]=[(0,l.eW)("添加题目")])),_:1,__:[11]},8,["onClick"]),(0,l.bF)($,{type:"success",onClick:K.importQuestions},{default:(0,l.k6)(()=>t[12]||(t[12]=[(0,l.eW)("导入题库")])),_:1,__:[12]},8,["onClick"])])])]),default:(0,l.k6)(()=>[K.examData?((0,l.uX)(),(0,l.CE)("div",i,[(0,l.bF)(z,{title:"考试信息",column:3,border:""},{default:(0,l.k6)(()=>[(0,l.bF)(A,{label:"考试名称"},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(K.examData.title),1)]),_:1}),(0,l.bF)(A,{label:"考试时长"},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(K.examData.duration)+"分钟",1)]),_:1}),(0,l.bF)(A,{label:"总分"},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(K.examData.total_score)+"分",1)]),_:1}),(0,l.bF)(A,{label:"及格分数"},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(K.examData.pass_score)+"分",1)]),_:1}),(0,l.bF)(A,{label:"考试状态"},{default:(0,l.k6)(()=>[(0,l.bF)(O,{type:K.getExamStatusType(K.examData.status)},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(K.getExamStatusText(K.examData.status)),1)]),_:1},8,["type"])]),_:1}),(0,l.bF)(A,{label:"创建时间"},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(K.formatDate(K.examData.created_at)),1)]),_:1})]),_:1})])):(0,l.Q3)("",!0),(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",r,[0===K.questions.length?((0,l.uX)(),(0,l.Wv)(B,{key:0,description:"暂无考试题目，请添加"})):((0,l.uX)(),(0,l.CE)("div",u,[(0,l.Lk)("div",c,[(0,l.Lk)("div",d,[(0,l.Lk)("div",p,(0,o.v_)(K.questions.length),1),t[14]||(t[14]=(0,l.Lk)("div",{class:"stat-label"},"总题数",-1))]),(0,l.Lk)("div",m,[(0,l.Lk)("div",_,(0,o.v_)(K.singleChoiceCount),1),t[15]||(t[15]=(0,l.Lk)("div",{class:"stat-label"},"单选题",-1))]),(0,l.Lk)("div",k,[(0,l.Lk)("div",f,(0,o.v_)(K.multipleChoiceCount),1),t[16]||(t[16]=(0,l.Lk)("div",{class:"stat-label"},"多选题",-1))]),(0,l.Lk)("div",b,[(0,l.Lk)("div",g,(0,o.v_)(K.trueFalseCount),1),t[17]||(t[17]=(0,l.Lk)("div",{class:"stat-label"},"判断题",-1))]),(0,l.Lk)("div",v,[(0,l.Lk)("div",y,(0,o.v_)(K.totalScore),1),t[18]||(t[18]=(0,l.Lk)("div",{class:"stat-label"},"总分",-1))])]),(0,l.bF)(M,{data:K.questions,border:"",style:{width:"100%"}},{default:(0,l.k6)(()=>[(0,l.bF)(j,{type:"index",width:"50",label:"#"}),(0,l.bF)(j,{label:"题型",width:"100"},{default:(0,l.k6)(e=>[(0,l.bF)(O,{type:K.getQuestionTypeTag(e.row.type)},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(K.getQuestionTypeText(e.row.type)),1)]),_:2},1032,["type"])]),_:1}),(0,l.bF)(j,{prop:"content",label:"题目内容","show-overflow-tooltip":""}),(0,l.bF)(j,{prop:"score",label:"分值",width:"80"}),(0,l.bF)(j,{label:"操作",width:"180",fixed:"right"},{default:(0,l.k6)(e=>[(0,l.bF)($,{size:"small",onClick:t=>K.viewQuestion(e.row)},{default:(0,l.k6)(()=>t[19]||(t[19]=[(0,l.eW)("查看")])),_:2,__:[19]},1032,["onClick"]),(0,l.bF)($,{size:"small",type:"primary",onClick:t=>K.editQuestion(e.row)},{default:(0,l.k6)(()=>t[20]||(t[20]=[(0,l.eW)("编辑")])),_:2,__:[20]},1032,["onClick"]),(0,l.bF)($,{size:"small",type:"danger",onClick:t=>K.deleteQuestion(e.row)},{default:(0,l.k6)(()=>t[21]||(t[21]=[(0,l.eW)("删除")])),_:2,__:[21]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]))])),[[ue,K.loading]])]),_:1}),(0,l.bF)(oe,{modelValue:K.dialogVisible,"onUpdate:modelValue":t[7]||(t[7]=e=>K.dialogVisible=e),title:K.isEdit?"编辑题目":"添加题目",width:"700px"},{footer:(0,l.k6)(()=>[(0,l.Lk)("span",F,[(0,l.bF)($,{onClick:t[6]||(t[6]=e=>K.dialogVisible=!1)},{default:(0,l.k6)(()=>t[27]||(t[27]=[(0,l.eW)("取消")])),_:1,__:[27]}),(0,l.bF)($,{type:"primary",onClick:K.submitForm},{default:(0,l.k6)(()=>t[28]||(t[28]=[(0,l.eW)("确认")])),_:1,__:[28]},8,["onClick"])])]),default:(0,l.k6)(()=>[(0,l.bF)(le,{ref:"questionFormRef",model:K.formData,rules:K.formRules,"label-width":"100px","label-position":"right"},{default:(0,l.k6)(()=>[(0,l.bF)(H,{label:"题目类型",prop:"type"},{default:(0,l.k6)(()=>[(0,l.bF)(G,{modelValue:K.formData.type,"onUpdate:modelValue":t[0]||(t[0]=e=>K.formData.type=e),placeholder:"请选择题目类型"},{default:(0,l.k6)(()=>[(0,l.bF)(Y,{label:"单选题",value:"single"}),(0,l.bF)(Y,{label:"多选题",value:"multiple"}),(0,l.bF)(Y,{label:"判断题",value:"true_false"})]),_:1},8,["modelValue"])]),_:1}),(0,l.bF)(H,{label:"题目内容",prop:"content"},{default:(0,l.k6)(()=>[(0,l.bF)(J,{modelValue:K.formData.content,"onUpdate:modelValue":t[1]||(t[1]=e=>K.formData.content=e),type:"textarea",rows:3,placeholder:"请输入题目内容"},null,8,["modelValue"])]),_:1}),(0,l.bF)(H,{label:"分值",prop:"score"},{default:(0,l.k6)(()=>[(0,l.bF)(P,{modelValue:K.formData.score,"onUpdate:modelValue":t[2]||(t[2]=e=>K.formData.score=e),min:1,max:100},null,8,["modelValue"])]),_:1}),"single"===K.formData.type||"multiple"===K.formData.type?((0,l.uX)(),(0,l.CE)(l.FK,{key:0},[(0,l.bF)(Z,{"content-position":"left"},{default:(0,l.k6)(()=>t[22]||(t[22]=[(0,l.eW)("选项")])),_:1,__:[22]}),((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(K.formData.options,(e,a)=>((0,l.uX)(),(0,l.CE)("div",{key:a,class:"option-item"},[(0,l.bF)(H,{label:`选项 ${String.fromCharCode(65+a)}`,prop:`options.${a}.content`,rules:{required:!0,message:"请输入选项内容",trigger:"blur"}},{default:(0,l.k6)(()=>[(0,l.Lk)("div",h,[(0,l.bF)(J,{modelValue:e.content,"onUpdate:modelValue":t=>e.content=t,placeholder:"请输入选项内容"},null,8,["modelValue","onUpdate:modelValue"]),"multiple"===K.formData.type?((0,l.uX)(),(0,l.Wv)(ee,{key:0,modelValue:e.is_correct,"onUpdate:modelValue":t=>e.is_correct=t,label:"正确答案"},null,8,["modelValue","onUpdate:modelValue"])):((0,l.uX)(),(0,l.Wv)(te,{key:1,modelValue:K.formData.correct_option,"onUpdate:modelValue":t[3]||(t[3]=e=>K.formData.correct_option=e),label:a,class:"option-radio"},{default:(0,l.k6)(()=>t[23]||(t[23]=[(0,l.eW)("正确答案")])),_:2,__:[23]},1032,["modelValue","label"])),K.formData.options.length>2?((0,l.uX)(),(0,l.Wv)($,{key:2,type:"danger",icon:"Delete",circle:"",onClick:e=>K.removeOption(a)},null,8,["onClick"])):(0,l.Q3)("",!0)])]),_:2},1032,["label","prop"])]))),128)),(0,l.Lk)("div",x,[(0,l.bF)($,{type:"primary",plain:"",onClick:K.addOption,disabled:K.formData.options.length>=6},{default:(0,l.k6)(()=>t[24]||(t[24]=[(0,l.eW)(" 添加选项 ")])),_:1,__:[24]},8,["onClick","disabled"])])],64)):(0,l.Q3)("",!0),"true_false"===K.formData.type?((0,l.uX)(),(0,l.Wv)(H,{key:1,label:"正确答案",prop:"true_false_answer"},{default:(0,l.k6)(()=>[(0,l.bF)(ae,{modelValue:K.formData.true_false_answer,"onUpdate:modelValue":t[4]||(t[4]=e=>K.formData.true_false_answer=e)},{default:(0,l.k6)(()=>[(0,l.bF)(te,{label:!0},{default:(0,l.k6)(()=>t[25]||(t[25]=[(0,l.eW)("正确")])),_:1,__:[25]}),(0,l.bF)(te,{label:!1},{default:(0,l.k6)(()=>t[26]||(t[26]=[(0,l.eW)("错误")])),_:1,__:[26]})]),_:1},8,["modelValue"])]),_:1})):(0,l.Q3)("",!0),(0,l.bF)(H,{label:"解析",prop:"explanation"},{default:(0,l.k6)(()=>[(0,l.bF)(J,{modelValue:K.formData.explanation,"onUpdate:modelValue":t[5]||(t[5]=e=>K.formData.explanation=e),type:"textarea",rows:2,placeholder:"请输入题目解析（可选）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),(0,l.bF)(oe,{modelValue:K.viewDialogVisible,"onUpdate:modelValue":t[8]||(t[8]=e=>K.viewDialogVisible=e),title:"题目详情",width:"700px"},{default:(0,l.k6)(()=>[K.currentQuestion?((0,l.uX)(),(0,l.CE)("div",C,[(0,l.Lk)("div",w,[(0,l.bF)(O,{type:K.getQuestionTypeTag(K.currentQuestion.type),size:"large"},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(K.getQuestionTypeText(K.currentQuestion.type)),1)]),_:1},8,["type"]),(0,l.Lk)("span",V,(0,o.v_)(K.currentQuestion.score)+"分",1)]),(0,l.Lk)("div",D,(0,o.v_)(K.currentQuestion.content),1),"true_false"!==K.currentQuestion.type?((0,l.uX)(),(0,l.CE)("div",L,[((0,l.uX)(!0),(0,l.CE)(l.FK,null,(0,l.pI)(K.currentQuestion.options,(e,t)=>((0,l.uX)(),(0,l.CE)("div",{key:t,class:(0,o.C4)(["option-item",{"correct-option":e.is_correct}])},[(0,l.Lk)("div",Q,(0,o.v_)(String.fromCharCode(65+t)),1),(0,l.Lk)("div",W,(0,o.v_)(e.content),1),e.is_correct?((0,l.uX)(),(0,l.CE)("div",E,[(0,l.bF)(ne,null,{default:(0,l.k6)(()=>[(0,l.bF)(se)]),_:1})])):(0,l.Q3)("",!0)],2))),128))])):((0,l.uX)(),(0,l.CE)("div",q,[t[29]||(t[29]=(0,l.Lk)("div",{class:"answer-label"},"正确答案：",-1)),(0,l.Lk)("div",T,[(0,l.bF)(O,{type:K.currentQuestion.true_false_answer?"success":"danger"},{default:(0,l.k6)(()=>[(0,l.eW)((0,o.v_)(K.currentQuestion.true_false_answer?"正确":"错误"),1)]),_:1},8,["type"])])])),K.currentQuestion.explanation?((0,l.uX)(),(0,l.CE)("div",X,[t[30]||(t[30]=(0,l.Lk)("div",{class:"explanation-label"},"解析：",-1)),(0,l.Lk)("div",U,(0,o.v_)(K.currentQuestion.explanation),1)])):(0,l.Q3)("",!0)])):(0,l.Q3)("",!0)]),_:1},8,["modelValue"]),(0,l.bF)(oe,{modelValue:K.importDialogVisible,"onUpdate:modelValue":t[9]||(t[9]=e=>K.importDialogVisible=e),title:"导入题库",width:"500px"},{default:(0,l.k6)(()=>[(0,l.bF)(re,{class:"upload-demo",drag:"",action:"http://localhost:3000/api/exams/questions/import",headers:{"Content-Type":"multipart/form-data"},data:{exam_id:K.examId},"on-success":K.handleImportSuccess,"on-error":K.handleImportError,"before-upload":K.beforeImportUpload,accept:".xlsx,.xls"},{tip:(0,l.k6)(()=>[(0,l.Lk)("div",S,[t[32]||(t[32]=(0,l.eW)(" 请上传Excel格式的题库文件，")),(0,l.bF)($,{type:"primary",link:"",onClick:K.downloadTemplate},{default:(0,l.k6)(()=>t[31]||(t[31]=[(0,l.eW)("下载模板")])),_:1,__:[31]},8,["onClick"])])]),default:(0,l.k6)(()=>[(0,l.bF)(ne,{class:"el-icon--upload"},{default:(0,l.k6)(()=>[(0,l.bF)(ie)]),_:1}),t[33]||(t[33]=(0,l.Lk)("div",{class:"el-upload__text"},[(0,l.eW)(" 将Excel文件拖到此处，或"),(0,l.Lk)("em",null,"点击上传")],-1))]),_:1,__:[33]},8,["data","on-success","on-error","before-upload"])]),_:1},8,["modelValue"])])}a(4114),a(8111),a(2489),a(5207),a(8237);var R=a(144),I=a(1387),$=a(1219),A=a(2933),O=a(7477),z=a(4373),B={name:"ExamQuestions",components:{Check:O.Check,UploadFilled:O.UploadFilled},setup(){const e=(0,I.lq)(),t=(0,I.rd)(),a=e.params.id,o=(0,R.KR)(!1),s=(0,R.KR)(null),n=(0,R.KR)([]),i=(0,R.KR)(null),r=(0,R.KR)(!1),u=(0,R.KR)(!1),c=(0,R.KR)(!1),d=(0,R.KR)(!1),p=(0,R.KR)(null),m=(0,R.Kh)({id:"",type:"single",content:"",score:5,options:[{content:"",is_correct:!1},{content:"",is_correct:!1},{content:"",is_correct:!1},{content:"",is_correct:!1}],correct_option:0,true_false_answer:!0,explanation:"",exam_id:a}),_={type:[{required:!0,message:"请选择题目类型",trigger:"change"}],content:[{required:!0,message:"请输入题目内容",trigger:"blur"}],score:[{required:!0,message:"请输入分值",trigger:"change"}]},k=(0,l.EW)(()=>n.value.filter(e=>"single"===e.type).length),f=(0,l.EW)(()=>n.value.filter(e=>"multiple"===e.type).length),b=(0,l.EW)(()=>n.value.filter(e=>"true_false"===e.type).length),g=(0,l.EW)(()=>n.value.reduce((e,t)=>e+t.score,0));(0,l.sV)(()=>{v(),y()});const v=async()=>{try{const e=await z.A.get(`http://localhost:3000/api/exams/${a}`);s.value=e.data.data}catch(e){console.error("获取考试信息失败:",e),$.nk.error("获取考试信息失败")}},y=async()=>{o.value=!0;try{const e=await z.A.get(`http://localhost:3000/api/exams/${a}/questions`);n.value=e.data.data}catch(e){console.error("获取题目列表失败:",e),$.nk.error("获取题目列表失败")}finally{o.value=!1}},h=e=>{const t={draft:"草稿",published:"已发布",in_progress:"进行中",completed:"已结束"};return t[e]||"未知状态"},x=e=>{const t={draft:"info",published:"success",in_progress:"warning",completed:"danger"};return t[e]||"info"},F=e=>{const t={single:"单选题",multiple:"多选题",true_false:"判断题"};return t[e]||"未知类型"},C=e=>{const t={single:"primary",multiple:"success",true_false:"warning"};return t[e]||"info"},w=e=>{if(!e)return"-";const t=new Date(e);return`${t.getFullYear()}-${(t.getMonth()+1).toString().padStart(2,"0")}-${t.getDate().toString().padStart(2,"0")}`},V=()=>{t.push("/exams/list")},D=()=>{d.value=!1,T(),r.value=!0},L=e=>{d.value=!0,p.value=e,Object.assign(m,{id:e.id,type:e.type,content:e.content,score:e.score,explanation:e.explanation||"",exam_id:a}),"true_false"===e.type?m.true_false_answer=e.true_false_answer:(m.options=[...e.options],"single"===e.type&&(m.correct_option=e.options.findIndex(e=>e.is_correct))),r.value=!0},Q=e=>{p.value=e,u.value=!0},W=e=>{A.s.confirm("确定要删除该题目吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await z.A.delete(`http://localhost:3000/api/exams/questions/${e.id}`),$.nk.success("删除成功"),y()}catch(t){console.error("删除失败:",t),$.nk.error("删除失败")}}).catch(()=>{$.nk.info("已取消删除")})},E=()=>{m.options.length<6&&m.options.push({content:"",is_correct:!1})},q=e=>{m.options.length>2&&(m.options.splice(e,1),"single"===m.type&&m.correct_option===e?m.correct_option=0:"single"===m.type&&m.correct_option>e&&m.correct_option--)},T=()=>{Object.assign(m,{id:"",type:"single",content:"",score:5,options:[{content:"",is_correct:!1},{content:"",is_correct:!1},{content:"",is_correct:!1},{content:"",is_correct:!1}],correct_option:0,true_false_answer:!0,explanation:"",exam_id:a}),i.value&&i.value.resetFields()},X=async()=>{i.value&&await i.value.validate(async e=>{if(!e)return!1;"single"===m.type&&m.options.forEach((e,t)=>{e.is_correct=t===m.correct_option});try{d.value?(await z.A.put(`http://localhost:3000/api/exams/questions/${m.id}`,m),$.nk.success("题目更新成功")):(await z.A.post("http://localhost:3000/api/exams/questions",m),$.nk.success("题目添加成功")),r.value=!1,y()}catch(t){console.error("操作失败:",t),$.nk.error("操作失败")}})},U=()=>{c.value=!0},S=()=>{window.open("http://localhost:3000/api/exams/questions/template","_blank")},K=e=>{const t="application/vnd.ms-excel"===e.type||"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"===e.type,a=e.size/1024/1024<2;return t||$.nk.error("只能上传Excel文件!"),a||$.nk.error("文件大小不能超过2MB!"),t&&a},O=e=>{$.nk.success(`成功导入${e.data.count}道题目`),c.value=!1,y()},B=e=>{console.error("导入失败:",e),$.nk.error("导入失败，请检查文件格式是否正确")};return{loading:o,examData:s,questions:n,dialogVisible:r,viewDialogVisible:u,importDialogVisible:c,isEdit:d,formData:m,formRules:_,questionFormRef:i,currentQuestion:p,examId:a,singleChoiceCount:k,multipleChoiceCount:f,trueFalseCount:b,totalScore:g,getExamStatusText:h,getExamStatusType:x,getQuestionTypeText:F,getQuestionTypeTag:C,formatDate:w,goBack:V,openAddDialog:D,editQuestion:L,viewQuestion:Q,deleteQuestion:W,addOption:E,removeOption:q,submitForm:X,importQuestions:U,downloadTemplate:S,beforeImportUpload:K,handleImportSuccess:O,handleImportError:B}}},j=a(1241);const M=(0,j.A)(B,[["render",K],["__scopeId","data-v-9ada6f44"]]);var N=M}}]);
//# sourceMappingURL=870.64680931.js.map