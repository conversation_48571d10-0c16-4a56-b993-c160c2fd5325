const User = require('../models/userModel');
const bcrypt = require('bcryptjs');
const { pool } = require('../config/db');

// 获取所有用户
exports.getAllUsers = async (req, res) => {
  try {
    const { page = 1, limit = 10, username, phone, status } = req.query;
    const offset = (page - 1) * limit;
    
    // 构建查询条件
    let conditions = [];
    let params = [];
    
    if (username) {
      conditions.push('username LIKE ?');
      params.push(`%${username}%`);
    }
    
    if (phone) {
      conditions.push('phone LIKE ?');
      params.push(`%${phone}%`);
    }
    
    if (status !== undefined && status !== '') {
      conditions.push('status = ?');
      params.push(parseInt(status));
    }
    
    // 构建 WHERE 子句
    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';
    
    // 获取满足条件的总数量
    const [countResult] = await pool.query(
      `SELECT COUNT(*) as total FROM users ${whereClause}`,
      params
    );
    const total = countResult[0].total;
    
    // 分页查询用户数据
    const [users] = await pool.query(
      `SELECT id, username, role, name, email, phone, teacher_id, status, last_login, created_at 
       FROM users ${whereClause}
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`,
      [...params, parseInt(limit), parseInt(offset)]
    );
    
    res.status(200).json({
      success: true,
      count: total,
      data: users,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取用户列表失败',
      error: error.message
    });
  }
};

// 获取单个用户
exports.getUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户'
      });
    }
    
    res.status(200).json({
      success: true,
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取用户信息失败',
      error: error.message
    });
  }
};

// 创建用户
exports.createUser = async (req, res) => {
  try {
    const { username, password, role, name, email, phone, teacher_id, status } = req.body;
    
    // 验证必要字段
    if (!username || !password || !name) {
      return res.status(400).json({
        success: false,
        message: '请提供用户名、密码和姓名'
      });
    }
    
    // 验证角色是否有效
    const validRoles = ['admin', 'teacher'];
    if (role && !validRoles.includes(role)) {
      return res.status(400).json({
        success: false,
        message: '无效的角色，角色必须是 admin 或 teacher'
      });
    }
    
    // 检查用户名是否已存在
    const existingUser = await User.findByUsername(username);
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: '该用户名已被使用'
      });
    }
    
    // 创建用户
    const userId = await User.create({
      username,
      password,
      role: role || 'teacher',
      name,
      email,
      phone,
      teacher_id,
      status: status !== undefined ? status : 1
    });
    
    // 获取创建的用户信息
    const user = await User.findById(userId);
    
    res.status(201).json({
      success: true,
      message: '用户创建成功',
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建用户失败',
      error: error.message
    });
  }
};

// 更新用户
exports.updateUser = async (req, res) => {
  try {
    const { role, name, email, phone, teacher_id, password, status } = req.body;
    
    // 获取当前用户信息，确保我们有完整的数据
    const currentUser = await User.findById(req.params.id);
    if (!currentUser) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户'
      });
    }
    
    // 验证角色是否有效
    const validRoles = ['admin', 'teacher'];
    // 使用当前角色作为默认值，防止role为null
    const updatedRole = role || currentUser.role;
    
    if (updatedRole && !validRoles.includes(updatedRole)) {
      return res.status(400).json({
        success: false,
        message: '无效的角色，角色必须是 admin 或 teacher'
      });
    }
    
    // 更新用户
    const success = await User.update(req.params.id, {
      role: updatedRole,
      name: name || currentUser.name,
      email: email || currentUser.email,
      phone: phone || currentUser.phone,
      teacher_id: teacher_id !== undefined ? teacher_id : currentUser.teacher_id,
      status: status !== undefined ? parseInt(status) : currentUser.status,
      password // 密码可以为空，表示不更新密码
    });
    
    if (!success) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户或更新失败'
      });
    }
    
    // 获取更新后的用户信息
    const user = await User.findById(req.params.id);
    
    res.status(200).json({
      success: true,
      message: '用户信息更新成功',
      data: user
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新用户失败',
      error: error.message
    });
  }
};

// 删除用户
exports.deleteUser = async (req, res) => {
  try {
    const success = await User.delete(req.params.id);
    
    if (!success) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户或删除失败'
      });
    }
    
    res.status(200).json({
      success: true,
      message: '用户删除成功'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除用户失败',
      error: error.message
    });
  }
};

// 批量删除用户
exports.batchDeleteUsers = async (req, res) => {
  try {
    const { ids } = req.body;
    
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: '请提供要删除的用户ID数组'
      });
    }
    
    let successCount = 0;
    let failCount = 0;
    
    // 逐个删除用户
    for (const id of ids) {
      try {
        const success = await User.delete(id);
        if (success) {
          successCount++;
        } else {
          failCount++;
        }
      } catch (error) {
        failCount++;
        console.error(`删除用户 ID ${id} 失败:`, error);
      }
    }
    
    res.status(200).json({
      success: true,
      message: `成功删除 ${successCount} 个用户，失败 ${failCount} 个`
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '批量删除用户失败',
      error: error.message
    });
  }
}; 

// 更新用户状态
exports.updateUserStatus = async (req, res) => {
  try {
    const userId = req.params.id;
    const { status } = req.body;
    
    // 确保状态是数字类型
    const statusNum = parseInt(status, 10);
    
    // 验证状态值
    if (statusNum !== 0 && statusNum !== 1) {
      return res.status(400).json({
        success: false,
        message: '无效的状态值，状态必须是 0（禁用）或 1（启用）'
      });
    }
    
    // 获取当前用户信息
    const currentUser = await User.findById(userId);
    if (!currentUser) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户'
      });
    }
    
    // 只更新状态字段
    const success = await User.update(userId, {
      role: currentUser.role,
      name: currentUser.name,
      email: currentUser.email,
      phone: currentUser.phone,
      teacher_id: currentUser.teacher_id,
      status: statusNum
    });
    
    if (!success) {
      return res.status(404).json({
        success: false,
        message: '未找到该用户或更新失败'
      });
    }
    
    res.status(200).json({
      success: true,
      message: statusNum === 1 ? '用户已启用' : '用户已禁用',
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新用户状态失败',
      error: error.message
    });
  }
}; 