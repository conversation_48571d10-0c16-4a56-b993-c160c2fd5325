{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createBlock as _createBlock, resolveDirective as _resolveDirective, withDirectives as _withDirectives, renderList as _renderList, Fragment as _Fragment, normalizeClass as _normalizeClass } from \"vue\";\nconst _hoisted_1 = {\n  class: \"exam-questions-container\"\n};\nconst _hoisted_2 = {\n  class: \"card-header\"\n};\nconst _hoisted_3 = {\n  key: 0,\n  class: \"exam-info\"\n};\nconst _hoisted_4 = {\n  class: \"question-list\"\n};\nconst _hoisted_5 = {\n  key: 1\n};\nconst _hoisted_6 = {\n  class: \"question-stats\"\n};\nconst _hoisted_7 = {\n  class: \"stat-item\"\n};\nconst _hoisted_8 = {\n  class: \"stat-value\"\n};\nconst _hoisted_9 = {\n  class: \"stat-item\"\n};\nconst _hoisted_10 = {\n  class: \"stat-value\"\n};\nconst _hoisted_11 = {\n  class: \"stat-item\"\n};\nconst _hoisted_12 = {\n  class: \"stat-value\"\n};\nconst _hoisted_13 = {\n  class: \"stat-item\"\n};\nconst _hoisted_14 = {\n  class: \"stat-value\"\n};\nconst _hoisted_15 = {\n  class: \"stat-item\"\n};\nconst _hoisted_16 = {\n  class: \"stat-value\"\n};\nconst _hoisted_17 = {\n  class: \"option-content\"\n};\nconst _hoisted_18 = {\n  class: \"add-option\"\n};\nconst _hoisted_19 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_20 = {\n  key: 0,\n  class: \"question-detail\"\n};\nconst _hoisted_21 = {\n  class: \"question-header\"\n};\nconst _hoisted_22 = {\n  class: \"question-score\"\n};\nconst _hoisted_23 = {\n  class: \"question-content\"\n};\nconst _hoisted_24 = {\n  key: 0,\n  class: \"options-list\"\n};\nconst _hoisted_25 = {\n  class: \"option-label\"\n};\nconst _hoisted_26 = {\n  class: \"option-content\"\n};\nconst _hoisted_27 = {\n  key: 0,\n  class: \"correct-mark\"\n};\nconst _hoisted_28 = {\n  class: \"true-false-answer\"\n};\nconst _hoisted_29 = {\n  class: \"answer-value\"\n};\nconst _hoisted_30 = {\n  key: 2,\n  class: \"question-explanation\"\n};\nconst _hoisted_31 = {\n  class: \"explanation-content\"\n};\nconst _hoisted_32 = {\n  class: \"el-upload__tip\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_descriptions_item = _resolveComponent(\"el-descriptions-item\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_el_descriptions = _resolveComponent(\"el-descriptions\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_table_column = _resolveComponent(\"el-table-column\");\n  const _component_el_table = _resolveComponent(\"el-table\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_input_number = _resolveComponent(\"el-input-number\");\n  const _component_el_divider = _resolveComponent(\"el-divider\");\n  const _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  const _component_el_radio = _resolveComponent(\"el-radio\");\n  const _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _component_Check = _resolveComponent(\"Check\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_upload_filled = _resolveComponent(\"upload-filled\");\n  const _component_el_upload = _resolveComponent(\"el-upload\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, {\n    class: \"box-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_cache[13] || (_cache[13] = _createElementVNode(\"span\", {\n      class: \"title\"\n    }, \"考试题目管理\", -1 /* CACHED */)), _createElementVNode(\"div\", null, [_createVNode(_component_el_button, {\n      onClick: $setup.goBack\n    }, {\n      default: _withCtx(() => _cache[10] || (_cache[10] = [_createTextVNode(\"返回考试列表\")])),\n      _: 1 /* STABLE */,\n      __: [10]\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.openAddDialog\n    }, {\n      default: _withCtx(() => _cache[11] || (_cache[11] = [_createTextVNode(\"添加题目\")])),\n      _: 1 /* STABLE */,\n      __: [11]\n    }, 8 /* PROPS */, [\"onClick\"]), _createVNode(_component_el_button, {\n      type: \"success\",\n      onClick: $setup.importQuestions\n    }, {\n      default: _withCtx(() => _cache[12] || (_cache[12] = [_createTextVNode(\"导入题库\")])),\n      _: 1 /* STABLE */,\n      __: [12]\n    }, 8 /* PROPS */, [\"onClick\"])])])]),\n    default: _withCtx(() => [$setup.examData ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createVNode(_component_el_descriptions, {\n      title: \"考试信息\",\n      column: 3,\n      border: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n        label: \"考试名称\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.examData.title), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"考试时长\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.examData.duration) + \"分钟\", 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"总分\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.examData.total_score) + \"分\", 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"及格分数\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.examData.pass_score) + \"分\", 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"考试状态\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_tag, {\n          type: $setup.getExamStatusType($setup.examData.status)\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getExamStatusText($setup.examData.status)), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"创建时间\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDate($setup.examData.created_at)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])) : _createCommentVNode(\"v-if\", true), _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_4, [$setup.questions.length === 0 ? (_openBlock(), _createBlock(_component_el_empty, {\n      key: 0,\n      description: \"暂无考试题目，请添加\"\n    })) : (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, _toDisplayString($setup.questions.length), 1 /* TEXT */), _cache[14] || (_cache[14] = _createElementVNode(\"div\", {\n      class: \"stat-label\"\n    }, \"总题数\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, _toDisplayString($setup.singleChoiceCount), 1 /* TEXT */), _cache[15] || (_cache[15] = _createElementVNode(\"div\", {\n      class: \"stat-label\"\n    }, \"单选题\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, _toDisplayString($setup.multipleChoiceCount), 1 /* TEXT */), _cache[16] || (_cache[16] = _createElementVNode(\"div\", {\n      class: \"stat-label\"\n    }, \"多选题\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"div\", _hoisted_14, _toDisplayString($setup.trueFalseCount), 1 /* TEXT */), _cache[17] || (_cache[17] = _createElementVNode(\"div\", {\n      class: \"stat-label\"\n    }, \"判断题\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, _toDisplayString($setup.totalScore), 1 /* TEXT */), _cache[18] || (_cache[18] = _createElementVNode(\"div\", {\n      class: \"stat-label\"\n    }, \"总分\", -1 /* CACHED */))])]), _createVNode(_component_el_table, {\n      data: $setup.questions,\n      border: \"\",\n      style: {\n        \"width\": \"100%\"\n      }\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_table_column, {\n        type: \"index\",\n        width: \"50\",\n        label: \"#\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"题型\",\n        width: \"100\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_tag, {\n          type: $setup.getQuestionTypeTag(scope.row.type)\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getQuestionTypeText(scope.row.type)), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        prop: \"content\",\n        label: \"题目内容\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_el_table_column, {\n        prop: \"score\",\n        label: \"分值\",\n        width: \"80\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"操作\",\n        width: \"180\",\n        fixed: \"right\"\n      }, {\n        default: _withCtx(scope => [_createVNode(_component_el_button, {\n          size: \"small\",\n          onClick: $event => $setup.viewQuestion(scope.row)\n        }, {\n          default: _withCtx(() => _cache[19] || (_cache[19] = [_createTextVNode(\"查看\")])),\n          _: 2 /* DYNAMIC */,\n          __: [19]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n          size: \"small\",\n          type: \"primary\",\n          onClick: $event => $setup.editQuestion(scope.row)\n        }, {\n          default: _withCtx(() => _cache[20] || (_cache[20] = [_createTextVNode(\"编辑\")])),\n          _: 2 /* DYNAMIC */,\n          __: [20]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), _createVNode(_component_el_button, {\n          size: \"small\",\n          type: \"danger\",\n          onClick: $event => $setup.deleteQuestion(scope.row)\n        }, {\n          default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\"删除\")])),\n          _: 2 /* DYNAMIC */,\n          __: [21]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"data\"])]))])), [[_directive_loading, $setup.loading]])]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 添加/编辑题目对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.dialogVisible,\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.dialogVisible = $event),\n    title: $setup.isEdit ? '编辑题目' : '添加题目',\n    width: \"700px\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_19, [_createVNode(_component_el_button, {\n      onClick: _cache[6] || (_cache[6] = $event => $setup.dialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [27]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.submitForm\n    }, {\n      default: _withCtx(() => _cache[28] || (_cache[28] = [_createTextVNode(\"确认\")])),\n      _: 1 /* STABLE */,\n      __: [28]\n    }, 8 /* PROPS */, [\"onClick\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      ref: \"questionFormRef\",\n      model: $setup.formData,\n      rules: $setup.formRules,\n      \"label-width\": \"100px\",\n      \"label-position\": \"right\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"题目类型\",\n        prop: \"type\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.formData.type,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.formData.type = $event),\n          placeholder: \"请选择题目类型\"\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_option, {\n            label: \"单选题\",\n            value: \"single\"\n          }), _createVNode(_component_el_option, {\n            label: \"多选题\",\n            value: \"multiple\"\n          }), _createVNode(_component_el_option, {\n            label: \"判断题\",\n            value: \"true_false\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"题目内容\",\n        prop: \"content\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.formData.content,\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.formData.content = $event),\n          type: \"textarea\",\n          rows: 3,\n          placeholder: \"请输入题目内容\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"分值\",\n        prop: \"score\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input_number, {\n          modelValue: $setup.formData.score,\n          \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.formData.score = $event),\n          min: 1,\n          max: 100\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createCommentVNode(\" 选项 (单选题和多选题) \"), $setup.formData.type === 'single' || $setup.formData.type === 'multiple' ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 0\n      }, [_createVNode(_component_el_divider, {\n        \"content-position\": \"left\"\n      }, {\n        default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\"选项\")])),\n        _: 1 /* STABLE */,\n        __: [22]\n      }), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.formData.options, (option, index) => {\n        return _openBlock(), _createElementBlock(\"div\", {\n          key: index,\n          class: \"option-item\"\n        }, [_createVNode(_component_el_form_item, {\n          label: `选项 ${String.fromCharCode(65 + index)}`,\n          prop: `options.${index}.content`,\n          rules: {\n            required: true,\n            message: '请输入选项内容',\n            trigger: 'blur'\n          }\n        }, {\n          default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_17, [_createVNode(_component_el_input, {\n            modelValue: option.content,\n            \"onUpdate:modelValue\": $event => option.content = $event,\n            placeholder: \"请输入选项内容\"\n          }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"]), $setup.formData.type === 'multiple' ? (_openBlock(), _createBlock(_component_el_checkbox, {\n            key: 0,\n            modelValue: option.is_correct,\n            \"onUpdate:modelValue\": $event => option.is_correct = $event,\n            label: \"正确答案\"\n          }, null, 8 /* PROPS */, [\"modelValue\", \"onUpdate:modelValue\"])) : (_openBlock(), _createBlock(_component_el_radio, {\n            key: 1,\n            modelValue: $setup.formData.correct_option,\n            \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.formData.correct_option = $event),\n            label: index,\n            class: \"option-radio\"\n          }, {\n            default: _withCtx(() => [...(_cache[23] || (_cache[23] = [_createTextVNode(\"正确答案\")]))]),\n            _: 2 /* DYNAMIC */,\n            __: [23]\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"modelValue\", \"label\"])), $setup.formData.options.length > 2 ? (_openBlock(), _createBlock(_component_el_button, {\n            key: 2,\n            type: \"danger\",\n            icon: \"Delete\",\n            circle: \"\",\n            onClick: $event => $setup.removeOption(index)\n          }, null, 8 /* PROPS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\", \"prop\"])]);\n      }), 128 /* KEYED_FRAGMENT */)), _createElementVNode(\"div\", _hoisted_18, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        plain: \"\",\n        onClick: $setup.addOption,\n        disabled: $setup.formData.options.length >= 6\n      }, {\n        default: _withCtx(() => _cache[24] || (_cache[24] = [_createTextVNode(\" 添加选项 \")])),\n        _: 1 /* STABLE */,\n        __: [24]\n      }, 8 /* PROPS */, [\"onClick\", \"disabled\"])])], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 判断题答案 \"), $setup.formData.type === 'true_false' ? (_openBlock(), _createBlock(_component_el_form_item, {\n        key: 1,\n        label: \"正确答案\",\n        prop: \"true_false_answer\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_radio_group, {\n          modelValue: $setup.formData.true_false_answer,\n          \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.formData.true_false_answer = $event)\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_radio, {\n            label: true\n          }, {\n            default: _withCtx(() => _cache[25] || (_cache[25] = [_createTextVNode(\"正确\")])),\n            _: 1 /* STABLE */,\n            __: [25]\n          }), _createVNode(_component_el_radio, {\n            label: false\n          }, {\n            default: _withCtx(() => _cache[26] || (_cache[26] = [_createTextVNode(\"错误\")])),\n            _: 1 /* STABLE */,\n            __: [26]\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_form_item, {\n        label: \"解析\",\n        prop: \"explanation\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.formData.explanation,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.formData.explanation = $event),\n          type: \"textarea\",\n          rows: 2,\n          placeholder: \"请输入题目解析（可选）\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\", \"rules\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"title\"]), _createCommentVNode(\" 查看题目对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.viewDialogVisible,\n    \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.viewDialogVisible = $event),\n    title: \"题目详情\",\n    width: \"700px\"\n  }, {\n    default: _withCtx(() => [$setup.currentQuestion ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_el_tag, {\n      type: $setup.getQuestionTypeTag($setup.currentQuestion.type),\n      size: \"large\"\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getQuestionTypeText($setup.currentQuestion.type)), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"type\"]), _createElementVNode(\"span\", _hoisted_22, _toDisplayString($setup.currentQuestion.score) + \"分\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_23, _toDisplayString($setup.currentQuestion.content), 1 /* TEXT */), _createCommentVNode(\" 选项 \"), $setup.currentQuestion.type !== 'true_false' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_24, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.currentQuestion.options, (option, index) => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: index,\n        class: _normalizeClass([\"option-item\", {\n          'correct-option': option.is_correct\n        }])\n      }, [_createElementVNode(\"div\", _hoisted_25, _toDisplayString(String.fromCharCode(65 + index)), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_26, _toDisplayString(option.content), 1 /* TEXT */), option.is_correct ? (_openBlock(), _createElementBlock(\"div\", _hoisted_27, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode(_component_Check)]),\n        _: 1 /* STABLE */\n      })])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */);\n    }), 128 /* KEYED_FRAGMENT */))])) : (_openBlock(), _createElementBlock(_Fragment, {\n      key: 1\n    }, [_createCommentVNode(\" 判断题答案 \"), _createElementVNode(\"div\", _hoisted_28, [_cache[29] || (_cache[29] = _createElementVNode(\"div\", {\n      class: \"answer-label\"\n    }, \"正确答案：\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_29, [_createVNode(_component_el_tag, {\n      type: $setup.currentQuestion.true_false_answer ? 'success' : 'danger'\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.currentQuestion.true_false_answer ? '正确' : '错误'), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"type\"])])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)), _createCommentVNode(\" 解析 \"), $setup.currentQuestion.explanation ? (_openBlock(), _createElementBlock(\"div\", _hoisted_30, [_cache[30] || (_cache[30] = _createElementVNode(\"div\", {\n      class: \"explanation-label\"\n    }, \"解析：\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_31, _toDisplayString($setup.currentQuestion.explanation), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 导入题库对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.importDialogVisible,\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $setup.importDialogVisible = $event),\n    title: \"导入题库\",\n    width: \"500px\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_upload, {\n      class: \"upload-demo\",\n      drag: \"\",\n      action: \"http://localhost:3000/api/exams/questions/import\",\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      },\n      data: {\n        exam_id: $setup.examId\n      },\n      \"on-success\": $setup.handleImportSuccess,\n      \"on-error\": $setup.handleImportError,\n      \"before-upload\": $setup.beforeImportUpload,\n      accept: \".xlsx,.xls\"\n    }, {\n      tip: _withCtx(() => [_createElementVNode(\"div\", _hoisted_32, [_cache[32] || (_cache[32] = _createTextVNode(\" 请上传Excel格式的题库文件，\")), _createVNode(_component_el_button, {\n        type: \"primary\",\n        link: \"\",\n        onClick: $setup.downloadTemplate\n      }, {\n        default: _withCtx(() => _cache[31] || (_cache[31] = [_createTextVNode(\"下载模板\")])),\n        _: 1 /* STABLE */,\n        __: [31]\n      }, 8 /* PROPS */, [\"onClick\"])])]),\n      default: _withCtx(() => [_createVNode(_component_el_icon, {\n        class: \"el-icon--upload\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_upload_filled)]),\n        _: 1 /* STABLE */\n      }), _cache[33] || (_cache[33] = _createElementVNode(\"div\", {\n        class: \"el-upload__text\"\n      }, [_createTextVNode(\" 将Excel文件拖到此处，或\"), _createElementVNode(\"em\", null, \"点击上传\")], -1 /* CACHED */))]),\n      _: 1 /* STABLE */,\n      __: [33]\n    }, 8 /* PROPS */, [\"data\", \"on-success\", \"on-error\", \"before-upload\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_component_el_button", "onClick", "$setup", "goBack", "_cache", "type", "openAddDialog", "importQuestions", "examData", "_hoisted_3", "_component_el_descriptions", "title", "column", "border", "_component_el_descriptions_item", "label", "duration", "total_score", "pass_score", "_component_el_tag", "getExamStatusType", "status", "getExamStatusText", "formatDate", "created_at", "_hoisted_4", "questions", "length", "_createBlock", "_component_el_empty", "description", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_toDisplayString", "_hoisted_9", "_hoisted_10", "singleChoiceCount", "_hoisted_11", "_hoisted_12", "multipleChoiceCount", "_hoisted_13", "_hoisted_14", "trueFalseCount", "_hoisted_15", "_hoisted_16", "totalScore", "_component_el_table", "data", "style", "_component_el_table_column", "width", "default", "scope", "getQuestionTypeTag", "row", "getQuestionTypeText", "prop", "fixed", "size", "$event", "viewQuestion", "editQuestion", "deleteQuestion", "loading", "_createCommentVNode", "_component_el_dialog", "dialogVisible", "isEdit", "footer", "_hoisted_19", "submitForm", "_component_el_form", "ref", "model", "formData", "rules", "formRules", "_component_el_form_item", "_component_el_select", "placeholder", "_component_el_option", "value", "_component_el_input", "content", "rows", "_component_el_input_number", "score", "min", "max", "_Fragment", "key", "_component_el_divider", "_renderList", "options", "option", "index", "String", "fromCharCode", "required", "message", "trigger", "_hoisted_17", "_component_el_checkbox", "is_correct", "_component_el_radio", "correct_option", "icon", "circle", "removeOption", "_hoisted_18", "plain", "addOption", "disabled", "_component_el_radio_group", "true_false_answer", "explanation", "viewDialogVisible", "currentQuestion", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_normalizeClass", "_hoisted_25", "_hoisted_26", "_hoisted_27", "_component_el_icon", "_component_Check", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_hoisted_31", "importDialogVisible", "_component_el_upload", "drag", "action", "headers", "exam_id", "examId", "handleImportSuccess", "handleImportError", "beforeImportUpload", "accept", "tip", "_hoisted_32", "link", "downloadTemplate", "_component_upload_filled"], "sources": ["D:\\admin\\202506\\督导系统\\后台管理系统\\ms\\src\\views\\exams\\ExamQuestions.vue"], "sourcesContent": ["<template>\r\n  <div class=\"exam-questions-container\">\r\n    <el-card class=\"box-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span class=\"title\">考试题目管理</span>\r\n          <div>\r\n            <el-button @click=\"goBack\">返回考试列表</el-button>\r\n            <el-button type=\"primary\" @click=\"openAddDialog\">添加题目</el-button>\r\n            <el-button type=\"success\" @click=\"importQuestions\">导入题库</el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <div class=\"exam-info\" v-if=\"examData\">\r\n        <el-descriptions title=\"考试信息\" :column=\"3\" border>\r\n          <el-descriptions-item label=\"考试名称\">{{ examData.title }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"考试时长\">{{ examData.duration }}分钟</el-descriptions-item>\r\n          <el-descriptions-item label=\"总分\">{{ examData.total_score }}分</el-descriptions-item>\r\n          <el-descriptions-item label=\"及格分数\">{{ examData.pass_score }}分</el-descriptions-item>\r\n          <el-descriptions-item label=\"考试状态\">\r\n            <el-tag :type=\"getExamStatusType(examData.status)\">\r\n              {{ getExamStatusText(examData.status) }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"创建时间\">{{ formatDate(examData.created_at) }}</el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n\r\n      <!-- 题目列表 -->\r\n      <div v-loading=\"loading\" class=\"question-list\">\r\n        <el-empty v-if=\"questions.length === 0\" description=\"暂无考试题目，请添加\" />\r\n        \r\n        <div v-else>\r\n          <div class=\"question-stats\">\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-value\">{{ questions.length }}</div>\r\n              <div class=\"stat-label\">总题数</div>\r\n            </div>\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-value\">{{ singleChoiceCount }}</div>\r\n              <div class=\"stat-label\">单选题</div>\r\n            </div>\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-value\">{{ multipleChoiceCount }}</div>\r\n              <div class=\"stat-label\">多选题</div>\r\n            </div>\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-value\">{{ trueFalseCount }}</div>\r\n              <div class=\"stat-label\">判断题</div>\r\n            </div>\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-value\">{{ totalScore }}</div>\r\n              <div class=\"stat-label\">总分</div>\r\n            </div>\r\n          </div>\r\n\r\n          <el-table :data=\"questions\" border style=\"width: 100%\">\r\n            <el-table-column type=\"index\" width=\"50\" label=\"#\" />\r\n            <el-table-column label=\"题型\" width=\"100\">\r\n              <template #default=\"scope\">\r\n                <el-tag :type=\"getQuestionTypeTag(scope.row.type)\">\r\n                  {{ getQuestionTypeText(scope.row.type) }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"content\" label=\"题目内容\" show-overflow-tooltip />\r\n            <el-table-column prop=\"score\" label=\"分值\" width=\"80\" />\r\n            <el-table-column label=\"操作\" width=\"180\" fixed=\"right\">\r\n              <template #default=\"scope\">\r\n                <el-button size=\"small\" @click=\"viewQuestion(scope.row)\">查看</el-button>\r\n                <el-button size=\"small\" type=\"primary\" @click=\"editQuestion(scope.row)\">编辑</el-button>\r\n                <el-button size=\"small\" type=\"danger\" @click=\"deleteQuestion(scope.row)\">删除</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 添加/编辑题目对话框 -->\r\n    <el-dialog\r\n      v-model=\"dialogVisible\"\r\n      :title=\"isEdit ? '编辑题目' : '添加题目'\"\r\n      width=\"700px\"\r\n    >\r\n      <el-form\r\n        ref=\"questionFormRef\"\r\n        :model=\"formData\"\r\n        :rules=\"formRules\"\r\n        label-width=\"100px\"\r\n        label-position=\"right\"\r\n      >\r\n        <el-form-item label=\"题目类型\" prop=\"type\">\r\n          <el-select v-model=\"formData.type\" placeholder=\"请选择题目类型\">\r\n            <el-option label=\"单选题\" value=\"single\" />\r\n            <el-option label=\"多选题\" value=\"multiple\" />\r\n            <el-option label=\"判断题\" value=\"true_false\" />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"题目内容\" prop=\"content\">\r\n          <el-input\r\n            v-model=\"formData.content\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入题目内容\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"分值\" prop=\"score\">\r\n          <el-input-number v-model=\"formData.score\" :min=\"1\" :max=\"100\" />\r\n        </el-form-item>\r\n\r\n        <!-- 选项 (单选题和多选题) -->\r\n        <template v-if=\"formData.type === 'single' || formData.type === 'multiple'\">\r\n          <el-divider content-position=\"left\">选项</el-divider>\r\n          \r\n          <div\r\n            v-for=\"(option, index) in formData.options\"\r\n            :key=\"index\"\r\n            class=\"option-item\"\r\n          >\r\n            <el-form-item\r\n              :label=\"`选项 ${String.fromCharCode(65 + index)}`\"\r\n              :prop=\"`options.${index}.content`\"\r\n              :rules=\"{ required: true, message: '请输入选项内容', trigger: 'blur' }\"\r\n            >\r\n              <div class=\"option-content\">\r\n                <el-input v-model=\"option.content\" placeholder=\"请输入选项内容\" />\r\n                <el-checkbox\r\n                  v-if=\"formData.type === 'multiple'\"\r\n                  v-model=\"option.is_correct\"\r\n                  label=\"正确答案\"\r\n                />\r\n                <el-radio\r\n                  v-else\r\n                  v-model=\"formData.correct_option\"\r\n                  :label=\"index\"\r\n                  class=\"option-radio\"\r\n                >正确答案</el-radio>\r\n                <el-button\r\n                  type=\"danger\"\r\n                  icon=\"Delete\"\r\n                  circle\r\n                  @click=\"removeOption(index)\"\r\n                  v-if=\"formData.options.length > 2\"\r\n                />\r\n              </div>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <div class=\"add-option\">\r\n            <el-button type=\"primary\" plain @click=\"addOption\" :disabled=\"formData.options.length >= 6\">\r\n              添加选项\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n\r\n        <!-- 判断题答案 -->\r\n        <template v-if=\"formData.type === 'true_false'\">\r\n          <el-form-item label=\"正确答案\" prop=\"true_false_answer\">\r\n            <el-radio-group v-model=\"formData.true_false_answer\">\r\n              <el-radio :label=\"true\">正确</el-radio>\r\n              <el-radio :label=\"false\">错误</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </template>\r\n\r\n        <el-form-item label=\"解析\" prop=\"explanation\">\r\n          <el-input\r\n            v-model=\"formData.explanation\"\r\n            type=\"textarea\"\r\n            :rows=\"2\"\r\n            placeholder=\"请输入题目解析（可选）\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitForm\">确认</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- 查看题目对话框 -->\r\n    <el-dialog\r\n      v-model=\"viewDialogVisible\"\r\n      title=\"题目详情\"\r\n      width=\"700px\"\r\n    >\r\n      <div v-if=\"currentQuestion\" class=\"question-detail\">\r\n        <div class=\"question-header\">\r\n          <el-tag :type=\"getQuestionTypeTag(currentQuestion.type)\" size=\"large\">\r\n            {{ getQuestionTypeText(currentQuestion.type) }}\r\n          </el-tag>\r\n          <span class=\"question-score\">{{ currentQuestion.score }}分</span>\r\n        </div>\r\n\r\n        <div class=\"question-content\">{{ currentQuestion.content }}</div>\r\n\r\n        <!-- 选项 -->\r\n        <div v-if=\"currentQuestion.type !== 'true_false'\" class=\"options-list\">\r\n          <div\r\n            v-for=\"(option, index) in currentQuestion.options\"\r\n            :key=\"index\"\r\n            class=\"option-item\"\r\n            :class=\"{ 'correct-option': option.is_correct }\"\r\n          >\r\n            <div class=\"option-label\">{{ String.fromCharCode(65 + index) }}</div>\r\n            <div class=\"option-content\">{{ option.content }}</div>\r\n            <div v-if=\"option.is_correct\" class=\"correct-mark\">\r\n              <el-icon><Check /></el-icon>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 判断题答案 -->\r\n        <div v-else class=\"true-false-answer\">\r\n          <div class=\"answer-label\">正确答案：</div>\r\n          <div class=\"answer-value\">\r\n            <el-tag :type=\"currentQuestion.true_false_answer ? 'success' : 'danger'\">\r\n              {{ currentQuestion.true_false_answer ? '正确' : '错误' }}\r\n            </el-tag>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 解析 -->\r\n        <div v-if=\"currentQuestion.explanation\" class=\"question-explanation\">\r\n          <div class=\"explanation-label\">解析：</div>\r\n          <div class=\"explanation-content\">{{ currentQuestion.explanation }}</div>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 导入题库对话框 -->\r\n    <el-dialog\r\n      v-model=\"importDialogVisible\"\r\n      title=\"导入题库\"\r\n      width=\"500px\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        drag\r\n        action=\"http://localhost:3000/api/exams/questions/import\"\r\n        :headers=\"{ 'Content-Type': 'multipart/form-data' }\"\r\n        :data=\"{ exam_id: examId }\"\r\n        :on-success=\"handleImportSuccess\"\r\n        :on-error=\"handleImportError\"\r\n        :before-upload=\"beforeImportUpload\"\r\n        accept=\".xlsx,.xls\"\r\n      >\r\n        <el-icon class=\"el-icon--upload\"><upload-filled /></el-icon>\r\n        <div class=\"el-upload__text\">\r\n          将Excel文件拖到此处，或<em>点击上传</em>\r\n        </div>\r\n        <template #tip>\r\n          <div class=\"el-upload__tip\">\r\n            请上传Excel格式的题库文件，<el-button type=\"primary\" link @click=\"downloadTemplate\">下载模板</el-button>\r\n          </div>\r\n        </template>\r\n      </el-upload>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, computed, onMounted } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { Check, UploadFilled } from '@element-plus/icons-vue'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'ExamQuestions',\r\n  components: {\r\n    Check,\r\n    UploadFilled\r\n  },\r\n  setup() {\r\n      let token = localStorage.getItem('token')\r\n    if (token) {\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`\r\n    }\r\n    const route = useRoute()\r\n    const router = useRouter()\r\n    const examId = route.params.id\r\n    \r\n    // 基础数据\r\n    const loading = ref(false)\r\n    const examData = ref(null)\r\n    const questions = ref([])\r\n    const questionFormRef = ref(null)\r\n    \r\n    // 对话框\r\n    const dialogVisible = ref(false)\r\n    const viewDialogVisible = ref(false)\r\n    const importDialogVisible = ref(false)\r\n    const isEdit = ref(false)\r\n    const currentQuestion = ref(null)\r\n    \r\n    // 表单数据\r\n    const formData = reactive({\r\n      id: '',\r\n      type: 'single',\r\n      content: '',\r\n      score: 5,\r\n      options: [\r\n        { content: '', is_correct: false },\r\n        { content: '', is_correct: false },\r\n        { content: '', is_correct: false },\r\n        { content: '', is_correct: false }\r\n      ],\r\n      correct_option: 0,\r\n      true_false_answer: true,\r\n      explanation: '',\r\n      exam_id: examId\r\n    })\r\n    \r\n    // 表单验证规则\r\n    const formRules = {\r\n      type: [\r\n        { required: true, message: '请选择题目类型', trigger: 'change' }\r\n      ],\r\n      content: [\r\n        { required: true, message: '请输入题目内容', trigger: 'blur' }\r\n      ],\r\n      score: [\r\n        { required: true, message: '请输入分值', trigger: 'change' }\r\n      ]\r\n    }\r\n    \r\n    // 计算属性\r\n    const singleChoiceCount = computed(() => {\r\n      return questions.value.filter(q => q.type === 'single').length\r\n    })\r\n    \r\n    const multipleChoiceCount = computed(() => {\r\n      return questions.value.filter(q => q.type === 'multiple').length\r\n    })\r\n    \r\n    const trueFalseCount = computed(() => {\r\n      return questions.value.filter(q => q.type === 'true_false').length\r\n    })\r\n    \r\n    const totalScore = computed(() => {\r\n      return questions.value.reduce((sum, q) => sum + q.score, 0)\r\n    })\r\n    \r\n    // 生命周期钩子\r\n    onMounted(() => {\r\n      fetchExamData()\r\n      fetchQuestions()\r\n    })\r\n    \r\n    // 获取考试信息\r\n    const fetchExamData = async () => {\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/exams/${examId}`)\r\n        examData.value = response.data.data\r\n      } catch (error) {\r\n        console.error('获取考试信息失败:', error)\r\n        ElMessage.error('获取考试信息失败')\r\n      }\r\n    }\r\n    \r\n    // 获取题目列表\r\n    const fetchQuestions = async () => {\r\n      loading.value = true\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/exams/${examId}/questions`)\r\n        questions.value = response.data.data\r\n      } catch (error) {\r\n        console.error('获取题目列表失败:', error)\r\n        ElMessage.error('获取题目列表失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 获取考试状态文本\r\n    const getExamStatusText = (status) => {\r\n      const statusMap = {\r\n        'draft': '草稿',\r\n        'published': '已发布',\r\n        'in_progress': '进行中',\r\n        'completed': '已结束'\r\n      }\r\n      return statusMap[status] || '未知状态'\r\n    }\r\n    \r\n    // 获取考试状态类型\r\n    const getExamStatusType = (status) => {\r\n      const typeMap = {\r\n        'draft': 'info',\r\n        'published': 'success',\r\n        'in_progress': 'warning',\r\n        'completed': 'danger'\r\n      }\r\n      return typeMap[status] || 'info'\r\n    }\r\n    \r\n    // 获取题目类型文本\r\n    const getQuestionTypeText = (type) => {\r\n      const typeMap = {\r\n        'single': '单选题',\r\n        'multiple': '多选题',\r\n        'true_false': '判断题'\r\n      }\r\n      return typeMap[type] || '未知类型'\r\n    }\r\n    \r\n    // 获取题目类型标签\r\n    const getQuestionTypeTag = (type) => {\r\n      const tagMap = {\r\n        'single': 'primary',\r\n        'multiple': 'success',\r\n        'true_false': 'warning'\r\n      }\r\n      return tagMap[type] || 'info'\r\n    }\r\n    \r\n    // 格式化日期\r\n    const formatDate = (dateString) => {\r\n      if (!dateString) return '-'\r\n      const date = new Date(dateString)\r\n      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`\r\n    }\r\n    \r\n    // 返回考试列表\r\n    const goBack = () => {\r\n      router.push('/exams/list')\r\n    }\r\n    \r\n    // 打开添加对话框\r\n    const openAddDialog = () => {\r\n      isEdit.value = false\r\n      resetFormData()\r\n      dialogVisible.value = true\r\n    }\r\n    \r\n    // 编辑题目\r\n    const editQuestion = (row) => {\r\n      isEdit.value = true\r\n      currentQuestion.value = row\r\n      \r\n      // 填充表单数据\r\n      Object.assign(formData, {\r\n        id: row.id,\r\n        type: row.type,\r\n        content: row.content,\r\n        score: row.score,\r\n        explanation: row.explanation || '',\r\n        exam_id: examId\r\n      })\r\n      \r\n      // 处理选项和答案\r\n      if (row.type === 'true_false') {\r\n        formData.true_false_answer = row.true_false_answer\r\n      } else {\r\n        formData.options = [...row.options]\r\n        if (row.type === 'single') {\r\n          formData.correct_option = row.options.findIndex(opt => opt.is_correct)\r\n        }\r\n      }\r\n      \r\n      dialogVisible.value = true\r\n    }\r\n    \r\n    // 查看题目\r\n    const viewQuestion = (row) => {\r\n      currentQuestion.value = row\r\n      viewDialogVisible.value = true\r\n    }\r\n    \r\n    // 删除题目\r\n    const deleteQuestion = (row) => {\r\n      ElMessageBox.confirm(\r\n        '确定要删除该题目吗？',\r\n        '警告',\r\n        {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n        }\r\n      )\r\n        .then(async () => {\r\n          try {\r\n            await axios.delete(`http://localhost:3000/api/exams/questions/${row.id}`)\r\n            ElMessage.success('删除成功')\r\n            fetchQuestions()\r\n          } catch (error) {\r\n            console.error('删除失败:', error)\r\n            ElMessage.error('删除失败')\r\n          }\r\n        })\r\n        .catch(() => {\r\n          ElMessage.info('已取消删除')\r\n        })\r\n    }\r\n    \r\n    // 添加选项\r\n    const addOption = () => {\r\n      if (formData.options.length < 6) {\r\n        formData.options.push({ content: '', is_correct: false })\r\n      }\r\n    }\r\n    \r\n    // 移除选项\r\n    const removeOption = (index) => {\r\n      if (formData.options.length > 2) {\r\n        formData.options.splice(index, 1)\r\n        \r\n        // 如果删除的是正确答案，重置正确答案\r\n        if (formData.type === 'single' && formData.correct_option === index) {\r\n          formData.correct_option = 0\r\n        } else if (formData.type === 'single' && formData.correct_option > index) {\r\n          formData.correct_option--\r\n        }\r\n      }\r\n    }\r\n    \r\n    // 重置表单数据\r\n    const resetFormData = () => {\r\n      Object.assign(formData, {\r\n        id: '',\r\n        type: 'single',\r\n        content: '',\r\n        score: 5,\r\n        options: [\r\n          { content: '', is_correct: false },\r\n          { content: '', is_correct: false },\r\n          { content: '', is_correct: false },\r\n          { content: '', is_correct: false }\r\n        ],\r\n        correct_option: 0,\r\n        true_false_answer: true,\r\n        explanation: '',\r\n        exam_id: examId\r\n      })\r\n      \r\n      if (questionFormRef.value) {\r\n        questionFormRef.value.resetFields()\r\n      }\r\n    }\r\n    \r\n    // 提交表单\r\n    const submitForm = async () => {\r\n      if (!questionFormRef.value) return\r\n      \r\n      await questionFormRef.value.validate(async (valid) => {\r\n        if (valid) {\r\n          // 处理单选题答案\r\n          if (formData.type === 'single') {\r\n            formData.options.forEach((opt, index) => {\r\n              opt.is_correct = index === formData.correct_option\r\n            })\r\n          }\r\n          \r\n          try {\r\n            if (isEdit.value) {\r\n              // 编辑模式\r\n              await axios.put(`http://localhost:3000/api/exams/questions/${formData.id}`, formData)\r\n              ElMessage.success('题目更新成功')\r\n            } else {\r\n              // 添加模式\r\n              await axios.post('http://localhost:3000/api/exams/questions', formData)\r\n              ElMessage.success('题目添加成功')\r\n            }\r\n            \r\n            dialogVisible.value = false\r\n            fetchQuestions()\r\n          } catch (error) {\r\n            console.error('操作失败:', error)\r\n            ElMessage.error('操作失败')\r\n          }\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    }\r\n    \r\n    // 导入题库\r\n    const importQuestions = () => {\r\n      importDialogVisible.value = true\r\n    }\r\n    \r\n    // 下载模板\r\n    const downloadTemplate = () => {\r\n      window.open('http://localhost:3000/api/exams/questions/template', '_blank')\r\n    }\r\n    \r\n    // 上传前验证\r\n    const beforeImportUpload = (file) => {\r\n      const isExcel = file.type === 'application/vnd.ms-excel' || \r\n                      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\r\n      const isLt2M = file.size / 1024 / 1024 < 2\r\n      \r\n      if (!isExcel) {\r\n        ElMessage.error('只能上传Excel文件!')\r\n      }\r\n      if (!isLt2M) {\r\n        ElMessage.error('文件大小不能超过2MB!')\r\n      }\r\n      \r\n      return isExcel && isLt2M\r\n    }\r\n    \r\n    // 导入成功\r\n    const handleImportSuccess = (response) => {\r\n      ElMessage.success(`成功导入${response.data.count}道题目`)\r\n      importDialogVisible.value = false\r\n      fetchQuestions()\r\n    }\r\n    \r\n    // 导入失败\r\n    const handleImportError = (error) => {\r\n      console.error('导入失败:', error)\r\n      ElMessage.error('导入失败，请检查文件格式是否正确')\r\n    }\r\n    \r\n    return {\r\n      loading,\r\n      examData,\r\n      questions,\r\n      dialogVisible,\r\n      viewDialogVisible,\r\n      importDialogVisible,\r\n      isEdit,\r\n      formData,\r\n      formRules,\r\n      questionFormRef,\r\n      currentQuestion,\r\n      examId,\r\n      singleChoiceCount,\r\n      multipleChoiceCount,\r\n      trueFalseCount,\r\n      totalScore,\r\n      getExamStatusText,\r\n      getExamStatusType,\r\n      getQuestionTypeText,\r\n      getQuestionTypeTag,\r\n      formatDate,\r\n      goBack,\r\n      openAddDialog,\r\n      editQuestion,\r\n      viewQuestion,\r\n      deleteQuestion,\r\n      addOption,\r\n      removeOption,\r\n      submitForm,\r\n      importQuestions,\r\n      downloadTemplate,\r\n      beforeImportUpload,\r\n      handleImportSuccess,\r\n      handleImportError\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.exam-questions-container {\r\n  padding: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.exam-info {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.question-list {\r\n  margin-top: 20px;\r\n}\r\n\r\n.question-stats {\r\n  display: flex;\r\n  margin-bottom: 20px;\r\n  background-color: #f7f7f7;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.stat-item {\r\n  flex: 1;\r\n  text-align: center;\r\n  border-right: 1px solid #eee;\r\n}\r\n\r\n.stat-item:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-top: 5px;\r\n}\r\n\r\n.option-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.option-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.option-radio {\r\n  margin-left: 10px;\r\n}\r\n\r\n.add-option {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 10px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.question-detail {\r\n  padding: 10px;\r\n}\r\n\r\n.question-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.question-score {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #f56c6c;\r\n}\r\n\r\n.question-content {\r\n  font-size: 16px;\r\n  margin-bottom: 20px;\r\n  padding: 10px;\r\n  background-color: #f7f7f7;\r\n  border-radius: 4px;\r\n}\r\n\r\n.options-list {\r\n  margin-top: 15px;\r\n}\r\n\r\n.options-list .option-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.options-list .option-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.options-list .option-item.correct-option {\r\n  background-color: #f0f9eb;\r\n}\r\n\r\n.options-list .option-label {\r\n  width: 30px;\r\n  height: 30px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-radius: 50%;\r\n  background-color: #f2f6fc;\r\n  margin-right: 10px;\r\n  font-weight: bold;\r\n}\r\n\r\n.options-list .option-content {\r\n  flex: 1;\r\n}\r\n\r\n.options-list .correct-mark {\r\n  color: #67c23a;\r\n  margin-left: 10px;\r\n}\r\n\r\n.true-false-answer {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-top: 15px;\r\n}\r\n\r\n.answer-label {\r\n  font-weight: bold;\r\n  margin-right: 10px;\r\n}\r\n\r\n.question-explanation {\r\n  margin-top: 20px;\r\n  padding: 10px;\r\n  background-color: #f7f7f7;\r\n  border-radius: 4px;\r\n}\r\n\r\n.explanation-label {\r\n  font-weight: bold;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.explanation-content {\r\n  color: #606266;\r\n  white-space: pre-line;\r\n}\r\n</style> "], "mappings": ";;EACOA,KAAK,EAAC;AAA0B;;EAG1BA,KAAK,EAAC;AAAa;;;EAUrBA,KAAK,EAAC;;;EAgBcA,KAAK,EAAC;AAAe;;;;;EAIrCA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAGpBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAGpBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAGpBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EAGpBA,KAAK,EAAC;AAAW;;EACfA,KAAK,EAAC;AAAY;;EA4ElBA,KAAK,EAAC;AAAgB;;EAwB1BA,KAAK,EAAC;AAAY;;EA4BnBA,KAAK,EAAC;AAAe;;;EAaDA,KAAK,EAAC;;;EAC3BA,KAAK,EAAC;AAAiB;;EAIpBA,KAAK,EAAC;AAAgB;;EAGzBA,KAAK,EAAC;AAAkB;;;EAGqBA,KAAK,EAAC;;;EAO/CA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAgB;;;EACGA,KAAK,EAAC;;;EAO5BA,KAAK,EAAC;AAAmB;;EAE9BA,KAAK,EAAC;AAAc;;;EAQaA,KAAK,EAAC;;;EAEvCA,KAAK,EAAC;AAAqB;;EA2B3BA,KAAK,EAAC;AAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;uBAlQnCC,mBAAA,CAwQM,OAxQNC,UAwQM,GAvQJC,YAAA,CA4EUC,kBAAA;IA5EDJ,KAAK,EAAC;EAAU;IACZK,MAAM,EAAAC,QAAA,CACf,MAOM,CAPNC,mBAAA,CAOM,OAPNC,UAOM,G,4BANJD,mBAAA,CAAiC;MAA3BP,KAAK,EAAC;IAAO,GAAC,QAAM,qBAC1BO,mBAAA,CAIM,cAHJJ,YAAA,CAA6CM,oBAAA;MAAjCC,OAAK,EAAEC,MAAA,CAAAC;IAAM;wBAAE,MAAMC,MAAA,SAAAA,MAAA,Q,iBAAN,QAAM,E;;;oCACjCV,YAAA,CAAiEM,oBAAA;MAAtDK,IAAI,EAAC,SAAS;MAAEJ,OAAK,EAAEC,MAAA,CAAAI;;wBAAe,MAAIF,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;oCACrDV,YAAA,CAAmEM,oBAAA;MAAxDK,IAAI,EAAC,SAAS;MAAEJ,OAAK,EAAEC,MAAA,CAAAK;;wBAAiB,MAAIH,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;;sBAGtD,MAoBZ,CAlBkCF,MAAA,CAAAM,QAAQ,I,cAArChB,mBAAA,CAaM,OAbNiB,UAaM,GAZJf,YAAA,CAWkBgB,0BAAA;MAXDC,KAAK,EAAC,MAAM;MAAEC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAN;;wBACxC,MAA8E,CAA9EnB,YAAA,CAA8EoB,+BAAA;QAAxDC,KAAK,EAAC;MAAM;0BAAC,MAAoB,C,kCAAjBb,MAAA,CAAAM,QAAQ,CAACG,KAAK,iB;;UACpDjB,YAAA,CAAmFoB,+BAAA;QAA7DC,KAAK,EAAC;MAAM;0BAAC,MAAuB,C,kCAApBb,MAAA,CAAAM,QAAQ,CAACQ,QAAQ,IAAG,IAAE,gB;;UAC5DtB,YAAA,CAAmFoB,+BAAA;QAA7DC,KAAK,EAAC;MAAI;0BAAC,MAA0B,C,kCAAvBb,MAAA,CAAAM,QAAQ,CAACS,WAAW,IAAG,GAAC,gB;;UAC5DvB,YAAA,CAAoFoB,+BAAA;QAA9DC,KAAK,EAAC;MAAM;0BAAC,MAAyB,C,kCAAtBb,MAAA,CAAAM,QAAQ,CAACU,UAAU,IAAG,GAAC,gB;;UAC7DxB,YAAA,CAIuBoB,+BAAA;QAJDC,KAAK,EAAC;MAAM;0BAChC,MAES,CAFTrB,YAAA,CAESyB,iBAAA;UAFAd,IAAI,EAAEH,MAAA,CAAAkB,iBAAiB,CAAClB,MAAA,CAAAM,QAAQ,CAACa,MAAM;;4BAC9C,MAAwC,C,kCAArCnB,MAAA,CAAAoB,iBAAiB,CAACpB,MAAA,CAAAM,QAAQ,CAACa,MAAM,kB;;;;UAGxC3B,YAAA,CAA+FoB,+BAAA;QAAzEC,KAAK,EAAC;MAAM;0BAAC,MAAqC,C,kCAAlCb,MAAA,CAAAqB,UAAU,CAACrB,MAAA,CAAAM,QAAQ,CAACgB,UAAU,kB;;;;8EAKxEhC,mBAAA,CA+CM,OA/CNiC,UA+CM,GA9CYvB,MAAA,CAAAwB,SAAS,CAACC,MAAM,U,cAAhCC,YAAA,CAAmEC,mBAAA;;MAA3BC,WAAW,EAAC;yBAEpDtC,mBAAA,CA2CM,OAAAuC,UAAA,GA1CJjC,mBAAA,CAqBM,OArBNkC,UAqBM,GApBJlC,mBAAA,CAGM,OAHNmC,UAGM,GAFJnC,mBAAA,CAAoD,OAApDoC,UAAoD,EAAAC,gBAAA,CAAzBjC,MAAA,CAAAwB,SAAS,CAACC,MAAM,kB,4BAC3C7B,mBAAA,CAAiC;MAA5BP,KAAK,EAAC;IAAY,GAAC,KAAG,oB,GAE7BO,mBAAA,CAGM,OAHNsC,UAGM,GAFJtC,mBAAA,CAAqD,OAArDuC,WAAqD,EAAAF,gBAAA,CAA1BjC,MAAA,CAAAoC,iBAAiB,kB,4BAC5CxC,mBAAA,CAAiC;MAA5BP,KAAK,EAAC;IAAY,GAAC,KAAG,oB,GAE7BO,mBAAA,CAGM,OAHNyC,WAGM,GAFJzC,mBAAA,CAAuD,OAAvD0C,WAAuD,EAAAL,gBAAA,CAA5BjC,MAAA,CAAAuC,mBAAmB,kB,4BAC9C3C,mBAAA,CAAiC;MAA5BP,KAAK,EAAC;IAAY,GAAC,KAAG,oB,GAE7BO,mBAAA,CAGM,OAHN4C,WAGM,GAFJ5C,mBAAA,CAAkD,OAAlD6C,WAAkD,EAAAR,gBAAA,CAAvBjC,MAAA,CAAA0C,cAAc,kB,4BACzC9C,mBAAA,CAAiC;MAA5BP,KAAK,EAAC;IAAY,GAAC,KAAG,oB,GAE7BO,mBAAA,CAGM,OAHN+C,WAGM,GAFJ/C,mBAAA,CAA8C,OAA9CgD,WAA8C,EAAAX,gBAAA,CAAnBjC,MAAA,CAAA6C,UAAU,kB,4BACrCjD,mBAAA,CAAgC;MAA3BP,KAAK,EAAC;IAAY,GAAC,IAAE,oB,KAI9BG,YAAA,CAkBWsD,mBAAA;MAlBAC,IAAI,EAAE/C,MAAA,CAAAwB,SAAS;MAAEb,MAAM,EAAN,EAAM;MAACqC,KAAmB,EAAnB;QAAA;MAAA;;wBACjC,MAAqD,CAArDxD,YAAA,CAAqDyD,0BAAA;QAApC9C,IAAI,EAAC,OAAO;QAAC+C,KAAK,EAAC,IAAI;QAACrC,KAAK,EAAC;UAC/CrB,YAAA,CAMkByD,0BAAA;QANDpC,KAAK,EAAC,IAAI;QAACqC,KAAK,EAAC;;QACrBC,OAAO,EAAAxD,QAAA,CAGPyD,KAHc,KACvB5D,YAAA,CAESyB,iBAAA;UAFAd,IAAI,EAAEH,MAAA,CAAAqD,kBAAkB,CAACD,KAAK,CAACE,GAAG,CAACnD,IAAI;;4BAC9C,MAAyC,C,kCAAtCH,MAAA,CAAAuD,mBAAmB,CAACH,KAAK,CAACE,GAAG,CAACnD,IAAI,kB;;;;UAI3CX,YAAA,CAAqEyD,0BAAA;QAApDO,IAAI,EAAC,SAAS;QAAC3C,KAAK,EAAC,MAAM;QAAC,uBAAqB,EAArB;UAC7CrB,YAAA,CAAsDyD,0BAAA;QAArCO,IAAI,EAAC,OAAO;QAAC3C,KAAK,EAAC,IAAI;QAACqC,KAAK,EAAC;UAC/C1D,YAAA,CAMkByD,0BAAA;QANDpC,KAAK,EAAC,IAAI;QAACqC,KAAK,EAAC,KAAK;QAACO,KAAK,EAAC;;QACjCN,OAAO,EAAAxD,QAAA,CACuDyD,KADhD,KACvB5D,YAAA,CAAuEM,oBAAA;UAA5D4D,IAAI,EAAC,OAAO;UAAE3D,OAAK,EAAA4D,MAAA,IAAE3D,MAAA,CAAA4D,YAAY,CAACR,KAAK,CAACE,GAAG;;4BAAG,MAAEpD,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;0DAC3DV,YAAA,CAAsFM,oBAAA;UAA3E4D,IAAI,EAAC,OAAO;UAACvD,IAAI,EAAC,SAAS;UAAEJ,OAAK,EAAA4D,MAAA,IAAE3D,MAAA,CAAA6D,YAAY,CAACT,KAAK,CAACE,GAAG;;4BAAG,MAAEpD,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;0DAC1EV,YAAA,CAAuFM,oBAAA;UAA5E4D,IAAI,EAAC,OAAO;UAACvD,IAAI,EAAC,QAAQ;UAAEJ,OAAK,EAAA4D,MAAA,IAAE3D,MAAA,CAAA8D,cAAc,CAACV,KAAK,CAACE,GAAG;;4BAAG,MAAEpD,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;;;6DA1CrEF,MAAA,CAAA+D,OAAO,E;;MAkDzBC,mBAAA,gBAAmB,EACnBxE,YAAA,CAwGYyE,oBAAA;gBAvGDjE,MAAA,CAAAkE,aAAa;+DAAblE,MAAA,CAAAkE,aAAa,GAAAP,MAAA;IACrBlD,KAAK,EAAET,MAAA,CAAAmE,MAAM;IACdjB,KAAK,EAAC;;IA+FKkB,MAAM,EAAAzE,QAAA,CACf,MAGO,CAHPC,mBAAA,CAGO,QAHPyE,WAGO,GAFL7E,YAAA,CAAwDM,oBAAA;MAA5CC,OAAK,EAAAG,MAAA,QAAAA,MAAA,MAAAyD,MAAA,IAAE3D,MAAA,CAAAkE,aAAa;;wBAAU,MAAEhE,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC5CV,YAAA,CAA4DM,oBAAA;MAAjDK,IAAI,EAAC,SAAS;MAAEJ,OAAK,EAAEC,MAAA,CAAAsE;;wBAAY,MAAEpE,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;sBAhGpD,MA2FU,CA3FVV,YAAA,CA2FU+E,kBAAA;MA1FRC,GAAG,EAAC,iBAAiB;MACpBC,KAAK,EAAEzE,MAAA,CAAA0E,QAAQ;MACfC,KAAK,EAAE3E,MAAA,CAAA4E,SAAS;MACjB,aAAW,EAAC,OAAO;MACnB,gBAAc,EAAC;;wBAEf,MAMe,CANfpF,YAAA,CAMeqF,uBAAA;QANDhE,KAAK,EAAC,MAAM;QAAC2C,IAAI,EAAC;;0BAC9B,MAIY,CAJZhE,YAAA,CAIYsF,oBAAA;sBAJQ9E,MAAA,CAAA0E,QAAQ,CAACvE,IAAI;qEAAbH,MAAA,CAAA0E,QAAQ,CAACvE,IAAI,GAAAwD,MAAA;UAAEoB,WAAW,EAAC;;4BAC7C,MAAwC,CAAxCvF,YAAA,CAAwCwF,oBAAA;YAA7BnE,KAAK,EAAC,KAAK;YAACoE,KAAK,EAAC;cAC7BzF,YAAA,CAA0CwF,oBAAA;YAA/BnE,KAAK,EAAC,KAAK;YAACoE,KAAK,EAAC;cAC7BzF,YAAA,CAA4CwF,oBAAA;YAAjCnE,KAAK,EAAC,KAAK;YAACoE,KAAK,EAAC;;;;;UAIjCzF,YAAA,CAOeqF,uBAAA;QAPDhE,KAAK,EAAC,MAAM;QAAC2C,IAAI,EAAC;;0BAC9B,MAKE,CALFhE,YAAA,CAKE0F,mBAAA;sBAJSlF,MAAA,CAAA0E,QAAQ,CAACS,OAAO;qEAAhBnF,MAAA,CAAA0E,QAAQ,CAACS,OAAO,GAAAxB,MAAA;UACzBxD,IAAI,EAAC,UAAU;UACdiF,IAAI,EAAE,CAAC;UACRL,WAAW,EAAC;;;UAIhBvF,YAAA,CAEeqF,uBAAA;QAFDhE,KAAK,EAAC,IAAI;QAAC2C,IAAI,EAAC;;0BAC5B,MAAgE,CAAhEhE,YAAA,CAAgE6F,0BAAA;sBAAtCrF,MAAA,CAAA0E,QAAQ,CAACY,KAAK;qEAAdtF,MAAA,CAAA0E,QAAQ,CAACY,KAAK,GAAA3B,MAAA;UAAG4B,GAAG,EAAE,CAAC;UAAGC,GAAG,EAAE;;;UAG3DxB,mBAAA,kBAAqB,EACLhE,MAAA,CAAA0E,QAAQ,CAACvE,IAAI,iBAAiBH,MAAA,CAAA0E,QAAQ,CAACvE,IAAI,mB,cAA3Db,mBAAA,CA0CWmG,SAAA;QAAAC,GAAA;MAAA,IAzCTlG,YAAA,CAAmDmG,qBAAA;QAAvC,kBAAgB,EAAC;MAAM;0BAAC,MAAEzF,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;6BAEtCZ,mBAAA,CAgCMmG,SAAA,QAAAG,WAAA,CA/BsB5F,MAAA,CAAA0E,QAAQ,CAACmB,OAAO,GAAlCC,MAAM,EAAEC,KAAK;6BADvBzG,mBAAA,CAgCM;UA9BHoG,GAAG,EAAEK,KAAK;UACX1G,KAAK,EAAC;YAENG,YAAA,CA0BeqF,uBAAA;UAzBZhE,KAAK,QAAQmF,MAAM,CAACC,YAAY,MAAMF,KAAK;UAC3CvC,IAAI,aAAauC,KAAK;UACtBpB,KAAK,EAAE;YAAAuB,QAAA;YAAAC,OAAA;YAAAC,OAAA;UAAA;;4BAER,MAoBM,CApBNxG,mBAAA,CAoBM,OApBNyG,WAoBM,GAnBJ7G,YAAA,CAA2D0F,mBAAA;wBAAxCY,MAAM,CAACX,OAAO;6CAAdW,MAAM,CAACX,OAAO,GAAAxB,MAAA;YAAEoB,WAAW,EAAC;0EAEvC/E,MAAA,CAAA0E,QAAQ,CAACvE,IAAI,mB,cADrBuB,YAAA,CAIE4E,sBAAA;;wBAFSR,MAAM,CAACS,UAAU;6CAAjBT,MAAM,CAACS,UAAU,GAAA5C,MAAA;YAC1B9C,KAAK,EAAC;2FAERa,YAAA,CAKgB8E,mBAAA;;wBAHLxG,MAAA,CAAA0E,QAAQ,CAAC+B,cAAc;uEAAvBzG,MAAA,CAAA0E,QAAQ,CAAC+B,cAAc,GAAA9C,MAAA;YAC/B9C,KAAK,EAAEkF,KAAK;YACb1G,KAAK,EAAC;;8BACP,MAAI,KAAAa,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;yEAMGF,MAAA,CAAA0E,QAAQ,CAACmB,OAAO,CAACpE,MAAM,Q,cAL/BC,YAAA,CAME5B,oBAAA;;YALAK,IAAI,EAAC,QAAQ;YACbuG,IAAI,EAAC,QAAQ;YACbC,MAAM,EAAN,EAAM;YACL5G,OAAK,EAAA4D,MAAA,IAAE3D,MAAA,CAAA4G,YAAY,CAACb,KAAK;;;;sCAOlCnG,mBAAA,CAIM,OAJNiH,WAIM,GAHJrH,YAAA,CAEYM,oBAAA;QAFDK,IAAI,EAAC,SAAS;QAAC2G,KAAK,EAAL,EAAK;QAAE/G,OAAK,EAAEC,MAAA,CAAA+G,SAAS;QAAGC,QAAQ,EAAEhH,MAAA,CAAA0E,QAAQ,CAACmB,OAAO,CAACpE,MAAM;;0BAAO,MAE5FvB,MAAA,SAAAA,MAAA,Q,iBAF4F,QAE5F,E;;;qHAIJ8D,mBAAA,WAAc,EACEhE,MAAA,CAAA0E,QAAQ,CAACvE,IAAI,qB,cAC3BuB,YAAA,CAKemD,uBAAA;;QALDhE,KAAK,EAAC,MAAM;QAAC2C,IAAI,EAAC;;0BAC9B,MAGiB,CAHjBhE,YAAA,CAGiByH,yBAAA;sBAHQjH,MAAA,CAAA0E,QAAQ,CAACwC,iBAAiB;qEAA1BlH,MAAA,CAAA0E,QAAQ,CAACwC,iBAAiB,GAAAvD,MAAA;;4BACjD,MAAqC,CAArCnE,YAAA,CAAqCgH,mBAAA;YAA1B3F,KAAK,EAAE;UAAI;8BAAE,MAAEX,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;cAC1BV,YAAA,CAAsCgH,mBAAA;YAA3B3F,KAAK,EAAE;UAAK;8BAAE,MAAEX,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;;;+CAKjCV,YAAA,CAOeqF,uBAAA;QAPDhE,KAAK,EAAC,IAAI;QAAC2C,IAAI,EAAC;;0BAC5B,MAKE,CALFhE,YAAA,CAKE0F,mBAAA;sBAJSlF,MAAA,CAAA0E,QAAQ,CAACyC,WAAW;qEAApBnH,MAAA,CAAA0E,QAAQ,CAACyC,WAAW,GAAAxD,MAAA;UAC7BxD,IAAI,EAAC,UAAU;UACdiF,IAAI,EAAE,CAAC;UACRL,WAAW,EAAC;;;;;;;8CAapBf,mBAAA,aAAgB,EAChBxE,YAAA,CA+CYyE,oBAAA;gBA9CDjE,MAAA,CAAAoH,iBAAiB;+DAAjBpH,MAAA,CAAAoH,iBAAiB,GAAAzD,MAAA;IAC1BlD,KAAK,EAAC,MAAM;IACZyC,KAAK,EAAC;;sBAMT,MA2CU,CA/CIlD,MAAA,CAAAqH,eAAe,I,cAA1B/H,mBAAA,CAyCM,OAzCNgI,WAyCM,GAxCJ1H,mBAAA,CAKM,OALN2H,WAKM,GAJJ/H,YAAA,CAESyB,iBAAA;MAFAd,IAAI,EAAEH,MAAA,CAAAqD,kBAAkB,CAACrD,MAAA,CAAAqH,eAAe,CAAClH,IAAI;MAAGuD,IAAI,EAAC;;wBAC5D,MAA+C,C,kCAA5C1D,MAAA,CAAAuD,mBAAmB,CAACvD,MAAA,CAAAqH,eAAe,CAAClH,IAAI,kB;;iCAE7CP,mBAAA,CAAgE,QAAhE4H,WAAgE,EAAAvF,gBAAA,CAAhCjC,MAAA,CAAAqH,eAAe,CAAC/B,KAAK,IAAG,GAAC,gB,GAG3D1F,mBAAA,CAAiE,OAAjE6H,WAAiE,EAAAxF,gBAAA,CAAhCjC,MAAA,CAAAqH,eAAe,CAAClC,OAAO,kBAExDnB,mBAAA,QAAW,EACAhE,MAAA,CAAAqH,eAAe,CAAClH,IAAI,qB,cAA/Bb,mBAAA,CAaM,OAbNoI,WAaM,I,kBAZJpI,mBAAA,CAWMmG,SAAA,QAAAG,WAAA,CAVsB5F,MAAA,CAAAqH,eAAe,CAACxB,OAAO,GAAzCC,MAAM,EAAEC,KAAK;2BADvBzG,mBAAA,CAWM;QATHoG,GAAG,EAAEK,KAAK;QACX1G,KAAK,EAAAsI,eAAA,EAAC,aAAa;UAAA,kBACS7B,MAAM,CAACS;QAAU;UAE7C3G,mBAAA,CAAqE,OAArEgI,WAAqE,EAAA3F,gBAAA,CAAxC+D,MAAM,CAACC,YAAY,MAAMF,KAAK,mBAC3DnG,mBAAA,CAAsD,OAAtDiI,WAAsD,EAAA5F,gBAAA,CAAvB6D,MAAM,CAACX,OAAO,kBAClCW,MAAM,CAACS,UAAU,I,cAA5BjH,mBAAA,CAEM,OAFNwI,WAEM,GADJtI,YAAA,CAA4BuI,kBAAA;0BAAnB,MAAS,CAATvI,YAAA,CAASwI,gBAAA,E;;;uDAMxB1I,mBAAA,CAOMmG,SAAA;MAAAC,GAAA;IAAA,IARN1B,mBAAA,WAAc,EACdpE,mBAAA,CAOM,OAPNqI,WAOM,G,4BANJrI,mBAAA,CAAqC;MAAhCP,KAAK,EAAC;IAAc,GAAC,OAAK,qBAC/BO,mBAAA,CAIM,OAJNsI,WAIM,GAHJ1I,YAAA,CAESyB,iBAAA;MAFAd,IAAI,EAAEH,MAAA,CAAAqH,eAAe,CAACH,iBAAiB;;wBAC9C,MAAqD,C,kCAAlDlH,MAAA,CAAAqH,eAAe,CAACH,iBAAiB,+B;;uFAK1ClD,mBAAA,QAAW,EACAhE,MAAA,CAAAqH,eAAe,CAACF,WAAW,I,cAAtC7H,mBAAA,CAGM,OAHN6I,WAGM,G,4BAFJvI,mBAAA,CAAwC;MAAnCP,KAAK,EAAC;IAAmB,GAAC,KAAG,qBAClCO,mBAAA,CAAwE,OAAxEwI,WAAwE,EAAAnG,gBAAA,CAApCjC,MAAA,CAAAqH,eAAe,CAACF,WAAW,iB;;qCAKrEnD,mBAAA,aAAgB,EAChBxE,YAAA,CA0BYyE,oBAAA;gBAzBDjE,MAAA,CAAAqI,mBAAmB;+DAAnBrI,MAAA,CAAAqI,mBAAmB,GAAA1E,MAAA;IAC5BlD,KAAK,EAAC,MAAM;IACZyC,KAAK,EAAC;;sBAEN,MAoBY,CApBZ1D,YAAA,CAoBY8I,oBAAA;MAnBVjJ,KAAK,EAAC,aAAa;MACnBkJ,IAAI,EAAJ,EAAI;MACJC,MAAM,EAAC,kDAAkD;MACxDC,OAAO,EAAE;QAAA;MAAA,CAAyC;MAClD1F,IAAI;QAAA2F,OAAA,EAAa1I,MAAA,CAAA2I;MAAM;MACvB,YAAU,EAAE3I,MAAA,CAAA4I,mBAAmB;MAC/B,UAAQ,EAAE5I,MAAA,CAAA6I,iBAAiB;MAC3B,eAAa,EAAE7I,MAAA,CAAA8I,kBAAkB;MAClCC,MAAM,EAAC;;MAMIC,GAAG,EAAArJ,QAAA,CACZ,MAEM,CAFNC,mBAAA,CAEM,OAFNqJ,WAEM,G,6CAFsB,mBACV,IAAAzJ,YAAA,CAAyEM,oBAAA;QAA9DK,IAAI,EAAC,SAAS;QAAC+I,IAAI,EAAJ,EAAI;QAAEnJ,OAAK,EAAEC,MAAA,CAAAmJ;;0BAAkB,MAAIjJ,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;;wBANjF,MAA4D,CAA5DV,YAAA,CAA4DuI,kBAAA;QAAnD1I,KAAK,EAAC;MAAiB;0BAAC,MAAiB,CAAjBG,YAAA,CAAiB4J,wBAAA,E;;sCAClDxJ,mBAAA,CAEM;QAFDP,KAAK,EAAC;MAAiB,I,iBAAC,iBACb,GAAAO,mBAAA,CAAa,YAAT,MAAI,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}