{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, reactive, onMounted, nextTick } from 'vue';\nimport { useRoute, useRouter } from 'vue-router';\nimport { ElMessage } from 'element-plus';\nimport axios from 'axios';\nexport default {\n  name: 'TeacherDetail',\n  setup() {\n    const route = useRoute();\n    const router = useRouter();\n    const teacherId = route.params.id;\n    let token = localStorage.getItem('token');\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    }\n    // API基础URL\n    const baseUrl = 'http://localhost:3000';\n\n    // 基础数据\n    const loading = ref(false);\n    const examLoading = ref(false);\n    const evaluationLoading = ref(false);\n    const teacherData = ref({});\n    const examResults = ref([]);\n    const evaluations = ref([]);\n    const competencyData = ref(null);\n    const activeTab = ref('exams');\n    const dataLoaded = ref({\n      teacher: false,\n      exams: false,\n      evaluations: false,\n      competency: false\n    });\n\n    // 评价详情相关\n    const evaluationDialogVisible = ref(false);\n    const currentEvaluation = ref({});\n\n    // 生命周期钩子 - 使用分阶段加载来减少布局循环\n    onMounted(() => {\n      // 首先加载基本教师信息\n      fetchTeacherData().then(() => {\n        // 等教师数据加载完成后，再加载其他数据\n        setTimeout(() => {\n          // 优先加载当前选中的标签页数据\n          if (activeTab.value === 'exams') {\n            fetchExamResults();\n            setTimeout(() => {\n              fetchCompetencyStatus();\n            }, 100);\n            setTimeout(() => {\n              fetchEvaluations();\n            }, 200);\n          } else {\n            fetchEvaluations();\n            setTimeout(() => {\n              fetchCompetencyStatus();\n            }, 100);\n            setTimeout(() => {\n              fetchExamResults();\n            }, 200);\n          }\n        }, 100);\n      });\n    });\n\n    // 获取教师信息\n    const fetchTeacherData = async () => {\n      loading.value = true;\n      try {\n        const response = await axios.get(`${baseUrl}/api/teachers/${teacherId}`);\n        teacherData.value = response.data.data || {};\n        dataLoaded.value.teacher = true;\n      } catch (error) {\n        console.error('获取教师信息失败:', error);\n        ElMessage.error('获取教师信息失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 获取考试成绩\n    const fetchExamResults = async () => {\n      if (activeTab.value !== 'exams' && dataLoaded.value.exams) return;\n      examLoading.value = true;\n      try {\n        const response = await axios.get(`${baseUrl}/api/exams/teacher/${teacherId}/results`);\n        await nextTick();\n        examResults.value = Array.isArray(response.data.data) ? response.data.data : [];\n        dataLoaded.value.exams = true;\n      } catch (error) {\n        console.error('获取考试成绩失败:', error);\n        ElMessage.error('获取考试成绩失败');\n        examResults.value = [];\n      } finally {\n        examLoading.value = false;\n      }\n    };\n\n    // 获取教学评价\n    const fetchEvaluations = async () => {\n      if (activeTab.value !== 'evaluations' && dataLoaded.value.evaluations) return;\n      evaluationLoading.value = true;\n      try {\n        const response = await axios.get(`${baseUrl}/api/evaluations/teacher/${teacherId}`);\n        await nextTick();\n        evaluations.value = Array.isArray(response.data.data) ? response.data.data : [];\n        dataLoaded.value.evaluations = true;\n      } catch (error) {\n        console.error('获取教学评价失败:', error);\n        ElMessage.error('获取教学评价失败');\n        evaluations.value = [];\n      } finally {\n        evaluationLoading.value = false;\n      }\n    };\n\n    // 获取能力认证状态\n    const fetchCompetencyStatus = async () => {\n      if (dataLoaded.value.competency) return;\n      try {\n        const response = await axios.get(`${baseUrl}/api/competency/teacher/${teacherId}`);\n        await nextTick();\n        competencyData.value = response.data.data || null;\n        dataLoaded.value.competency = true;\n      } catch (error) {\n        console.error('获取能力认证状态失败:', error);\n        // 这里不显示错误信息，因为有些教师可能没有能力认证记录\n      }\n    };\n\n    // 标签页切换处理\n    const handleTabClick = tab => {\n      if (tab.props.name === 'exams' && !dataLoaded.value.exams) {\n        fetchExamResults();\n      } else if (tab.props.name === 'evaluations' && !dataLoaded.value.evaluations) {\n        fetchEvaluations();\n      }\n    };\n\n    // 返回列表页\n    const goBack = () => {\n      router.push('/teachers/list');\n    };\n\n    // 编辑教师\n    const editTeacher = () => {\n      router.push(`/teachers/edit/${teacherId}`);\n    };\n\n    // 查看评价详情\n    const viewEvaluation = evaluation => {\n      currentEvaluation.value = evaluation;\n      // 使用nextTick确保DOM更新后再显示对话框\n      nextTick(() => {\n        evaluationDialogVisible.value = true;\n      });\n    };\n\n    // 格式化日期\n    const formatDate = dateString => {\n      if (!dateString) return '--';\n      const date = new Date(dateString);\n      return date.toLocaleString();\n    };\n\n    // 格式化百分比\n    const percentFormat = percentage => {\n      return `${percentage}%`;\n    };\n    return {\n      baseUrl,\n      teacherData,\n      examResults,\n      evaluations,\n      competencyData,\n      loading,\n      examLoading,\n      evaluationLoading,\n      activeTab,\n      evaluationDialogVisible,\n      currentEvaluation,\n      goBack,\n      editTeacher,\n      viewEvaluation,\n      formatDate,\n      percentFormat,\n      handleTabClick\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "nextTick", "useRoute", "useRouter", "ElMessage", "axios", "name", "setup", "route", "router", "teacherId", "params", "id", "token", "localStorage", "getItem", "defaults", "headers", "common", "baseUrl", "loading", "examLoading", "evaluationLoading", "teacher<PERSON><PERSON>", "examResults", "evaluations", "competencyData", "activeTab", "dataLoaded", "teacher", "exams", "competency", "evaluationDialogVisible", "currentEvaluation", "fetchTeacherData", "then", "setTimeout", "value", "fetchExamResults", "fetchCompetencyStatus", "fetchEvaluations", "response", "get", "data", "error", "console", "Array", "isArray", "handleTabClick", "tab", "props", "goBack", "push", "edit<PERSON><PERSON><PERSON>", "viewEvaluation", "evaluation", "formatDate", "dateString", "date", "Date", "toLocaleString", "percentFormat", "percentage"], "sources": ["D:\\admin\\202506\\督导系统\\后台管理系统\\ms\\src\\views\\teachers\\TeacherDetail.vue"], "sourcesContent": ["<template>\r\n  <div class=\"teacher-detail-container\">\r\n    <el-card class=\"box-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span class=\"title\">教师详情</span>\r\n          <div>\r\n            <el-button @click=\"goBack\">返回列表</el-button>\r\n       \r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- Use a single loading state for the whole component -->\r\n      <el-row :gutter=\"20\">\r\n        <!-- 左侧个人信息 -->\r\n        <el-col :span=\"8\" v-loading=\"loading\">\r\n          <div class=\"teacher-avatar\">\r\n            <el-image\r\n              v-if=\"teacherData.photo\"\r\n              :src=\"`${baseUrl}${teacherData.photo}`\"\r\n              fit=\"cover\"\r\n              class=\"avatar-image\"\r\n              :preview-src-list=\"[`${baseUrl}${teacherData.photo}`]\"\r\n              :preview-teleported=\"true\"\r\n            />\r\n            <el-avatar v-else :size=\"150\" icon=\"UserFilled\" />\r\n          </div>\r\n          \r\n          <el-descriptions title=\"基本信息\" direction=\"vertical\" :column=\"1\" border>\r\n            <el-descriptions-item label=\"姓名\">{{ teacherData.name || '--' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"性别\">{{ teacherData.gender || '--' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"科室\">{{ teacherData.department || '--' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"学校\">{{ teacherData.school || '--' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"专业\">{{ teacherData.major || '--' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"学历\">{{ teacherData.education || '--' }}</el-descriptions-item>\r\n            <el-descriptions-item label=\"在聘状态\">\r\n              <el-tag :type=\"teacherData.is_employed ? 'success' : 'danger'\" v-if=\"teacherData.is_employed !== undefined\">\r\n                {{ teacherData.is_employed ? '在聘' : '不在聘' }}\r\n              </el-tag>\r\n              <span v-else>--</span>\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"聘期\" v-if=\"teacherData.is_employed && teacherData.employment_period\">\r\n              {{ teacherData.employment_period }}\r\n            </el-descriptions-item>\r\n            <el-descriptions-item label=\"联系方式\" v-if=\"teacherData.phone\">\r\n              {{ teacherData.phone }}\r\n            </el-descriptions-item>\r\n          </el-descriptions>\r\n\r\n          <!-- 能力认证状态 -->\r\n          <el-card class=\"certification-card\" v-if=\"competencyData\">\r\n            <template #header>\r\n              <div class=\"card-header\">\r\n                <span>能力认证状态</span>\r\n              </div>\r\n            </template>\r\n            <div class=\"certification-status\">\r\n              <div class=\"certification-tag\">\r\n                <el-tag :type=\"competencyData.is_certified ? 'success' : 'info'\" size=\"large\">\r\n                  {{ competencyData.is_certified ? '已认证' : '未认证' }}\r\n                </el-tag>\r\n              </div>\r\n              <el-progress \r\n                :percentage=\"competencyData.approval_rate || 0\" \r\n                :status=\"competencyData.is_certified ? 'success' : ''\" \r\n                :stroke-width=\"18\"\r\n                :format=\"percentFormat\"\r\n              />\r\n              <div class=\"certification-stats\">\r\n                <div>总评价数: {{ competencyData.total_evaluations || 0 }}</div>\r\n                <div>认可数: {{ competencyData.approved_count || 0 }}</div>\r\n              </div>\r\n              <div class=\"certification-note\">\r\n                <i class=\"el-icon-info\"></i>\r\n                注：需要80%以上的督导评价认可才能获得认证\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n\r\n        <!-- 右侧选项卡 -->\r\n        <el-col :span=\"16\">\r\n          <el-tabs v-model=\"activeTab\" @tab-click=\"handleTabClick\">\r\n            <!-- 考试成绩 -->\r\n            <el-tab-pane label=\"考试成绩\" name=\"exams\">\r\n              <div class=\"tab-content\">\r\n                <div v-loading=\"examLoading\">\r\n                  <el-table\r\n                    v-if=\"examResults.length > 0\"\r\n                    :data=\"examResults\"\r\n                    border\r\n                    style=\"width: 100%\"\r\n                    :key=\"'exam-table'\"\r\n                  >\r\n                    <el-table-column prop=\"exam_title\" label=\"考试名称\" />\r\n                    <el-table-column prop=\"exam_type\" label=\"考试类型\" width=\"100\">\r\n                      <template #default=\"scope\">\r\n                        <el-tag :type=\"scope.row.exam_type === '资格认定考试' ? 'danger' : 'primary'\">\r\n                          {{ scope.row.exam_type }}\r\n                        </el-tag>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"score\" label=\"分数\" width=\"80\" />\r\n                    <el-table-column label=\"是否及格\" width=\"100\">\r\n                      <template #default=\"scope\">\r\n                        <el-tag :type=\"scope.row.score >= scope.row.pass_score ? 'success' : 'danger'\">\r\n                          {{ scope.row.score >= scope.row.pass_score ? '及格' : '不及格' }}\r\n                        </el-tag>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"exam_date\" label=\"考试时间\" width=\"180\">\r\n                      <template #default=\"scope\">\r\n                        {{ formatDate(scope.row.exam_date) }}\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n\r\n                  <div v-else-if=\"!examLoading\" class=\"empty-data\">\r\n                    暂无考试记录\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n\r\n            <!-- 教学评价 -->\r\n            <el-tab-pane label=\"教学评价\" name=\"evaluations\">\r\n              <div class=\"tab-content\">\r\n                <div v-loading=\"evaluationLoading\">\r\n                  <el-table\r\n                    v-if=\"evaluations.length > 0\"\r\n                    :data=\"evaluations\"\r\n                    border\r\n                    style=\"width: 100%\"\r\n                    :key=\"'evaluation-table'\"\r\n                  >\r\n                    <el-table-column prop=\"supervising_department\" label=\"督导教研室\" width=\"120\" />\r\n                    <el-table-column prop=\"case_topic\" label=\"病例/主题\" />\r\n                    <el-table-column prop=\"teaching_form\" label=\"教学活动形式\" width=\"120\" />\r\n                    <el-table-column prop=\"student_name\" label=\"学员姓名\" width=\"100\" />\r\n                    <el-table-column prop=\"student_type\" label=\"学员类别\" width=\"120\" />\r\n                    <el-table-column prop=\"average_score\" label=\"平均分\" width=\"80\" />\r\n                    <el-table-column label=\"能力认定\" width=\"100\">\r\n                      <template #default=\"scope\">\r\n                        <el-tag :type=\"scope.row.competency_approved ? 'success' : 'danger'\">\r\n                          {{ scope.row.competency_approved ? '同意' : '不同意' }}\r\n                        </el-tag>\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column prop=\"evaluation_date\" label=\"评价时间\" width=\"180\">\r\n                      <template #default=\"scope\">\r\n                        {{ formatDate(scope.row.evaluation_date) }}\r\n                      </template>\r\n                    </el-table-column>\r\n                    <el-table-column label=\"操作\" width=\"80\" fixed=\"right\">\r\n                      <template #default=\"scope\">\r\n                        <el-button size=\"small\" @click=\"viewEvaluation(scope.row)\">详情</el-button>\r\n                      </template>\r\n                    </el-table-column>\r\n                  </el-table>\r\n\r\n                  <div v-else-if=\"!evaluationLoading\" class=\"empty-data\">\r\n                    暂无教学评价记录\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-tab-pane>\r\n          </el-tabs>\r\n        </el-col>\r\n      </el-row>\r\n    </el-card>\r\n\r\n    <!-- 评价详情对话框 -->\r\n    <el-dialog \r\n      v-model=\"evaluationDialogVisible\" \r\n      title=\"评价详情\" \r\n      width=\"60%\" \r\n      :append-to-body=\"true\"\r\n      :destroy-on-close=\"true\"\r\n    >\r\n      <el-descriptions title=\"基本信息\" :column=\"2\" border>\r\n        <el-descriptions-item label=\"督导教研室\">{{ currentEvaluation.supervising_department }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"病例/主题\">{{ currentEvaluation.case_topic }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"教学活动形式\">{{ currentEvaluation.teaching_form }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"带教老师职称\">{{ currentEvaluation.teacher_title }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"学员姓名\">{{ currentEvaluation.student_name }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"学员类别\">{{ currentEvaluation.student_type }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"评估人\">{{ currentEvaluation.evaluator_name }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"评价时间\">{{ formatDate(currentEvaluation.evaluation_date) }}</el-descriptions-item>\r\n      </el-descriptions>\r\n\r\n      <el-divider />\r\n\r\n      <el-descriptions title=\"评价内容\" :column=\"1\" border>\r\n        <el-descriptions-item label=\"平均分\">{{ currentEvaluation.average_score }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"亮点\">{{ currentEvaluation.highlights || '无' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"不足\">{{ currentEvaluation.shortcomings || '无' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"改进建议\">{{ currentEvaluation.improvement_suggestions || '无' }}</el-descriptions-item>\r\n        <el-descriptions-item label=\"能力认定\">\r\n          <el-tag :type=\"currentEvaluation.competency_approved ? 'success' : 'danger'\">\r\n            {{ currentEvaluation.competency_approved ? '同意' : '不同意' }}\r\n          </el-tag>\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, onMounted, nextTick } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'TeacherDetail',\r\n  setup() {\r\n    const route = useRoute()\r\n    const router = useRouter()\r\n    const teacherId = route.params.id\r\n      let token = localStorage.getItem('token')\r\n    if (token) {\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`\r\n    }\r\n    // API基础URL\r\n    const baseUrl = 'http://localhost:3000'\r\n    \r\n    // 基础数据\r\n    const loading = ref(false)\r\n    const examLoading = ref(false)\r\n    const evaluationLoading = ref(false)\r\n    const teacherData = ref({})\r\n    const examResults = ref([])\r\n    const evaluations = ref([])\r\n    const competencyData = ref(null)\r\n    const activeTab = ref('exams')\r\n    const dataLoaded = ref({\r\n      teacher: false,\r\n      exams: false,\r\n      evaluations: false,\r\n      competency: false\r\n    })\r\n\r\n    // 评价详情相关\r\n    const evaluationDialogVisible = ref(false)\r\n    const currentEvaluation = ref({})\r\n    \r\n    // 生命周期钩子 - 使用分阶段加载来减少布局循环\r\n    onMounted(() => {\r\n      // 首先加载基本教师信息\r\n      fetchTeacherData().then(() => {\r\n        // 等教师数据加载完成后，再加载其他数据\r\n        setTimeout(() => {\r\n          // 优先加载当前选中的标签页数据\r\n          if (activeTab.value === 'exams') {\r\n            fetchExamResults()\r\n            setTimeout(() => {\r\n              fetchCompetencyStatus()\r\n            }, 100)\r\n            setTimeout(() => {\r\n              fetchEvaluations()\r\n            }, 200)\r\n          } else {\r\n            fetchEvaluations()\r\n            setTimeout(() => {\r\n              fetchCompetencyStatus()\r\n            }, 100)\r\n            setTimeout(() => {\r\n              fetchExamResults()\r\n            }, 200)\r\n          }\r\n        }, 100)\r\n      })\r\n    })\r\n    \r\n    // 获取教师信息\r\n    const fetchTeacherData = async () => {\r\n      loading.value = true\r\n      try {\r\n        const response = await axios.get(`${baseUrl}/api/teachers/${teacherId}`)\r\n        teacherData.value = response.data.data || {}\r\n        dataLoaded.value.teacher = true\r\n      } catch (error) {\r\n        console.error('获取教师信息失败:', error)\r\n        ElMessage.error('获取教师信息失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 获取考试成绩\r\n    const fetchExamResults = async () => {\r\n      if (activeTab.value !== 'exams' && dataLoaded.value.exams) return\r\n      \r\n      examLoading.value = true\r\n      try {\r\n        const response = await axios.get(`${baseUrl}/api/exams/teacher/${teacherId}/results`)\r\n        await nextTick()\r\n        examResults.value = Array.isArray(response.data.data) ? response.data.data : []\r\n        dataLoaded.value.exams = true\r\n      } catch (error) {\r\n        console.error('获取考试成绩失败:', error)\r\n        ElMessage.error('获取考试成绩失败')\r\n        examResults.value = []\r\n      } finally {\r\n        examLoading.value = false\r\n      }\r\n    }\r\n    \r\n    // 获取教学评价\r\n    const fetchEvaluations = async () => {\r\n      if (activeTab.value !== 'evaluations' && dataLoaded.value.evaluations) return\r\n      \r\n      evaluationLoading.value = true\r\n      try {\r\n        const response = await axios.get(`${baseUrl}/api/evaluations/teacher/${teacherId}`)\r\n        await nextTick()\r\n        evaluations.value = Array.isArray(response.data.data) ? response.data.data : []\r\n        dataLoaded.value.evaluations = true\r\n      } catch (error) {\r\n        console.error('获取教学评价失败:', error)\r\n        ElMessage.error('获取教学评价失败')\r\n        evaluations.value = []\r\n      } finally {\r\n        evaluationLoading.value = false\r\n      }\r\n    }\r\n    \r\n    // 获取能力认证状态\r\n    const fetchCompetencyStatus = async () => {\r\n      if (dataLoaded.value.competency) return\r\n      \r\n      try {\r\n        const response = await axios.get(`${baseUrl}/api/competency/teacher/${teacherId}`)\r\n        await nextTick()\r\n        competencyData.value = response.data.data || null\r\n        dataLoaded.value.competency = true\r\n      } catch (error) {\r\n        console.error('获取能力认证状态失败:', error)\r\n        // 这里不显示错误信息，因为有些教师可能没有能力认证记录\r\n      }\r\n    }\r\n    \r\n    // 标签页切换处理\r\n    const handleTabClick = (tab) => {\r\n      if (tab.props.name === 'exams' && !dataLoaded.value.exams) {\r\n        fetchExamResults()\r\n      } else if (tab.props.name === 'evaluations' && !dataLoaded.value.evaluations) {\r\n        fetchEvaluations()\r\n      }\r\n    }\r\n    \r\n    // 返回列表页\r\n    const goBack = () => {\r\n      router.push('/teachers/list')\r\n    }\r\n    \r\n    // 编辑教师\r\n    const editTeacher = () => {\r\n      router.push(`/teachers/edit/${teacherId}`)\r\n    }\r\n    \r\n    // 查看评价详情\r\n    const viewEvaluation = (evaluation) => {\r\n      currentEvaluation.value = evaluation\r\n      // 使用nextTick确保DOM更新后再显示对话框\r\n      nextTick(() => {\r\n        evaluationDialogVisible.value = true\r\n      })\r\n    }\r\n    \r\n    // 格式化日期\r\n    const formatDate = (dateString) => {\r\n      if (!dateString) return '--'\r\n      const date = new Date(dateString)\r\n      return date.toLocaleString()\r\n    }\r\n    \r\n    // 格式化百分比\r\n    const percentFormat = (percentage) => {\r\n      return `${percentage}%`\r\n    }\r\n    \r\n    return {\r\n      baseUrl,\r\n      teacherData,\r\n      examResults,\r\n      evaluations,\r\n      competencyData,\r\n      loading,\r\n      examLoading,\r\n      evaluationLoading,\r\n      activeTab,\r\n      evaluationDialogVisible,\r\n      currentEvaluation,\r\n      goBack,\r\n      editTeacher,\r\n      viewEvaluation,\r\n      formatDate,\r\n      percentFormat,\r\n      handleTabClick\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.teacher-detail-container {\r\n  padding: 20px;\r\n  /* 防止容器尺寸变化导致的重绘问题 */\r\n  min-height: 400px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.teacher-avatar {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-bottom: 20px;\r\n  /* 固定尺寸以减少重布局 */\r\n  height: 170px;\r\n}\r\n\r\n.avatar-image {\r\n  width: 150px;\r\n  height: 150px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n  border: 2px solid #eee;\r\n}\r\n\r\n.tab-content {\r\n  margin-top: 10px;\r\n  /* 设置最小高度避免内容变化时的跳动 */\r\n  min-height: 200px;\r\n}\r\n\r\n.empty-data {\r\n  padding: 30px;\r\n  text-align: center;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.certification-card {\r\n  margin-top: 20px;\r\n}\r\n\r\n.certification-status {\r\n  text-align: center;\r\n  padding: 10px;\r\n}\r\n\r\n.certification-tag {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.certification-stats {\r\n  margin-top: 10px;\r\n  display: flex;\r\n  justify-content: space-around;\r\n  color: #606266;\r\n  font-size: 14px;\r\n}\r\n\r\n.certification-note {\r\n  margin-top: 15px;\r\n  color: #909399;\r\n  font-size: 12px;\r\n}\r\n\r\n.el-descriptions {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n/* 解决表格重绘问题 */\r\n.el-table {\r\n  width: 100% !important;\r\n}\r\n</style> "], "mappings": ";AAiNA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAO,QAAS,KAAI;AACvD,SAASC,QAAQ,EAAEC,SAAQ,QAAS,YAAW;AAC/C,SAASC,SAAQ,QAAS,cAAa;AACvC,OAAOC,KAAI,MAAO,OAAM;AAExB,eAAe;EACbC,IAAI,EAAE,eAAe;EACrBC,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIN,QAAQ,CAAC;IACvB,MAAMO,MAAK,GAAIN,SAAS,CAAC;IACzB,MAAMO,SAAQ,GAAIF,KAAK,CAACG,MAAM,CAACC,EAAC;IAC9B,IAAIC,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,OAAO;IAC1C,IAAIF,KAAK,EAAE;MACTR,KAAK,CAACW,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,IAAI,UAAUL,KAAK,EAAC;IACnE;IACA;IACA,MAAMM,OAAM,GAAI,uBAAsB;;IAEtC;IACA,MAAMC,OAAM,GAAItB,GAAG,CAAC,KAAK;IACzB,MAAMuB,WAAU,GAAIvB,GAAG,CAAC,KAAK;IAC7B,MAAMwB,iBAAgB,GAAIxB,GAAG,CAAC,KAAK;IACnC,MAAMyB,WAAU,GAAIzB,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAM0B,WAAU,GAAI1B,GAAG,CAAC,EAAE;IAC1B,MAAM2B,WAAU,GAAI3B,GAAG,CAAC,EAAE;IAC1B,MAAM4B,cAAa,GAAI5B,GAAG,CAAC,IAAI;IAC/B,MAAM6B,SAAQ,GAAI7B,GAAG,CAAC,OAAO;IAC7B,MAAM8B,UAAS,GAAI9B,GAAG,CAAC;MACrB+B,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE,KAAK;MACZL,WAAW,EAAE,KAAK;MAClBM,UAAU,EAAE;IACd,CAAC;;IAED;IACA,MAAMC,uBAAsB,GAAIlC,GAAG,CAAC,KAAK;IACzC,MAAMmC,iBAAgB,GAAInC,GAAG,CAAC,CAAC,CAAC;;IAEhC;IACAE,SAAS,CAAC,MAAM;MACd;MACAkC,gBAAgB,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QAC5B;QACAC,UAAU,CAAC,MAAM;UACf;UACA,IAAIT,SAAS,CAACU,KAAI,KAAM,OAAO,EAAE;YAC/BC,gBAAgB,CAAC;YACjBF,UAAU,CAAC,MAAM;cACfG,qBAAqB,CAAC;YACxB,CAAC,EAAE,GAAG;YACNH,UAAU,CAAC,MAAM;cACfI,gBAAgB,CAAC;YACnB,CAAC,EAAE,GAAG;UACR,OAAO;YACLA,gBAAgB,CAAC;YACjBJ,UAAU,CAAC,MAAM;cACfG,qBAAqB,CAAC;YACxB,CAAC,EAAE,GAAG;YACNH,UAAU,CAAC,MAAM;cACfE,gBAAgB,CAAC;YACnB,CAAC,EAAE,GAAG;UACR;QACF,CAAC,EAAE,GAAG;MACR,CAAC;IACH,CAAC;;IAED;IACA,MAAMJ,gBAAe,GAAI,MAAAA,CAAA,KAAY;MACnCd,OAAO,CAACiB,KAAI,GAAI,IAAG;MACnB,IAAI;QACF,MAAMI,QAAO,GAAI,MAAMpC,KAAK,CAACqC,GAAG,CAAC,GAAGvB,OAAO,iBAAiBT,SAAS,EAAE;QACvEa,WAAW,CAACc,KAAI,GAAII,QAAQ,CAACE,IAAI,CAACA,IAAG,IAAK,CAAC;QAC3Cf,UAAU,CAACS,KAAK,CAACR,OAAM,GAAI,IAAG;MAChC,EAAE,OAAOe,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChCxC,SAAS,CAACwC,KAAK,CAAC,UAAU;MAC5B,UAAU;QACRxB,OAAO,CAACiB,KAAI,GAAI,KAAI;MACtB;IACF;;IAEA;IACA,MAAMC,gBAAe,GAAI,MAAAA,CAAA,KAAY;MACnC,IAAIX,SAAS,CAACU,KAAI,KAAM,OAAM,IAAKT,UAAU,CAACS,KAAK,CAACP,KAAK,EAAE;MAE3DT,WAAW,CAACgB,KAAI,GAAI,IAAG;MACvB,IAAI;QACF,MAAMI,QAAO,GAAI,MAAMpC,KAAK,CAACqC,GAAG,CAAC,GAAGvB,OAAO,sBAAsBT,SAAS,UAAU;QACpF,MAAMT,QAAQ,CAAC;QACfuB,WAAW,CAACa,KAAI,GAAIS,KAAK,CAACC,OAAO,CAACN,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACA,IAAG,GAAI,EAAC;QAC9Ef,UAAU,CAACS,KAAK,CAACP,KAAI,GAAI,IAAG;MAC9B,EAAE,OAAOc,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChCxC,SAAS,CAACwC,KAAK,CAAC,UAAU;QAC1BpB,WAAW,CAACa,KAAI,GAAI,EAAC;MACvB,UAAU;QACRhB,WAAW,CAACgB,KAAI,GAAI,KAAI;MAC1B;IACF;;IAEA;IACA,MAAMG,gBAAe,GAAI,MAAAA,CAAA,KAAY;MACnC,IAAIb,SAAS,CAACU,KAAI,KAAM,aAAY,IAAKT,UAAU,CAACS,KAAK,CAACZ,WAAW,EAAE;MAEvEH,iBAAiB,CAACe,KAAI,GAAI,IAAG;MAC7B,IAAI;QACF,MAAMI,QAAO,GAAI,MAAMpC,KAAK,CAACqC,GAAG,CAAC,GAAGvB,OAAO,4BAA4BT,SAAS,EAAE;QAClF,MAAMT,QAAQ,CAAC;QACfwB,WAAW,CAACY,KAAI,GAAIS,KAAK,CAACC,OAAO,CAACN,QAAQ,CAACE,IAAI,CAACA,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACA,IAAG,GAAI,EAAC;QAC9Ef,UAAU,CAACS,KAAK,CAACZ,WAAU,GAAI,IAAG;MACpC,EAAE,OAAOmB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChCxC,SAAS,CAACwC,KAAK,CAAC,UAAU;QAC1BnB,WAAW,CAACY,KAAI,GAAI,EAAC;MACvB,UAAU;QACRf,iBAAiB,CAACe,KAAI,GAAI,KAAI;MAChC;IACF;;IAEA;IACA,MAAME,qBAAoB,GAAI,MAAAA,CAAA,KAAY;MACxC,IAAIX,UAAU,CAACS,KAAK,CAACN,UAAU,EAAE;MAEjC,IAAI;QACF,MAAMU,QAAO,GAAI,MAAMpC,KAAK,CAACqC,GAAG,CAAC,GAAGvB,OAAO,2BAA2BT,SAAS,EAAE;QACjF,MAAMT,QAAQ,CAAC;QACfyB,cAAc,CAACW,KAAI,GAAII,QAAQ,CAACE,IAAI,CAACA,IAAG,IAAK,IAAG;QAChDf,UAAU,CAACS,KAAK,CAACN,UAAS,GAAI,IAAG;MACnC,EAAE,OAAOa,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK;QAClC;MACF;IACF;;IAEA;IACA,MAAMI,cAAa,GAAKC,GAAG,IAAK;MAC9B,IAAIA,GAAG,CAACC,KAAK,CAAC5C,IAAG,KAAM,OAAM,IAAK,CAACsB,UAAU,CAACS,KAAK,CAACP,KAAK,EAAE;QACzDQ,gBAAgB,CAAC;MACnB,OAAO,IAAIW,GAAG,CAACC,KAAK,CAAC5C,IAAG,KAAM,aAAY,IAAK,CAACsB,UAAU,CAACS,KAAK,CAACZ,WAAW,EAAE;QAC5Ee,gBAAgB,CAAC;MACnB;IACF;;IAEA;IACA,MAAMW,MAAK,GAAIA,CAAA,KAAM;MACnB1C,MAAM,CAAC2C,IAAI,CAAC,gBAAgB;IAC9B;;IAEA;IACA,MAAMC,WAAU,GAAIA,CAAA,KAAM;MACxB5C,MAAM,CAAC2C,IAAI,CAAC,kBAAkB1C,SAAS,EAAE;IAC3C;;IAEA;IACA,MAAM4C,cAAa,GAAKC,UAAU,IAAK;MACrCtB,iBAAiB,CAACI,KAAI,GAAIkB,UAAS;MACnC;MACAtD,QAAQ,CAAC,MAAM;QACb+B,uBAAuB,CAACK,KAAI,GAAI,IAAG;MACrC,CAAC;IACH;;IAEA;IACA,MAAMmB,UAAS,GAAKC,UAAU,IAAK;MACjC,IAAI,CAACA,UAAU,EAAE,OAAO,IAAG;MAC3B,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU;MAChC,OAAOC,IAAI,CAACE,cAAc,CAAC;IAC7B;;IAEA;IACA,MAAMC,aAAY,GAAKC,UAAU,IAAK;MACpC,OAAO,GAAGA,UAAU,GAAE;IACxB;IAEA,OAAO;MACL3C,OAAO;MACPI,WAAW;MACXC,WAAW;MACXC,WAAW;MACXC,cAAc;MACdN,OAAO;MACPC,WAAW;MACXC,iBAAiB;MACjBK,SAAS;MACTK,uBAAuB;MACvBC,iBAAiB;MACjBkB,MAAM;MACNE,WAAW;MACXC,cAAc;MACdE,UAAU;MACVK,aAAa;MACbb;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}