{"version": 3, "file": "js/258.477337aa.js", "mappings": "iLACOA,MAAM,2B,GAGAA,MAAM,e,GAORA,MAAM,oB,GACJA,MAAM,e,GASNA,MAAM,kB,SAQ0CA,MAAM,c,SA4BjBA,MAAM,gB,mBAwB3CA,MAAM,wB,GA4ELA,MAAM,iB,ocA5JlBC,EAAAA,EAAAA,IAkKM,MAlKNC,EAkKM,EAjKJC,EAAAA,EAAAA,IA2FUC,EAAA,CA3FDJ,MAAM,YAAU,CACZK,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNC,EAAAA,EAAAA,IAGM,MAHNC,EAGM,C,eAFJD,EAAAA,EAAAA,IAA+B,QAAzBP,MAAM,SAAQ,QAAI,IAC8BS,EAAAC,U,WAAtDC,EAAAA,EAAAA,IAAiFC,EAAA,C,MAAtEC,KAAK,UAAWC,QAAKC,EAAA,KAAAA,EAAA,GAAAC,GAAEP,EAAAQ,e,kBAA6B,IAAMF,EAAA,KAAAA,EAAA,K,QAAN,a,mDAKnE,IAcM,EAdNR,EAAAA,EAAAA,IAcM,MAdNW,EAcM,EAbJX,EAAAA,EAAAA,IAQM,MARNY,EAQM,C,eAPJZ,EAAAA,EAAAA,IAAsC,QAAhCP,MAAM,gBAAe,QAAI,KAC/BG,EAAAA,EAAAA,IAKYiB,EAAA,C,WALQX,EAAAY,WAAWC,Y,qCAAXb,EAAAY,WAAWC,YAAWN,GAAEO,YAAY,SAASC,UAAA,GAAUxB,MAAM,iB,kBAC/E,IAAqC,EAArCG,EAAAA,EAAAA,IAAqCsB,EAAA,CAA1BC,MAAM,MAAMC,MAAM,SAC7BxB,EAAAA,EAAAA,IAA2CsB,EAAA,CAAhCC,MAAM,SAASC,MAAM,YAChCxB,EAAAA,EAAAA,IAAuCsB,EAAA,CAA5BC,MAAM,OAAOC,MAAM,UAC9BxB,EAAAA,EAAAA,IAAmCsB,EAAA,CAAxBC,MAAM,KAAKC,MAAM,S,0BAGhCpB,EAAAA,EAAAA,IAGM,MAHNqB,EAGM,EAFJzB,EAAAA,EAAAA,IAA8DS,EAAA,CAAnDC,KAAK,UAAWC,QAAOL,EAAAoB,c,kBAAc,IAAEd,EAAA,MAAAA,EAAA,M,QAAF,S,6BAChDZ,EAAAA,EAAAA,IAA8CS,EAAA,CAAlCE,QAAOL,EAAAqB,aAAW,C,iBAAE,IAAEf,EAAA,MAAAA,EAAA,M,QAAF,S,sDAKpCd,EAAAA,EAAAA,IAgEM,YA/DkC,IAA3BQ,EAAAsB,gBAAgBC,QAAiBvB,EAAAwB,U,WAI5CtB,EAAAA,EAAAA,IA6CWuB,EAAA,C,MA3CRC,KAAM1B,EAAAsB,gBACPK,MAAA,eACAC,OAAA,I,kBAEA,IAAmF,EAAnFlC,EAAAA,EAAAA,IAAmFmC,EAAA,CAAlEC,KAAK,QAAQb,MAAM,OAAO,YAAU,MAAM,8BAE3DvB,EAAAA,EAAAA,IAIkBmC,EAAA,CAJDC,KAAK,cAAcb,MAAM,OAAOc,MAAM,MAAMC,MAAM,U,CACtDC,SAAOpC,EAAAA,EAAAA,IACsEqC,GAD/D,EACvBxC,EAAAA,EAAAA,IAAsFyC,EAAA,CAA7E/B,KAAMJ,EAAAoC,WAAWF,EAAMG,IAAIxB,c,kBAAc,IAA2B,E,iBAAxBqB,EAAMG,IAAIxB,aAAW,K,6BAI9EnB,EAAAA,EAAAA,IAAyFmC,EAAA,CAAxEC,KAAK,cAAcb,MAAM,OAAO,YAAU,MAAM,8BAEjEvB,EAAAA,EAAAA,IAIkBmC,EAAA,CAJDC,KAAK,aAAab,MAAM,OAAOc,MAAM,MAAMC,MAAM,U,CACrDC,SAAOpC,EAAAA,EAAAA,IACsBqC,GADf,E,iBACpBlC,EAAAsC,WAAWJ,EAAMG,IAAIE,aAAU,K,OAItC7C,EAAAA,EAAAA,IAekBmC,EAAA,CAfDZ,MAAM,OAAOc,MAAM,MAAMC,MAAM,U,CACnCC,SAAOpC,EAAAA,EAAAA,IAWVqC,GAXiB,CACZA,EAAMG,IAAIG,gB,WAArBhD,EAAAA,EAAAA,IAUM,MAVNiD,EAUM,EATJ/C,EAAAA,EAAAA,IAOYS,EAAA,CANVC,KAAK,UACLsC,KAAA,GACCrC,QAAKE,GAAEP,EAAA2C,YAAYT,EAAMG,KAC1BO,MAAM,Q,kBAEN,IAA+B,EAA/BlD,EAAAA,EAAAA,IAA+BmD,EAAA,M,iBAAtB,IAAY,EAAZnD,EAAAA,EAAAA,IAAYoD,K,eAAU,KAACC,EAAAA,EAAAA,IAAGb,EAAMG,IAAIW,mBAAiB,K,uCAIlExD,EAAAA,EAAAA,IAAuB,OAAAyD,EAAV,U,OAIjBvD,EAAAA,EAAAA,IAKkBmC,EAAA,CALDZ,MAAM,KAAKc,MAAM,MAAMC,MAAM,SAASkB,MAAM,S,CAChDjB,SAAOpC,EAAAA,EAAAA,IACyEqC,GADlE,EACvBxC,EAAAA,EAAAA,IAAyFS,EAAA,CAA9EC,KAAK,UAAUsC,KAAA,GAAKS,KAAK,QAAS9C,QAAKE,GAAEP,EAAAQ,WAAW0B,EAAMG,M,kBAAM,IAAE/B,EAAA,MAAAA,EAAA,M,QAAF,S,gCAC3EZ,EAAAA,EAAAA,IAA0FS,EAAA,CAA/EC,KAAK,SAASsC,KAAA,GAAKS,KAAK,QAAS9C,QAAKE,GAAEP,EAAAoD,aAAalB,EAAMG,M,kBAAM,IAAE/B,EAAA,MAAAA,EAAA,M,QAAF,S,wEA9ClFd,EAAAA,EAAAA,IAEM,MAFN6D,EAAwE,oBAoDhCrD,EAAAsB,gBAAgBC,OAAS,I,WAAjE/B,EAAAA,EAAAA,IAUM,MAVN8D,EAUM,EATJ5D,EAAAA,EAAAA,IAQE6D,EAAA,CAPQ,eAAcvD,EAAAwD,Y,sCAAAxD,EAAAwD,YAAWjD,GACzB,YAAWP,EAAAyD,S,mCAAAzD,EAAAyD,SAAQlD,GAC1B,aAAY,CAAC,GAAI,GAAI,GAAI,IAC1BmD,OAAO,0CACNC,MAAO3D,EAAA2D,MACPC,aAAa5D,EAAA6D,iBACbC,gBAAgB9D,EAAA+D,qB,wGA7DP/D,EAAAwB,a,OAoElB9B,EAAAA,EAAAA,IAkEYsE,EAAA,C,WAjEDhE,EAAAiE,c,qCAAAjE,EAAAiE,cAAa1D,GACrBqC,MAAO5C,EAAAkE,SAASC,GAAK,SAAW,SACjCpC,MAAM,MACN,uB,CAwDWqC,QAAMvE,EAAAA,EAAAA,IACf,IAGO,EAHPC,EAAAA,EAAAA,IAGO,OAHPuE,EAGO,EAFL3E,EAAAA,EAAAA,IAAwDS,EAAA,CAA5CE,QAAKC,EAAA,KAAAA,EAAA,GAAAC,GAAEP,EAAAiE,eAAgB,I,kBAAO,IAAE3D,EAAA,MAAAA,EAAA,M,QAAF,S,eAC1CZ,EAAAA,EAAAA,IAAkFS,EAAA,CAAvEC,KAAK,UAAWC,QAAOL,EAAAsE,WAAa9C,QAASxB,EAAAuE,Y,kBAAY,IAAEjE,EAAA,MAAAA,EAAA,M,QAAF,S,2DAzDxE,IAoDU,EApDVZ,EAAAA,EAAAA,IAoDU8E,EAAA,CAnDRC,IAAI,gBACHC,MAAO1E,EAAAkE,SACPS,MAAO3E,EAAA4E,UACR,cAAY,QACZ,iBAAe,S,kBAEf,IAEe,EAFflF,EAAAA,EAAAA,IAEemF,EAAA,CAFD5D,MAAM,OAAOa,KAAK,S,kBAC9B,IAA2D,EAA3DpC,EAAAA,EAAAA,IAA2DoF,EAAA,C,WAAxC9E,EAAAkE,SAAStB,M,qCAAT5C,EAAAkE,SAAStB,MAAKrC,GAAEO,YAAY,W,gCAGjDpB,EAAAA,EAAAA,IAOemF,EAAA,CAPD5D,MAAM,OAAOa,KAAK,e,kBAC9B,IAKY,EALZpC,EAAAA,EAAAA,IAKYiB,EAAA,C,WALQX,EAAAkE,SAASrD,Y,qCAATb,EAAAkE,SAASrD,YAAWN,GAAEO,YAAY,SAASa,MAAA,gB,kBAC7D,IAAqC,EAArCjC,EAAAA,EAAAA,IAAqCsB,EAAA,CAA1BC,MAAM,MAAMC,MAAM,SAC7BxB,EAAAA,EAAAA,IAA2CsB,EAAA,CAAhCC,MAAM,SAASC,MAAM,YAChCxB,EAAAA,EAAAA,IAAuCsB,EAAA,CAA5BC,MAAM,OAAOC,MAAM,UAC9BxB,EAAAA,EAAAA,IAAmCsB,EAAA,CAAxBC,MAAM,KAAKC,MAAM,S,gCAIhCxB,EAAAA,EAAAA,IAOemF,EAAA,CAPD5D,MAAM,OAAOa,KAAK,e,kBAC9B,IAKE,EALFpC,EAAAA,EAAAA,IAKEoF,EAAA,C,WAJS9E,EAAAkE,SAASa,Y,qCAAT/E,EAAAkE,SAASa,YAAWxE,GAC7BH,KAAK,WACL4E,KAAK,IACLlE,YAAY,W,gCAIhBpB,EAAAA,EAAAA,IAsBemF,EAAA,CAtBD5D,MAAM,SAAO,C,iBACzB,IAoBY,EApBZvB,EAAAA,EAAAA,IAoBYuF,EAAA,CAnBV1F,MAAM,oBACN2F,KAAA,GACAC,OAAO,IACN,eAAa,EACb,YAAWnF,EAAAoF,iBACX,YAAWpF,EAAAqF,aACX,gBAAerF,EAAAsF,qBACf,YAAWtF,EAAAuF,SACXC,MAAO,G,CAMGC,KAAG5F,EAAAA,EAAAA,IACZ,IAEMS,EAAA,MAAAA,EAAA,MAFNR,EAAAA,EAAAA,IAEM,OAFDP,MAAM,kBAAiB,mCAE5B,M,iBAPF,IAAqD,EAArDG,EAAAA,EAAAA,IAAqDmD,EAAA,CAA5CtD,MAAM,mBAAiB,C,iBAAC,IAAU,EAAVG,EAAAA,EAAAA,IAAUgG,K,qBAC3C5F,EAAAA,EAAAA,IAEM,OAFDP,MAAM,mBAAiB,E,QAAC,eAClBO,EAAAA,EAAAA,IAAa,UAAT,U,iOCzIpB,MAAM6F,EAAkBC,MAAOC,EAAS,CAAC,KAC9C,IACE,MAAMC,QAAiBC,EAAAA,EAAMC,IAAI,GAAGC,EAAAA,cAAqB,CAAEJ,WAC3D,OAAOC,EAASpE,IAClB,CAAE,MAAOwE,GAEP,MADAC,QAAQD,MAAM,cAAeA,GACvBA,CACR,GASWE,EAAqBR,MAAOxF,EAAMyF,EAAS,CAAC,KACvD,IACE,MAAMC,QAAiBC,EAAAA,EAAMC,IAAI,GAAGC,EAAAA,oBAA0BI,mBAAmBjG,KAAS,CAAEyF,WAC5F,OAAOC,EAASpE,IAClB,CAAE,MAAOwE,GAEP,MADAC,QAAQD,MAAM,KAAK9F,aAAiB8F,GAC9BA,CACR,GAwBWI,EAAiBV,MAAOW,EAAcC,KACjD,IACE,MAAMtC,EAAW,IAAIuC,SAGrBvC,EAASwC,OAAO,QAASH,EAAa3D,OACtCsB,EAASwC,OAAO,cAAeH,EAAaxB,aAAe,IAC3Db,EAASwC,OAAO,cAAeH,EAAa1F,aAGxC2F,GACFtC,EAASwC,OAAO,WAAYF,GAG9B,MAAMV,QAAiBC,EAAAA,EAAMY,KAAK,GAAGV,EAAAA,cAAqB/B,EAAU,CAClE0C,QAAS,CACP,eAAgB,yBAIpB,OAAOd,EAASpE,IAClB,CAAE,MAAOwE,GAEP,MADAC,QAAQD,MAAM,YAAaA,GACrBA,CACR,GAUWW,EAAiBjB,MAAOzB,EAAIoC,EAAcC,KACrD,IACE,MAAMtC,EAAW,IAAIuC,SAGrBvC,EAASwC,OAAO,QAASH,EAAa3D,OACtCsB,EAASwC,OAAO,cAAeH,EAAaxB,aAAe,IAC3Db,EAASwC,OAAO,cAAeH,EAAa1F,aAGxC2F,EACFtC,EAASwC,OAAO,WAAYF,GAGrBD,EAAa/D,eAAiB+D,EAAavD,oBAClDkB,EAASwC,OAAO,gBAAiBH,EAAa/D,eAC9C0B,EAASwC,OAAO,oBAAqBH,EAAavD,oBAGpD,MAAM8C,QAAiBC,EAAAA,EAAMe,IAAI,GAAGb,EAAAA,eAAqB9B,IAAMD,EAAU,CACvE0C,QAAS,CACP,eAAgB,yBAIpB,OAAOd,EAASpE,IAClB,CAAE,MAAOwE,GAEP,MADAC,QAAQD,MAAM,YAAaA,GACrBA,CACR,GAQWa,EAAiBnB,UAC5B,IACE,MAAME,QAAiBC,EAAAA,EAAMiB,OAAO,GAAGf,EAAAA,eAAqB9B,KAC5D,OAAO2B,EAASpE,IAClB,CAAE,MAAOwE,GAEP,MADAC,QAAQD,MAAM,YAAaA,GACrBA,CACR,GDyCF,OACEe,KAAM,eACNC,WAAY,CACVC,SAAQ,WACRC,OAAM,SACNC,KAAI,OACJC,SAAQA,EAAAA,UAEVC,KAAAA,GAEE,MAAM/F,GAAUiD,EAAAA,EAAAA,KAAI,GACdF,GAAaE,EAAAA,EAAAA,KAAI,GACjBnD,GAAkBmD,EAAAA,EAAAA,IAAI,IACtBR,GAAgBQ,EAAAA,EAAAA,KAAI,GACpB+C,GAAgB/C,EAAAA,EAAAA,IAAI,MACpBd,GAAQc,EAAAA,EAAAA,IAAI,GACZjB,GAAciB,EAAAA,EAAAA,IAAI,GAClBhB,GAAWgB,EAAAA,EAAAA,IAAI,IACfc,GAAWd,EAAAA,EAAAA,IAAI,IACfgD,GAAahD,EAAAA,EAAAA,IAAI,MACvB,IAAIiD,EAAWC,aAAaC,QAAQ,YAAcC,KAAKC,MAAMH,aAAaC,QAAQ,aAAc,GAEpG,MAAM3H,GAAUwE,EAAAA,EAAAA,IAAsB,SAAlBiD,GAAUK,MAEpBnH,GAAaoH,EAAAA,EAAAA,IAAS,CAC1BnH,YAAa,KAITqD,GAAW8D,EAAAA,EAAAA,IAAS,CACxB7D,GAAI,GACJvB,MAAO,GACP/B,YAAa,GACbkE,YAAa,GACbvC,cAAe,GACfQ,kBAAmB,KAIf4B,EAAY,CAChBhC,MAAO,CACL,CAAEqF,UAAU,EAAMC,QAAS,UAAWC,QAAS,QAC/C,CAAEC,IAAK,EAAGC,IAAK,IAAKH,QAAS,kBAAmBC,QAAS,SAE3DtH,YAAa,CACX,CAAEoH,UAAU,EAAMC,QAAS,UAAWC,QAAS,aAKnDG,EAAAA,EAAAA,IAAU,KACRC,MAIF,MAAMA,EAAuB3C,UAC3BpE,EAAQN,OAAQ,EAChB,IACE,IAAI4E,EACJ,MAAMD,EAAS,CACb2C,KAAMhF,EAAYtC,MAClBsE,MAAO/B,EAASvC,OAIhB4E,EADElF,EAAWC,kBACI4H,EAAmC7H,EAAWC,YAAagF,SAE3D4C,EAAgC5C,GAGnDvE,EAAgBJ,MAAQ4E,EAASpE,KACjCiC,EAAMzC,MAAQ4E,EAAS4C,KACzB,CAAE,MAAOxC,GACPC,QAAQD,MAAM,YAAaA,GAC3ByC,EAAAA,GAAUzC,MAAM,WAClB,CAAE,QACA1E,EAAQN,OAAQ,CAClB,GAIIE,EAAeA,KACnBoC,EAAYtC,MAAQ,EACpBqH,KAIIlH,EAAcA,KAClBT,EAAWC,YAAc,GACzBO,KAIIyC,EAAoB+E,IACxBnF,EAASvC,MAAQ0H,EACjBL,KAGIxE,EAAuB6E,IAC3BpF,EAAYtC,MAAQ0H,EACpBL,KAIIjD,EAAwBkB,IAC5B,MAAMqC,EAAWrC,EAAKrD,KAAO,KAAO,KAAO,IAM3C,OAJK0F,GACHF,EAAAA,GAAUzC,MAAM,qBAGX2C,GAIHzD,EAAoBoB,IACxBiB,EAAWvG,MAAQsF,EAAKsC,KAIpBzD,EAAeA,KACnBnB,EAAS1B,cAAgB,GACzB0B,EAASlB,kBAAoB,GAC7BuC,EAASrE,MAAQ,GACjBuG,EAAWvG,MAAQ,MAIfV,EAAcuI,IACdA,GAEFC,OAAOC,KAAK/E,GAAUgF,QAAQC,IAC5BjF,EAASiF,GAAOJ,EAAOI,KAIzB5D,EAASrE,MAAQ,GACb6H,EAAOvG,eAAiBuG,EAAO/F,oBACjCuC,EAASrE,MAAQ,CACf,CACE+F,KAAMmC,mBAAmBL,EAAO/F,mBAChCqG,IAAK,0BAA0BN,EAAOvG,qBAM5CwG,OAAOC,KAAK/E,GAAUgF,QAAQC,IAC5BjF,EAASiF,GAAO,KAElB5D,EAASrE,MAAQ,GACjBuG,EAAWvG,MAAQ,MAGrB+C,EAAc/C,OAAQ,GAIlBoD,EAAasB,UACZ4B,EAActG,aAEbsG,EAActG,MAAMoI,SAAS1D,UACjC,IAAI2D,EAwBF,OAAO,EAvBPhF,EAAWrD,OAAQ,EAEnB,IAEMgD,EAASC,UAELsE,EAA+BvE,EAASC,GAAID,EAAUuD,EAAWvG,OACvEyH,EAAAA,GAAUa,QAAQ,kBAGZf,EAA+BvE,EAAUuD,EAAWvG,OAC1DyH,EAAAA,GAAUa,QAAQ,WAGpBvF,EAAc/C,OAAQ,EACtBqH,GACF,CAAE,MAAOrC,GACPC,QAAQD,MAAM,QAASA,GACvByC,EAAAA,GAAUzC,MAAM,UAAYA,EAAMJ,UAAUpE,MAAMwG,SAAWhC,EAAMgC,SACrE,CAAE,QACA3D,EAAWrD,OAAQ,CACrB,KAQAkC,EAAgB2F,IACpBU,EAAAA,EAAaC,QACX,cAAcX,EAAOnG,YACrB,KACA,CACE+G,kBAAmB,KACnBC,iBAAkB,KAClBxJ,KAAM,YAGPyJ,KAAKjE,UACJ,UACQ6C,EAA+BM,EAAO5E,IAC5CwE,EAAAA,GAAUa,QAAQ,QAClBjB,GACF,CAAE,MAAOrC,GACPC,QAAQD,MAAM,QAASA,GACvByC,EAAAA,GAAUzC,MAAM,OAClB,IAED4D,MAAM,KACLnB,EAAAA,GAAUoB,KAAK,YAKfC,EAAgBA,CAACC,EAASlB,KACd,SAAZkB,EACFzJ,EAAWuI,GACU,WAAZkB,GACT7G,EAAa2F,IAKX3G,EAAchC,IAClB,OAAQA,GACN,IAAK,MACH,MAAO,UACT,IAAK,SACH,MAAO,UACT,IAAK,OACH,MAAO,SACT,QACE,MAAO,SAKPkC,EAAc4H,IAClB,IAAKA,EAAY,MAAO,IACxB,MAAMC,EAAO,IAAIC,KAAKF,GACtB,MAAO,GAAGC,EAAKE,kBAAkBF,EAAKG,WAAa,GAAGC,WAAWC,SAAS,EAAG,QAAQL,EAAKM,UAAUF,WAAWC,SAAS,EAAG,QAIvH7H,EAAeoG,IACnB2B,OAAOC,KAAK,0BAA0B5B,EAAOvG,gBAAiB,WAGhE,MAAO,CACLhB,UACA+C,aACAjD,kBACA2C,gBACAuD,gBACAtD,WACAU,YACAhE,aACA2E,WACAkC,aACAjE,cACAC,WACAE,QACA1D,UACAmB,eACAC,cACAwC,mBACAE,sBACAvD,aACA8D,aACAgB,uBACAF,mBACAC,eACA2E,gBACA5G,eACAhB,aACAE,aACAK,cAEJ,G,UEjcF,MAAMiI,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://ms/./src/views/trainings/TrainingList.vue", "webpack://ms/./src/services/trainingService.js", "webpack://ms/./src/views/trainings/TrainingList.vue?5990"], "sourcesContent": ["<template>\r\n  <div class=\"training-list-container\">\r\n    <el-card class=\"box-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span class=\"title\">专项培训</span>\r\n          <el-button type=\"primary\" @click=\"openDialog()\" v-if=\"isAdmin\">添加培训课程</el-button>\r\n        </div>\r\n      </template>\r\n      \r\n      <!-- 筛选区域 -->\r\n      <div class=\"filter-container\">\r\n        <div class=\"filter-item\">\r\n          <span class=\"filter-label\">课程类型</span>\r\n          <el-select v-model=\"filterForm.course_type\" placeholder=\"选择课程类型\" clearable class=\"filter-select\">\r\n            <el-option label=\"小讲课\" value=\"小讲课\" />\r\n            <el-option label=\"教学病例讨论\" value=\"教学病例讨论\" />\r\n            <el-option label=\"教学查房\" value=\"教学查房\" />\r\n            <el-option label=\"其他\" value=\"其他\" />\r\n          </el-select>\r\n        </div>\r\n        <div class=\"filter-buttons\">\r\n          <el-button type=\"primary\" @click=\"handleFilter\">筛选</el-button>\r\n          <el-button @click=\"resetFilter\">重置</el-button>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 课程列表表格 -->\r\n      <div v-loading=\"loading\">\r\n        <div v-if=\"trainingCourses.length === 0 && !loading\" class=\"empty-data\">\r\n          暂无培训课程，请添加新课程\r\n        </div>\r\n        \r\n        <el-table\r\n          v-else\r\n          :data=\"trainingCourses\"\r\n          style=\"width: 100%\"\r\n          border\r\n        >\r\n          <el-table-column prop=\"title\" label=\"课程标题\" min-width=\"180\" show-overflow-tooltip />\r\n          \r\n          <el-table-column prop=\"course_type\" label=\"课程类型\" width=\"150\" align=\"center\">\r\n            <template #default=\"scope\">\r\n              <el-tag :type=\"getTagType(scope.row.course_type)\">{{ scope.row.course_type }}</el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column prop=\"description\" label=\"课程描述\" min-width=\"200\" show-overflow-tooltip />\r\n          \r\n          <el-table-column prop=\"created_at\" label=\"创建时间\" width=\"120\" align=\"center\">\r\n            <template #default=\"scope\">\r\n              {{ formatDate(scope.row.created_at) }}\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column label=\"课程资料\" width=\"220\" align=\"center\">\r\n            <template #default=\"scope\">\r\n              <div v-if=\"scope.row.material_path\" class=\"file-actions\">\r\n                <el-button\r\n                  type=\"primary\"\r\n                  link\r\n                  @click=\"previewFile(scope.row)\"\r\n                  title=\"预览文件\"\r\n                >\r\n                  <el-icon><Document /></el-icon> {{ scope.row.original_filename }}\r\n                </el-button>\r\n                \r\n              </div>\r\n              <span v-else>无资料</span>\r\n            </template>\r\n          </el-table-column>\r\n          \r\n          <el-table-column label=\"操作\" width=\"150\" align=\"center\" fixed=\"right\">\r\n            <template #default=\"scope\">\r\n              <el-button type=\"primary\" link size=\"small\" @click=\"openDialog(scope.row)\">编辑</el-button>\r\n              <el-button type=\"danger\" link size=\"small\" @click=\"handleDelete(scope.row)\">删除</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-container\" v-if=\"trainingCourses.length > 0\">\r\n          <el-pagination\r\n            v-model:current-page=\"currentPage\"\r\n            v-model:page-size=\"pageSize\"\r\n            :page-sizes=\"[10, 20, 30, 50]\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 添加/编辑课程对话框 -->\r\n    <el-dialog\r\n      v-model=\"dialogVisible\"\r\n      :title=\"formData.id ? '编辑培训课程' : '添加培训课程'\"\r\n      width=\"50%\"\r\n      destroy-on-close\r\n    >\r\n      <el-form\r\n        ref=\"courseFormRef\"\r\n        :model=\"formData\"\r\n        :rules=\"formRules\"\r\n        label-width=\"100px\"\r\n        label-position=\"right\"\r\n      >\r\n        <el-form-item label=\"课程标题\" prop=\"title\">\r\n          <el-input v-model=\"formData.title\" placeholder=\"请输入课程标题\" />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"课程类型\" prop=\"course_type\">\r\n          <el-select v-model=\"formData.course_type\" placeholder=\"选择课程类型\" style=\"width: 100%\">\r\n            <el-option label=\"小讲课\" value=\"小讲课\" />\r\n            <el-option label=\"教学病例讨论\" value=\"教学病例讨论\" />\r\n            <el-option label=\"教学查房\" value=\"教学查房\" />\r\n            <el-option label=\"其他\" value=\"其他\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"课程描述\" prop=\"description\">\r\n          <el-input\r\n            v-model=\"formData.description\"\r\n            type=\"textarea\"\r\n            rows=\"3\"\r\n            placeholder=\"请输入课程描述\"\r\n          />\r\n        </el-form-item>\r\n        \r\n        <el-form-item label=\"课件/视频\">\r\n          <el-upload\r\n            class=\"material-uploader\"\r\n            drag\r\n            action=\"#\"\r\n            :auto-upload=\"false\"\r\n            :on-change=\"handleFileChange\"\r\n            :on-remove=\"handleRemove\"\r\n            :before-upload=\"beforeMaterialUpload\"\r\n            :file-list=\"fileList\"\r\n            :limit=\"1\"\r\n          >\r\n            <el-icon class=\"el-icon--upload\"><Upload /></el-icon>\r\n            <div class=\"el-upload__text\">\r\n              拖拽文件到此处或 <em>点击上传</em>\r\n            </div>\r\n            <template #tip>\r\n              <div class=\"el-upload__tip\">\r\n                支持各种文档、PPT、视频等格式，文件大小不超过100MB\r\n              </div>\r\n            </template>\r\n          </el-upload>\r\n        </el-form-item>\r\n      </el-form>\r\n      \r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitForm\" :loading=\"submitting\">确定</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, onMounted } from 'vue'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { Document, Upload, More, Download } from '@element-plus/icons-vue'\r\nimport axios from 'axios'\r\nimport { API_URL } from '@/utils/api'\r\nimport * as trainingService from '@/services/trainingService'\r\n\r\nexport default {\r\n  name: 'TrainingList',\r\n  components: {\r\n    Document,\r\n    Upload,\r\n    More,\r\n    Download\r\n  },\r\n  setup() {\r\n    // 基础数据\r\n    const loading = ref(false)\r\n    const submitting = ref(false)\r\n    const trainingCourses = ref([])\r\n    const dialogVisible = ref(false)\r\n    const courseFormRef = ref(null)\r\n    const total = ref(0)\r\n    const currentPage = ref(1)\r\n    const pageSize = ref(10)\r\n    const fileList = ref([])\r\n    const uploadFile = ref(null) // 存储要上传的文件\r\n    let userInfo = localStorage.getItem(\"userInfo\") ? JSON.parse(localStorage.getItem(\"userInfo\")): ''\r\n// 用户角色\r\nconst isAdmin = ref(userInfo?.role == 'admin')\r\n    // 筛选条件\r\n    const filterForm = reactive({\r\n      course_type: ''\r\n    })\r\n    \r\n    // 表单数据\r\n    const formData = reactive({\r\n      id: '',\r\n      title: '',\r\n      course_type: '',\r\n      description: '',\r\n      material_path: '',\r\n      original_filename: ''\r\n    })\r\n    \r\n    // 表单验证规则\r\n    const formRules = {\r\n      title: [\r\n        { required: true, message: '请输入课程标题', trigger: 'blur' },\r\n        { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }\r\n      ],\r\n      course_type: [\r\n        { required: true, message: '请选择课程类型', trigger: 'change' }\r\n      ]\r\n    }\r\n    \r\n    // 生命周期钩子\r\n    onMounted(() => {\r\n      fetchTrainingCourses()\r\n    })\r\n    \r\n    // 获取培训课程列表\r\n    const fetchTrainingCourses = async () => {\r\n      loading.value = true\r\n      try {\r\n        let response\r\n        const params = {\r\n          page: currentPage.value,\r\n          limit: pageSize.value\r\n        }\r\n        \r\n        if (filterForm.course_type) {\r\n          response = await trainingService.getTrainingsByType(filterForm.course_type, params)\r\n        } else {\r\n          response = await trainingService.getAllTrainings(params)\r\n        }\r\n        \r\n        trainingCourses.value = response.data\r\n        total.value = response.count\r\n      } catch (error) {\r\n        console.error('获取培训课程失败:', error)\r\n        ElMessage.error('获取培训课程失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 筛选操作\r\n    const handleFilter = () => {\r\n      currentPage.value = 1\r\n      fetchTrainingCourses()\r\n    }\r\n    \r\n    // 重置筛选\r\n    const resetFilter = () => {\r\n      filterForm.course_type = ''\r\n      handleFilter()\r\n    }\r\n    \r\n    // 分页操作\r\n    const handleSizeChange = (val) => {\r\n      pageSize.value = val\r\n      fetchTrainingCourses()\r\n    }\r\n    \r\n    const handleCurrentChange = (val) => {\r\n      currentPage.value = val\r\n      fetchTrainingCourses()\r\n    }\r\n    \r\n    // 文件上传前检查\r\n    const beforeMaterialUpload = (file) => {\r\n      const isLt100M = file.size / 1024 / 1024 < 100\r\n      \r\n      if (!isLt100M) {\r\n        ElMessage.error('上传文件大小不能超过 100MB!')\r\n      }\r\n      \r\n      return isLt100M\r\n    }\r\n    \r\n    // 文件改变处理\r\n    const handleFileChange = (file) => {\r\n      uploadFile.value = file.raw\r\n    }\r\n    \r\n    // 移除文件\r\n    const handleRemove = () => {\r\n      formData.material_path = ''\r\n      formData.original_filename = ''\r\n      fileList.value = []\r\n      uploadFile.value = null\r\n    }\r\n    \r\n    // 打开对话框\r\n    const openDialog = (course) => {\r\n      if (course) {\r\n        // 编辑模式\r\n        Object.keys(formData).forEach(key => {\r\n          formData[key] = course[key]\r\n        })\r\n        \r\n        // 如果有文件，添加到文件列表\r\n        fileList.value = []\r\n        if (course.material_path && course.original_filename) {\r\n          fileList.value = [\r\n            {\r\n              name: decodeURIComponent(course.original_filename),\r\n              url: `http://localhost:3000${course.material_path}`\r\n            }\r\n          ]\r\n        }\r\n      } else {\r\n        // 新增模式\r\n        Object.keys(formData).forEach(key => {\r\n          formData[key] = ''\r\n        })\r\n        fileList.value = []\r\n        uploadFile.value = null\r\n      }\r\n      \r\n      dialogVisible.value = true\r\n    }\r\n    \r\n    // 提交表单\r\n    const submitForm = async () => {\r\n      if (!courseFormRef.value) return\r\n      \r\n      await courseFormRef.value.validate(async (valid) => {\r\n        if (valid) {\r\n          submitting.value = true\r\n          \r\n          try {\r\n            // 提交表单数据，直接将文件传递给service\r\n            if (formData.id) {\r\n              // 编辑\r\n              await trainingService.updateTraining(formData.id, formData, uploadFile.value);\r\n              ElMessage.success('课程更新成功')\r\n            } else {\r\n              // 新增\r\n              await trainingService.createTraining(formData, uploadFile.value);\r\n              ElMessage.success('课程添加成功')\r\n            }\r\n            \r\n            dialogVisible.value = false\r\n            fetchTrainingCourses()\r\n          } catch (error) {\r\n            console.error('操作失败:', error)\r\n            ElMessage.error('操作失败: ' + (error.response?.data?.message || error.message))\r\n          } finally {\r\n            submitting.value = false\r\n          }\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    }\r\n    \r\n    // 处理删除\r\n    const handleDelete = (course) => {\r\n      ElMessageBox.confirm(\r\n        `确定要删除培训课程 \"${course.title}\" 吗?`,\r\n        '警告',\r\n        {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n        }\r\n      )\r\n        .then(async () => {\r\n          try {\r\n            await trainingService.deleteTraining(course.id)\r\n            ElMessage.success('删除成功')\r\n            fetchTrainingCourses()\r\n          } catch (error) {\r\n            console.error('删除失败:', error)\r\n            ElMessage.error('删除失败')\r\n          }\r\n        })\r\n        .catch(() => {\r\n          ElMessage.info('已取消删除')\r\n        })\r\n    }\r\n    \r\n    // 下拉菜单命令处理 - 保留但不再使用\r\n    const handleCommand = (command, course) => {\r\n      if (command === 'edit') {\r\n        openDialog(course)\r\n      } else if (command === 'delete') {\r\n        handleDelete(course)\r\n      }\r\n    }\r\n    \r\n    // 根据课程类型获取标签类型\r\n    const getTagType = (type) => {\r\n      switch (type) {\r\n        case '小讲课':\r\n          return 'success'\r\n        case '教学病例讨论':\r\n          return 'warning'\r\n        case '教学查房':\r\n          return 'danger'\r\n        default:\r\n          return 'info'\r\n      }\r\n    }\r\n    \r\n    // 日期格式化\r\n    const formatDate = (dateString) => {\r\n      if (!dateString) return '-'\r\n      const date = new Date(dateString)\r\n      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`\r\n    }\r\n\r\n    // 添加预览文件方法\r\n    const previewFile = (course) => {\r\n      window.open(`http://localhost:3000${course.material_path}`, '_blank')\r\n    }\r\n    \r\n    return {\r\n      loading,\r\n      submitting,\r\n      trainingCourses,\r\n      dialogVisible,\r\n      courseFormRef,\r\n      formData,\r\n      formRules,\r\n      filterForm,\r\n      fileList,\r\n      uploadFile,\r\n      currentPage,\r\n      pageSize,\r\n      total,\r\n      isAdmin,\r\n      handleFilter,\r\n      resetFilter,\r\n      handleSizeChange,\r\n      handleCurrentChange,\r\n      openDialog,\r\n      submitForm,\r\n      beforeMaterialUpload,\r\n      handleFileChange,\r\n      handleRemove,\r\n      handleCommand,\r\n      handleDelete,\r\n      getTagType,\r\n      formatDate,\r\n      previewFile\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.training-list-container {\r\n  padding: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.filter-form {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.empty-data {\r\n  padding: 50px;\r\n  text-align: center;\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.material-uploader {\r\n  width: 100%;\r\n}\r\n\r\n.material-uploader .el-upload-dragger {\r\n  width: 100%;\r\n}\r\n\r\n.filter-container {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 20px;\r\n  background-color: #f5f7fa;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.filter-item {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 20px;\r\n}\r\n\r\n.filter-label {\r\n  font-weight: bold;\r\n  margin-right: 10px;\r\n  min-width: 70px;\r\n}\r\n\r\n.filter-select {\r\n  width: 200px;\r\n}\r\n\r\n.filter-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.file-actions {\r\n  display: flex;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n</style> ", "import axios from 'axios';\r\nimport { API_URL } from '../utils/api';\r\n\r\n/**\r\n * 获取所有培训课程\r\n * @param {Object} params - 请求参数\r\n * @returns {Promise} - 请求Promise\r\n */\r\nexport const getAllTrainings = async (params = {}) => {\r\n  try {\r\n    const response = await axios.get(`${API_URL}/trainings`, { params });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('获取培训课程列表失败:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * 按类型获取培训课程\r\n * @param {string} type - 课程类型\r\n * @param {Object} params - 请求参数\r\n * @returns {Promise} - 请求Promise\r\n */\r\nexport const getTrainingsByType = async (type, params = {}) => {\r\n  try {\r\n    const response = await axios.get(`${API_URL}/trainings/type/${encodeURIComponent(type)}`, { params });\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error(`获取${type}类型培训课程失败:`, error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * 获取单个培训课程详情\r\n * @param {string} id - 课程ID\r\n * @returns {Promise} - 请求Promise\r\n */\r\nexport const getTrainingById = async (id) => {\r\n  try {\r\n    const response = await axios.get(`${API_URL}/trainings/${id}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('获取培训课程详情失败:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * 创建新的培训课程\r\n * @param {Object} trainingData - 培训课程数据\r\n * @param {File} file - 课程文件\r\n * @returns {Promise} - 请求Promise\r\n */\r\nexport const createTraining = async (trainingData, file) => {\r\n  try {\r\n    const formData = new FormData();\r\n    \r\n    // 添加基本课程数据\r\n    formData.append('title', trainingData.title);\r\n    formData.append('description', trainingData.description || '');\r\n    formData.append('course_type', trainingData.course_type);\r\n    \r\n    // 添加文件（如果有）\r\n    if (file) {\r\n      formData.append('material', file);\r\n    }\r\n    \r\n    const response = await axios.post(`${API_URL}/trainings`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data'\r\n      }\r\n    });\r\n    \r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('创建培训课程失败:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * 更新培训课程\r\n * @param {string} id - 课程ID\r\n * @param {Object} trainingData - 更新的课程数据\r\n * @param {File} file - 课程文件（可选）\r\n * @returns {Promise} - 请求Promise\r\n */\r\nexport const updateTraining = async (id, trainingData, file) => {\r\n  try {\r\n    const formData = new FormData();\r\n    \r\n    // 添加基本课程数据\r\n    formData.append('title', trainingData.title);\r\n    formData.append('description', trainingData.description || '');\r\n    formData.append('course_type', trainingData.course_type);\r\n    \r\n    // 添加文件（如果有）\r\n    if (file) {\r\n      formData.append('material', file);\r\n    }\r\n    // 如果没有新文件但有旧文件信息，则传递旧文件信息\r\n    else if (trainingData.material_path && trainingData.original_filename) {\r\n      formData.append('material_path', trainingData.material_path);\r\n      formData.append('original_filename', trainingData.original_filename);\r\n    }\r\n    \r\n    const response = await axios.put(`${API_URL}/trainings/${id}`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data'\r\n      }\r\n    });\r\n    \r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('更新培训课程失败:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * 删除培训课程\r\n * @param {string} id - 课程ID\r\n * @returns {Promise} - 请求Promise\r\n */\r\nexport const deleteTraining = async (id) => {\r\n  try {\r\n    const response = await axios.delete(`${API_URL}/trainings/${id}`);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('删除培训课程失败:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n/**\r\n * 上传课程材料\r\n * @param {File} file - 文件对象\r\n * @returns {Promise} - 请求Promise\r\n */\r\nexport const uploadCourseMaterial = async (file) => {\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append('material', file);\r\n    \r\n    const response = await axios.post(`${API_URL}/upload/course`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data'\r\n      }\r\n    });\r\n    \r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('上传课程材料失败:', error);\r\n    throw error;\r\n  }\r\n}; ", "import { render } from \"./TrainingList.vue?vue&type=template&id=54ccfd7e&scoped=true\"\nimport script from \"./TrainingList.vue?vue&type=script&lang=js\"\nexport * from \"./TrainingList.vue?vue&type=script&lang=js\"\n\nimport \"./TrainingList.vue?vue&type=style&index=0&id=54ccfd7e&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-54ccfd7e\"]])\n\nexport default __exports__"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "$setup", "isAdmin", "_createBlock", "_component_el_button", "type", "onClick", "_cache", "$event", "openDialog", "_hoisted_3", "_hoisted_4", "_component_el_select", "filterForm", "course_type", "placeholder", "clearable", "_component_el_option", "label", "value", "_hoisted_5", "handleFilter", "resetFilter", "trainingCourses", "length", "loading", "_component_el_table", "data", "style", "border", "_component_el_table_column", "prop", "width", "align", "default", "scope", "_component_el_tag", "getTagType", "row", "formatDate", "created_at", "material_path", "_hoisted_7", "link", "previewFile", "title", "_component_el_icon", "_component_Document", "_toDisplayString", "original_filename", "_hoisted_8", "fixed", "size", "handleDelete", "_hoisted_6", "_hoisted_9", "_component_el_pagination", "currentPage", "pageSize", "layout", "total", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "_component_el_dialog", "dialogVisible", "formData", "id", "footer", "_hoisted_10", "submitForm", "submitting", "_component_el_form", "ref", "model", "rules", "formRules", "_component_el_form_item", "_component_el_input", "description", "rows", "_component_el_upload", "drag", "action", "handleFileChange", "handleRemove", "beforeMaterialUpload", "fileList", "limit", "tip", "_component_Upload", "getAllTrainings", "async", "params", "response", "axios", "get", "API_URL", "error", "console", "getTrainingsByType", "encodeURIComponent", "createTraining", "trainingData", "file", "FormData", "append", "post", "headers", "updateTraining", "put", "deleteTraining", "delete", "name", "components", "Document", "Upload", "More", "Download", "setup", "courseFormRef", "uploadFile", "userInfo", "localStorage", "getItem", "JSON", "parse", "role", "reactive", "required", "message", "trigger", "min", "max", "onMounted", "fetchTrainingCourses", "page", "trainingService", "count", "ElMessage", "val", "isLt100M", "raw", "course", "Object", "keys", "for<PERSON>ach", "key", "decodeURIComponent", "url", "validate", "valid", "success", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "then", "catch", "info", "handleCommand", "command", "dateString", "date", "Date", "getFullYear", "getMonth", "toString", "padStart", "getDate", "window", "open", "__exports__", "render"], "sourceRoot": ""}