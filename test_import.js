const XLSX = require('xlsx');
const path = require('path');

// 创建测试用的Excel文件
const createTestExcel = () => {
  // 测试数据
  const testData = [
    ['姓名', '性别', '科室', '学校', '专业', '学历', '是否在聘', '聘期', '联系方式'],
    ['张三', '男', '内科', '某某医学院', '临床医学', '本科', '是', '2023-01至2025-12', '13800138000'],
    ['李四', '女', '外科', '某某大学', '外科学', '硕士', '是', '2022-06至2024-06', '13900139000'],
    ['王五', '男', '儿科', '医科大学', '儿科学', '博士', '否', '', '13700137000']
  ];

  // 创建工作簿
  const workbook = XLSX.utils.book_new();
  const worksheet = XLSX.utils.aoa_to_sheet(testData);
  
  // 设置列宽
  worksheet['!cols'] = [
    { wch: 10 }, // 姓名
    { wch: 6 },  // 性别
    { wch: 15 }, // 科室
    { wch: 20 }, // 学校
    { wch: 15 }, // 专业
    { wch: 10 }, // 学历
    { wch: 10 }, // 是否在聘
    { wch: 20 }, // 聘期
    { wch: 15 }  // 联系方式
  ];

  XLSX.utils.book_append_sheet(workbook, worksheet, '教师信息');

  // 保存文件
  const filePath = path.join(__dirname, 'test_teachers.xlsx');
  XLSX.writeFile(workbook, filePath);
  
  console.log(`测试Excel文件已创建: ${filePath}`);
  return filePath;
};

// 运行测试
if (require.main === module) {
  createTestExcel();
}

module.exports = { createTestExcel };
