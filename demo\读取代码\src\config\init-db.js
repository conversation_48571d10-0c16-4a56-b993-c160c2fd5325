const { pool } = require('./db');
const bcrypt = require('bcryptjs');
const supervisorModel = require('../models/supervisorModel');
const trainingModel = require('../models/trainingModel');

// 创建默认管理员账号
const createDefaultAdmin = async () => {
  try {
    const connection = await pool.getConnection();
    
    // 检查是否已存在管理员账号
    const [admins] = await connection.query('SELECT * FROM users WHERE role = "admin" LIMIT 1');
    
    // 如果没有管理员账号，则创建一个
    if (admins.length === 0) {
      // 默认管理员信息
      const adminUsername = 'admin';
      const adminPassword = 'admin123';
      const adminName = '系统管理员';
      
      // 加密密码
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(adminPassword, salt);
      
      // 插入管理员记录
      await connection.query(
        `INSERT INTO users (username, password, role, name) VALUES (?, ?, ?, ?)`,
        [adminUsername, hashedPassword, 'admin', adminName]
      );
      
      console.log('默认管理员账号创建成功');
      console.log('用户名: admin');
      console.log('密码: admin123');
    }
    
    connection.release();
    return true;
  } catch (error) {
    console.error('创建默认管理员账号失败:', error);
    return false;
  }
};

// 初始化数据库表
const initDatabase = async () => {
  try {
    const connection = await pool.getConnection();
    
    // 1. 创建教师基本信息表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS teachers (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) NOT NULL COMMENT '姓名',
        gender ENUM('男', '女') NOT NULL COMMENT '性别',
        department VARCHAR(100) NOT NULL COMMENT '科室',
        school VARCHAR(100) NOT NULL COMMENT '学校',
        major VARCHAR(100) NOT NULL COMMENT '专业',
        education VARCHAR(50) NOT NULL COMMENT '学历',
        is_employed BOOLEAN NOT NULL DEFAULT 1 COMMENT '是否在聘',
        employment_period VARCHAR(100) COMMENT '聘期（年月-年月）',
        phone VARCHAR(20) COMMENT '联系方式',
        photo VARCHAR(255) COMMENT '照片路径',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);
    
    // 2. 创建用户表 (移到前面)
    await connection.query(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
        password VARCHAR(255) NOT NULL COMMENT '密码',
        role ENUM('admin', 'teacher') NOT NULL DEFAULT 'teacher' COMMENT '角色',
        name VARCHAR(50) NOT NULL COMMENT '姓名',
        email VARCHAR(100) COMMENT '邮箱',
        phone VARCHAR(20) COMMENT '电话',
        teacher_id INT COMMENT '关联的教师ID',
        status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
        reset_token VARCHAR(255) COMMENT '重置密码令牌',
        reset_token_expires DATETIME COMMENT '重置密码令牌过期时间',
        last_login TIMESTAMP COMMENT '最后登录时间',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);
    
    // 检查是否已有status列，如果没有则添加
    await connection.query(`
      SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'status'
    `).then(async ([result]) => {
      if (result[0].count === 0) {
        await connection.query(`
          ALTER TABLE users ADD COLUMN status TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态：1-启用，0-禁用' AFTER teacher_id
        `);
        console.log('向users表添加status字段');
      }
    });
    
    // 检查是否存在student_id列，如果存在则转换为teacher_id
    await connection.query(`
      SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'student_id'
    `).then(async ([result]) => {
      if (result[0].count > 0) {
        // 检查是否已有teacher_id列
        const [teacherIdCheck] = await connection.query(`
          SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS 
          WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'teacher_id'
        `);
        
        if (teacherIdCheck[0].count === 0) {
          // 如果没有teacher_id列，先添加它
          await connection.query(`
            ALTER TABLE users ADD COLUMN teacher_id INT COMMENT '关联的教师ID' AFTER phone,
            ADD FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE SET NULL
          `);
        }
        
        // 删除student_id列
        await connection.query(`
          ALTER TABLE users DROP COLUMN student_id
        `);
        console.log('从users表中删除student_id字段，转换为teacher_id');
      }
    });
    
    // 检查role列是否限制为admin和teacher，如果不是则修改
    await connection.query(`
      SELECT COLUMN_TYPE FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'users' AND COLUMN_NAME = 'role'
    `).then(async ([result]) => {
      // 如果角色不是只有admin和teacher
      if (!result[0].COLUMN_TYPE.includes("enum('admin','teacher')")) {
        // 先将所有非admin、teacher的角色更新为teacher
        await connection.query(`
          UPDATE users SET role = 'teacher' WHERE role NOT IN ('admin', 'teacher')
        `);
        
        // 然后修改列定义
        await connection.query(`
          ALTER TABLE users MODIFY COLUMN role ENUM('admin', 'teacher') NOT NULL DEFAULT 'teacher' COMMENT '角色'
        `);
        console.log('更新users表role字段，限制为admin和teacher');
      }
    });
    
    // 3. 创建专项培训课程表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS training_courses (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(200) NOT NULL COMMENT '课程标题',
        description TEXT COMMENT '课程描述',
        course_type ENUM('小讲课', '教学病例讨论', '教学查房', '其他') NOT NULL COMMENT '课程类型',
        material_path VARCHAR(255) COMMENT '课件/视频路径',
        original_filename VARCHAR(255) COMMENT '原始文件名',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 4. 创建考试表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS exams (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(200) NOT NULL COMMENT '考试标题',
        description TEXT COMMENT '考试描述',
        duration INT NOT NULL COMMENT '考试时长(分钟)',
        pass_score INT NOT NULL DEFAULT 60 COMMENT '及格分数',
        total_score INT NOT NULL COMMENT '总分',
        exam_type ENUM('线上考试', '资格认定考试') NOT NULL DEFAULT '线上考试' COMMENT '考试类型',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 5. 创建考试试题表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS exam_questions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        question TEXT NOT NULL COMMENT '题目内容',
        options JSON COMMENT '选项(对于选择题)',
        correct_answer TEXT NOT NULL COMMENT '正确答案',
        question_type ENUM('单选题', '多选题', '判断题', '简答题') NOT NULL COMMENT '题目类型',
        score INT DEFAULT 5 COMMENT '分值',
        exam_id INT NOT NULL COMMENT '所属考试ID',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (exam_id) REFERENCES exams(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 6. 创建考试成绩表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS exam_results (
        id INT AUTO_INCREMENT PRIMARY KEY,
        teacher_id INT NOT NULL COMMENT '教师ID',
        exam_id INT NOT NULL COMMENT '考试ID',
        score DECIMAL(5,2) NOT NULL COMMENT '成绩',
        answers JSON COMMENT '答题内容',
        is_qualified BOOLEAN GENERATED ALWAYS AS (score >= 60) STORED COMMENT '是否合格',
        exam_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '考试时间',
        FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
        FOREIGN KEY (exam_id) REFERENCES exams(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 7. 创建教学活动督导评价表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS teaching_evaluations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        supervising_department VARCHAR(100) NOT NULL COMMENT '督导教研室',
        case_topic VARCHAR(200) NOT NULL COMMENT '病例/主题',
        teaching_form VARCHAR(100) NOT NULL COMMENT '教学活动形式',
        teacher_id INT NOT NULL COMMENT '带教老师ID',
        teacher_title VARCHAR(50) NOT NULL COMMENT '带教老师职称',
        student_name VARCHAR(50) NOT NULL COMMENT '学员姓名',
        student_type ENUM('实习生', '进修生', '低年资轮转') NOT NULL COMMENT '学员类别',
        average_score DECIMAL(3,1) NOT NULL COMMENT '平均分',
        highlights TEXT COMMENT '亮点',
        shortcomings TEXT COMMENT '不足',
        improvement_suggestions TEXT COMMENT '改进建议',
        competency_approved BOOLEAN NOT NULL DEFAULT 0 COMMENT '能力认定是否同意',
        evaluator_id INT NOT NULL COMMENT '评估人ID',
        evaluation_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '评估日期',
        FOREIGN KEY (teacher_id) REFERENCES teachers(id) ON DELETE CASCADE,
        FOREIGN KEY (evaluator_id) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    // 8. 创建督导小组成员表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS supervision_team (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL COMMENT '用户ID',
        role VARCHAR(50) NOT NULL COMMENT '在督导小组中的角色',
        photo VARCHAR(255) COMMENT '照片路径',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);
    
    // 9. 创建督导成员表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS supervisors (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL COMMENT '姓名',
        gender VARCHAR(10) DEFAULT '男' COMMENT '性别',
        department VARCHAR(100) NOT NULL COMMENT '科室',
        title VARCHAR(100) NOT NULL COMMENT '职称',
        specialty TEXT COMMENT '专长',
        phone VARCHAR(20) COMMENT '联系电话',
        email VARCHAR(100) COMMENT '邮箱',
        status TINYINT DEFAULT 1 COMMENT '状态：1-在职，0-离职',
        avatar VARCHAR(255) COMMENT '头像路径',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    `);

    console.log('数据库表初始化成功');
    connection.release();
    
    // 创建默认管理员账号
    await createDefaultAdmin();
    
    // 初始化督导表
    await supervisorModel.initTable();
    
    // 初始化培训表
    await trainingModel.initTable();
    
    return true;
  } catch (error) {
    console.error('数据库表初始化失败:', error);
    return false;
  }
};

module.exports = {
  initDatabase
}; 