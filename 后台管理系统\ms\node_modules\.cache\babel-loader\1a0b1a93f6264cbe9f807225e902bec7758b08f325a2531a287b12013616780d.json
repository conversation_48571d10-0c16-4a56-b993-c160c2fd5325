{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createTextVNode as _createTextVNode, withModifiers as _withModifiers, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createStaticVNode as _createStaticVNode } from \"vue\";\nimport _imports_0 from '@/assets/logo.png';\nconst _hoisted_1 = {\n  class: \"login-container\"\n};\nconst _hoisted_2 = {\n  class: \"login-card\"\n};\nconst _hoisted_3 = {\n  class: \"login-info\"\n};\nconst _hoisted_4 = {\n  class: \"feature-list\"\n};\nconst _hoisted_5 = {\n  class: \"feature-item\"\n};\nconst _hoisted_6 = {\n  class: \"feature-icon\"\n};\nconst _hoisted_7 = {\n  class: \"feature-item\"\n};\nconst _hoisted_8 = {\n  class: \"feature-icon\"\n};\nconst _hoisted_9 = {\n  class: \"feature-item\"\n};\nconst _hoisted_10 = {\n  class: \"feature-icon\"\n};\nconst _hoisted_11 = {\n  class: \"login-form-wrapper\"\n};\nconst _hoisted_12 = {\n  class: \"login-form-container\"\n};\nconst _hoisted_13 = {\n  class: \"form-options\"\n};\nconst _hoisted_14 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_15 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_form_item = _resolveComponent(\"el-form-item\");\n  const _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_form = _resolveComponent(\"el-form\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createCommentVNode(\" Left side \"), _createElementVNode(\"div\", _hoisted_3, [_cache[20] || (_cache[20] = _createStaticVNode(\"<div class=\\\"logo-wrapper\\\" data-v-5c6101e4><div class=\\\"logo-icon\\\" data-v-5c6101e4><img src=\\\"\" + _imports_0 + \"\\\" alt=\\\"Logo\\\" class=\\\"logo-img\\\" data-v-5c6101e4><i class=\\\"el-icon-monitor\\\" data-v-5c6101e4></i></div><div class=\\\"logo-text\\\" data-v-5c6101e4> 教学师资评价与能力认定系统 </div></div><div class=\\\"welcome-text\\\" data-v-5c6101e4><h2 data-v-5c6101e4>欢迎回来</h2><p data-v-5c6101e4>登录您的账户以继续访问系统</p></div>\", 2)), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode($setup[\"Check\"])]),\n    _: 1 /* STABLE */\n  })]), _cache[17] || (_cache[17] = _createElementVNode(\"div\", {\n    class: \"feature-text\"\n  }, \"现代化的管理界面\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode($setup[\"Check\"])]),\n    _: 1 /* STABLE */\n  })]), _cache[18] || (_cache[18] = _createElementVNode(\"div\", {\n    class: \"feature-text\"\n  }, \"强大的功能模块\", -1 /* CACHED */))]), _createElementVNode(\"div\", _hoisted_9, [_createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(() => [_createVNode($setup[\"Check\"])]),\n    _: 1 /* STABLE */\n  })]), _cache[19] || (_cache[19] = _createElementVNode(\"div\", {\n    class: \"feature-text\"\n  }, \"安全可靠的数据保护\", -1 /* CACHED */))])])]), _createCommentVNode(\" Right side \"), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, [_cache[23] || (_cache[23] = _createElementVNode(\"h2\", {\n    class: \"form-title\"\n  }, \"用户登录\", -1 /* CACHED */)), _cache[24] || (_cache[24] = _createElementVNode(\"p\", {\n    class: \"form-subtitle\"\n  }, \"请输入您的账户信息\", -1 /* CACHED */)), _createVNode(_component_el_form, {\n    model: $setup.loginForm,\n    rules: $setup.loginRules,\n    ref: \"loginFormRef\",\n    class: \"login-form\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_form_item, {\n      prop: \"username\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.loginForm.username,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.loginForm.username = $event),\n        placeholder: \"\",\n        \"prefix-icon\": $setup.User\n      }, null, 8 /* PROPS */, [\"modelValue\", \"prefix-icon\"])]),\n      _: 1 /* STABLE */\n    }), _createVNode(_component_el_form_item, {\n      prop: \"password\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_input, {\n        modelValue: $setup.loginForm.password,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.loginForm.password = $event),\n        type: \"password\",\n        placeholder: \"\",\n        \"prefix-icon\": $setup.Lock,\n        \"show-password\": \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"prefix-icon\"])]),\n      _: 1 /* STABLE */\n    }), _createElementVNode(\"div\", _hoisted_13, [_createVNode(_component_el_checkbox, {\n      modelValue: $setup.loginForm.remember,\n      \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.loginForm.remember = $event)\n    }, {\n      default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\"记住我\")])),\n      _: 1 /* STABLE */,\n      __: [21]\n    }, 8 /* PROPS */, [\"modelValue\"]), _createElementVNode(\"a\", {\n      href: \"#\",\n      class: \"forgot-link\",\n      onClick: _withModifiers($setup.showForgotPassword, [\"prevent\"])\n    }, \"忘记密码?\")]), _createVNode(_component_el_form_item, null, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        loading: $setup.loading,\n        onClick: $setup.handleLogin,\n        class: \"login-button\"\n      }, {\n        default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\" 登录 \")])),\n        _: 1 /* STABLE */,\n        __: [22]\n      }, 8 /* PROPS */, [\"loading\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\"])])])]), _createCommentVNode(\" 注册对话框 \"), _createVNode(_component_el_dialog, {\n    title: \"用户注册\",\n    modelValue: $setup.registerDialogVisible,\n    \"onUpdate:modelValue\": _cache[10] || (_cache[10] = $event => $setup.registerDialogVisible = $event),\n    width: \"400px\",\n    center: \"\",\n    \"destroy-on-close\": \"\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_14, [_createVNode(_component_el_button, {\n      onClick: _cache[9] || (_cache[9] = $event => $setup.registerDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[25] || (_cache[25] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [25]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      loading: $setup.registerLoading,\n      onClick: $setup.handleRegister\n    }, {\n      default: _withCtx(() => _cache[26] || (_cache[26] = [_createTextVNode(\"注册\")])),\n      _: 1 /* STABLE */,\n      __: [26]\n    }, 8 /* PROPS */, [\"loading\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.registerForm,\n      rules: $setup.registerRules,\n      ref: \"registerFormRef\",\n      \"label-width\": \"80px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"用户名\",\n        prop: \"username\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.registerForm.username,\n          \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.registerForm.username = $event),\n          placeholder: \"请输入用户名\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"密码\",\n        prop: \"password\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.registerForm.password,\n          \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.registerForm.password = $event),\n          type: \"password\",\n          placeholder: \"请输入密码\",\n          \"show-password\": \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"确认密码\",\n        prop: \"confirmPassword\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.registerForm.confirmPassword,\n          \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.registerForm.confirmPassword = $event),\n          type: \"password\",\n          placeholder: \"请再次输入密码\",\n          \"show-password\": \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"姓名\",\n        prop: \"name\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.registerForm.name,\n          \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.registerForm.name = $event),\n          placeholder: \"请输入姓名\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"邮箱\",\n        prop: \"email\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.registerForm.email,\n          \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.registerForm.email = $event),\n          placeholder: \"请输入邮箱\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"手机号\",\n        prop: \"phone\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.registerForm.phone,\n          \"onUpdate:modelValue\": _cache[8] || (_cache[8] = $event => $setup.registerForm.phone = $event),\n          placeholder: \"请输入手机号\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 忘记密码对话框 \"), _createVNode(_component_el_dialog, {\n    title: \"忘记密码\",\n    modelValue: $setup.forgotPasswordDialogVisible,\n    \"onUpdate:modelValue\": _cache[16] || (_cache[16] = $event => $setup.forgotPasswordDialogVisible = $event),\n    width: \"400px\",\n    center: \"\",\n    \"destroy-on-close\": \"\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"span\", _hoisted_15, [_createVNode(_component_el_button, {\n      onClick: _cache[15] || (_cache[15] = $event => $setup.forgotPasswordDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [27]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      loading: $setup.forgotPasswordLoading,\n      onClick: $setup.handleForgotPassword\n    }, {\n      default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.verifiedIdentity ? '重置密码' : '验证身份'), 1 /* TEXT */)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"loading\"])])]),\n    default: _withCtx(() => [_createVNode(_component_el_form, {\n      model: $setup.forgotPasswordForm,\n      rules: $setup.forgotPasswordRules,\n      ref: \"forgotPasswordFormRef\",\n      \"label-width\": \"100px\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_form_item, {\n        label: \"用户名\",\n        prop: \"username\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.forgotPasswordForm.username,\n          \"onUpdate:modelValue\": _cache[11] || (_cache[11] = $event => $setup.forgotPasswordForm.username = $event),\n          placeholder: \"请输入用户名\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"邮箱\",\n        prop: \"email\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.forgotPasswordForm.email,\n          \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $setup.forgotPasswordForm.email = $event),\n          placeholder: \"请输入注册时的邮箱\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), $setup.verifiedIdentity ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 0\n      }, [_createVNode(_component_el_form_item, {\n        label: \"新密码\",\n        prop: \"newPassword\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.forgotPasswordForm.newPassword,\n          \"onUpdate:modelValue\": _cache[13] || (_cache[13] = $event => $setup.forgotPasswordForm.newPassword = $event),\n          type: \"password\",\n          placeholder: \"请输入新密码\",\n          \"show-password\": \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: \"确认新密码\",\n        prop: \"confirmNewPassword\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_input, {\n          modelValue: $setup.forgotPasswordForm.confirmNewPassword,\n          \"onUpdate:modelValue\": _cache[14] || (_cache[14] = $event => $setup.forgotPasswordForm.confirmNewPassword = $event),\n          type: \"password\",\n          placeholder: \"请再次输入新密码\",\n          \"show-password\": \"\"\n        }, null, 8 /* PROPS */, [\"modelValue\"])]),\n        _: 1 /* STABLE */\n      })], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"model\"])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createCommentVNode", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_createVNode", "_component_el_icon", "$setup", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_component_el_form", "model", "loginForm", "rules", "loginRules", "ref", "_component_el_form_item", "prop", "_component_el_input", "username", "$event", "placeholder", "User", "password", "type", "Lock", "_hoisted_13", "_component_el_checkbox", "remember", "_cache", "href", "onClick", "_withModifiers", "showForgotPassword", "_component_el_button", "loading", "handleLogin", "_component_el_dialog", "title", "registerDialogVisible", "width", "center", "footer", "_withCtx", "_hoisted_14", "registerLoading", "handleRegister", "registerForm", "registerRules", "label", "confirmPassword", "name", "email", "phone", "forgotPasswordDialogVisible", "_hoisted_15", "forgotPasswordLoading", "handleForgotPassword", "verifiedIdentity", "forgotPasswordForm", "forgotPasswordRules", "_Fragment", "key", "newPassword", "confirmNewPassword"], "sources": ["D:\\admin\\202506\\督导系统\\后台管理系统\\ms\\src\\views\\LoginView.vue"], "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <div class=\"login-card\">\n      <!-- Left side -->\n      <div class=\"login-info\">\n        <div class=\"logo-wrapper\">\n          <div class=\"logo-icon\">\n            <img src=\"@/assets/logo.png\" alt=\"Logo\" class=\"logo-img\" />\n            <i class=\"el-icon-monitor\"></i>\n          </div>\n          <div class=\"logo-text\">\n            教学师资评价与能力认定系统\n          </div>\n        </div>\n        \n        <div class=\"welcome-text\">\n          <h2>欢迎回来</h2>\n          <p>登录您的账户以继续访问系统</p>\n        </div>\n        \n        <div class=\"feature-list\">\n          <div class=\"feature-item\">\n            <div class=\"feature-icon\">\n              <el-icon><Check /></el-icon>\n            </div>\n            <div class=\"feature-text\">现代化的管理界面</div>\n          </div>\n          <div class=\"feature-item\">\n            <div class=\"feature-icon\">\n              <el-icon><Check /></el-icon>\n            </div>\n            <div class=\"feature-text\">强大的功能模块</div>\n          </div>\n          <div class=\"feature-item\">\n            <div class=\"feature-icon\">\n              <el-icon><Check /></el-icon>\n            </div>\n            <div class=\"feature-text\">安全可靠的数据保护</div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- Right side -->\n      <div class=\"login-form-wrapper\">\n        <div class=\"login-form-container\">\n          <h2 class=\"form-title\">用户登录</h2>\n          <p class=\"form-subtitle\">请输入您的账户信息</p>\n          \n          <el-form :model=\"loginForm\" :rules=\"loginRules\" ref=\"loginFormRef\" class=\"login-form\">\n            <el-form-item prop=\"username\">\n              <el-input \n                v-model=\"loginForm.username\" \n                placeholder=\"\" \n                :prefix-icon=\"User\">\n              </el-input>\n            </el-form-item>\n            \n            <el-form-item prop=\"password\">\n              <el-input \n                v-model=\"loginForm.password\" \n                type=\"password\" \n                placeholder=\"\" \n                :prefix-icon=\"Lock\"\n                show-password>\n              </el-input>\n            </el-form-item>\n            \n            <div class=\"form-options\">\n              <el-checkbox v-model=\"loginForm.remember\">记住我</el-checkbox>\n              <a href=\"#\" class=\"forgot-link\" @click.prevent=\"showForgotPassword\">忘记密码?</a>\n            </div>\n            \n            <el-form-item>\n              <el-button type=\"primary\" :loading=\"loading\" @click=\"handleLogin\" class=\"login-button\">\n                登录\n              </el-button>\n            </el-form-item>\n            \n           \n          </el-form>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 注册对话框 -->\n    <el-dialog\n      title=\"用户注册\"\n      v-model=\"registerDialogVisible\"\n      width=\"400px\"\n      center\n      destroy-on-close\n    >\n      <el-form :model=\"registerForm\" :rules=\"registerRules\" ref=\"registerFormRef\" label-width=\"80px\">\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input v-model=\"registerForm.username\" placeholder=\"请输入用户名\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"密码\" prop=\"password\">\n          <el-input v-model=\"registerForm.password\" type=\"password\" placeholder=\"请输入密码\" show-password></el-input>\n        </el-form-item>\n        <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\n          <el-input v-model=\"registerForm.confirmPassword\" type=\"password\" placeholder=\"请再次输入密码\" show-password></el-input>\n        </el-form-item>\n        <el-form-item label=\"姓名\" prop=\"name\">\n          <el-input v-model=\"registerForm.name\" placeholder=\"请输入姓名\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"邮箱\" prop=\"email\">\n          <el-input v-model=\"registerForm.email\" placeholder=\"请输入邮箱\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"手机号\" prop=\"phone\">\n          <el-input v-model=\"registerForm.phone\" placeholder=\"请输入手机号\"></el-input>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"registerDialogVisible = false\">取消</el-button>\n          <el-button type=\"primary\" :loading=\"registerLoading\" @click=\"handleRegister\">注册</el-button>\n        </span>\n      </template>\n    </el-dialog>\n    \n    <!-- 忘记密码对话框 -->\n    <el-dialog\n      title=\"忘记密码\"\n      v-model=\"forgotPasswordDialogVisible\"\n      width=\"400px\"\n      center\n      destroy-on-close\n    >\n      <el-form :model=\"forgotPasswordForm\" :rules=\"forgotPasswordRules\" ref=\"forgotPasswordFormRef\" label-width=\"100px\">\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input v-model=\"forgotPasswordForm.username\" placeholder=\"请输入用户名\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"邮箱\" prop=\"email\">\n          <el-input v-model=\"forgotPasswordForm.email\" placeholder=\"请输入注册时的邮箱\"></el-input>\n        </el-form-item>\n        \n        <template v-if=\"verifiedIdentity\">\n          <el-form-item label=\"新密码\" prop=\"newPassword\">\n            <el-input v-model=\"forgotPasswordForm.newPassword\" type=\"password\" placeholder=\"请输入新密码\" show-password></el-input>\n          </el-form-item>\n          <el-form-item label=\"确认新密码\" prop=\"confirmNewPassword\">\n            <el-input v-model=\"forgotPasswordForm.confirmNewPassword\" type=\"password\" placeholder=\"请再次输入新密码\" show-password></el-input>\n          </el-form-item>\n        </template>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"forgotPasswordDialogVisible = false\">取消</el-button>\n          <el-button type=\"primary\" :loading=\"forgotPasswordLoading\" @click=\"handleForgotPassword\">\n            {{ verifiedIdentity ? '重置密码' : '验证身份' }}\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script setup>\nimport { reactive, ref, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage } from 'element-plus'\nimport { User, Lock, Check } from '@element-plus/icons-vue'\nimport axios from 'axios'\n\n// 配置全局API请求拦截器，自动添加token\naxios.interceptors.request.use(\n  config => {\n    const token = localStorage.getItem('token')\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`\n    }\n    return config\n  },\n  error => {\n    return Promise.reject(error)\n  }\n)\n\n// 响应拦截器，处理401错误\naxios.interceptors.response.use(\n  response => response,\n  error => {\n    if (error.response && error.response.status === 401) {\n      // 清除本地存储的token\n      localStorage.removeItem('token')\n      localStorage.removeItem('userInfo')\n      // 如果用户不在登录页，重定向到登录页\n      if (window.location.pathname !== '/login') {\n        ElMessage.error('登录已过期，请重新登录')\n        window.location.href = '/#/login'\n      }\n    }\n    return Promise.reject(error)\n  }\n)\n\nconst router = useRouter()\nconst loginFormRef = ref(null)\nconst loading = ref(false)\nconst API_URL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api'\n\n// 登录相关\nconst loginForm = reactive({\n  username: '',\n  password: '',\n  remember: false\n})\n\nconst loginRules = {\n  username: [\n    { required: true, message: '请输入用户名', trigger: 'blur' },\n    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }\n  ],\n  password: [\n    { required: true, message: '请输入密码', trigger: 'blur' },\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\n  ]\n}\n\nconst handleLogin = async () => {\n  if (!loginFormRef.value) return\n  \n  try {\n    await loginFormRef.value.validate(async (valid) => {\n      if (valid) {\n        loading.value = true\n        \n        try {\n          const response = await axios.post(`${API_URL}/auth/login`, {\n            username: loginForm.username,\n            password: loginForm.password\n          })\n          \n          // 登录成功，保存token和用户信息\n          const { token, data } = response.data\n          localStorage.setItem('token', token)\n\n          // 如果选择记住我，保存用户名\n          if (loginForm.remember) {\n            localStorage.setItem('rememberedUsername', loginForm.username)\n          } else {\n            localStorage.removeItem('rememberedUsername')\n          }\n\n          // 存储用户信息\n          localStorage.setItem('userInfo', JSON.stringify(data))\n\n          // 单独保存关键信息，方便使用\n          localStorage.setItem('userId', data.id)\n          localStorage.setItem('userRole', data.role)\n          if (data.teacher_id) {\n            localStorage.setItem('teacherId', data.teacher_id)\n            console.log('保存教师ID:', data.teacher_id)\n          } else if (data.role === 'student') {\n            // 如果是学生但没有student_id，尝试从其他地方获取\n            try {\n              const studentResponse = await axios.get(`${API_URL}/students/by-user/${data.id}`)\n              if (studentResponse.data.success && studentResponse.data.data) {\n                localStorage.setItem('studentId', studentResponse.data.data.id)\n                console.log('通过用户ID获取并保存学生ID:', studentResponse.data.data.id)\n              } else {\n                console.error('无法获取学生ID，但用户角色为学生')\n                ElMessage.warning('无法获取您的学生信息，部分功能可能无法使用')\n              }\n            } catch (err) {\n              console.error('获取学生信息失败:', err)\n            }\n          }\n\n          console.log('登录成功，用户信息:', data)\n          \n          ElMessage.success('登录成功')\n          \n          // 根据角色跳转到不同页面\n          if (data.role === 'admin' || data.role === 'administrator') {\n            router.push('/teachers/list')\n          } else if (data.role === 'teacher') {\n            router.push('/exams/list')\n          } else {\n            router.push('/dashboard') // 默认页面\n          }\n        } catch (error) {\n          console.error('登录失败:', error)\n          ElMessage.error(error.response?.data?.message || '登录失败，请检查用户名和密码')\n        } finally {\n          loading.value = false\n        }\n      }\n    })\n  } catch (error) {\n    loading.value = false\n    ElMessage.error('表单验证失败')\n  }\n}\n\n// 注册相关\nconst registerDialogVisible = ref(false)\nconst registerFormRef = ref(null)\nconst registerLoading = ref(false)\n\nconst registerForm = reactive({\n  username: '',\n  password: '',\n  confirmPassword: '',\n  name: '',\n  email: '',\n  phone: '',\n  student_id: '',\n  role: 'user'  // 默认注册为普通用户\n})\n\nconst validatePass = (rule, value, callback) => {\n  if (value === '') {\n    callback(new Error('请再次输入密码'))\n  } else if (value !== registerForm.password) {\n    callback(new Error('两次输入密码不一致!'))\n  } else {\n    callback()\n  }\n}\n\nconst registerRules = {\n  username: [\n    { required: true, message: '请输入用户名', trigger: 'blur' },\n    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }\n  ],\n  password: [\n    { required: true, message: '请输入密码', trigger: 'blur' },\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\n  ],\n  confirmPassword: [\n    { required: true, message: '请再次输入密码', trigger: 'blur' },\n    { validator: validatePass, trigger: 'blur' }\n  ],\n  name: [\n    { required: true, message: '请输入姓名', trigger: 'blur' }\n  ],\n  email: [\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\n  ],\n  phone: [\n    { pattern: /^1[3456789]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\n  ]\n}\n\nconst showRegister = () => {\n  registerDialogVisible.value = true\n}\n\nconst handleRegister = async () => {\n  if (!registerFormRef.value) return\n  \n  try {\n    await registerFormRef.value.validate(async (valid) => {\n      if (valid) {\n        registerLoading.value = true\n        \n        try {\n          const { confirmPassword, ...registerData } = registerForm\n          \n          const response = await axios.post(`${API_URL}/auth/register`, registerData)\n          \n          ElMessage.success('注册成功，请登录')\n          registerDialogVisible.value = false\n          \n          // 可选：自动填充登录表单\n          loginForm.username = registerForm.username\n          loginForm.password = ''\n        } catch (error) {\n          console.error('注册失败:', error)\n          ElMessage.error(error.response?.data?.message || '注册失败，请稍后重试')\n        } finally {\n          registerLoading.value = false\n        }\n      }\n    })\n  } catch (error) {\n    registerLoading.value = false\n    ElMessage.error('表单验证失败')\n  }\n}\n\n// 忘记密码相关\nconst forgotPasswordDialogVisible = ref(false)\nconst forgotPasswordFormRef = ref(null)\nconst forgotPasswordLoading = ref(false)\nconst verifiedIdentity = ref(false)\n\nconst forgotPasswordForm = reactive({\n  username: '',\n  email: '',\n  newPassword: '',\n  confirmNewPassword: ''\n})\n\nconst validateNewPass = (rule, value, callback) => {\n  if (value === '') {\n    callback(new Error('请输入新密码'))\n  } else if (value.length < 6 || value.length > 20) {\n    callback(new Error('密码长度应在6到20个字符之间'))\n  } else {\n    // 如果确认密码已输入，重新验证\n    if (forgotPasswordForm.confirmNewPassword !== '') {\n      forgotPasswordFormRef.value?.validateField('confirmNewPassword')\n    }\n    callback()\n  }\n}\n\nconst validateConfirmNewPass = (rule, value, callback) => {\n  if (value === '') {\n    callback(new Error('请再次输入新密码'))\n  } else if (value !== forgotPasswordForm.newPassword) {\n    callback(new Error('两次输入密码不一致!'))\n  } else {\n    callback()\n  }\n}\n\nconst forgotPasswordRules = {\n  username: [\n    { required: true, message: '请输入用户名', trigger: 'blur' }\n  ],\n  email: [\n    { required: true, message: '请输入邮箱', trigger: 'blur' },\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\n  ],\n  newPassword: [\n    { required: true, message: '请输入新密码', trigger: 'blur' },\n    { validator: validateNewPass, trigger: 'blur' }\n  ],\n  confirmNewPassword: [\n    { required: true, message: '请再次输入新密码', trigger: 'blur' },\n    { validator: validateConfirmNewPass, trigger: 'blur' }\n  ]\n}\n\nconst showForgotPassword = () => {\n  forgotPasswordDialogVisible.value = true\n  verifiedIdentity.value = false\n  forgotPasswordForm.newPassword = ''\n  forgotPasswordForm.confirmNewPassword = ''\n}\n\nconst handleForgotPassword = async () => {\n  if (!forgotPasswordFormRef.value) return\n  \n  try {\n    // 根据当前步骤验证不同的字段\n    if (verifiedIdentity.value) {\n      // 只验证密码字段\n      await forgotPasswordFormRef.value.validateField(['newPassword', 'confirmNewPassword'])\n    } else {\n      // 只验证用户名和邮箱\n      await forgotPasswordFormRef.value.validateField(['username', 'email'])\n    }\n    \n    forgotPasswordLoading.value = true\n    \n    try {\n      if (!verifiedIdentity.value) {\n        // 第一步：验证用户名和邮箱是否匹配\n        const response = await axios.post(`${API_URL}/auth/verify-identity`, {\n          username: forgotPasswordForm.username,\n          email: forgotPasswordForm.email\n        })\n        \n        if (response.data.success) {\n          ElMessage.success('身份验证成功，请设置新密码')\n          verifiedIdentity.value = true\n        } else {\n          ElMessage.error(response.data.message || '用户名和邮箱不匹配')\n        }\n      } else {\n        // 第二步：重置密码\n        const response = await axios.post(`${API_URL}/auth/reset-password`, {\n          username: forgotPasswordForm.username,\n          email: forgotPasswordForm.email,\n          newPassword: forgotPasswordForm.newPassword\n        })\n        \n        ElMessage.success('密码重置成功，请使用新密码登录')\n        forgotPasswordDialogVisible.value = false\n        \n        // 可以选择自动填充用户名到登录表单\n        loginForm.username = forgotPasswordForm.username\n        loginForm.password = ''\n      }\n    } catch (error) {\n      console.error('操作失败:', error)\n      if (verifiedIdentity.value) {\n        ElMessage.error(error.response?.data?.message || '密码重置失败，请稍后重试')\n      } else {\n        ElMessage.error(error.response?.data?.message || '身份验证失败，请确认用户名和邮箱是否正确')\n      }\n    } finally {\n      forgotPasswordLoading.value = false\n    }\n  } catch (error) {\n    forgotPasswordLoading.value = false\n    ElMessage.error('表单验证失败')\n    console.error(error)\n  }\n}\n\n// 检查是否有记住的用户名\nconst checkRememberedUsername = () => {\n  const rememberedUsername = localStorage.getItem('rememberedUsername')\n  if (rememberedUsername) {\n    loginForm.username = rememberedUsername\n    loginForm.remember = true\n  }\n}\n\n// 组件挂载时检查记住的用户名\ncheckRememberedUsername()\n</script>\n\n<style scoped>\n.login-container {\n  height: 100vh;\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgb(149, 117, 205);\n}\n\n.login-card {\n  width: 1000px;\n  height: 600px;\n  display: flex;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.1);\n}\n\n/* Left side */\n.login-info {\n  width: 50%;\n  background: linear-gradient(135deg, #8E44AD 0%, #7D3C98 100%);\n  padding: 40px;\n  display: flex;\n  flex-direction: column;\n  color: white;\n}\n\n.logo-wrapper {\n  display: flex;\n  align-items: center;\n  margin-bottom: 60px;\n}\n\n.logo-icon {\n  width: 120px;\n  height: 120px;\n  background-color: white;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 12px;\n  font-size: 24px;\n  color: #8E44AD;\n}\n\n.logo-text {\n  font-size: 20px;\n  font-weight: bold;\n}\n\n.welcome-text {\n  margin-bottom: 60px;\n}\n\n.welcome-text h2 {\n  font-size: 32px;\n  margin-bottom: 12px;\n  font-weight: 600;\n}\n\n.welcome-text p {\n  font-size: 16px;\n  opacity: 0.8;\n}\n\n.feature-list {\n  margin-top: auto;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.feature-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background-color: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 12px;\n}\n\n.feature-text {\n  font-size: 16px;\n}\n\n/* Right side */\n.login-form-wrapper {\n  width: 50%;\n  background-color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40px;\n}\n\n.login-form-container {\n  width: 100%;\n  max-width: 320px;\n}\n\n.form-title {\n  font-size: 24px;\n  color: #333;\n  margin-bottom: 8px;\n  text-align: center;\n}\n\n.form-subtitle {\n  font-size: 14px;\n  color: #999;\n  margin-bottom: 30px;\n  text-align: center;\n}\n\n.login-form :deep(.el-input__wrapper) {\n  padding: 0 15px;\n  height: 50px;\n  box-shadow: 0 0 0 1px #e4e7ed inset;\n}\n\n.login-form :deep(.el-input__wrapper.is-focus) {\n  box-shadow: 0 0 0 1px #8E44AD inset;\n}\n\n.form-options {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.forgot-link {\n  color: #9B59B6;\n  text-decoration: none;\n  font-size: 14px;\n}\n\n.login-button {\n  width: 100%;\n  height: 50px;\n  border-radius: 6px;\n  font-size: 16px;\n  background: linear-gradient(135deg, #8E44AD 0%, #7D3C98 100%);\n  border: none;\n  margin-bottom: 20px;\n}\n\n.register-link {\n  text-align: center;\n  font-size: 14px;\n  color: #666;\n}\n\n.register-link a {\n  color: #9B59B6;\n  text-decoration: none;\n  margin-left: 5px;\n}\n\n/* Responsive */\n@media (max-width: 992px) {\n  .login-card {\n    width: 90%;\n    height: auto;\n    flex-direction: column;\n  }\n  \n  .login-info,\n  .login-form-wrapper {\n    width: 100%;\n    padding: 30px;\n  }\n  \n  .login-info {\n    padding-bottom: 40px;\n  }\n  \n  .welcome-text {\n    margin-bottom: 30px;\n  }\n  \n  .feature-list {\n    margin-top: 0;\n  }\n}\n.logo-img {\n  width: 80px;\n  height: 80px;\n  /* margin-right: 4px; */\n  object-fit: contain;\n}\n\n\n</style> "], "mappings": ";OAOiBA,UAAuB;;EANjCC,KAAK,EAAC;AAAiB;;EACrBA,KAAK,EAAC;AAAY;;EAEhBA,KAAK,EAAC;AAAY;;EAgBhBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAc;;EAKtBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAc;;EAKtBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAc;;EAS1BA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAsB;;EAuBxBA,KAAK,EAAC;AAAc;;EA8CvBA,KAAK,EAAC;AAAe;;EAiCrBA,KAAK,EAAC;AAAe;;;;;;;;;uBAjJjCC,mBAAA,CAyJM,OAzJNC,UAyJM,GAxJJC,mBAAA,CAgFM,OAhFNC,UAgFM,GA/EJC,mBAAA,eAAkB,EAClBF,mBAAA,CAoCM,OApCNG,UAoCM,G,2cApBJH,mBAAA,CAmBM,OAnBNI,UAmBM,GAlBJJ,mBAAA,CAKM,OALNK,UAKM,GAJJL,mBAAA,CAEM,OAFNM,UAEM,GADJC,YAAA,CAA4BC,kBAAA;sBAAnB,MAAS,CAATD,YAAA,CAASE,MAAA,W;;oCAEpBT,mBAAA,CAAwC;IAAnCH,KAAK,EAAC;EAAc,GAAC,UAAQ,oB,GAEpCG,mBAAA,CAKM,OALNU,UAKM,GAJJV,mBAAA,CAEM,OAFNW,UAEM,GADJJ,YAAA,CAA4BC,kBAAA;sBAAnB,MAAS,CAATD,YAAA,CAASE,MAAA,W;;oCAEpBT,mBAAA,CAAuC;IAAlCH,KAAK,EAAC;EAAc,GAAC,SAAO,oB,GAEnCG,mBAAA,CAKM,OALNY,UAKM,GAJJZ,mBAAA,CAEM,OAFNa,WAEM,GADJN,YAAA,CAA4BC,kBAAA;sBAAnB,MAAS,CAATD,YAAA,CAASE,MAAA,W;;oCAEpBT,mBAAA,CAAyC;IAApCH,KAAK,EAAC;EAAc,GAAC,WAAS,oB,OAKzCK,mBAAA,gBAAmB,EACnBF,mBAAA,CAsCM,OAtCNc,WAsCM,GArCJd,mBAAA,CAoCM,OApCNe,WAoCM,G,4BAnCJf,mBAAA,CAAgC;IAA5BH,KAAK,EAAC;EAAY,GAAC,MAAI,qB,4BAC3BG,mBAAA,CAAsC;IAAnCH,KAAK,EAAC;EAAe,GAAC,WAAS,qBAElCU,YAAA,CA+BUS,kBAAA;IA/BAC,KAAK,EAAER,MAAA,CAAAS,SAAS;IAAGC,KAAK,EAAEV,MAAA,CAAAW,UAAU;IAAEC,GAAG,EAAC,cAAc;IAACxB,KAAK,EAAC;;sBACvE,MAMe,CANfU,YAAA,CAMee,uBAAA;MANDC,IAAI,EAAC;IAAU;wBAC3B,MAIW,CAJXhB,YAAA,CAIWiB,mBAAA;oBAHAf,MAAA,CAAAS,SAAS,CAACO,QAAQ;mEAAlBhB,MAAA,CAAAS,SAAS,CAACO,QAAQ,GAAAC,MAAA;QAC3BC,WAAW,EAAC,EAAE;QACb,aAAW,EAAElB,MAAA,CAAAmB;;;QAIlBrB,YAAA,CAQee,uBAAA;MARDC,IAAI,EAAC;IAAU;wBAC3B,MAMW,CANXhB,YAAA,CAMWiB,mBAAA;oBALAf,MAAA,CAAAS,SAAS,CAACW,QAAQ;mEAAlBpB,MAAA,CAAAS,SAAS,CAACW,QAAQ,GAAAH,MAAA;QAC3BI,IAAI,EAAC,UAAU;QACfH,WAAW,EAAC,EAAE;QACb,aAAW,EAAElB,MAAA,CAAAsB,IAAI;QAClB,eAAa,EAAb;;;QAIJ/B,mBAAA,CAGM,OAHNgC,WAGM,GAFJzB,YAAA,CAA2D0B,sBAAA;kBAArCxB,MAAA,CAAAS,SAAS,CAACgB,QAAQ;iEAAlBzB,MAAA,CAAAS,SAAS,CAACgB,QAAQ,GAAAR,MAAA;;wBAAE,MAAGS,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;uCAC7CnC,mBAAA,CAA6E;MAA1EoC,IAAI,EAAC,GAAG;MAACvC,KAAK,EAAC,aAAa;MAAEwC,OAAK,EAAAC,cAAA,CAAU7B,MAAA,CAAA8B,kBAAkB;OAAE,OAAK,E,GAG3EhC,YAAA,CAIee,uBAAA;wBAHb,MAEY,CAFZf,YAAA,CAEYiC,oBAAA;QAFDV,IAAI,EAAC,SAAS;QAAEW,OAAO,EAAEhC,MAAA,CAAAgC,OAAO;QAAGJ,OAAK,EAAE5B,MAAA,CAAAiC,WAAW;QAAE7C,KAAK,EAAC;;0BAAe,MAEvFsC,MAAA,SAAAA,MAAA,Q,iBAFuF,MAEvF,E;;;;;;;sCASVjC,mBAAA,WAAc,EACdK,YAAA,CAiCYoC,oBAAA;IAhCVC,KAAK,EAAC,MAAM;gBACHnC,MAAA,CAAAoC,qBAAqB;iEAArBpC,MAAA,CAAAoC,qBAAqB,GAAAnB,MAAA;IAC9BoB,KAAK,EAAC,OAAO;IACbC,MAAM,EAAN,EAAM;IACN,kBAAgB,EAAhB;;IAsBWC,MAAM,EAAAC,QAAA,CACf,MAGO,CAHPjD,mBAAA,CAGO,QAHPkD,WAGO,GAFL3C,YAAA,CAAgEiC,oBAAA;MAApDH,OAAK,EAAAF,MAAA,QAAAA,MAAA,MAAAT,MAAA,IAAEjB,MAAA,CAAAoC,qBAAqB;;wBAAU,MAAEV,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QACpD5B,YAAA,CAA2FiC,oBAAA;MAAhFV,IAAI,EAAC,SAAS;MAAEW,OAAO,EAAEhC,MAAA,CAAA0C,eAAe;MAAGd,OAAK,EAAE5B,MAAA,CAAA2C;;wBAAgB,MAAEjB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;sBAvBnF,MAmBU,CAnBV5B,YAAA,CAmBUS,kBAAA;MAnBAC,KAAK,EAAER,MAAA,CAAA4C,YAAY;MAAGlC,KAAK,EAAEV,MAAA,CAAA6C,aAAa;MAAEjC,GAAG,EAAC,iBAAiB;MAAC,aAAW,EAAC;;wBACtF,MAEe,CAFfd,YAAA,CAEee,uBAAA;QAFDiC,KAAK,EAAC,KAAK;QAAChC,IAAI,EAAC;;0BAC7B,MAA0E,CAA1EhB,YAAA,CAA0EiB,mBAAA;sBAAvDf,MAAA,CAAA4C,YAAY,CAAC5B,QAAQ;qEAArBhB,MAAA,CAAA4C,YAAY,CAAC5B,QAAQ,GAAAC,MAAA;UAAEC,WAAW,EAAC;;;UAExDpB,YAAA,CAEee,uBAAA;QAFDiC,KAAK,EAAC,IAAI;QAAChC,IAAI,EAAC;;0BAC5B,MAAuG,CAAvGhB,YAAA,CAAuGiB,mBAAA;sBAApFf,MAAA,CAAA4C,YAAY,CAACxB,QAAQ;qEAArBpB,MAAA,CAAA4C,YAAY,CAACxB,QAAQ,GAAAH,MAAA;UAAEI,IAAI,EAAC,UAAU;UAACH,WAAW,EAAC,OAAO;UAAC,eAAa,EAAb;;;UAEhFpB,YAAA,CAEee,uBAAA;QAFDiC,KAAK,EAAC,MAAM;QAAChC,IAAI,EAAC;;0BAC9B,MAAgH,CAAhHhB,YAAA,CAAgHiB,mBAAA;sBAA7Ff,MAAA,CAAA4C,YAAY,CAACG,eAAe;qEAA5B/C,MAAA,CAAA4C,YAAY,CAACG,eAAe,GAAA9B,MAAA;UAAEI,IAAI,EAAC,UAAU;UAACH,WAAW,EAAC,SAAS;UAAC,eAAa,EAAb;;;UAEzFpB,YAAA,CAEee,uBAAA;QAFDiC,KAAK,EAAC,IAAI;QAAChC,IAAI,EAAC;;0BAC5B,MAAqE,CAArEhB,YAAA,CAAqEiB,mBAAA;sBAAlDf,MAAA,CAAA4C,YAAY,CAACI,IAAI;qEAAjBhD,MAAA,CAAA4C,YAAY,CAACI,IAAI,GAAA/B,MAAA;UAAEC,WAAW,EAAC;;;UAEpDpB,YAAA,CAEee,uBAAA;QAFDiC,KAAK,EAAC,IAAI;QAAChC,IAAI,EAAC;;0BAC5B,MAAsE,CAAtEhB,YAAA,CAAsEiB,mBAAA;sBAAnDf,MAAA,CAAA4C,YAAY,CAACK,KAAK;qEAAlBjD,MAAA,CAAA4C,YAAY,CAACK,KAAK,GAAAhC,MAAA;UAAEC,WAAW,EAAC;;;UAErDpB,YAAA,CAEee,uBAAA;QAFDiC,KAAK,EAAC,KAAK;QAAChC,IAAI,EAAC;;0BAC7B,MAAuE,CAAvEhB,YAAA,CAAuEiB,mBAAA;sBAApDf,MAAA,CAAA4C,YAAY,CAACM,KAAK;qEAAlBlD,MAAA,CAAA4C,YAAY,CAACM,KAAK,GAAAjC,MAAA;UAAEC,WAAW,EAAC;;;;;;;qCAWzDzB,mBAAA,aAAgB,EAChBK,YAAA,CAgCYoC,oBAAA;IA/BVC,KAAK,EAAC,MAAM;gBACHnC,MAAA,CAAAmD,2BAA2B;iEAA3BnD,MAAA,CAAAmD,2BAA2B,GAAAlC,MAAA;IACpCoB,KAAK,EAAC,OAAO;IACbC,MAAM,EAAN,EAAM;IACN,kBAAgB,EAAhB;;IAmBWC,MAAM,EAAAC,QAAA,CACf,MAKO,CALPjD,mBAAA,CAKO,QALP6D,WAKO,GAJLtD,YAAA,CAAsEiC,oBAAA;MAA1DH,OAAK,EAAAF,MAAA,SAAAA,MAAA,OAAAT,MAAA,IAAEjB,MAAA,CAAAmD,2BAA2B;;wBAAU,MAAEzB,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC1D5B,YAAA,CAEYiC,oBAAA;MAFDV,IAAI,EAAC,SAAS;MAAEW,OAAO,EAAEhC,MAAA,CAAAqD,qBAAqB;MAAGzB,OAAK,EAAE5B,MAAA,CAAAsD;;wBACjE,MAAwC,C,kCAArCtD,MAAA,CAAAuD,gBAAgB,mC;;;sBArBzB,MAgBU,CAhBVzD,YAAA,CAgBUS,kBAAA;MAhBAC,KAAK,EAAER,MAAA,CAAAwD,kBAAkB;MAAG9C,KAAK,EAAEV,MAAA,CAAAyD,mBAAmB;MAAE7C,GAAG,EAAC,uBAAuB;MAAC,aAAW,EAAC;;wBACxG,MAEe,CAFfd,YAAA,CAEee,uBAAA;QAFDiC,KAAK,EAAC,KAAK;QAAChC,IAAI,EAAC;;0BAC7B,MAAgF,CAAhFhB,YAAA,CAAgFiB,mBAAA;sBAA7Df,MAAA,CAAAwD,kBAAkB,CAACxC,QAAQ;uEAA3BhB,MAAA,CAAAwD,kBAAkB,CAACxC,QAAQ,GAAAC,MAAA;UAAEC,WAAW,EAAC;;;UAE9DpB,YAAA,CAEee,uBAAA;QAFDiC,KAAK,EAAC,IAAI;QAAChC,IAAI,EAAC;;0BAC5B,MAAgF,CAAhFhB,YAAA,CAAgFiB,mBAAA;sBAA7Df,MAAA,CAAAwD,kBAAkB,CAACP,KAAK;uEAAxBjD,MAAA,CAAAwD,kBAAkB,CAACP,KAAK,GAAAhC,MAAA;UAAEC,WAAW,EAAC;;;UAG3ClB,MAAA,CAAAuD,gBAAgB,I,cAAhClE,mBAAA,CAOWqE,SAAA;QAAAC,GAAA;MAAA,IANT7D,YAAA,CAEee,uBAAA;QAFDiC,KAAK,EAAC,KAAK;QAAChC,IAAI,EAAC;;0BAC7B,MAAiH,CAAjHhB,YAAA,CAAiHiB,mBAAA;sBAA9Ff,MAAA,CAAAwD,kBAAkB,CAACI,WAAW;uEAA9B5D,MAAA,CAAAwD,kBAAkB,CAACI,WAAW,GAAA3C,MAAA;UAAEI,IAAI,EAAC,UAAU;UAACH,WAAW,EAAC,QAAQ;UAAC,eAAa,EAAb;;;UAE1FpB,YAAA,CAEee,uBAAA;QAFDiC,KAAK,EAAC,OAAO;QAAChC,IAAI,EAAC;;0BAC/B,MAA0H,CAA1HhB,YAAA,CAA0HiB,mBAAA;sBAAvGf,MAAA,CAAAwD,kBAAkB,CAACK,kBAAkB;uEAArC7D,MAAA,CAAAwD,kBAAkB,CAACK,kBAAkB,GAAA5C,MAAA;UAAEI,IAAI,EAAC,UAAU;UAACH,WAAW,EAAC,UAAU;UAAC,eAAa,EAAb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}