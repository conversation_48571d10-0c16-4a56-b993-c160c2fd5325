<template>
  <div class="exam-taking-container">
    <div v-if="!examStarted" class="exam-intro">
      <el-card class="intro-card" v-loading="loading">
        <template #header>
          <div class="card-header">
            <span>{{ exam.title || '加载中...' }}</span>
          </div>
        </template>
        <div class="exam-info">
          <p><strong>考试描述：</strong>{{ exam.description || '加载中...' }}</p>
          <p><strong>考试时长：</strong>{{ exam.duration || '-' }} 分钟</p>
          <p><strong>及格分数：</strong>{{ exam.pass_score || '-' }}</p>
          <p><strong>总分：</strong>{{ exam.total_score || '-' }}</p>
          <p v-if="attemptsInfo.loaded"><strong>考试次数：</strong>第 {{ attemptsInfo.current + 1 }} 次 / 共 2 次</p>
          <div class="exam-rules">
            <h4>考试须知：</h4>
            <ol>
              <li>开始考试后，考试时间将自动倒计时</li>
              <li>请在规定时间内完成所有题目并提交</li>
              <li>提交后将自动评分并显示结果</li>
              <li>若时间结束未提交，系统将自动提交当前答案</li>
              <li>每位学生最多有2次参加考试的机会</li>
            </ol>
          </div>
        </div>
        <div v-if="!loading">
          <div v-if="questions.length > 0 && attemptsInfo.remaining > 0" class="start-button">
            <el-button type="primary" size="large" @click="startExam">开始考试</el-button>
          </div>
          <div v-else-if="attemptsInfo.remaining <= 0" class="no-attempts-message">
            <el-alert
              title="无法参加考试"
              type="error"
              description="您已经达到了该考试的最大尝试次数（2次）。"
              show-icon
              :closable="false"
            />
            <div class="return-button">
              <el-button type="info" @click="returnToList">返回列表</el-button>
            </div>
          </div>
          <div v-else class="no-questions-message">
            <el-alert
              title="暂无考题"
              type="warning"
              description="该考试暂未添加题目，请联系管理员。"
              show-icon
              :closable="false"
            />
            <div class="return-button">
              <el-button type="info" @click="returnToList">返回列表</el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <div v-else class="exam-content">
      <el-card class="exam-header-card">
        <div class="exam-header">
          <h2>{{ exam.title }}</h2>
          <div class="exam-timer">
            剩余时间：<span :class="{ 'time-warning': timeRemaining <= 300 }">{{ formatTime(timeRemaining) }}</span>
          </div>
        </div>
      </el-card>

      <el-card class="questions-card">
        <div class="questions-progress">
          <el-steps :active="currentQuestionIndex + 1" simple>
            <el-step 
              v-for="(question, index) in questions" 
              :key="index" 
              :title="`题 ${index + 1}`"
              @click="goToQuestion(index)"
              class="question-step"
              :class="{ 'question-answered': userAnswers[question.id] }"
            />
          </el-steps>
        </div>

        <div v-if="currentQuestion" class="question-content">
          <div class="question-type">{{ getQuestionTypeText(currentQuestion.question_type) }} ({{ currentQuestion.score }}分)</div>
          <div class="question-text">{{ currentQuestion.question }}</div>

          <!-- 单选题 -->
          <div v-if="currentQuestion.question_type === '单选题'" class="options-container">
            <el-radio-group v-model="userAnswers[currentQuestion.id]">
              <el-radio 
                v-for="option in parseOptions(currentQuestion.options)" 
                :key="option.key" 
                :label="option.key" 
                class="option-item"
              >
                {{ option.key }}. {{ option.text }}
              </el-radio>
            </el-radio-group>
          </div>

          <!-- 多选题 -->
          <div v-else-if="currentQuestion.question_type === '多选题'" class="options-container">
            <el-checkbox-group v-model="userAnswers[currentQuestion.id]">
              <el-checkbox 
                v-for="option in parseOptions(currentQuestion.options)" 
                :key="option.key" 
                :label="option.key"
                class="option-item"
              >
                {{ option.key }}. {{ option.text }}
              </el-checkbox>
            </el-checkbox-group>
          </div>

          <!-- 判断题 -->
          <div v-else-if="currentQuestion.question_type === '判断题'" class="options-container">
            <el-radio-group v-model="userAnswers[currentQuestion.id]">
              <el-radio label="正确" class="option-item">正确</el-radio>
              <el-radio label="错误" class="option-item">错误</el-radio>
            </el-radio-group>
          </div>

          <!-- 简答题 -->
          <div v-else-if="currentQuestion.question_type === '简答题'" class="options-container">
            <el-input
              v-model="userAnswers[currentQuestion.id]"
              type="textarea"
              :rows="5"
              placeholder="请输入答案"
            />
          </div>

          <div class="question-navigation">
            <el-button @click="previousQuestion" :disabled="currentQuestionIndex === 0">上一题</el-button>
            <el-button type="primary" @click="nextQuestion" v-if="currentQuestionIndex < questions.length - 1">下一题</el-button>
            <el-button type="success" @click="showSubmitConfirm" v-else>提交试卷</el-button>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 提交确认对话框 -->
    <el-dialog
      title="确认提交"
      v-model="submitDialogVisible"
      width="400px"
    >
      <div class="submit-dialog-content">
        <p>您即将提交试卷，请确认：</p>
        <p class="unanswered-warning" v-if="unansweredCount > 0">
          您还有 {{ unansweredCount }} 道题目未作答，确定提交吗？
        </p>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="submitDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitExam" :loading="submitting">确认提交</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 考试结果对话框 -->
    <el-dialog
      title="考试结果"
      v-model="resultDialogVisible"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="result-dialog-content" v-if="examResult">
        <div class="result-header">
          <div class="result-status" :class="{ 'pass': examResult.summary.is_passed, 'fail': !examResult.summary.is_passed }">
            {{ examResult.summary.is_passed ? '通过' : '未通过' }}
          </div>
          <div class="result-score">
            得分：<span>{{ examResult.summary.score }}</span> / {{ exam.total_score }}
          </div>
        </div>
        
        <div class="result-details">
          <div class="detail-item">
            <div class="detail-label">总题数：</div>
            <div class="detail-value">{{ examResult.summary.total_questions }}</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">正确题数：</div>
            <div class="detail-value">{{ examResult.summary.correct_questions }}</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">错误题数：</div>
            <div class="detail-value">{{ examResult.summary.total_questions - examResult.summary.correct_questions }}</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">及格分数：</div>
            <div class="detail-value">{{ exam.pass_score }}</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">尝试次数：</div>
            <div class="detail-value">第 {{ examResult.summary.attempt_number }} 次 / 共 2 次</div>
          </div>
          <div class="detail-item">
            <div class="detail-label">剩余次数：</div>
            <div class="detail-value">{{ examResult.summary.remaining_attempts }} 次</div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="returnToList">返回列表</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import examService from '@/services/examService'
import { formatDate } from '@/utils/dateFormat'

const route = useRoute()
const router = useRouter()
const examId = route.params.id

// 考试状态
const loading = ref(true)
const examStarted = ref(false)
const timeRemaining = ref(0)
const currentQuestionIndex = ref(0)
const submitDialogVisible = ref(false)
const resultDialogVisible = ref(false)
const submitting = ref(false)

// 考试数据
const exam = ref({})
const questions = ref([])
const userAnswers = reactive({})
const examResult = ref(null)

// 考试尝试次数信息
const attemptsInfo = reactive({
  loaded: false,
  current: 0,
  total: 2,
  remaining: 2
})

// 计时器
let timer = null

// 获取考试信息和题目
const fetchExamData = async () => {
  loading.value = true
  try {
    const examResponse = await examService.getExam(examId)
    exam.value = examResponse.data.data
    
    const questionsResponse = await examService.getExamQuestions(examId)
    questions.value = questionsResponse.data.data
    
    if (questions.value.length === 0) {
      ElMessage.warning('该考试暂无题目')
    }
    
    // 获取学生尝试次数
    await checkAttemptsCount()
    
    loading.value = false
  } catch (error) {
    console.error('获取考试数据失败', error)
    ElMessage.error('获取考试数据失败')
    loading.value = false
  }
}

// 检查考试尝试次数
const checkAttemptsCount = async () => {
  try {
    const teacherId = localStorage.getItem('teacherId')
    if (!teacherId) {
      throw new Error('未找到有效的教师ID')
    }
    
    console.log('检查考试尝试次数 - 教师ID:', teacherId)
    
    const response = await examService.getExamResults(examId, { teacher_id: teacherId })
    const attempts = response.data.data.results ? response.data.data.results.length : 0
    
    attemptsInfo.loaded = true
    attemptsInfo.current = attempts
    attemptsInfo.remaining = Math.max(0, 2 - attempts)
    
    if (attemptsInfo.remaining <= 0) {
      ElMessage.warning('您已达到该考试的最大尝试次数')
    }
  } catch (error) {
    console.error('检查考试尝试次数失败', error)
    ElMessage.error('检查考试尝试次数失败')
  }
}

// 开始考试
const startExam = () => {
  examStarted.value = true
  timeRemaining.value = exam.value.duration * 60 // 转换为秒
  startTimer()
}

// 启动计时器
const startTimer = () => {
  timer = setInterval(() => {
    if (timeRemaining.value > 0) {
      timeRemaining.value--
    } else {
      // 时间结束，自动提交
      clearInterval(timer)
      submitExam()
    }
  }, 1000)
}

// 格式化时间
const formatTime = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60
  
  let result = ''
  
  if (hours > 0) {
    result += `${hours}小时 `
  }
  
  if (minutes > 0 || hours > 0) {
    result += `${minutes}分钟 `
  }
  
  result += `${secs}秒`
  
  return result
}

// 导航到上一题
const previousQuestion = () => {
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--
  }
}

// 导航到下一题
const nextQuestion = () => {
  if (currentQuestionIndex.value < questions.value.length - 1) {
    currentQuestionIndex.value++
  }
}

// 导航到指定题目
const goToQuestion = (index) => {
  currentQuestionIndex.value = index
}

// 获取当前题目
const currentQuestion = computed(() => {
  return questions.value[currentQuestionIndex.value]
})

// 解析题目选项
const parseOptions = (options) => {
  if (typeof options === 'string') {
    try {
      return JSON.parse(options)
    } catch (e) {
      console.error('解析选项失败', e)
      return []
    }
  }
  return options || []
}

// 获取题型文本
const getQuestionTypeText = (type) => {
  return type
}

// 显示提交确认对话框
const showSubmitConfirm = () => {
  submitDialogVisible.value = true
}

// 提交试卷
const submitExam = async () => {
  submitting.value = true
  submitDialogVisible.value = false
  
  try {
    // 准备答案数据
    const answers = []
    questions.value.forEach(question => {
      answers.push({
        question_id: question.id,
        answer: userAnswers[question.id] || '' // 处理未回答的题目
      })
    })
    
    // 获取学生ID (而不是用户ID)
    const teacherId = localStorage.getItem('teacherId')
    
    // 检查是否有有效的学生ID
    if (!teacherId) {
      ElMessage.error('未找到有效的教师ID，请重新登录')
      returnToList()
      return
    }
    
    console.log('提交考试 - 使用教师ID:', teacherId, '考试ID:', examId)
    
    // 提交到后端
    const response = await examService.submitExam({
      teacher_id: teacherId,
      exam_id: examId,
      answers: answers
    })
    
    // 清除计时器
    if (timer) {
      clearInterval(timer)
      timer = null
    }
    
    // 显示结果
    examResult.value = response.data.data
    resultDialogVisible.value = true
    
  } catch (error) {
    console.error('提交考试失败', error)
    
    if (error.response && error.response.status === 403) {
      // 尝试次数已用完
      ElMessage.error('您已达到该考试的最大尝试次数')
      returnToList()
    } else {
      ElMessage.error('提交考试失败: ' + (error.response?.data?.message || error.message))
    }
    
    submitting.value = false
  }
}

// 未回答的题目数量
const unansweredCount = computed(() => {
  let count = 0
  questions.value.forEach(question => {
    if (!userAnswers[question.id]) {
      count++
    }
  })
  return count
})

// 返回列表页
const returnToList = () => {
  router.push('/exams/list')
}

onMounted(() => {
  fetchExamData()
})

onBeforeUnmount(() => {
  // 清除计时器
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style scoped>
.exam-taking-container {
  padding: 20px;
}

.exam-intro {
  max-width: 800px;
  margin: 0 auto;
}

.intro-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 18px;
  font-weight: 600;
}

.exam-info {
  padding: 10px 0;
}

.exam-rules {
  margin-top: 20px;
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
}

.exam-rules h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #606266;
}

.exam-rules ol {
  padding-left: 20px;
  margin-bottom: 0;
}

.exam-rules li {
  margin-bottom: 5px;
}

.exam-rules li:last-child {
  margin-bottom: 0;
}

.start-button {
  text-align: center;
  margin-top: 30px;
}

.no-attempts-message {
  text-align: center;
  margin-top: 30px;
}

.no-questions-message {
  text-align: center;
  margin-top: 30px;
}

.return-button {
  margin-top: 20px;
}

.exam-header-card {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.exam-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.exam-header h2 {
  margin: 0;
  font-weight: 600;
}

.exam-timer {
  font-size: 16px;
  font-weight: 600;
}

.time-warning {
  color: #F56C6C;
}

.questions-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.questions-progress {
  margin-bottom: 30px;
}

.question-step {
  cursor: pointer;
}

.question-answered {
  color: #67C23A;
}

.question-content {
  padding: 10px 0;
}

.question-type {
  color: #909399;
  margin-bottom: 10px;
  font-size: 14px;
}

.question-text {
  font-size: 16px;
  margin-bottom: 20px;
  line-height: 1.6;
}

.options-container {
  margin-top: 15px;
  margin-bottom: 30px;
}

.option-item {
  display: block;
  margin-bottom: 15px;
}

.question-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: 30px;
}

.submit-dialog-content {
  text-align: center;
  padding: 10px 0;
}

.unanswered-warning {
  color: #E6A23C;
  font-weight: bold;
}

.result-dialog-content {
  padding: 20px 0;
}

.result-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
}

.result-status {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
}

.pass {
  color: #67C23A;
}

.fail {
  color: #F56C6C;
}

.result-score {
  font-size: 18px;
}

.result-score span {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.result-details {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.detail-item {
  display: flex;
  margin-bottom: 10px;
}

.detail-label {
  width: 100px;
  color: #606266;
}

.detail-value {
  font-weight: 600;
}

/* Style the Element Plus components to match LoginView style */
:deep(.el-button--primary) {
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
  border: none;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #66b1ff 0%, #5098fa 100%);
  border: none;
}
</style> 