"use strict";(self["webpackChunkms"]=self["webpackChunkms"]||[]).push([[901],{7901:function(a,e,t){t.r(e),t.d(e,{default:function(){return E}});var l=t(6768),s=t(4232);const c={class:"evaluation-detail-container"},i={class:"card-header"},r={class:"description-content"},o={class:"description-content"},n={class:"description-content"},d={class:"teacher-card-header"},u={class:"teacher-avatar"},v={key:0,class:"teacher-stats"},_={class:"teacher-stats-item"},k={class:"stats-value"},b={class:"teacher-stats-item"},p={class:"stats-value"},h={class:"teacher-stats-item"},f={class:"stats-value"},m={class:"teacher-stats-item"},g={class:"stats-value"};function D(a,e,t,D,F,y){const W=(0,l.g2)("el-button"),L=(0,l.g2)("el-descriptions-item"),w=(0,l.g2)("el-descriptions"),C=(0,l.g2)("el-divider"),$=(0,l.g2)("el-rate"),E=(0,l.g2)("el-tag"),X=(0,l.g2)("el-col"),A=(0,l.g2)("el-row"),K=(0,l.g2)("el-image"),R=(0,l.g2)("el-avatar"),S=(0,l.g2)("el-card"),V=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",c,[(0,l.bF)(S,{class:"box-card"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",i,[e[3]||(e[3]=(0,l.Lk)("span",{class:"title"},"督导评价详情",-1)),(0,l.Lk)("div",null,[(0,l.bF)(W,{onClick:D.goBack},{default:(0,l.k6)(()=>e[1]||(e[1]=[(0,l.eW)("返回列表")])),_:1,__:[1]},8,["onClick"]),D.isAdmin?((0,l.uX)(),(0,l.Wv)(W,{key:0,type:"primary",onClick:D.editEvaluation},{default:(0,l.k6)(()=>e[2]||(e[2]=[(0,l.eW)("编辑")])),_:1,__:[2]},8,["onClick"])):(0,l.Q3)("",!0)])])]),default:(0,l.k6)(()=>[(0,l.bo)(((0,l.uX)(),(0,l.CE)("div",null,[(0,l.bF)(A,{gutter:20},{default:(0,l.k6)(()=>[(0,l.bF)(X,{span:24},{default:(0,l.k6)(()=>[(0,l.bF)(w,{title:"评价基本信息",column:3,border:""},{default:(0,l.k6)(()=>[(0,l.bF)(L,{label:"督导教研室"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(D.evaluationData.supervising_department),1)]),_:1}),(0,l.bF)(L,{label:"评价日期"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(D.formatDate(D.evaluationData.evaluation_date)),1)]),_:1}),(0,l.bF)(L,{label:"评估人"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(D.evaluationData.evaluator_name),1)]),_:1})]),_:1}),(0,l.bF)(C),(0,l.bF)(w,{title:"教学活动信息",column:3,border:""},{default:(0,l.k6)(()=>[(0,l.bF)(L,{label:"病例/主题",span:3},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(D.evaluationData.case_topic),1)]),_:1}),(0,l.bF)(L,{label:"教学活动形式"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(D.evaluationData.teaching_form),1)]),_:1}),(0,l.bF)(L,{label:"带教老师",span:2},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(D.evaluationData.teacher_name)+" ("+(0,s.v_)(D.evaluationData.teacher_title)+") ",1)]),_:1}),(0,l.bF)(L,{label:"学员姓名"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(D.evaluationData.student_name),1)]),_:1}),(0,l.bF)(L,{label:"学员类别",span:2},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(D.evaluationData.student_type),1)]),_:1})]),_:1}),(0,l.bF)(C),(0,l.bF)(w,{title:"评价内容",column:1,border:""},{default:(0,l.k6)(()=>[(0,l.bF)(L,{label:"平均分"},{default:(0,l.k6)(()=>[(0,l.bF)($,{modelValue:D.score,"onUpdate:modelValue":e[0]||(e[0]=a=>D.score=a),max:10,"show-score":"",disabled:"","score-template":"{value}"},null,8,["modelValue"])]),_:1}),(0,l.bF)(L,{label:"亮点"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",r,(0,s.v_)(D.evaluationData.highlights||"无"),1)]),_:1}),(0,l.bF)(L,{label:"不足"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",o,(0,s.v_)(D.evaluationData.shortcomings||"无"),1)]),_:1}),(0,l.bF)(L,{label:"改进建议"},{default:(0,l.k6)(()=>[(0,l.Lk)("div",n,(0,s.v_)(D.evaluationData.improvement_suggestions||"无"),1)]),_:1}),(0,l.bF)(L,{label:"能力认定"},{default:(0,l.k6)(()=>[(0,l.bF)(E,{type:D.evaluationData.competency_approved?"success":"danger",size:"large"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(D.evaluationData.competency_approved?"同意":"不同意"),1)]),_:1},8,["type"])]),_:1})]),_:1})]),_:1})]),_:1}),(0,l.bF)(C),D.teacherData.id?((0,l.uX)(),(0,l.Wv)(S,{key:0,class:"teacher-info-card",shadow:"hover"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",d,[e[5]||(e[5]=(0,l.Lk)("span",null,"带教老师信息",-1)),(0,l.bF)(W,{type:"text",onClick:D.viewTeacherDetail},{default:(0,l.k6)(()=>e[4]||(e[4]=[(0,l.eW)("查看详情")])),_:1,__:[4]},8,["onClick"])])]),default:(0,l.k6)(()=>[(0,l.bF)(A,{gutter:20},{default:(0,l.k6)(()=>[(0,l.bF)(X,{span:4},{default:(0,l.k6)(()=>[(0,l.Lk)("div",u,[D.teacherData.photo?((0,l.uX)(),(0,l.Wv)(K,{key:0,src:`http://localhost:3000${D.teacherData.photo}`,fit:"cover",class:"avatar-image","preview-src-list":[`http://localhost:3000${D.teacherData.photo}`]},null,8,["src","preview-src-list"])):((0,l.uX)(),(0,l.Wv)(R,{key:1,size:100,icon:"UserFilled"}))])]),_:1}),(0,l.bF)(X,{span:20},{default:(0,l.k6)(()=>[(0,l.bF)(w,{column:3,border:""},{default:(0,l.k6)(()=>[(0,l.bF)(L,{label:"姓名"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(D.teacherData.name),1)]),_:1}),(0,l.bF)(L,{label:"性别"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(D.teacherData.gender),1)]),_:1}),(0,l.bF)(L,{label:"科室"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(D.teacherData.department),1)]),_:1}),(0,l.bF)(L,{label:"学校"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(D.teacherData.school),1)]),_:1}),(0,l.bF)(L,{label:"专业"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(D.teacherData.major),1)]),_:1}),(0,l.bF)(L,{label:"学历"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(D.teacherData.education),1)]),_:1})]),_:1}),D.competencyData?((0,l.uX)(),(0,l.CE)("div",v,[(0,l.Lk)("div",_,[e[6]||(e[6]=(0,l.Lk)("div",{class:"stats-label"},"评价总数:",-1)),(0,l.Lk)("div",k,(0,s.v_)(D.competencyData.total_evaluations),1)]),(0,l.Lk)("div",b,[e[7]||(e[7]=(0,l.Lk)("div",{class:"stats-label"},"认可数:",-1)),(0,l.Lk)("div",p,(0,s.v_)(D.competencyData.approved_count),1)]),(0,l.Lk)("div",h,[e[8]||(e[8]=(0,l.Lk)("div",{class:"stats-label"},"认可率:",-1)),(0,l.Lk)("div",f,(0,s.v_)(D.competencyData.approval_rate)+"%",1)]),(0,l.Lk)("div",m,[e[9]||(e[9]=(0,l.Lk)("div",{class:"stats-label"},"认证状态:",-1)),(0,l.Lk)("div",g,[(0,l.bF)(E,{type:D.competencyData.is_certified?"success":"info"},{default:(0,l.k6)(()=>[(0,l.eW)((0,s.v_)(D.competencyData.is_certified?"已认证":"未认证"),1)]),_:1},8,["type"])])])])):(0,l.Q3)("",!0)]),_:1})]),_:1})]),_:1})):(0,l.Q3)("",!0)])),[[V,D.loading]])]),_:1})])}t(4114);var F=t(144),y=t(1387),W=t(1219),L=t(4373),w={name:"EvaluationDetail",setup(){const a=(0,y.lq)(),e=(0,y.rd)(),t=a.params.id,s=(0,F.KR)(!1),c=(0,F.KR)({}),i=(0,F.KR)({}),r=(0,F.KR)(null),o=(0,l.EW)(()=>!0),n=(0,l.EW)(()=>c.value.average_score||0);(0,l.sV)(()=>{d()});const d=async()=>{s.value=!0;try{const a=await L.A.get(`http://localhost:3000/api/evaluations/${t}`);c.value=a.data.data,c.value.teacher_id&&(await u(c.value.teacher_id),await v(c.value.teacher_id))}catch(a){console.error("获取评价详情失败:",a),W.nk.error("获取评价详情失败")}finally{s.value=!1}},u=async a=>{try{const e=await L.A.get(`http://localhost:3000/api/teachers/${a}`);i.value=e.data.data}catch(e){console.error("获取教师信息失败:",e)}},v=async a=>{try{const e=await L.A.get(`http://localhost:3000/api/evaluations/competency/teacher/${a}`);r.value=e.data.data}catch(e){console.error("获取能力认证状态失败:",e)}},_=a=>{if(!a)return"-";const e=new Date(a);return`${e.getFullYear()}-${(e.getMonth()+1).toString().padStart(2,"0")}-${e.getDate().toString().padStart(2,"0")}`},k=()=>{e.push("/evaluations/list")},b=()=>{e.push(`/evaluations/add?id=${t}`)},p=()=>{i.value.id&&e.push(`/teachers/detail/${i.value.id}`)};return{loading:s,evaluationData:c,teacherData:i,competencyData:r,score:n,isAdmin:o,formatDate:_,goBack:k,editEvaluation:b,viewTeacherDetail:p}}},C=t(1241);const $=(0,C.A)(w,[["render",D],["__scopeId","data-v-0e8fc6dd"]]);var E=$}}]);
//# sourceMappingURL=901.159545f8.js.map