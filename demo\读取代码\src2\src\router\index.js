import { createRouter, createWebHistory } from 'vue-router'
import AppLayout from '../layout/AppLayout.vue'
import ResetPasswordView from '@/views/ResetPasswordView.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/LoginView.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/reset-password',
    name: 'resetPassword',
    component: ResetPasswordView,
    meta: { title: '重置密码', requiresAuth: false }
  },
  {
    path: '/',
    component: AppLayout,
    redirect: '/teachers/list',
    children: [
      // 教师管理
      {
        path: 'teachers',
        name: 'Teachers',
        redirect: '/teachers/list',
        meta: { title: '教师管理', icon: 'User' },
        children: [
          {
            path: 'list',
            name: 'TeacherList',
            component: () => import('@/views/teachers/TeacherList.vue'),
            meta: { title: '教师列表' }
          },
          {
            path: 'detail/:id',
            name: 'TeacherDetail',
            component: () => import('@/views/teachers/TeacherDetail.vue'),
            meta: { title: '教师详情', activeMenu: '/teachers/list' },
            hidden: true
          }
        ]
      },
      // 专项培训管理
      {
        path: 'trainings',
        name: 'Trainings',
        redirect: '/trainings/list',
        meta: { title: '专项培训', icon: 'Reading' },
        children: [
          {
            path: 'list',
            name: 'TrainingList',
            component: () => import('@/views/trainings/TrainingList.vue'),
            meta: { title: '培训课程管理' }
          }
        ]
      },
      // 考试管理
      {
        path: 'exams',
        name: 'Exams',
        redirect: '/exams/list',
        meta: { title: '考试管理', icon: 'DocumentChecked' },
        children: [
          {
            path: 'list',
            name: 'ExamList',
            component: () => import('@/views/exams/ExamList.vue'),
            meta: { title: '考试列表' }
          },
          {
            path: 'questions/:id',
            name: 'ExamQuestions',
            component: () => import('@/views/exams/ExamQuestions.vue'),
            meta: { title: '试题管理', activeMenu: '/exams/list' },
            hidden: true
          },
          {
            path: 'results/:id',
            name: 'ExamResults',
            component: () => import('@/views/exams/ExamResults.vue'),
            meta: { title: '考试成绩', activeMenu: '/exams/list' },
            hidden: true
          },
          {
            path: 'take/:id',
            name: 'ExamTaking',
            component: () => import('@/views/exams/ExamTaking.vue'),
            meta: { title: '参加考试', activeMenu: '/exams/list' },
            hidden: true
          },
          {
            path: 'my-results',
            name: 'MyExamResults',
            component: () => import('@/views/exams/MyExamResults.vue'),
            meta: { title: '我的考试成绩', roles: ['teacher'] }
          }
        ]
      },
      // 督导评价管理
      {
        path: 'evaluations',
        name: 'Evaluations',
        redirect: '/evaluations/list',
        meta: { title: '督导评价', icon: 'Comment' },
        children: [
          {
            path: 'list',
            name: 'EvaluationList',
            component: () => import('@/views/evaluations/EvaluationList.vue'),
            meta: { title: '督导评价记录' }
          },
          {
            path: 'add',
            name: 'AddEvaluation',
            component: () => import('@/views/evaluations/AddEvaluation.vue'),
            meta: { title: '添加督导评价', roles: ['supervisor', 'admin'] }
          },
          {
            path: 'detail/:id',
            name: 'EvaluationDetail',
            component: () => import('@/views/evaluations/EvaluationDetail.vue'),
            meta: { title: '评价详情', activeMenu: '/evaluations/list' },
            hidden: true
          }
        ]
      },
      // 能力认定管理
      {
        path: 'competency',
        name: 'Competency',
        redirect: '/competency/list',
        meta: { title: '能力认定', icon: 'Medal' },
        children: [
          {
            path: 'list',
            name: 'CompetencyList',
            component: () => import('@/views/competency/CompetencyList.vue'),
            meta: { title: '能力认定列表' }
          },
          {
            path: 'detail/:id',
            name: 'CompetencyDetail',
            component: () => import('@/views/competency/CompetencyDetail.vue'),
            meta: { title: '认定详情', activeMenu: '/competency/list' },
            hidden: true
          }
        ]
      },
      // 督导小组管理
      {
        path: 'supervision',
        name: 'Supervision',
        redirect: '/supervision/team',
        meta: { title: '督导小组', icon: 'UserFilled' },
        children: [
          {
            path: 'team',
            name: 'SupervisionTeam',
            component: () => import('@/views/supervision/SupervisionTeam.vue'),
            meta: { title: '督导小组成员', roles: ['admin', 'supervisor'] }
          }
        ]
      },
      // 用户管理
      {
        path: 'users',
        name: 'Users',
        redirect: '/users/list',
        meta: { title: '用户管理', icon: 'Setting' },
        children: [
          {
            path: 'list',
            name: 'UserList',
            component: () => import('@/views/users/UserList.vue'),
            meta: { title: '用户列表', roles: ['admin'] }
          }
        ]
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

export default router
