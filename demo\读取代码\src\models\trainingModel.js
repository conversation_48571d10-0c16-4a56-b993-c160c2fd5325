const { pool } = require('../config/db');

// 创建培训课程表
const createTableSQL = `
CREATE TABLE IF NOT EXISTS training_courses (
  id INT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(200) NOT NULL COMMENT '课程标题',
  description TEXT COMMENT '课程描述',
  course_type ENUM('小讲课', '教学病例讨论', '教学查房', '其他') NOT NULL COMMENT '课程类型',
  material_path VARCHAR(255) COMMENT '课件/视频路径',
  original_filename VARCHAR(255) COMMENT '原始文件名',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
`;

// 初始化表
const initTable = async () => {
  try {
    await pool.execute(createTableSQL);
    console.log('培训课程表创建成功或已存在');
  } catch (error) {
    console.error('创建培训课程表失败:', error);
    throw error;
  }
};

// 获取所有培训课程
const findAll = async (filters = {}, page = 1, limit = 10) => {
  try {
    let query = 'SELECT * FROM training_courses WHERE 1=1';
    const params = [];

    // 添加过滤条件
    if (filters.title) {
      query += ' AND title LIKE ?';
      params.push(`%${filters.title}%`);
    }

    if (filters.course_type) {
      query += ' AND course_type = ?';
      params.push(filters.course_type);
    }

    // 计算总数
    const [countResult] = await pool.execute(
      `SELECT COUNT(*) as count FROM training_courses WHERE 1=1${
        filters.title ? ' AND title LIKE ?' : ''
      }${filters.course_type ? ' AND course_type = ?' : ''}`,
      [...params]
    );
    
    const count = countResult[0].count;

    // 添加排序和分页
    query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    const offset = (page - 1) * limit;
    params.push(Number(limit), Number(offset));

    // 执行查询
    const [rows] = await pool.execute(query, params);

    return { count, data: rows };
  } catch (error) {
    console.error('获取培训课程列表失败:', error);
    throw error;
  }
};

// 根据ID查找培训课程
const findById = async (id) => {
  try {
    const [rows] = await pool.execute('SELECT * FROM training_courses WHERE id = ?', [id]);
    return rows[0] || null;
  } catch (error) {
    console.error('根据ID查找培训课程失败:', error);
    throw error;
  }
};

// 创建培训课程
const create = async (trainingData) => {
  try {
    const { title, course_type, description, material_path, original_filename } = trainingData;
    
    const [result] = await pool.execute(
      'INSERT INTO training_courses (title, course_type, description, material_path, original_filename) VALUES (?, ?, ?, ?, ?)',
      [title, course_type, description, material_path, original_filename]
    );
    
    const id = result.insertId;
    return { id, ...trainingData };
  } catch (error) {
    console.error('创建培训课程失败:', error);
    throw error;
  }
};

// 更新培训课程
const update = async (id, trainingData) => {
  try {
    const { title, course_type, description, material_path, original_filename } = trainingData;
    
    const [result] = await pool.execute(
      'UPDATE training_courses SET title = ?, course_type = ?, description = ?, material_path = ?, original_filename = ? WHERE id = ?',
      [title, course_type, description, material_path, original_filename, id]
    );
    
    if (result.affectedRows === 0) {
      return null;
    }
    
    return { id, ...trainingData };
  } catch (error) {
    console.error('更新培训课程失败:', error);
    throw error;
  }
};

// 删除培训课程
const remove = async (id) => {
  try {
    const [result] = await pool.execute('DELETE FROM training_courses WHERE id = ?', [id]);
    return result.affectedRows > 0;
  } catch (error) {
    console.error('删除培训课程失败:', error);
    throw error;
  }
};

module.exports = {
  initTable,
  findAll,
  findById,
  create,
  update,
  remove
}; 