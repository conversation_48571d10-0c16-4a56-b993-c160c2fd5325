"use strict";(self["webpackChunkms"]=self["webpackChunkms"]||[]).push([[807],{6807:function(e,a,r){r.r(a),r.d(a,{default:function(){return x}});r(4114);var l=r(6768),o=r(144),s=r(5130),t=r(4232),d=r(3153),n=r(1387),u=r(1219),i=r(7477),m=r(4373);const c={class:"login-container"},p={class:"login-card"},g={class:"login-info"},f={class:"feature-list"},w={class:"feature-item"},b={class:"feature-icon"},k={class:"feature-item"},v={class:"feature-icon"},h={class:"feature-item"},_={class:"feature-icon"},F={class:"login-form-wrapper"},V={class:"login-form-container"},y={class:"form-options"},P={class:"dialog-footer"},L={class:"dialog-footer"};var I={__name:"LoginView",setup(e){m.A.interceptors.request.use(e=>{const a=localStorage.getItem("token");return a&&(e.headers.Authorization=`Bearer ${a}`),e},e=>Promise.reject(e)),m.A.interceptors.response.use(e=>e,e=>(e.response&&401===e.response.status&&(localStorage.removeItem("token"),localStorage.removeItem("userInfo"),"/login"!==window.location.pathname&&(u.nk.error("登录已过期，请重新登录"),window.location.href="/#/login")),Promise.reject(e)));const a=(0,n.rd)(),r=(0,o.KR)(null),I=(0,o.KR)(!1),U={NODE_ENV:"production",BASE_URL:"/"}.VUE_APP_API_URL||"http://localhost:3000/api",R=(0,o.Kh)({username:"",password:"",remember:!1}),x={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"}]},S=async()=>{if(r.value)try{await r.value.validate(async e=>{if(e){I.value=!0;try{const e=await m.A.post(`${U}/auth/login`,{username:R.username,password:R.password}),{token:l,data:o}=e.data;if(localStorage.setItem("token",l),R.remember?localStorage.setItem("rememberedUsername",R.username):localStorage.removeItem("rememberedUsername"),localStorage.setItem("userInfo",JSON.stringify(o)),localStorage.setItem("userId",o.id),localStorage.setItem("userRole",o.role),o.teacher_id)localStorage.setItem("teacherId",o.teacher_id),console.log("保存教师ID:",o.teacher_id);else if("student"===o.role)try{const e=await m.A.get(`${U}/students/by-user/${o.id}`);e.data.success&&e.data.data?(localStorage.setItem("studentId",e.data.data.id),console.log("通过用户ID获取并保存学生ID:",e.data.data.id)):(console.error("无法获取学生ID，但用户角色为学生"),u.nk.warning("无法获取您的学生信息，部分功能可能无法使用"))}catch(r){console.error("获取学生信息失败:",r)}console.log("登录成功，用户信息:",o),u.nk.success("登录成功"),"admin"===o.role||"administrator"===o.role?a.push("/teachers/list"):"teacher"===o.role?a.push("/exams/list"):a.push("/dashboard")}catch(l){console.error("登录失败:",l),u.nk.error(l.response?.data?.message||"登录失败，请检查用户名和密码")}finally{I.value=!1}}})}catch(e){I.value=!1,u.nk.error("表单验证失败")}},C=(0,o.KR)(!1),K=(0,o.KR)(null),A=(0,o.KR)(!1),E=(0,o.Kh)({username:"",password:"",confirmPassword:"",name:"",email:"",phone:"",student_id:"",role:"user"}),N=(e,a,r)=>{""===a?r(new Error("请再次输入密码")):a!==E.password?r(new Error("两次输入密码不一致!")):r()},q={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入密码",trigger:"blur"},{validator:N,trigger:"blur"}],name:[{required:!0,message:"请输入姓名",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],phone:[{pattern:/^1[3456789]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}]},$=async()=>{if(K.value)try{await K.value.validate(async e=>{if(e){A.value=!0;try{const{confirmPassword:e,...a}=E;await m.A.post(`${U}/auth/register`,a);u.nk.success("注册成功，请登录"),C.value=!1,R.username=E.username,R.password=""}catch(a){console.error("注册失败:",a),u.nk.error(a.response?.data?.message||"注册失败，请稍后重试")}finally{A.value=!1}}})}catch(e){A.value=!1,u.nk.error("表单验证失败")}},D=(0,o.KR)(!1),W=(0,o.KR)(null),j=(0,o.KR)(!1),B=(0,o.KR)(!1),O=(0,o.Kh)({username:"",email:"",newPassword:"",confirmNewPassword:""}),X=(e,a,r)=>{""===a?r(new Error("请输入新密码")):a.length<6||a.length>20?r(new Error("密码长度应在6到20个字符之间")):(""!==O.confirmNewPassword&&W.value?.validateField("confirmNewPassword"),r())},z=(e,a,r)=>{""===a?r(new Error("请再次输入新密码")):a!==O.newPassword?r(new Error("两次输入密码不一致!")):r()},J={username:[{required:!0,message:"请输入用户名",trigger:"blur"}],email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{validator:X,trigger:"blur"}],confirmNewPassword:[{required:!0,message:"请再次输入新密码",trigger:"blur"},{validator:z,trigger:"blur"}]},Q=()=>{D.value=!0,B.value=!1,O.newPassword="",O.confirmNewPassword=""},G=async()=>{if(W.value)try{B.value?await W.value.validateField(["newPassword","confirmNewPassword"]):await W.value.validateField(["username","email"]),j.value=!0;try{if(B.value){await m.A.post(`${U}/auth/reset-password`,{username:O.username,email:O.email,newPassword:O.newPassword});u.nk.success("密码重置成功，请使用新密码登录"),D.value=!1,R.username=O.username,R.password=""}else{const e=await m.A.post(`${U}/auth/verify-identity`,{username:O.username,email:O.email});e.data.success?(u.nk.success("身份验证成功，请设置新密码"),B.value=!0):u.nk.error(e.data.message||"用户名和邮箱不匹配")}}catch(e){console.error("操作失败:",e),B.value?u.nk.error(e.response?.data?.message||"密码重置失败，请稍后重试"):u.nk.error(e.response?.data?.message||"身份验证失败，请确认用户名和邮箱是否正确")}finally{j.value=!1}}catch(e){j.value=!1,u.nk.error("表单验证失败"),console.error(e)}},H=()=>{const e=localStorage.getItem("rememberedUsername");e&&(R.username=e,R.remember=!0)};return H(),(e,a)=>{const n=(0,l.g2)("el-icon"),u=(0,l.g2)("el-input"),m=(0,l.g2)("el-form-item"),U=(0,l.g2)("el-checkbox"),N=(0,l.g2)("el-button"),X=(0,l.g2)("el-form"),z=(0,l.g2)("el-dialog");return(0,l.uX)(),(0,l.CE)("div",c,[(0,l.Lk)("div",p,[(0,l.Lk)("div",g,[a[20]||(a[20]=(0,l.Fv)('<div class="logo-wrapper" data-v-6af90103><div class="logo-icon" data-v-6af90103><img src="'+d+'" alt="Logo" class="logo-img" data-v-6af90103><i class="el-icon-monitor" data-v-6af90103></i></div><div class="logo-text" data-v-6af90103> 教学师资评价与能力认定系统 </div></div><div class="welcome-text" data-v-6af90103><h2 data-v-6af90103>欢迎回来</h2><p data-v-6af90103>登录您的账户以继续访问系统</p></div>',2)),(0,l.Lk)("div",f,[(0,l.Lk)("div",w,[(0,l.Lk)("div",b,[(0,l.bF)(n,null,{default:(0,l.k6)(()=>[(0,l.bF)((0,o.R1)(i.Check))]),_:1})]),a[17]||(a[17]=(0,l.Lk)("div",{class:"feature-text"},"现代化的管理界面",-1))]),(0,l.Lk)("div",k,[(0,l.Lk)("div",v,[(0,l.bF)(n,null,{default:(0,l.k6)(()=>[(0,l.bF)((0,o.R1)(i.Check))]),_:1})]),a[18]||(a[18]=(0,l.Lk)("div",{class:"feature-text"},"强大的功能模块",-1))]),(0,l.Lk)("div",h,[(0,l.Lk)("div",_,[(0,l.bF)(n,null,{default:(0,l.k6)(()=>[(0,l.bF)((0,o.R1)(i.Check))]),_:1})]),a[19]||(a[19]=(0,l.Lk)("div",{class:"feature-text"},"安全可靠的数据保护",-1))])])]),(0,l.Lk)("div",F,[(0,l.Lk)("div",V,[a[23]||(a[23]=(0,l.Lk)("h2",{class:"form-title"},"用户登录",-1)),a[24]||(a[24]=(0,l.Lk)("p",{class:"form-subtitle"},"请输入您的账户信息",-1)),(0,l.bF)(X,{model:R,rules:x,ref_key:"loginFormRef",ref:r,class:"login-form"},{default:(0,l.k6)(()=>[(0,l.bF)(m,{prop:"username"},{default:(0,l.k6)(()=>[(0,l.bF)(u,{modelValue:R.username,"onUpdate:modelValue":a[0]||(a[0]=e=>R.username=e),placeholder:"","prefix-icon":(0,o.R1)(i.User)},null,8,["modelValue","prefix-icon"])]),_:1}),(0,l.bF)(m,{prop:"password"},{default:(0,l.k6)(()=>[(0,l.bF)(u,{modelValue:R.password,"onUpdate:modelValue":a[1]||(a[1]=e=>R.password=e),type:"password",placeholder:"","prefix-icon":(0,o.R1)(i.Lock),"show-password":""},null,8,["modelValue","prefix-icon"])]),_:1}),(0,l.Lk)("div",y,[(0,l.bF)(U,{modelValue:R.remember,"onUpdate:modelValue":a[2]||(a[2]=e=>R.remember=e)},{default:(0,l.k6)(()=>a[21]||(a[21]=[(0,l.eW)("记住我")])),_:1,__:[21]},8,["modelValue"]),(0,l.Lk)("a",{href:"#",class:"forgot-link",onClick:(0,s.D$)(Q,["prevent"])},"忘记密码?")]),(0,l.bF)(m,null,{default:(0,l.k6)(()=>[(0,l.bF)(N,{type:"primary",loading:I.value,onClick:S,class:"login-button"},{default:(0,l.k6)(()=>a[22]||(a[22]=[(0,l.eW)(" 登录 ")])),_:1,__:[22]},8,["loading"])]),_:1})]),_:1},8,["model"])])])]),(0,l.bF)(z,{title:"用户注册",modelValue:C.value,"onUpdate:modelValue":a[10]||(a[10]=e=>C.value=e),width:"400px",center:"","destroy-on-close":""},{footer:(0,l.k6)(()=>[(0,l.Lk)("span",P,[(0,l.bF)(N,{onClick:a[9]||(a[9]=e=>C.value=!1)},{default:(0,l.k6)(()=>a[25]||(a[25]=[(0,l.eW)("取消")])),_:1,__:[25]}),(0,l.bF)(N,{type:"primary",loading:A.value,onClick:$},{default:(0,l.k6)(()=>a[26]||(a[26]=[(0,l.eW)("注册")])),_:1,__:[26]},8,["loading"])])]),default:(0,l.k6)(()=>[(0,l.bF)(X,{model:E,rules:q,ref_key:"registerFormRef",ref:K,"label-width":"80px"},{default:(0,l.k6)(()=>[(0,l.bF)(m,{label:"用户名",prop:"username"},{default:(0,l.k6)(()=>[(0,l.bF)(u,{modelValue:E.username,"onUpdate:modelValue":a[3]||(a[3]=e=>E.username=e),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1}),(0,l.bF)(m,{label:"密码",prop:"password"},{default:(0,l.k6)(()=>[(0,l.bF)(u,{modelValue:E.password,"onUpdate:modelValue":a[4]||(a[4]=e=>E.password=e),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])]),_:1}),(0,l.bF)(m,{label:"确认密码",prop:"confirmPassword"},{default:(0,l.k6)(()=>[(0,l.bF)(u,{modelValue:E.confirmPassword,"onUpdate:modelValue":a[5]||(a[5]=e=>E.confirmPassword=e),type:"password",placeholder:"请再次输入密码","show-password":""},null,8,["modelValue"])]),_:1}),(0,l.bF)(m,{label:"姓名",prop:"name"},{default:(0,l.k6)(()=>[(0,l.bF)(u,{modelValue:E.name,"onUpdate:modelValue":a[6]||(a[6]=e=>E.name=e),placeholder:"请输入姓名"},null,8,["modelValue"])]),_:1}),(0,l.bF)(m,{label:"邮箱",prop:"email"},{default:(0,l.k6)(()=>[(0,l.bF)(u,{modelValue:E.email,"onUpdate:modelValue":a[7]||(a[7]=e=>E.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])]),_:1}),(0,l.bF)(m,{label:"手机号",prop:"phone"},{default:(0,l.k6)(()=>[(0,l.bF)(u,{modelValue:E.phone,"onUpdate:modelValue":a[8]||(a[8]=e=>E.phone=e),placeholder:"请输入手机号"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),(0,l.bF)(z,{title:"忘记密码",modelValue:D.value,"onUpdate:modelValue":a[16]||(a[16]=e=>D.value=e),width:"400px",center:"","destroy-on-close":""},{footer:(0,l.k6)(()=>[(0,l.Lk)("span",L,[(0,l.bF)(N,{onClick:a[15]||(a[15]=e=>D.value=!1)},{default:(0,l.k6)(()=>a[27]||(a[27]=[(0,l.eW)("取消")])),_:1,__:[27]}),(0,l.bF)(N,{type:"primary",loading:j.value,onClick:G},{default:(0,l.k6)(()=>[(0,l.eW)((0,t.v_)(B.value?"重置密码":"验证身份"),1)]),_:1},8,["loading"])])]),default:(0,l.k6)(()=>[(0,l.bF)(X,{model:O,rules:J,ref_key:"forgotPasswordFormRef",ref:W,"label-width":"100px"},{default:(0,l.k6)(()=>[(0,l.bF)(m,{label:"用户名",prop:"username"},{default:(0,l.k6)(()=>[(0,l.bF)(u,{modelValue:O.username,"onUpdate:modelValue":a[11]||(a[11]=e=>O.username=e),placeholder:"请输入用户名"},null,8,["modelValue"])]),_:1}),(0,l.bF)(m,{label:"邮箱",prop:"email"},{default:(0,l.k6)(()=>[(0,l.bF)(u,{modelValue:O.email,"onUpdate:modelValue":a[12]||(a[12]=e=>O.email=e),placeholder:"请输入注册时的邮箱"},null,8,["modelValue"])]),_:1}),B.value?((0,l.uX)(),(0,l.CE)(l.FK,{key:0},[(0,l.bF)(m,{label:"新密码",prop:"newPassword"},{default:(0,l.k6)(()=>[(0,l.bF)(u,{modelValue:O.newPassword,"onUpdate:modelValue":a[13]||(a[13]=e=>O.newPassword=e),type:"password",placeholder:"请输入新密码","show-password":""},null,8,["modelValue"])]),_:1}),(0,l.bF)(m,{label:"确认新密码",prop:"confirmNewPassword"},{default:(0,l.k6)(()=>[(0,l.bF)(u,{modelValue:O.confirmNewPassword,"onUpdate:modelValue":a[14]||(a[14]=e=>O.confirmNewPassword=e),type:"password",placeholder:"请再次输入新密码","show-password":""},null,8,["modelValue"])]),_:1})],64)):(0,l.Q3)("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}},U=r(1241);const R=(0,U.A)(I,[["__scopeId","data-v-6af90103"]]);var x=R}}]);
//# sourceMappingURL=807.b12062b6.js.map