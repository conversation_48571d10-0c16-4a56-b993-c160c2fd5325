<template>
  <div class="competency-detail-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="title">能力认定详情</span>
          <div>
            <el-button @click="goBack">返回列表</el-button>
          </div>
        </div>
      </template>

      <div v-loading="loading">
        <el-row :gutter="20">
          <!-- 左侧教师基本信息 -->
          <el-col :span="6">
            <div class="teacher-info-card">
              <div class="teacher-avatar">
                <el-image
                  v-if="teacherData.photo"
                  :src="`http://127.0.0.1:3000${teacherData.photo}`"
                  fit="cover"
                  class="avatar-image"
                  :preview-src-list="[`http://127.0.0.1:3000${teacherData.photo}`]"
                />
                <el-avatar v-else :size="120" icon="UserFilled" />
              </div>
              
              <el-descriptions title="教师信息" direction="vertical" :column="1" border>
                <el-descriptions-item label="姓名">{{ teacherData.name }}</el-descriptions-item>
                <el-descriptions-item label="性别">{{ teacherData.gender }}</el-descriptions-item>
                <el-descriptions-item label="科室">{{ teacherData.department }}</el-descriptions-item>
                <el-descriptions-item label="学校">{{ teacherData.school }}</el-descriptions-item>
                <el-descriptions-item label="专业">{{ teacherData.major }}</el-descriptions-item>
                <el-descriptions-item label="学历">{{ teacherData.education }}</el-descriptions-item>
                <el-descriptions-item label="在聘状态">
                  <el-tag :type="teacherData.is_employed ? 'success' : 'danger'">
                    {{ teacherData.is_employed ? '在聘' : '不在聘' }}
                  </el-tag>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-col>
          
          <!-- 右侧认定信息 -->
          <el-col :span="18">
            <!-- 认定状态卡片 -->
            <el-card class="certification-status-card" shadow="hover">
              <template #header>
                <div class="certification-header">
                  <span>认定状态</span>
                </div>
              </template>
              
              <div v-if="competencyData">
                <div class="certification-info">
                  <div class="certification-status">
                    <el-tag :type="competencyData.is_certified ? 'success' : 'info'" size="large">
                      {{ competencyData.is_certified ? '已认证' : '未认证' }}
                    </el-tag>
                    <div class="certification-note">
                      <el-alert
                        :title="getCertificationNote()"
                        :type="competencyData.is_certified ? 'success' : 'info'"
                        :closable="false"
                        show-icon
                      />
                    </div>
                  </div>
                  
                  <div class="certification-progress">
                    <el-progress 
                      :percentage="competencyData.approval_rate || 0" 
                      :status="competencyData.is_certified ? 'success' : ''" 
                      :stroke-width="20"
                      :format="percentFormat"
                    />
                    
                    <div class="progress-stats">
                      <div class="stat-item">
                        <div class="stat-value">{{ competencyData.total_evaluations }}</div>
                        <div class="stat-label">评价总数</div>
                      </div>
                      <div class="stat-item">
                        <div class="stat-value">{{ competencyData.approved_count }}</div>
                        <div class="stat-label">认可数</div>
                      </div>
                      <div class="stat-item">
                        <div class="stat-value">{{ competencyData.approval_rate }}%</div>
                        <div class="stat-label">认可率</div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div class="certification-rules">
                  <el-divider content-position="left">认定规则说明</el-divider>
                  <div class="rules-content">
                    <p>1. 需要至少参加3次督导评价才能被认定资格</p>
                    <p>2. 督导评价的认可率需达到80%以上才能获得认证</p>
                    <p>3. 每次评价中，督导老师需要对教师的教学能力进行认可评定</p>
                    <p>4. 认证状态会随着新的评价自动更新</p>
                  </div>
                </div>
              </div>
              
              <div v-else class="no-data">
                该教师暂无能力认定数据
              </div>
            </el-card>

            <!-- 评价列表卡片 -->
            <el-card class="evaluations-card" shadow="hover">
              <template #header>
                <div class="evaluations-header">
                  <span>评价记录</span>
                </div>
              </template>
              
              <div v-loading="evaluationsLoading">
                <el-table
                  :data="evaluations"
                  border
                  style="width: 100%"
                >
                  <el-table-column type="index" width="50" label="#" />
                  <el-table-column prop="supervising_department" label="督导教研室" width="120" />
                  <el-table-column prop="case_topic" label="病例/主题" show-overflow-tooltip />
                  <el-table-column prop="teaching_form" label="教学活动形式" width="120" />
                  <el-table-column prop="average_score" label="平均分" width="80" />
                  <el-table-column label="能力认定" width="100">
                    <template #default="scope">
                      <el-tag :type="scope.row.competency_approved ? 'success' : 'danger'">
                        {{ scope.row.competency_approved ? '同意' : '不同意' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="evaluation_date" label="评价时间" width="180">
                    <template #default="scope">
                      {{ formatDate(scope.row.evaluation_date) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="evaluator_name" label="评估人" width="100" />
                  <el-table-column label="操作" width="100">
                    <template #default="scope">
                      <el-button size="small" @click="viewEvaluation(scope.row.id)">详情</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                
                <div v-if="evaluations.length === 0 && !evaluationsLoading" class="no-data">
                  暂无评价记录
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import axios from 'axios'

export default {
  name: 'CompetencyDetail',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const teacherId = route.params.id
    
    // 基础数据
    const loading = ref(false)
    const evaluationsLoading = ref(false)
    const teacherData = ref({})
    const competencyData = ref(null)
    const evaluations = ref([])
    
    // 生命周期钩子
    onMounted(() => {
      fetchTeacherData()
      fetchCompetencyStatus()
      fetchEvaluations()
    })
    
    // 获取教师信息
    const fetchTeacherData = async () => {
      loading.value = true
      try {
        const response = await axios.get(`http://127.0.0.1:3000/api/teachers/${teacherId}`)
        teacherData.value = response.data.data
      } catch (error) {
        console.error('获取教师信息失败:', error)
        ElMessage.error('获取教师信息失败')
      } finally {
        loading.value = false
      }
    }
    
    // 获取能力认证状态
    const fetchCompetencyStatus = async () => {
      try {
        const response = await axios.get(`http://127.0.0.1:3000/api/evaluations/competency/teacher/${teacherId}`)
        competencyData.value = response.data.data
      } catch (error) {
        console.error('获取能力认证状态失败:', error)
      }
    }
    
    // 获取教师评价列表
    const fetchEvaluations = async () => {
      evaluationsLoading.value = true
      try {
        const response = await axios.get(`http://127.0.0.1:3000/api/evaluations/teacher/${teacherId}`)
        evaluations.value = response.data.data
      } catch (error) {
        console.error('获取评价列表失败:', error)
        ElMessage.error('获取评价列表失败')
      } finally {
        evaluationsLoading.value = false
      }
    }
    
    // 获取认证提示信息
    const getCertificationNote = () => {
      if (!competencyData.value) return ''
      
      if (competencyData.value.is_certified) {
        return `该教师已通过能力认定，认可率为 ${competencyData.value.approval_rate}%`
      } else {
        if (competencyData.value.total_evaluations < 3) {
          return `评价次数不足，至少需要3次评价才能进行认定（当前: ${competencyData.value.total_evaluations}次）`
        } else {
          return `认可率不足，需要80%以上才能获得认定（当前: ${competencyData.value.approval_rate}%）`
        }
      }
    }
    
    // 格式化百分比
    const percentFormat = (percentage) => {
      return `${percentage}%`
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`
    }
    
    // 返回列表
    const goBack = () => {
      router.push('/competency/list')
    }
    
    // 查看评价详情
    const viewEvaluation = (id) => {
      router.push(`/evaluations/detail/${id}`)
    }
    
    return {
      loading,
      evaluationsLoading,
      teacherData,
      competencyData,
      evaluations,
      goBack,
      viewEvaluation,
      getCertificationNote,
      percentFormat,
      formatDate
    }
  }
}
</script>

<style scoped>
.competency-detail-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.teacher-info-card {
  padding: 20px 0;
}

.teacher-avatar {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}

.avatar-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #eee;
}

.certification-status-card {
  margin-bottom: 20px;
}

.certification-info {
  display: flex;
  margin-bottom: 20px;
}

.certification-status {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 200px;
  padding-right: 20px;
}

.certification-note {
  margin-top: 20px;
  width: 100%;
}

.certification-progress {
  flex: 1;
  padding-left: 20px;
  border-left: 1px solid #eee;
}

.progress-stats {
  display: flex;
  margin-top: 20px;
}

.stat-item {
  flex: 1;
  text-align: center;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.certification-rules {
  margin-top: 20px;
}

.rules-content {
  color: #606266;
  font-size: 14px;
  line-height: 1.8;
  padding: 0 20px;
}

.evaluations-card {
  margin-top: 20px;
}

.no-data {
  padding: 30px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}
</style> 