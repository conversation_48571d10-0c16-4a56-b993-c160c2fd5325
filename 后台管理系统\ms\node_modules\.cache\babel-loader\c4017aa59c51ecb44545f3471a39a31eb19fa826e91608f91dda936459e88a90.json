{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, resolveDirective as _resolveDirective, createBlock as _createBlock, withDirectives as _withDirectives, normalizeClass as _normalizeClass, renderList as _renderList, Fragment as _Fragment } from \"vue\";\nconst _hoisted_1 = {\n  class: \"exam-taking-container\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  class: \"exam-intro\"\n};\nconst _hoisted_3 = {\n  class: \"card-header\"\n};\nconst _hoisted_4 = {\n  class: \"exam-info\"\n};\nconst _hoisted_5 = {\n  key: 0\n};\nconst _hoisted_6 = {\n  key: 0\n};\nconst _hoisted_7 = {\n  key: 0,\n  class: \"start-button\"\n};\nconst _hoisted_8 = {\n  key: 1,\n  class: \"no-attempts-message\"\n};\nconst _hoisted_9 = {\n  class: \"return-button\"\n};\nconst _hoisted_10 = {\n  key: 2,\n  class: \"no-questions-message\"\n};\nconst _hoisted_11 = {\n  class: \"return-button\"\n};\nconst _hoisted_12 = {\n  key: 1,\n  class: \"exam-content\"\n};\nconst _hoisted_13 = {\n  class: \"exam-header\"\n};\nconst _hoisted_14 = {\n  class: \"exam-timer\"\n};\nconst _hoisted_15 = {\n  class: \"questions-progress\"\n};\nconst _hoisted_16 = {\n  key: 0,\n  class: \"question-content\"\n};\nconst _hoisted_17 = {\n  class: \"question-type\"\n};\nconst _hoisted_18 = {\n  class: \"question-text\"\n};\nconst _hoisted_19 = {\n  key: 0,\n  class: \"options-container\"\n};\nconst _hoisted_20 = {\n  class: \"options-container\"\n};\nconst _hoisted_21 = {\n  class: \"options-container\"\n};\nconst _hoisted_22 = {\n  class: \"options-container\"\n};\nconst _hoisted_23 = {\n  class: \"question-navigation\"\n};\nconst _hoisted_24 = {\n  class: \"submit-dialog-content\"\n};\nconst _hoisted_25 = {\n  key: 0,\n  class: \"unanswered-warning\"\n};\nconst _hoisted_26 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_27 = {\n  key: 0,\n  class: \"result-dialog-content\"\n};\nconst _hoisted_28 = {\n  class: \"result-header\"\n};\nconst _hoisted_29 = {\n  class: \"result-score\"\n};\nconst _hoisted_30 = {\n  class: \"result-details\"\n};\nconst _hoisted_31 = {\n  class: \"detail-item\"\n};\nconst _hoisted_32 = {\n  class: \"detail-value\"\n};\nconst _hoisted_33 = {\n  class: \"detail-item\"\n};\nconst _hoisted_34 = {\n  class: \"detail-value\"\n};\nconst _hoisted_35 = {\n  class: \"detail-item\"\n};\nconst _hoisted_36 = {\n  class: \"detail-value\"\n};\nconst _hoisted_37 = {\n  class: \"detail-item\"\n};\nconst _hoisted_38 = {\n  class: \"detail-value\"\n};\nconst _hoisted_39 = {\n  class: \"detail-item\"\n};\nconst _hoisted_40 = {\n  class: \"detail-value\"\n};\nconst _hoisted_41 = {\n  class: \"detail-item\"\n};\nconst _hoisted_42 = {\n  class: \"detail-value\"\n};\nconst _hoisted_43 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_step = _resolveComponent(\"el-step\");\n  const _component_el_steps = _resolveComponent(\"el-steps\");\n  const _component_el_radio = _resolveComponent(\"el-radio\");\n  const _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  const _component_el_checkbox = _resolveComponent(\"el-checkbox\");\n  const _component_el_checkbox_group = _resolveComponent(\"el-checkbox-group\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _directive_loading = _resolveDirective(\"loading\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [!$setup.examStarted ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_withDirectives((_openBlock(), _createBlock(_component_el_card, {\n    class: \"intro-card\"\n  }, {\n    header: _withCtx(() => [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"span\", null, _toDisplayString($setup.exam.title || '加载中...'), 1 /* TEXT */)])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"p\", null, [_cache[7] || (_cache[7] = _createElementVNode(\"strong\", null, \"考试描述：\", -1 /* CACHED */)), _createTextVNode(_toDisplayString($setup.exam.description || '加载中...'), 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[8] || (_cache[8] = _createElementVNode(\"strong\", null, \"考试时长：\", -1 /* CACHED */)), _createTextVNode(_toDisplayString($setup.exam.duration || '-') + \" 分钟\", 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[9] || (_cache[9] = _createElementVNode(\"strong\", null, \"及格分数：\", -1 /* CACHED */)), _createTextVNode(_toDisplayString($setup.exam.pass_score || '-'), 1 /* TEXT */)]), _createElementVNode(\"p\", null, [_cache[10] || (_cache[10] = _createElementVNode(\"strong\", null, \"总分：\", -1 /* CACHED */)), _createTextVNode(_toDisplayString($setup.exam.total_score || '-'), 1 /* TEXT */)]), $setup.attemptsInfo.loaded ? (_openBlock(), _createElementBlock(\"p\", _hoisted_5, [_cache[11] || (_cache[11] = _createElementVNode(\"strong\", null, \"考试次数：\", -1 /* CACHED */)), _createTextVNode(\"第 \" + _toDisplayString($setup.attemptsInfo.current + 1) + \" 次 / 共 2 次\", 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _cache[12] || (_cache[12] = _createElementVNode(\"div\", {\n      class: \"exam-rules\"\n    }, [_createElementVNode(\"h4\", null, \"考试须知：\"), _createElementVNode(\"ol\", null, [_createElementVNode(\"li\", null, \"开始考试后，考试时间将自动倒计时\"), _createElementVNode(\"li\", null, \"请在规定时间内完成所有题目并提交\"), _createElementVNode(\"li\", null, \"提交后将自动评分并显示结果\"), _createElementVNode(\"li\", null, \"若时间结束未提交，系统将自动提交当前答案\"), _createElementVNode(\"li\", null, \"每位学生最多有2次参加考试的机会\")])], -1 /* CACHED */))]), !$setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [$setup.questions.length > 0 && $setup.attemptsInfo.remaining > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      size: \"large\",\n      onClick: $setup.startExam\n    }, {\n      default: _withCtx(() => _cache[13] || (_cache[13] = [_createTextVNode(\"开始考试\")])),\n      _: 1 /* STABLE */,\n      __: [13]\n    })])) : $setup.attemptsInfo.remaining <= 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_createVNode(_component_el_alert, {\n      title: \"无法参加考试\",\n      type: \"error\",\n      description: \"您已经达到了该考试的最大尝试次数（2次）。\",\n      \"show-icon\": \"\",\n      closable: false\n    }), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_el_button, {\n      type: \"info\",\n      onClick: $setup.returnToList\n    }, {\n      default: _withCtx(() => _cache[14] || (_cache[14] = [_createTextVNode(\"返回列表\")])),\n      _: 1 /* STABLE */,\n      __: [14]\n    })])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [_createVNode(_component_el_alert, {\n      title: \"暂无考题\",\n      type: \"warning\",\n      description: \"该考试暂未添加题目，请联系管理员。\",\n      \"show-icon\": \"\",\n      closable: false\n    }), _createElementVNode(\"div\", _hoisted_11, [_createVNode(_component_el_button, {\n      type: \"info\",\n      onClick: $setup.returnToList\n    }, {\n      default: _withCtx(() => _cache[15] || (_cache[15] = [_createTextVNode(\"返回列表\")])),\n      _: 1 /* STABLE */,\n      __: [15]\n    })])]))])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  })), [[_directive_loading, $setup.loading]])])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createVNode(_component_el_card, {\n    class: \"exam-header-card\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_13, [_createElementVNode(\"h2\", null, _toDisplayString($setup.exam.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_14, [_cache[16] || (_cache[16] = _createTextVNode(\" 剩余时间：\")), _createElementVNode(\"span\", {\n      class: _normalizeClass({\n        'time-warning': $setup.timeRemaining <= 300\n      })\n    }, _toDisplayString($setup.formatTime($setup.timeRemaining)), 3 /* TEXT, CLASS */)])])]),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_card, {\n    class: \"questions-card\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_15, [_createVNode(_component_el_steps, {\n      active: $setup.currentQuestionIndex + 1,\n      simple: \"\"\n    }, {\n      default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.questions, (question, index) => {\n        return _openBlock(), _createBlock(_component_el_step, {\n          key: index,\n          title: `题 ${index + 1}`,\n          onClick: $event => $setup.goToQuestion(index),\n          class: _normalizeClass([\"question-step\", {\n            'question-answered': $setup.userAnswers[question.id]\n          }])\n        }, null, 8 /* PROPS */, [\"title\", \"onClick\", \"class\"]);\n      }), 128 /* KEYED_FRAGMENT */))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"active\"])]), $setup.currentQuestion ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, _toDisplayString($setup.getQuestionTypeText($setup.currentQuestion.question_type)) + \" (\" + _toDisplayString($setup.currentQuestion.score) + \"分)\", 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_18, _toDisplayString($setup.currentQuestion.question), 1 /* TEXT */), _createCommentVNode(\" 单选题 \"), $setup.currentQuestion.question_type === '单选题' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_19, [_createVNode(_component_el_radio_group, {\n      modelValue: $setup.userAnswers[$setup.currentQuestion.id],\n      \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.userAnswers[$setup.currentQuestion.id] = $event)\n    }, {\n      default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.parseOptions($setup.currentQuestion.options), option => {\n        return _openBlock(), _createBlock(_component_el_radio, {\n          key: option.key,\n          label: option.key,\n          class: \"option-item\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString(option.key) + \". \" + _toDisplayString(option.text), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"]);\n      }), 128 /* KEYED_FRAGMENT */))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])) : $setup.currentQuestion.question_type === '多选题' ? (_openBlock(), _createElementBlock(_Fragment, {\n      key: 1\n    }, [_createCommentVNode(\" 多选题 \"), _createElementVNode(\"div\", _hoisted_20, [_createVNode(_component_el_checkbox_group, {\n      modelValue: $setup.userAnswers[$setup.currentQuestion.id],\n      \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.userAnswers[$setup.currentQuestion.id] = $event)\n    }, {\n      default: _withCtx(() => [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.parseOptions($setup.currentQuestion.options), option => {\n        return _openBlock(), _createBlock(_component_el_checkbox, {\n          key: option.key,\n          label: option.key,\n          class: \"option-item\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString(option.key) + \". \" + _toDisplayString(option.text), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"label\"]);\n      }), 128 /* KEYED_FRAGMENT */))]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : $setup.currentQuestion.question_type === '判断题' ? (_openBlock(), _createElementBlock(_Fragment, {\n      key: 2\n    }, [_createCommentVNode(\" 判断题 \"), _createElementVNode(\"div\", _hoisted_21, [_createVNode(_component_el_radio_group, {\n      modelValue: $setup.userAnswers[$setup.currentQuestion.id],\n      \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $setup.userAnswers[$setup.currentQuestion.id] = $event)\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_radio, {\n        label: \"正确\",\n        class: \"option-item\"\n      }, {\n        default: _withCtx(() => _cache[17] || (_cache[17] = [_createTextVNode(\"正确\")])),\n        _: 1 /* STABLE */,\n        __: [17]\n      }), _createVNode(_component_el_radio, {\n        label: \"错误\",\n        class: \"option-item\"\n      }, {\n        default: _withCtx(() => _cache[18] || (_cache[18] = [_createTextVNode(\"错误\")])),\n        _: 1 /* STABLE */,\n        __: [18]\n      })]),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"modelValue\"])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : $setup.currentQuestion.question_type === '简答题' ? (_openBlock(), _createElementBlock(_Fragment, {\n      key: 3\n    }, [_createCommentVNode(\" 简答题 \"), _createElementVNode(\"div\", _hoisted_22, [_createVNode(_component_el_input, {\n      modelValue: $setup.userAnswers[$setup.currentQuestion.id],\n      \"onUpdate:modelValue\": _cache[3] || (_cache[3] = $event => $setup.userAnswers[$setup.currentQuestion.id] = $event),\n      type: \"textarea\",\n      rows: 5,\n      placeholder: \"请输入答案\"\n    }, null, 8 /* PROPS */, [\"modelValue\"])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_23, [_createVNode(_component_el_button, {\n      onClick: $setup.previousQuestion,\n      disabled: $setup.currentQuestionIndex === 0\n    }, {\n      default: _withCtx(() => _cache[19] || (_cache[19] = [_createTextVNode(\"上一题\")])),\n      _: 1 /* STABLE */,\n      __: [19]\n    }, 8 /* PROPS */, [\"disabled\"]), $setup.currentQuestionIndex < $setup.questions.length - 1 ? (_openBlock(), _createBlock(_component_el_button, {\n      key: 0,\n      type: \"primary\",\n      onClick: $setup.nextQuestion\n    }, {\n      default: _withCtx(() => _cache[20] || (_cache[20] = [_createTextVNode(\"下一题\")])),\n      _: 1 /* STABLE */,\n      __: [20]\n    })) : (_openBlock(), _createBlock(_component_el_button, {\n      key: 1,\n      type: \"success\",\n      onClick: $setup.showSubmitConfirm\n    }, {\n      default: _withCtx(() => _cache[21] || (_cache[21] = [_createTextVNode(\"提交试卷\")])),\n      _: 1 /* STABLE */,\n      __: [21]\n    }))])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  })])), _createCommentVNode(\" 提交确认对话框 \"), _createVNode(_component_el_dialog, {\n    title: \"确认提交\",\n    modelValue: $setup.submitDialogVisible,\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.submitDialogVisible = $event),\n    width: \"400px\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_26, [_createVNode(_component_el_button, {\n      onClick: _cache[4] || (_cache[4] = $event => $setup.submitDialogVisible = false)\n    }, {\n      default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\"取消\")])),\n      _: 1 /* STABLE */,\n      __: [23]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.submitExam,\n      loading: $setup.submitting\n    }, {\n      default: _withCtx(() => _cache[24] || (_cache[24] = [_createTextVNode(\"确认提交\")])),\n      _: 1 /* STABLE */,\n      __: [24]\n    }, 8 /* PROPS */, [\"loading\"])])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_24, [_cache[22] || (_cache[22] = _createElementVNode(\"p\", null, \"您即将提交试卷，请确认：\", -1 /* CACHED */)), $setup.unansweredCount > 0 ? (_openBlock(), _createElementBlock(\"p\", _hoisted_25, \" 您还有 \" + _toDisplayString($setup.unansweredCount) + \" 道题目未作答，确定提交吗？ \", 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 考试结果对话框 \"), _createVNode(_component_el_dialog, {\n    title: \"考试结果\",\n    modelValue: $setup.resultDialogVisible,\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = $event => $setup.resultDialogVisible = $event),\n    width: \"500px\",\n    \"close-on-click-modal\": false,\n    \"close-on-press-escape\": false,\n    \"show-close\": false\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_43, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.returnToList\n    }, {\n      default: _withCtx(() => _cache[32] || (_cache[32] = [_createTextVNode(\"返回列表\")])),\n      _: 1 /* STABLE */,\n      __: [32]\n    })])]),\n    default: _withCtx(() => [$setup.examResult ? (_openBlock(), _createElementBlock(\"div\", _hoisted_27, [_createElementVNode(\"div\", _hoisted_28, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"result-status\", {\n        'pass': $setup.examResult.summary.is_passed,\n        'fail': !$setup.examResult.summary.is_passed\n      }])\n    }, _toDisplayString($setup.examResult.summary.is_passed ? '通过' : '未通过'), 3 /* TEXT, CLASS */), _createElementVNode(\"div\", _hoisted_29, [_cache[25] || (_cache[25] = _createTextVNode(\" 得分：\")), _createElementVNode(\"span\", null, _toDisplayString($setup.examResult.summary.score), 1 /* TEXT */), _createTextVNode(\" / \" + _toDisplayString($setup.exam.total_score), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_30, [_createElementVNode(\"div\", _hoisted_31, [_cache[26] || (_cache[26] = _createElementVNode(\"div\", {\n      class: \"detail-label\"\n    }, \"总题数：\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_32, _toDisplayString($setup.examResult.summary.total_questions), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_33, [_cache[27] || (_cache[27] = _createElementVNode(\"div\", {\n      class: \"detail-label\"\n    }, \"正确题数：\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_34, _toDisplayString($setup.examResult.summary.correct_questions), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_35, [_cache[28] || (_cache[28] = _createElementVNode(\"div\", {\n      class: \"detail-label\"\n    }, \"错误题数：\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_36, _toDisplayString($setup.examResult.summary.total_questions - $setup.examResult.summary.correct_questions), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_37, [_cache[29] || (_cache[29] = _createElementVNode(\"div\", {\n      class: \"detail-label\"\n    }, \"及格分数：\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_38, _toDisplayString($setup.exam.pass_score), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_39, [_cache[30] || (_cache[30] = _createElementVNode(\"div\", {\n      class: \"detail-label\"\n    }, \"尝试次数：\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_40, \"第 \" + _toDisplayString($setup.examResult.summary.attempt_number) + \" 次 / 共 2 次\", 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_41, [_cache[31] || (_cache[31] = _createElementVNode(\"div\", {\n      class: \"detail-label\"\n    }, \"剩余次数：\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_42, _toDisplayString($setup.examResult.summary.remaining_attempts) + \" 次\", 1 /* TEXT */)])])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "$setup", "examStarted", "_hoisted_2", "_createBlock", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_3", "_toDisplayString", "exam", "title", "_hoisted_4", "description", "duration", "pass_score", "total_score", "attemptsInfo", "loaded", "_hoisted_5", "current", "loading", "_hoisted_6", "questions", "length", "remaining", "_hoisted_7", "_createVNode", "_component_el_button", "type", "size", "onClick", "startExam", "_cache", "_hoisted_8", "_component_el_alert", "closable", "_hoisted_9", "returnToList", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_normalizeClass", "timeRemaining", "formatTime", "_hoisted_15", "_component_el_steps", "active", "currentQuestionIndex", "simple", "_Fragment", "_renderList", "question", "index", "_component_el_step", "key", "$event", "goToQuestion", "userAnswers", "id", "currentQuestion", "_hoisted_16", "_hoisted_17", "getQuestionTypeText", "question_type", "score", "_hoisted_18", "_createCommentVNode", "_hoisted_19", "_component_el_radio_group", "parseOptions", "options", "option", "_component_el_radio", "label", "text", "_hoisted_20", "_component_el_checkbox_group", "_component_el_checkbox", "_hoisted_21", "_hoisted_22", "_component_el_input", "rows", "placeholder", "_hoisted_23", "previousQuestion", "disabled", "nextQuestion", "showSubmitConfirm", "_component_el_dialog", "submitDialogVisible", "width", "footer", "_hoisted_26", "submitExam", "submitting", "_hoisted_24", "unansweredCount", "_hoisted_25", "resultDialogVisible", "_hoisted_43", "examResult", "_hoisted_27", "_hoisted_28", "summary", "is_passed", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_32", "total_questions", "_hoisted_33", "_hoisted_34", "correct_questions", "_hoisted_35", "_hoisted_36", "_hoisted_37", "_hoisted_38", "_hoisted_39", "_hoisted_40", "attempt_number", "_hoisted_41", "_hoisted_42", "remaining_attempts"], "sources": ["D:\\admin\\202506\\督导系统\\后台管理系统\\ms\\src\\views\\exams\\ExamTaking.vue"], "sourcesContent": ["<template>\r\n  <div class=\"exam-taking-container\">\r\n    <div v-if=\"!examStarted\" class=\"exam-intro\">\r\n      <el-card class=\"intro-card\" v-loading=\"loading\">\r\n        <template #header>\r\n          <div class=\"card-header\">\r\n            <span>{{ exam.title || '加载中...' }}</span>\r\n          </div>\r\n        </template>\r\n        <div class=\"exam-info\">\r\n          <p><strong>考试描述：</strong>{{ exam.description || '加载中...' }}</p>\r\n          <p><strong>考试时长：</strong>{{ exam.duration || '-' }} 分钟</p>\r\n          <p><strong>及格分数：</strong>{{ exam.pass_score || '-' }}</p>\r\n          <p><strong>总分：</strong>{{ exam.total_score || '-' }}</p>\r\n          <p v-if=\"attemptsInfo.loaded\"><strong>考试次数：</strong>第 {{ attemptsInfo.current + 1 }} 次 / 共 2 次</p>\r\n          <div class=\"exam-rules\">\r\n            <h4>考试须知：</h4>\r\n            <ol>\r\n              <li>开始考试后，考试时间将自动倒计时</li>\r\n              <li>请在规定时间内完成所有题目并提交</li>\r\n              <li>提交后将自动评分并显示结果</li>\r\n              <li>若时间结束未提交，系统将自动提交当前答案</li>\r\n              <li>每位学生最多有2次参加考试的机会</li>\r\n            </ol>\r\n          </div>\r\n        </div>\r\n        <div v-if=\"!loading\">\r\n          <div v-if=\"questions.length > 0 && attemptsInfo.remaining > 0\" class=\"start-button\">\r\n            <el-button type=\"primary\" size=\"large\" @click=\"startExam\">开始考试</el-button>\r\n          </div>\r\n          <div v-else-if=\"attemptsInfo.remaining <= 0\" class=\"no-attempts-message\">\r\n            <el-alert\r\n              title=\"无法参加考试\"\r\n              type=\"error\"\r\n              description=\"您已经达到了该考试的最大尝试次数（2次）。\"\r\n              show-icon\r\n              :closable=\"false\"\r\n            />\r\n            <div class=\"return-button\">\r\n              <el-button type=\"info\" @click=\"returnToList\">返回列表</el-button>\r\n            </div>\r\n          </div>\r\n          <div v-else class=\"no-questions-message\">\r\n            <el-alert\r\n              title=\"暂无考题\"\r\n              type=\"warning\"\r\n              description=\"该考试暂未添加题目，请联系管理员。\"\r\n              show-icon\r\n              :closable=\"false\"\r\n            />\r\n            <div class=\"return-button\">\r\n              <el-button type=\"info\" @click=\"returnToList\">返回列表</el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <div v-else class=\"exam-content\">\r\n      <el-card class=\"exam-header-card\">\r\n        <div class=\"exam-header\">\r\n          <h2>{{ exam.title }}</h2>\r\n          <div class=\"exam-timer\">\r\n            剩余时间：<span :class=\"{ 'time-warning': timeRemaining <= 300 }\">{{ formatTime(timeRemaining) }}</span>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n\r\n      <el-card class=\"questions-card\">\r\n        <div class=\"questions-progress\">\r\n          <el-steps :active=\"currentQuestionIndex + 1\" simple>\r\n            <el-step \r\n              v-for=\"(question, index) in questions\" \r\n              :key=\"index\" \r\n              :title=\"`题 ${index + 1}`\"\r\n              @click=\"goToQuestion(index)\"\r\n              class=\"question-step\"\r\n              :class=\"{ 'question-answered': userAnswers[question.id] }\"\r\n            />\r\n          </el-steps>\r\n        </div>\r\n\r\n        <div v-if=\"currentQuestion\" class=\"question-content\">\r\n          <div class=\"question-type\">{{ getQuestionTypeText(currentQuestion.question_type) }} ({{ currentQuestion.score }}分)</div>\r\n          <div class=\"question-text\">{{ currentQuestion.question }}</div>\r\n\r\n          <!-- 单选题 -->\r\n          <div v-if=\"currentQuestion.question_type === '单选题'\" class=\"options-container\">\r\n            <el-radio-group v-model=\"userAnswers[currentQuestion.id]\">\r\n              <el-radio \r\n                v-for=\"option in parseOptions(currentQuestion.options)\" \r\n                :key=\"option.key\" \r\n                :label=\"option.key\" \r\n                class=\"option-item\"\r\n              >\r\n                {{ option.key }}. {{ option.text }}\r\n              </el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n\r\n          <!-- 多选题 -->\r\n          <div v-else-if=\"currentQuestion.question_type === '多选题'\" class=\"options-container\">\r\n            <el-checkbox-group v-model=\"userAnswers[currentQuestion.id]\">\r\n              <el-checkbox \r\n                v-for=\"option in parseOptions(currentQuestion.options)\" \r\n                :key=\"option.key\" \r\n                :label=\"option.key\"\r\n                class=\"option-item\"\r\n              >\r\n                {{ option.key }}. {{ option.text }}\r\n              </el-checkbox>\r\n            </el-checkbox-group>\r\n          </div>\r\n\r\n          <!-- 判断题 -->\r\n          <div v-else-if=\"currentQuestion.question_type === '判断题'\" class=\"options-container\">\r\n            <el-radio-group v-model=\"userAnswers[currentQuestion.id]\">\r\n              <el-radio label=\"正确\" class=\"option-item\">正确</el-radio>\r\n              <el-radio label=\"错误\" class=\"option-item\">错误</el-radio>\r\n            </el-radio-group>\r\n          </div>\r\n\r\n          <!-- 简答题 -->\r\n          <div v-else-if=\"currentQuestion.question_type === '简答题'\" class=\"options-container\">\r\n            <el-input\r\n              v-model=\"userAnswers[currentQuestion.id]\"\r\n              type=\"textarea\"\r\n              :rows=\"5\"\r\n              placeholder=\"请输入答案\"\r\n            />\r\n          </div>\r\n\r\n          <div class=\"question-navigation\">\r\n            <el-button @click=\"previousQuestion\" :disabled=\"currentQuestionIndex === 0\">上一题</el-button>\r\n            <el-button type=\"primary\" @click=\"nextQuestion\" v-if=\"currentQuestionIndex < questions.length - 1\">下一题</el-button>\r\n            <el-button type=\"success\" @click=\"showSubmitConfirm\" v-else>提交试卷</el-button>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 提交确认对话框 -->\r\n    <el-dialog\r\n      title=\"确认提交\"\r\n      v-model=\"submitDialogVisible\"\r\n      width=\"400px\"\r\n    >\r\n      <div class=\"submit-dialog-content\">\r\n        <p>您即将提交试卷，请确认：</p>\r\n        <p class=\"unanswered-warning\" v-if=\"unansweredCount > 0\">\r\n          您还有 {{ unansweredCount }} 道题目未作答，确定提交吗？\r\n        </p>\r\n      </div>\r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button @click=\"submitDialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitExam\" :loading=\"submitting\">确认提交</el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- 考试结果对话框 -->\r\n    <el-dialog\r\n      title=\"考试结果\"\r\n      v-model=\"resultDialogVisible\"\r\n      width=\"500px\"\r\n      :close-on-click-modal=\"false\"\r\n      :close-on-press-escape=\"false\"\r\n      :show-close=\"false\"\r\n    >\r\n      <div class=\"result-dialog-content\" v-if=\"examResult\">\r\n        <div class=\"result-header\">\r\n          <div class=\"result-status\" :class=\"{ 'pass': examResult.summary.is_passed, 'fail': !examResult.summary.is_passed }\">\r\n            {{ examResult.summary.is_passed ? '通过' : '未通过' }}\r\n          </div>\r\n          <div class=\"result-score\">\r\n            得分：<span>{{ examResult.summary.score }}</span> / {{ exam.total_score }}\r\n          </div>\r\n        </div>\r\n        \r\n        <div class=\"result-details\">\r\n          <div class=\"detail-item\">\r\n            <div class=\"detail-label\">总题数：</div>\r\n            <div class=\"detail-value\">{{ examResult.summary.total_questions }}</div>\r\n          </div>\r\n          <div class=\"detail-item\">\r\n            <div class=\"detail-label\">正确题数：</div>\r\n            <div class=\"detail-value\">{{ examResult.summary.correct_questions }}</div>\r\n          </div>\r\n          <div class=\"detail-item\">\r\n            <div class=\"detail-label\">错误题数：</div>\r\n            <div class=\"detail-value\">{{ examResult.summary.total_questions - examResult.summary.correct_questions }}</div>\r\n          </div>\r\n          <div class=\"detail-item\">\r\n            <div class=\"detail-label\">及格分数：</div>\r\n            <div class=\"detail-value\">{{ exam.pass_score }}</div>\r\n          </div>\r\n          <div class=\"detail-item\">\r\n            <div class=\"detail-label\">尝试次数：</div>\r\n            <div class=\"detail-value\">第 {{ examResult.summary.attempt_number }} 次 / 共 2 次</div>\r\n          </div>\r\n          <div class=\"detail-item\">\r\n            <div class=\"detail-label\">剩余次数：</div>\r\n            <div class=\"detail-value\">{{ examResult.summary.remaining_attempts }} 次</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <template #footer>\r\n        <div class=\"dialog-footer\">\r\n          <el-button type=\"primary\" @click=\"returnToList\">返回列表</el-button>\r\n        </div>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, reactive, computed, onMounted, onBeforeUnmount } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport examService from '@/services/examService'\r\nimport { formatDate } from '@/utils/dateFormat'\r\n\r\nconst route = useRoute()\r\nconst router = useRouter()\r\nconst examId = route.params.id\r\n\r\n// 考试状态\r\nconst loading = ref(true)\r\nconst examStarted = ref(false)\r\nconst timeRemaining = ref(0)\r\nconst currentQuestionIndex = ref(0)\r\nconst submitDialogVisible = ref(false)\r\nconst resultDialogVisible = ref(false)\r\nconst submitting = ref(false)\r\n\r\n// 考试数据\r\nconst exam = ref({})\r\nconst questions = ref([])\r\nconst userAnswers = reactive({})\r\nconst examResult = ref(null)\r\n\r\n// 考试尝试次数信息\r\nconst attemptsInfo = reactive({\r\n  loaded: false,\r\n  current: 0,\r\n  total: 2,\r\n  remaining: 2\r\n})\r\n\r\n// 计时器\r\nlet timer = null\r\n\r\n// 获取考试信息和题目\r\nconst fetchExamData = async () => {\r\n  loading.value = true\r\n  try {\r\n    const examResponse = await examService.getExam(examId)\r\n    exam.value = examResponse.data.data\r\n    \r\n    const questionsResponse = await examService.getExamQuestions(examId)\r\n    questions.value = questionsResponse.data.data\r\n    \r\n    if (questions.value.length === 0) {\r\n      ElMessage.warning('该考试暂无题目')\r\n    }\r\n    \r\n    // 获取学生尝试次数\r\n    await checkAttemptsCount()\r\n    \r\n    loading.value = false\r\n  } catch (error) {\r\n    console.error('获取考试数据失败', error)\r\n    ElMessage.error('获取考试数据失败')\r\n    loading.value = false\r\n  }\r\n}\r\n\r\n// 检查考试尝试次数\r\nconst checkAttemptsCount = async () => {\r\n  try {\r\n    const teacherId = localStorage.getItem('teacherId')\r\n    if (!teacherId) {\r\n      throw new Error('未找到有效的教师ID')\r\n    }\r\n    \r\n    console.log('检查考试尝试次数 - 教师ID:', teacherId)\r\n    \r\n    const response = await examService.getExamResults(examId, { teacher_id: teacherId })\r\n    const attempts = response.data.data.results ? response.data.data.results.length : 0\r\n    \r\n    attemptsInfo.loaded = true\r\n    attemptsInfo.current = attempts\r\n    attemptsInfo.remaining = Math.max(0, 2 - attempts)\r\n    \r\n    if (attemptsInfo.remaining <= 0) {\r\n      ElMessage.warning('您已达到该考试的最大尝试次数')\r\n    }\r\n  } catch (error) {\r\n    console.error('检查考试尝试次数失败', error)\r\n    ElMessage.error('检查考试尝试次数失败')\r\n  }\r\n}\r\n\r\n// 开始考试\r\nconst startExam = () => {\r\n  examStarted.value = true\r\n  timeRemaining.value = exam.value.duration * 60 // 转换为秒\r\n  startTimer()\r\n}\r\n\r\n// 启动计时器\r\nconst startTimer = () => {\r\n  timer = setInterval(() => {\r\n    if (timeRemaining.value > 0) {\r\n      timeRemaining.value--\r\n    } else {\r\n      // 时间结束，自动提交\r\n      clearInterval(timer)\r\n      submitExam()\r\n    }\r\n  }, 1000)\r\n}\r\n\r\n// 格式化时间\r\nconst formatTime = (seconds) => {\r\n  const hours = Math.floor(seconds / 3600)\r\n  const minutes = Math.floor((seconds % 3600) / 60)\r\n  const secs = seconds % 60\r\n  \r\n  let result = ''\r\n  \r\n  if (hours > 0) {\r\n    result += `${hours}小时 `\r\n  }\r\n  \r\n  if (minutes > 0 || hours > 0) {\r\n    result += `${minutes}分钟 `\r\n  }\r\n  \r\n  result += `${secs}秒`\r\n  \r\n  return result\r\n}\r\n\r\n// 导航到上一题\r\nconst previousQuestion = () => {\r\n  if (currentQuestionIndex.value > 0) {\r\n    currentQuestionIndex.value--\r\n  }\r\n}\r\n\r\n// 导航到下一题\r\nconst nextQuestion = () => {\r\n  if (currentQuestionIndex.value < questions.value.length - 1) {\r\n    currentQuestionIndex.value++\r\n  }\r\n}\r\n\r\n// 导航到指定题目\r\nconst goToQuestion = (index) => {\r\n  currentQuestionIndex.value = index\r\n}\r\n\r\n// 获取当前题目\r\nconst currentQuestion = computed(() => {\r\n  return questions.value[currentQuestionIndex.value]\r\n})\r\n\r\n// 解析题目选项\r\nconst parseOptions = (options) => {\r\n  if (typeof options === 'string') {\r\n    try {\r\n      return JSON.parse(options)\r\n    } catch (e) {\r\n      console.error('解析选项失败', e)\r\n      return []\r\n    }\r\n  }\r\n  return options || []\r\n}\r\n\r\n// 获取题型文本\r\nconst getQuestionTypeText = (type) => {\r\n  return type\r\n}\r\n\r\n// 显示提交确认对话框\r\nconst showSubmitConfirm = () => {\r\n  submitDialogVisible.value = true\r\n}\r\n\r\n// 提交试卷\r\nconst submitExam = async () => {\r\n  submitting.value = true\r\n  submitDialogVisible.value = false\r\n  \r\n  try {\r\n    // 准备答案数据\r\n    const answers = []\r\n    questions.value.forEach(question => {\r\n      answers.push({\r\n        question_id: question.id,\r\n        answer: userAnswers[question.id] || '' // 处理未回答的题目\r\n      })\r\n    })\r\n    \r\n    // 获取学生ID (而不是用户ID)\r\n    const teacherId = localStorage.getItem('teacherId')\r\n    \r\n    // 检查是否有有效的学生ID\r\n    if (!teacherId) {\r\n      ElMessage.error('未找到有效的教师ID，请重新登录')\r\n      returnToList()\r\n      return\r\n    }\r\n    \r\n    console.log('提交考试 - 使用教师ID:', teacherId, '考试ID:', examId)\r\n    \r\n    // 提交到后端\r\n    const response = await examService.submitExam({\r\n      teacher_id: teacherId,\r\n      exam_id: examId,\r\n      answers: answers\r\n    })\r\n    \r\n    // 清除计时器\r\n    if (timer) {\r\n      clearInterval(timer)\r\n      timer = null\r\n    }\r\n    \r\n    // 显示结果\r\n    examResult.value = response.data.data\r\n    resultDialogVisible.value = true\r\n    \r\n  } catch (error) {\r\n    console.error('提交考试失败', error)\r\n    \r\n    if (error.response && error.response.status === 403) {\r\n      // 尝试次数已用完\r\n      ElMessage.error('您已达到该考试的最大尝试次数')\r\n      returnToList()\r\n    } else {\r\n      ElMessage.error('提交考试失败: ' + (error.response?.data?.message || error.message))\r\n    }\r\n    \r\n    submitting.value = false\r\n  }\r\n}\r\n\r\n// 未回答的题目数量\r\nconst unansweredCount = computed(() => {\r\n  let count = 0\r\n  questions.value.forEach(question => {\r\n    if (!userAnswers[question.id]) {\r\n      count++\r\n    }\r\n  })\r\n  return count\r\n})\r\n\r\n// 返回列表页\r\nconst returnToList = () => {\r\n  router.push('/exams/list')\r\n}\r\n\r\nonMounted(() => {\r\n  fetchExamData()\r\n})\r\n\r\nonBeforeUnmount(() => {\r\n  // 清除计时器\r\n  if (timer) {\r\n    clearInterval(timer)\r\n  }\r\n})\r\n</script>\r\n\r\n<style scoped>\r\n.exam-taking-container {\r\n  padding: 20px;\r\n}\r\n\r\n.exam-intro {\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n}\r\n\r\n.intro-card {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.card-header span {\r\n  font-size: 18px;\r\n  font-weight: 600;\r\n}\r\n\r\n.exam-info {\r\n  padding: 10px 0;\r\n}\r\n\r\n.exam-rules {\r\n  margin-top: 20px;\r\n  background-color: #f8f9fa;\r\n  padding: 15px;\r\n  border-radius: 8px;\r\n}\r\n\r\n.exam-rules h4 {\r\n  margin-top: 0;\r\n  margin-bottom: 10px;\r\n  color: #606266;\r\n}\r\n\r\n.exam-rules ol {\r\n  padding-left: 20px;\r\n  margin-bottom: 0;\r\n}\r\n\r\n.exam-rules li {\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.exam-rules li:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.start-button {\r\n  text-align: center;\r\n  margin-top: 30px;\r\n}\r\n\r\n.no-attempts-message {\r\n  text-align: center;\r\n  margin-top: 30px;\r\n}\r\n\r\n.no-questions-message {\r\n  text-align: center;\r\n  margin-top: 30px;\r\n}\r\n\r\n.return-button {\r\n  margin-top: 20px;\r\n}\r\n\r\n.exam-header-card {\r\n  margin-bottom: 20px;\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.exam-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.exam-header h2 {\r\n  margin: 0;\r\n  font-weight: 600;\r\n}\r\n\r\n.exam-timer {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n}\r\n\r\n.time-warning {\r\n  color: #F56C6C;\r\n}\r\n\r\n.questions-card {\r\n  border-radius: 12px;\r\n  overflow: hidden;\r\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);\r\n}\r\n\r\n.questions-progress {\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.question-step {\r\n  cursor: pointer;\r\n}\r\n\r\n.question-answered {\r\n  color: #67C23A;\r\n}\r\n\r\n.question-content {\r\n  padding: 10px 0;\r\n}\r\n\r\n.question-type {\r\n  color: #909399;\r\n  margin-bottom: 10px;\r\n  font-size: 14px;\r\n}\r\n\r\n.question-text {\r\n  font-size: 16px;\r\n  margin-bottom: 20px;\r\n  line-height: 1.6;\r\n}\r\n\r\n.options-container {\r\n  margin-top: 15px;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.option-item {\r\n  display: block;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.question-navigation {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  margin-top: 30px;\r\n}\r\n\r\n.submit-dialog-content {\r\n  text-align: center;\r\n  padding: 10px 0;\r\n}\r\n\r\n.unanswered-warning {\r\n  color: #E6A23C;\r\n  font-weight: bold;\r\n}\r\n\r\n.result-dialog-content {\r\n  padding: 20px 0;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  margin-bottom: 30px;\r\n}\r\n\r\n.result-status {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.pass {\r\n  color: #67C23A;\r\n}\r\n\r\n.fail {\r\n  color: #F56C6C;\r\n}\r\n\r\n.result-score {\r\n  font-size: 18px;\r\n}\r\n\r\n.result-score span {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #409EFF;\r\n}\r\n\r\n.result-details {\r\n  background-color: #f8f9fa;\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n}\r\n\r\n.detail-item {\r\n  display: flex;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.detail-label {\r\n  width: 100px;\r\n  color: #606266;\r\n}\r\n\r\n.detail-value {\r\n  font-weight: 600;\r\n}\r\n\r\n/* Style the Element Plus components to match LoginView style */\r\n:deep(.el-button--primary) {\r\n  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);\r\n  border: none;\r\n}\r\n\r\n:deep(.el-button--primary:hover) {\r\n  background: linear-gradient(135deg, #66b1ff 0%, #5098fa 100%);\r\n  border: none;\r\n}\r\n</style> "], "mappings": ";;EACOA,KAAK,EAAC;AAAuB;;;EACPA,KAAK,EAAC;;;EAGpBA,KAAK,EAAC;AAAa;;EAIrBA,KAAK,EAAC;AAAW;;;;;;;;;EAkB2CA,KAAK,EAAC;;;;EAGxBA,KAAK,EAAC;;;EAQ5CA,KAAK,EAAC;AAAe;;;EAIhBA,KAAK,EAAC;;;EAQXA,KAAK,EAAC;AAAe;;;EAQtBA,KAAK,EAAC;;;EAETA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAY;;EAOpBA,KAAK,EAAC;AAAoB;;;EAaHA,KAAK,EAAC;;;EAC3BA,KAAK,EAAC;AAAe;;EACrBA,KAAK,EAAC;AAAe;;;EAG0BA,KAAK,EAAC;;;EAcDA,KAAK,EAAC;AAAmB;;EAczBA,KAAK,EAAC;AAAmB;;EAQzBA,KAAK,EAAC;AAAmB;;EAS7EA,KAAK,EAAC;AAAqB;;EAe/BA,KAAK,EAAC;AAAuB;;;EAE7BA,KAAK,EAAC;;;EAKJA,KAAK,EAAC;AAAe;;;EAgBvBA,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAe;;EAInBA,KAAK,EAAC;AAAc;;EAKtBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAc;;EAEtBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAc;;EAEtBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAc;;EAEtBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAc;;EAEtBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAc;;EAEtBA,KAAK,EAAC;AAAa;;EAEjBA,KAAK,EAAC;AAAc;;EAKxBA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;uBA/MhCC,mBAAA,CAoNM,OApNNC,UAoNM,G,CAnNQC,MAAA,CAAAC,WAAW,I,cAAvBH,mBAAA,CAsDM,OAtDNI,UAsDM,G,+BArDJC,YAAA,CAoDUC,kBAAA;IApDDP,KAAK,EAAC;EAAY;IACdQ,MAAM,EAAAC,QAAA,CACf,MAEM,CAFNC,mBAAA,CAEM,OAFNC,UAEM,GADJD,mBAAA,CAAyC,cAAAE,gBAAA,CAAhCT,MAAA,CAAAU,IAAI,CAACC,KAAK,6B;sBAGvB,MAgBM,CAhBNJ,mBAAA,CAgBM,OAhBNK,UAgBM,GAfJL,mBAAA,CAA+D,Y,0BAA5DA,mBAAA,CAAsB,gBAAd,OAAK,qB,kCAAYP,MAAA,CAAAU,IAAI,CAACG,WAAW,6B,GAC5CN,mBAAA,CAA0D,Y,0BAAvDA,mBAAA,CAAsB,gBAAd,OAAK,qB,kCAAYP,MAAA,CAAAU,IAAI,CAACI,QAAQ,WAAU,KAAG,gB,GACtDP,mBAAA,CAAyD,Y,0BAAtDA,mBAAA,CAAsB,gBAAd,OAAK,qB,kCAAYP,MAAA,CAAAU,IAAI,CAACK,UAAU,wB,GAC3CR,mBAAA,CAAwD,Y,4BAArDA,mBAAA,CAAoB,gBAAZ,KAAG,qB,kCAAYP,MAAA,CAAAU,IAAI,CAACM,WAAW,wB,GACjChB,MAAA,CAAAiB,YAAY,CAACC,MAAM,I,cAA5BpB,mBAAA,CAAkG,KAAAqB,UAAA,G,4BAApEZ,mBAAA,CAAsB,gBAAd,OAAK,qB,iBAAS,IAAE,GAAAE,gBAAA,CAAGT,MAAA,CAAAiB,YAAY,CAACG,OAAO,QAAO,YAAU,gB,oEAC9Fb,mBAAA,CASM;MATDV,KAAK,EAAC;IAAY,IACrBU,mBAAA,CAAc,YAAV,OAAK,GACTA,mBAAA,CAMK,aALHA,mBAAA,CAAyB,YAArB,kBAAgB,GACpBA,mBAAA,CAAyB,YAArB,kBAAgB,GACpBA,mBAAA,CAAsB,YAAlB,eAAa,GACjBA,mBAAA,CAA6B,YAAzB,sBAAoB,GACxBA,mBAAA,CAAyB,YAArB,kBAAgB,E,0BAIdP,MAAA,CAAAqB,OAAO,I,cAAnBvB,mBAAA,CA4BM,OAAAwB,UAAA,GA3BOtB,MAAA,CAAAuB,SAAS,CAACC,MAAM,QAAQxB,MAAA,CAAAiB,YAAY,CAACQ,SAAS,Q,cAAzD3B,mBAAA,CAEM,OAFN4B,UAEM,GADJC,YAAA,CAA0EC,oBAAA;MAA/DC,IAAI,EAAC,SAAS;MAACC,IAAI,EAAC,OAAO;MAAEC,OAAK,EAAE/B,MAAA,CAAAgC;;wBAAW,MAAIC,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;YAEhDjC,MAAA,CAAAiB,YAAY,CAACQ,SAAS,S,cAAtC3B,mBAAA,CAWM,OAXNoC,UAWM,GAVJP,YAAA,CAMEQ,mBAAA;MALAxB,KAAK,EAAC,QAAQ;MACdkB,IAAI,EAAC,OAAO;MACZhB,WAAW,EAAC,uBAAuB;MACnC,WAAS,EAAT,EAAS;MACRuB,QAAQ,EAAE;QAEb7B,mBAAA,CAEM,OAFN8B,UAEM,GADJV,YAAA,CAA6DC,oBAAA;MAAlDC,IAAI,EAAC,MAAM;MAAEE,OAAK,EAAE/B,MAAA,CAAAsC;;wBAAc,MAAIL,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;6BAGrDnC,mBAAA,CAWM,OAXNyC,WAWM,GAVJZ,YAAA,CAMEQ,mBAAA;MALAxB,KAAK,EAAC,MAAM;MACZkB,IAAI,EAAC,SAAS;MACdhB,WAAW,EAAC,mBAAmB;MAC/B,WAAS,EAAT,EAAS;MACRuB,QAAQ,EAAE;QAEb7B,mBAAA,CAEM,OAFNiC,WAEM,GADJb,YAAA,CAA6DC,oBAAA;MAAlDC,IAAI,EAAC,MAAM;MAAEE,OAAK,EAAE/B,MAAA,CAAAsC;;wBAAc,MAAIL,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;;;6BAhDlBjC,MAAA,CAAAqB,OAAO,E,sBAuDhDvB,mBAAA,CAiFM,OAjFN2C,WAiFM,GAhFJd,YAAA,CAOUvB,kBAAA;IAPDP,KAAK,EAAC;EAAkB;sBAC/B,MAKM,CALNU,mBAAA,CAKM,OALNmC,WAKM,GAJJnC,mBAAA,CAAyB,YAAAE,gBAAA,CAAlBT,MAAA,CAAAU,IAAI,CAACC,KAAK,kBACjBJ,mBAAA,CAEM,OAFNoC,WAEM,G,6CAFkB,QACjB,IAAApC,mBAAA,CAA8F;MAAvFV,KAAK,EAAA+C,eAAA;QAAA,gBAAoB5C,MAAA,CAAA6C,aAAa;MAAA;wBAAc7C,MAAA,CAAA8C,UAAU,CAAC9C,MAAA,CAAA6C,aAAa,yB;;MAK9FlB,YAAA,CAsEUvB,kBAAA;IAtEDP,KAAK,EAAC;EAAgB;sBAC7B,MAWM,CAXNU,mBAAA,CAWM,OAXNwC,WAWM,GAVJpB,YAAA,CASWqB,mBAAA;MATAC,MAAM,EAAEjD,MAAA,CAAAkD,oBAAoB;MAAMC,MAAM,EAAN;;wBAEzC,MAAsC,E,kBADxCrD,mBAAA,CAOEsD,SAAA,QAAAC,WAAA,CAN4BrD,MAAA,CAAAuB,SAAS,GAA7B+B,QAAQ,EAAEC,KAAK;6BADzBpD,YAAA,CAOEqD,kBAAA;UALCC,GAAG,EAAEF,KAAK;UACV5C,KAAK,OAAO4C,KAAK;UACjBxB,OAAK,EAAA2B,MAAA,IAAE1D,MAAA,CAAA2D,YAAY,CAACJ,KAAK;UAC1B1D,KAAK,EAAA+C,eAAA,EAAC,eAAe;YAAA,qBACU5C,MAAA,CAAA4D,WAAW,CAACN,QAAQ,CAACO,EAAE;UAAA;;;;qCAKjD7D,MAAA,CAAA8D,eAAe,I,cAA1BhE,mBAAA,CAuDM,OAvDNiE,WAuDM,GAtDJxD,mBAAA,CAAwH,OAAxHyD,WAAwH,EAAAvD,gBAAA,CAA1FT,MAAA,CAAAiE,mBAAmB,CAACjE,MAAA,CAAA8D,eAAe,CAACI,aAAa,KAAI,IAAE,GAAAzD,gBAAA,CAAGT,MAAA,CAAA8D,eAAe,CAACK,KAAK,IAAG,IAAE,iBAClH5D,mBAAA,CAA+D,OAA/D6D,WAA+D,EAAA3D,gBAAA,CAAjCT,MAAA,CAAA8D,eAAe,CAACR,QAAQ,kBAEtDe,mBAAA,SAAY,EACDrE,MAAA,CAAA8D,eAAe,CAACI,aAAa,c,cAAxCpE,mBAAA,CAWM,OAXNwE,WAWM,GAVJ3C,YAAA,CASiB4C,yBAAA;kBATQvE,MAAA,CAAA4D,WAAW,CAAC5D,MAAA,CAAA8D,eAAe,CAACD,EAAE;iEAA9B7D,MAAA,CAAA4D,WAAW,CAAC5D,MAAA,CAAA8D,eAAe,CAACD,EAAE,IAAAH,MAAA;;wBAEnD,MAAuD,E,kBADzD5D,mBAAA,CAOWsD,SAAA,QAAAC,WAAA,CANQrD,MAAA,CAAAwE,YAAY,CAACxE,MAAA,CAAA8D,eAAe,CAACW,OAAO,GAA9CC,MAAM;6BADfvE,YAAA,CAOWwE,mBAAA;UALRlB,GAAG,EAAEiB,MAAM,CAACjB,GAAG;UACfmB,KAAK,EAAEF,MAAM,CAACjB,GAAG;UAClB5D,KAAK,EAAC;;4BAEN,MAAgB,C,kCAAb6E,MAAM,CAACjB,GAAG,IAAG,IAAE,GAAAhD,gBAAA,CAAGiE,MAAM,CAACG,IAAI,iB;;;;;2CAMtB7E,MAAA,CAAA8D,eAAe,CAACI,aAAa,c,cAA7CpE,mBAAA,CAWMsD,SAAA;MAAAK,GAAA;IAAA,IAZNY,mBAAA,SAAY,EACZ9D,mBAAA,CAWM,OAXNuE,WAWM,GAVJnD,YAAA,CASoBoD,4BAAA;kBATQ/E,MAAA,CAAA4D,WAAW,CAAC5D,MAAA,CAAA8D,eAAe,CAACD,EAAE;iEAA9B7D,MAAA,CAAA4D,WAAW,CAAC5D,MAAA,CAAA8D,eAAe,CAACD,EAAE,IAAAH,MAAA;;wBAEtD,MAAuD,E,kBADzD5D,mBAAA,CAOcsD,SAAA,QAAAC,WAAA,CANKrD,MAAA,CAAAwE,YAAY,CAACxE,MAAA,CAAA8D,eAAe,CAACW,OAAO,GAA9CC,MAAM;6BADfvE,YAAA,CAOc6E,sBAAA;UALXvB,GAAG,EAAEiB,MAAM,CAACjB,GAAG;UACfmB,KAAK,EAAEF,MAAM,CAACjB,GAAG;UAClB5D,KAAK,EAAC;;4BAEN,MAAgB,C,kCAAb6E,MAAM,CAACjB,GAAG,IAAG,IAAE,GAAAhD,gBAAA,CAAGiE,MAAM,CAACG,IAAI,iB;;;;;4FAMtB7E,MAAA,CAAA8D,eAAe,CAACI,aAAa,c,cAA7CpE,mBAAA,CAKMsD,SAAA;MAAAK,GAAA;IAAA,IANNY,mBAAA,SAAY,EACZ9D,mBAAA,CAKM,OALN0E,WAKM,GAJJtD,YAAA,CAGiB4C,yBAAA;kBAHQvE,MAAA,CAAA4D,WAAW,CAAC5D,MAAA,CAAA8D,eAAe,CAACD,EAAE;iEAA9B7D,MAAA,CAAA4D,WAAW,CAAC5D,MAAA,CAAA8D,eAAe,CAACD,EAAE,IAAAH,MAAA;;wBACrD,MAAsD,CAAtD/B,YAAA,CAAsDgD,mBAAA;QAA5CC,KAAK,EAAC,IAAI;QAAC/E,KAAK,EAAC;;0BAAc,MAAEoC,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;UAC3CN,YAAA,CAAsDgD,mBAAA;QAA5CC,KAAK,EAAC,IAAI;QAAC/E,KAAK,EAAC;;0BAAc,MAAEoC,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;;;4FAK/BjC,MAAA,CAAA8D,eAAe,CAACI,aAAa,c,cAA7CpE,mBAAA,CAOMsD,SAAA;MAAAK,GAAA;IAAA,IARNY,mBAAA,SAAY,EACZ9D,mBAAA,CAOM,OAPN2E,WAOM,GANJvD,YAAA,CAKEwD,mBAAA;kBAJSnF,MAAA,CAAA4D,WAAW,CAAC5D,MAAA,CAAA8D,eAAe,CAACD,EAAE;iEAA9B7D,MAAA,CAAA4D,WAAW,CAAC5D,MAAA,CAAA8D,eAAe,CAACD,EAAE,IAAAH,MAAA;MACvC7B,IAAI,EAAC,UAAU;MACduD,IAAI,EAAE,CAAC;MACRC,WAAW,EAAC;qIAIhB9E,mBAAA,CAIM,OAJN+E,WAIM,GAHJ3D,YAAA,CAA2FC,oBAAA;MAA/EG,OAAK,EAAE/B,MAAA,CAAAuF,gBAAgB;MAAGC,QAAQ,EAAExF,MAAA,CAAAkD,oBAAoB;;wBAAQ,MAAGjB,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;qCACzBjC,MAAA,CAAAkD,oBAAoB,GAAGlD,MAAA,CAAAuB,SAAS,CAACC,MAAM,Q,cAA7FrB,YAAA,CAAkHyB,oBAAA;;MAAvGC,IAAI,EAAC,SAAS;MAAEE,OAAK,EAAE/B,MAAA,CAAAyF;;wBAAiE,MAAGxD,MAAA,SAAAA,MAAA,Q,iBAAH,KAAG,E;;;yBACtG9B,YAAA,CAA4EyB,oBAAA;;MAAjEC,IAAI,EAAC,SAAS;MAAEE,OAAK,EAAE/B,MAAA,CAAA0F;;wBAA0B,MAAIzD,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;;;SAMxEoC,mBAAA,aAAgB,EAChB1C,YAAA,CAiBYgE,oBAAA;IAhBVhF,KAAK,EAAC,MAAM;gBACHX,MAAA,CAAA4F,mBAAmB;+DAAnB5F,MAAA,CAAA4F,mBAAmB,GAAAlC,MAAA;IAC5BmC,KAAK,EAAC;;IAQKC,MAAM,EAAAxF,QAAA,CACf,MAGM,CAHNC,mBAAA,CAGM,OAHNwF,WAGM,GAFJpE,YAAA,CAA8DC,oBAAA;MAAlDG,OAAK,EAAAE,MAAA,QAAAA,MAAA,MAAAyB,MAAA,IAAE1D,MAAA,CAAA4F,mBAAmB;;wBAAU,MAAE3D,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAClDN,YAAA,CAAoFC,oBAAA;MAAzEC,IAAI,EAAC,SAAS;MAAEE,OAAK,EAAE/B,MAAA,CAAAgG,UAAU;MAAG3E,OAAO,EAAErB,MAAA,CAAAiG;;wBAAY,MAAIhE,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;;sBAT5E,MAKM,CALN1B,mBAAA,CAKM,OALN2F,WAKM,G,4BAJJ3F,mBAAA,CAAmB,WAAhB,cAAY,qBACqBP,MAAA,CAAAmG,eAAe,Q,cAAnDrG,mBAAA,CAEI,KAFJsG,WAEI,EAFqD,OACnD,GAAA3F,gBAAA,CAAGT,MAAA,CAAAmG,eAAe,IAAG,iBAC3B,mB;;qCAUJ9B,mBAAA,aAAgB,EAChB1C,YAAA,CAkDYgE,oBAAA;IAjDVhF,KAAK,EAAC,MAAM;gBACHX,MAAA,CAAAqG,mBAAmB;+DAAnBrG,MAAA,CAAAqG,mBAAmB,GAAA3C,MAAA;IAC5BmC,KAAK,EAAC,OAAO;IACZ,sBAAoB,EAAE,KAAK;IAC3B,uBAAqB,EAAE,KAAK;IAC5B,YAAU,EAAE;;IAuCFC,MAAM,EAAAxF,QAAA,CACf,MAEM,CAFNC,mBAAA,CAEM,OAFN+F,WAEM,GADJ3E,YAAA,CAAgEC,oBAAA;MAArDC,IAAI,EAAC,SAAS;MAAEE,OAAK,EAAE/B,MAAA,CAAAsC;;wBAAc,MAAIL,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;;sBA1CnB,MAuDjC,CApDqCjC,MAAA,CAAAuG,UAAU,I,cAAnDzG,mBAAA,CAoCM,OApCN0G,WAoCM,GAnCJjG,mBAAA,CAOM,OAPNkG,WAOM,GANJlG,mBAAA,CAEM;MAFDV,KAAK,EAAA+C,eAAA,EAAC,eAAe;QAAA,QAAmB5C,MAAA,CAAAuG,UAAU,CAACG,OAAO,CAACC,SAAS;QAAA,SAAW3G,MAAA,CAAAuG,UAAU,CAACG,OAAO,CAACC;MAAS;wBAC3G3G,MAAA,CAAAuG,UAAU,CAACG,OAAO,CAACC,SAAS,wCAEjCpG,mBAAA,CAEM,OAFNqG,WAEM,G,6CAFoB,MACrB,IAAArG,mBAAA,CAA2C,cAAAE,gBAAA,CAAlCT,MAAA,CAAAuG,UAAU,CAACG,OAAO,CAACvC,KAAK,kB,iBAAU,KAAG,GAAA1D,gBAAA,CAAGT,MAAA,CAAAU,IAAI,CAACM,WAAW,iB,KAIxET,mBAAA,CAyBM,OAzBNsG,WAyBM,GAxBJtG,mBAAA,CAGM,OAHNuG,WAGM,G,4BAFJvG,mBAAA,CAAoC;MAA/BV,KAAK,EAAC;IAAc,GAAC,MAAI,qBAC9BU,mBAAA,CAAwE,OAAxEwG,WAAwE,EAAAtG,gBAAA,CAA3CT,MAAA,CAAAuG,UAAU,CAACG,OAAO,CAACM,eAAe,iB,GAEjEzG,mBAAA,CAGM,OAHN0G,WAGM,G,4BAFJ1G,mBAAA,CAAqC;MAAhCV,KAAK,EAAC;IAAc,GAAC,OAAK,qBAC/BU,mBAAA,CAA0E,OAA1E2G,WAA0E,EAAAzG,gBAAA,CAA7CT,MAAA,CAAAuG,UAAU,CAACG,OAAO,CAACS,iBAAiB,iB,GAEnE5G,mBAAA,CAGM,OAHN6G,WAGM,G,4BAFJ7G,mBAAA,CAAqC;MAAhCV,KAAK,EAAC;IAAc,GAAC,OAAK,qBAC/BU,mBAAA,CAA+G,OAA/G8G,WAA+G,EAAA5G,gBAAA,CAAlFT,MAAA,CAAAuG,UAAU,CAACG,OAAO,CAACM,eAAe,GAAGhH,MAAA,CAAAuG,UAAU,CAACG,OAAO,CAACS,iBAAiB,iB,GAExG5G,mBAAA,CAGM,OAHN+G,WAGM,G,4BAFJ/G,mBAAA,CAAqC;MAAhCV,KAAK,EAAC;IAAc,GAAC,OAAK,qBAC/BU,mBAAA,CAAqD,OAArDgH,WAAqD,EAAA9G,gBAAA,CAAxBT,MAAA,CAAAU,IAAI,CAACK,UAAU,iB,GAE9CR,mBAAA,CAGM,OAHNiH,WAGM,G,4BAFJjH,mBAAA,CAAqC;MAAhCV,KAAK,EAAC;IAAc,GAAC,OAAK,qBAC/BU,mBAAA,CAAmF,OAAnFkH,WAAmF,EAAzD,IAAE,GAAAhH,gBAAA,CAAGT,MAAA,CAAAuG,UAAU,CAACG,OAAO,CAACgB,cAAc,IAAG,YAAU,gB,GAE/EnH,mBAAA,CAGM,OAHNoH,WAGM,G,4BAFJpH,mBAAA,CAAqC;MAAhCV,KAAK,EAAC;IAAc,GAAC,OAAK,qBAC/BU,mBAAA,CAA6E,OAA7EqH,WAA6E,EAAAnH,gBAAA,CAAhDT,MAAA,CAAAuG,UAAU,CAACG,OAAO,CAACmB,kBAAkB,IAAG,IAAE,gB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}