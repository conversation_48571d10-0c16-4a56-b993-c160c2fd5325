const { pool } = require('../config/db');

// 创建督导表的SQL
const createTableSQL = `
CREATE TABLE IF NOT EXISTS supervisors (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  gender VARCHAR(10) DEFAULT '男',
  department VARCHAR(100) NOT NULL,
  title VARCHAR(100) NOT NULL,
  specialty TEXT,
  phone VARCHAR(20),
  email VARCHAR(100),
  status TINYINT DEFAULT 1,
  avatar VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
)`;

// 初始化表
const initTable = async () => {
  try {
    await pool.execute(createTableSQL);
    console.log('督导表创建成功或已存在');
  } catch (error) {
    console.error('创建督导表失败:', error);
    throw error;
  }
};

// 获取所有督导成员
const findAll = async (filters = {}, page = 1, limit = 10) => {
  try {
    let query = 'SELECT * FROM supervisors WHERE 1=1';
    const params = [];

    // 添加过滤条件
    if (filters.name) {
      query += ' AND name LIKE ?';
      params.push(`%${filters.name}%`);
    }

    if (filters.department) {
      query += ' AND department LIKE ?';
      params.push(`%${filters.department}%`);
    }

    if (filters.status !== undefined && filters.status !== '') {
      query += ' AND status = ?';
      params.push(Number(filters.status));
    }

    // 计算总数
    const [countResult] = await pool.execute(`SELECT COUNT(*) as count FROM supervisors WHERE 1=1${
      filters.name ? ' AND name LIKE ?' : ''
    }${filters.department ? ' AND department LIKE ?' : ''}${
      filters.status !== undefined && filters.status !== '' ? ' AND status = ?' : ''
    }`, [...params]);
    
    const count = countResult[0].count;

    // 添加排序和分页
    query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    const offset = (page - 1) * limit;
    params.push(Number(limit), Number(offset));

    // 执行查询
    const [rows] = await pool.execute(query, params);

    return { count, data: rows };
  } catch (error) {
    console.error('获取督导成员列表失败:', error);
    throw error;
  }
};

// 根据ID查找督导成员
const findById = async (id) => {
  try {
    const [rows] = await pool.execute('SELECT * FROM supervisors WHERE id = ?', [id]);
    return rows[0] || null;
  } catch (error) {
    console.error('根据ID查找督导成员失败:', error);
    throw error;
  }
};

// 创建督导成员
const create = async (supervisorData) => {
  try {
    const { name, gender, department, title, specialty, phone, email, status, avatar } = supervisorData;
    
    const [result] = await pool.execute(
      'INSERT INTO supervisors (name, gender, department, title, specialty, phone, email, status, avatar) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
      [name, gender, department, title, specialty, phone, email, status, avatar]
    );
    
    const id = result.insertId;
    return { id, ...supervisorData };
  } catch (error) {
    console.error('创建督导成员失败:', error);
    throw error;
  }
};

// 更新督导成员
const update = async (id, supervisorData) => {
  try {
    const { name, gender, department, title, specialty, phone, email, status, avatar } = supervisorData;
    
    const [result] = await pool.execute(
      'UPDATE supervisors SET name = ?, gender = ?, department = ?, title = ?, specialty = ?, phone = ?, email = ?, status = ?, avatar = ? WHERE id = ?',
      [name, gender, department, title, specialty, phone, email, status, avatar, id]
    );
    
    if (result.affectedRows === 0) {
      return null;
    }
    
    return { id, ...supervisorData };
  } catch (error) {
    console.error('更新督导成员失败:', error);
    throw error;
  }
};

// 删除督导成员
const remove = async (id) => {
  try {
    const [result] = await pool.execute('DELETE FROM supervisors WHERE id = ?', [id]);
    return result.affectedRows > 0;
  } catch (error) {
    console.error('删除督导成员失败:', error);
    throw error;
  }
};

module.exports = {
  initTable,
  findAll,
  findById,
  create,
  update,
  remove
}; 