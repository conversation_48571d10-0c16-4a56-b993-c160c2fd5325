"use strict";(self["webpackChunkms"]=self["webpackChunkms"]||[]).push([[941],{3941:function(e,a,t){t.r(a),t.d(a,{default:function(){return b}});var l=t(6768),n=t(4232);const r={class:"evaluation-list-container"},o={class:"card-header"},i={class:"pagination-container"};function d(e,a,t,d,c,p){const u=(0,l.g2)("el-button"),s=(0,l.g2)("el-input"),h=(0,l.g2)("el-form-item"),m=(0,l.g2)("el-form"),g=(0,l.g2)("el-table-column"),v=(0,l.g2)("el-tag"),b=(0,l.g2)("el-table"),_=(0,l.g2)("el-pagination"),k=(0,l.g2)("el-card"),f=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",r,[(0,l.bF)(k,{class:"box-card"},{header:(0,l.k6)(()=>[(0,l.Lk)("div",o,[a[4]||(a[4]=(0,l.Lk)("span",{class:"title"},"教学活动督导评价",-1)),(0,l.bF)(u,{type:"primary",onClick:d.goToAddEvaluation},{default:(0,l.k6)(()=>a[3]||(a[3]=[(0,l.eW)("添加评价")])),_:1,__:[3]},8,["onClick"])])]),default:(0,l.k6)(()=>[(0,l.bF)(m,{inline:!0,model:d.searchForm,class:"search-form"},{default:(0,l.k6)(()=>[(0,l.bF)(h,{label:"教师姓名"},{default:(0,l.k6)(()=>[(0,l.bF)(s,{modelValue:d.searchForm.teacherName,"onUpdate:modelValue":a[0]||(a[0]=e=>d.searchForm.teacherName=e),placeholder:"教师姓名",clearable:""},null,8,["modelValue"])]),_:1}),(0,l.bF)(h,null,{default:(0,l.k6)(()=>[(0,l.bF)(u,{type:"primary",onClick:d.handleSearch},{default:(0,l.k6)(()=>a[5]||(a[5]=[(0,l.eW)("查询")])),_:1,__:[5]},8,["onClick"]),(0,l.bF)(u,{onClick:d.resetSearch},{default:(0,l.k6)(()=>a[6]||(a[6]=[(0,l.eW)("重置")])),_:1,__:[6]},8,["onClick"])]),_:1})]),_:1},8,["model"]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(b,{data:d.evaluationList,border:"",style:{width:"100%"}},{default:(0,l.k6)(()=>[(0,l.bF)(g,{type:"index",width:"50",label:"#"}),(0,l.bF)(g,{prop:"teacher_name",label:"教师姓名",width:"100"}),(0,l.bF)(g,{prop:"supervising_department",label:"督导教研室",width:"120"}),(0,l.bF)(g,{prop:"case_topic",label:"病例/主题","show-overflow-tooltip":""}),(0,l.bF)(g,{prop:"teaching_form",label:"教学活动形式",width:"120"}),(0,l.bF)(g,{prop:"student_name",label:"学员姓名",width:"100"}),(0,l.bF)(g,{prop:"student_type",label:"学员类别",width:"120"}),(0,l.bF)(g,{prop:"average_score",label:"平均分",width:"80"}),(0,l.bF)(g,{label:"能力认定",width:"100"},{default:(0,l.k6)(e=>[(0,l.bF)(v,{type:e.row.competency_approved?"success":"danger"},{default:(0,l.k6)(()=>[(0,l.eW)((0,n.v_)(e.row.competency_approved?"同意":"不同意"),1)]),_:2},1032,["type"])]),_:1}),(0,l.bF)(g,{prop:"evaluation_date",label:"评价时间",width:"180"},{default:(0,l.k6)(e=>[(0,l.eW)((0,n.v_)(d.formatDate(e.row.evaluation_date)),1)]),_:1}),(0,l.bF)(g,{prop:"evaluator_name",label:"评估人",width:"100"}),(0,l.bF)(g,{label:"操作",width:"180",fixed:"right"},{default:(0,l.k6)(e=>[(0,l.bF)(u,{size:"small",onClick:a=>d.viewDetails(e.row.id)},{default:(0,l.k6)(()=>a[7]||(a[7]=[(0,l.eW)("详情")])),_:2,__:[7]},1032,["onClick"]),d.isAdmin?((0,l.uX)(),(0,l.Wv)(u,{key:0,size:"small",type:"primary",onClick:a=>d.editEvaluation(e.row.id)},{default:(0,l.k6)(()=>a[8]||(a[8]=[(0,l.eW)("编辑")])),_:2,__:[8]},1032,["onClick"])):(0,l.Q3)("",!0),d.isAdmin?((0,l.uX)(),(0,l.Wv)(u,{key:1,size:"small",type:"danger",onClick:a=>d.handleDelete(e.row)},{default:(0,l.k6)(()=>a[9]||(a[9]=[(0,l.eW)("删除")])),_:2,__:[9]},1032,["onClick"])):(0,l.Q3)("",!0)]),_:1})]),_:1},8,["data"])),[[f,d.loading]]),(0,l.Lk)("div",i,[(0,l.bF)(_,{"current-page":d.currentPage,"onUpdate:currentPage":a[1]||(a[1]=e=>d.currentPage=e),"page-size":d.pageSize,"onUpdate:pageSize":a[2]||(a[2]=e=>d.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:d.total,onSizeChange:d.handleSizeChange,onCurrentChange:d.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}t(4114),t(8111),t(5207);var c=t(144),p=t(1387),u=t(1219),s=t(2933),h=t(4373),m={name:"EvaluationList",setup(){const e=(0,p.rd)(),a=(0,c.KR)(!1),t=(0,c.KR)([]),n=(0,c.KR)(0),r=(0,c.KR)(1),o=(0,c.KR)(10),i=(0,l.EW)(()=>!0),d=(0,c.Kh)({teacherName:"",department:"",competencyApproved:""});(0,l.sV)(()=>{m()});const m=async()=>{a.value=!0;try{const e={page:r.value,limit:o.value};d.teacherName&&(e.teacherName=d.teacherName),d.department&&(e.department=d.department),""!==d.competencyApproved&&(e.competencyApproved=d.competencyApproved);const a=await h.A.get("http://localhost:3000/api/evaluations",{params:e});t.value=a.data.data,n.value=a.data.count}catch(e){console.error("获取督导评价列表失败:",e),u.nk.error("获取督导评价列表失败")}finally{a.value=!1}},g=()=>{r.value=1,m()},v=()=>{Object.keys(d).forEach(e=>{d[e]=""}),r.value=1,m()},b=e=>{o.value=e,m()},_=e=>{r.value=e,m()},k=a=>{e.push(`/evaluations/detail/${a}`)},f=()=>{e.push("/evaluations/add")},w=a=>{e.push(`/evaluations/add?id=${a}`)},y=e=>{s.s.confirm(`确定要删除"${e.teacher_name}"的督导评价记录吗?`,"警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(async()=>{try{await h.A.delete(`http://localhost:3000/api/evaluations/${e.id}`),u.nk.success("删除成功"),m()}catch(a){console.error("删除失败:",a),u.nk.error("删除失败")}}).catch(()=>{u.nk.info("已取消删除")})},F=e=>{if(!e)return"-";const a=new Date(e);return`${a.getFullYear()}-${(a.getMonth()+1).toString().padStart(2,"0")}-${a.getDate().toString().padStart(2,"0")}`};return{loading:a,evaluationList:t,searchForm:d,currentPage:r,pageSize:o,total:n,isAdmin:i,handleSearch:g,resetSearch:v,handleSizeChange:b,handleCurrentChange:_,viewDetails:k,goToAddEvaluation:f,editEvaluation:w,handleDelete:y,formatDate:F}}},g=t(1241);const v=(0,g.A)(m,[["render",d],["__scopeId","data-v-b657b8ce"]]);var b=v}}]);
//# sourceMappingURL=941.14757eb3.js.map