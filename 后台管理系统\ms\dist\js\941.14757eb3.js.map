{"version": 3, "file": "js/941.14757eb3.js", "mappings": "iLACOA,MAAM,6B,GAGAA,MAAM,e,GAyDRA,MAAM,wB,kSA5DfC,EAAAA,EAAAA,IAwEM,MAxENC,EAwEM,EAvEJC,EAAAA,EAAAA,IAsEUC,EAAA,CAtEDJ,MAAM,YAAU,CACZK,QAAMC,EAAAA,EAAAA,IACf,IAGM,EAHNC,EAAAA,EAAAA,IAGM,MAHNC,EAGM,C,aAFJD,EAAAA,EAAAA,IAAmC,QAA7BP,MAAM,SAAQ,YAAQ,KAC5BG,EAAAA,EAAAA,IAAqEM,EAAA,CAA1DC,KAAK,UAAWC,QAAOC,EAAAC,mB,kBAAmB,IAAIC,EAAA,KAAAA,EAAA,K,QAAJ,W,gDAKzD,IAUU,EAVVX,EAAAA,EAAAA,IAUUY,EAAA,CAVAC,QAAQ,EAAOC,MAAOL,EAAAM,WAAYlB,MAAM,e,kBAChD,IAEe,EAFfG,EAAAA,EAAAA,IAEegB,EAAA,CAFDC,MAAM,QAAM,C,iBACxB,IAA0E,EAA1EjB,EAAAA,EAAAA,IAA0EkB,EAAA,C,WAAvDT,EAAAM,WAAWI,Y,qCAAXV,EAAAM,WAAWI,YAAWC,GAAEC,YAAY,OAAOC,UAAA,I,gCAIhEtB,EAAAA,EAAAA,IAGegB,EAAA,M,iBAFb,IAA8D,EAA9DhB,EAAAA,EAAAA,IAA8DM,EAAA,CAAnDC,KAAK,UAAWC,QAAOC,EAAAc,c,kBAAc,IAAEZ,EAAA,KAAAA,EAAA,K,QAAF,S,4BAChDX,EAAAA,EAAAA,IAA8CM,EAAA,CAAlCE,QAAOC,EAAAe,aAAW,C,iBAAE,IAAEb,EAAA,KAAAA,EAAA,K,QAAF,S,6EAKpCc,EAAAA,EAAAA,IAkCWC,EAAA,CAhCRC,KAAMlB,EAAAmB,eACPC,OAAA,GACAC,MAAA,gB,kBAEA,IAAqD,EAArD9B,EAAAA,EAAAA,IAAqD+B,EAAA,CAApCxB,KAAK,QAAQyB,MAAM,KAAKf,MAAM,OAC/CjB,EAAAA,EAAAA,IAAgE+B,EAAA,CAA/CE,KAAK,eAAehB,MAAM,OAAOe,MAAM,SACxDhC,EAAAA,EAAAA,IAA2E+B,EAAA,CAA1DE,KAAK,yBAAyBhB,MAAM,QAAQe,MAAM,SACnEhC,EAAAA,EAAAA,IAAyE+B,EAAA,CAAxDE,KAAK,aAAahB,MAAM,QAAQ,8BACjDjB,EAAAA,EAAAA,IAAmE+B,EAAA,CAAlDE,KAAK,gBAAgBhB,MAAM,SAASe,MAAM,SAC3DhC,EAAAA,EAAAA,IAAgE+B,EAAA,CAA/CE,KAAK,eAAehB,MAAM,OAAOe,MAAM,SACxDhC,EAAAA,EAAAA,IAAgE+B,EAAA,CAA/CE,KAAK,eAAehB,MAAM,OAAOe,MAAM,SACxDhC,EAAAA,EAAAA,IAA+D+B,EAAA,CAA9CE,KAAK,gBAAgBhB,MAAM,MAAMe,MAAM,QACxDhC,EAAAA,EAAAA,IAMkB+B,EAAA,CANDd,MAAM,OAAOe,MAAM,O,CACvBE,SAAO/B,EAAAA,EAAAA,IAGPgC,GAHc,EACvBnC,EAAAA,EAAAA,IAESoC,EAAA,CAFA7B,KAAM4B,EAAME,IAAIC,oBAAsB,UAAY,U,kBACzD,IAAkD,E,iBAA/CH,EAAME,IAAIC,oBAAsB,KAAO,OAAV,K,6BAItCtC,EAAAA,EAAAA,IAIkB+B,EAAA,CAJDE,KAAK,kBAAkBhB,MAAM,OAAOe,MAAM,O,CAC9CE,SAAO/B,EAAAA,EAAAA,IAC2BgC,GADpB,E,iBACpB1B,EAAA8B,WAAWJ,EAAME,IAAIG,kBAAe,K,OAG3CxC,EAAAA,EAAAA,IAAiE+B,EAAA,CAAhDE,KAAK,iBAAiBhB,MAAM,MAAMe,MAAM,SACzDhC,EAAAA,EAAAA,IAMkB+B,EAAA,CANDd,MAAM,KAAKe,MAAM,MAAMS,MAAM,S,CACjCP,SAAO/B,EAAAA,EAAAA,IACyDgC,GADlD,EACvBnC,EAAAA,EAAAA,IAAyEM,EAAA,CAA9DoC,KAAK,QAASlC,QAAKY,GAAEX,EAAAkC,YAAYR,EAAME,IAAIO,K,kBAAK,IAAEjC,EAAA,KAAAA,EAAA,K,QAAF,S,8BACwBF,EAAAoC,U,WAAnFpB,EAAAA,EAAAA,IAA0GnB,EAAA,C,MAA/FoC,KAAK,QAAQnC,KAAK,UAAWC,QAAKY,GAAEX,EAAAqC,eAAeX,EAAME,IAAIO,K,kBAAoB,IAAEjC,EAAA,KAAAA,EAAA,K,QAAF,S,+CACfF,EAAAoC,U,WAA7EpB,EAAAA,EAAAA,IAAoGnB,EAAA,C,MAAzFoC,KAAK,QAAQnC,KAAK,SAAUC,QAAKY,GAAEX,EAAAsC,aAAaZ,EAAME,M,kBAAqB,IAAE1B,EAAA,KAAAA,EAAA,K,QAAF,S,+EA9B/EF,EAAAuC,YAoCb5C,EAAAA,EAAAA,IAUM,MAVN6C,EAUM,EATJjD,EAAAA,EAAAA,IAQEkD,EAAA,CAPQ,eAAczC,EAAA0C,Y,sCAAA1C,EAAA0C,YAAW/B,GACzB,YAAWX,EAAA2C,S,mCAAA3C,EAAA2C,SAAQhC,GAC1B,aAAY,CAAC,GAAI,GAAI,GAAI,KAC1BiC,OAAO,0CACNC,MAAO7C,EAAA6C,MACPC,aAAa9C,EAAA+C,iBACbC,gBAAgBhD,EAAAiD,qB,uKAa3B,GACEC,KAAM,iBACNC,KAAAA,GACE,MAAMC,GAASC,EAAAA,EAAAA,MAGTd,GAAUe,EAAAA,EAAAA,KAAI,GACdnC,GAAiBmC,EAAAA,EAAAA,IAAI,IACrBT,GAAQS,EAAAA,EAAAA,IAAI,GACZZ,GAAcY,EAAAA,EAAAA,IAAI,GAClBX,GAAWW,EAAAA,EAAAA,IAAI,IAGflB,GAAUmB,EAAAA,EAAAA,IAAS,KAGhB,GAIHjD,GAAakD,EAAAA,EAAAA,IAAS,CAC1B9C,YAAa,GACb+C,WAAY,GACZC,mBAAoB,MAItBC,EAAAA,EAAAA,IAAU,KACRC,MAIF,MAAMA,EAAmBC,UACvBtB,EAAQuB,OAAQ,EAChB,IAEE,MAAMC,EAAS,CACbC,KAAMtB,EAAYoB,MAClBG,MAAOtB,EAASmB,OAIdxD,EAAWI,cACbqD,EAAOrD,YAAcJ,EAAWI,aAE9BJ,EAAWmD,aACbM,EAAON,WAAanD,EAAWmD,YAEK,KAAlCnD,EAAWoD,qBACbK,EAAOL,mBAAqBpD,EAAWoD,oBAGzC,MAAMQ,QAAiBC,EAAAA,EAAMC,IAAI,0CAA2C,CAAEL,WAC9E5C,EAAe2C,MAAQI,EAAShD,KAAKA,KACrC2B,EAAMiB,MAAQI,EAAShD,KAAKmD,KAC9B,CAAE,MAAOC,GACPC,QAAQD,MAAM,cAAeA,GAC7BE,EAAAA,GAAUF,MAAM,aAClB,CAAE,QACA/B,EAAQuB,OAAQ,CAClB,GAIIhD,EAAeA,KACnB4B,EAAYoB,MAAQ,EACpBF,KAII7C,EAAcA,KAClB0D,OAAOC,KAAKpE,GAAYqE,QAAQC,IAC9BtE,EAAWsE,GAAO,KAEpBlC,EAAYoB,MAAQ,EACpBF,KAIIb,EAAoB8B,IACxBlC,EAASmB,MAAQe,EACjBjB,KAGIX,EAAuB4B,IAC3BnC,EAAYoB,MAAQe,EACpBjB,KAII1B,EAAeC,IACnBiB,EAAO0B,KAAK,uBAAuB3C,MAI/BlC,EAAoBA,KACxBmD,EAAO0B,KAAK,qBAIRzC,EAAkBF,IACtBiB,EAAO0B,KAAK,uBAAuB3C,MAI/BG,EAAgBV,IACpBmD,EAAAA,EAAaC,QACX,SAASpD,EAAIqD,yBACb,KACA,CACEC,kBAAmB,KACnBC,iBAAkB,KAClBrF,KAAM,YAGPsF,KAAKvB,UACJ,UACQM,EAAAA,EAAMkB,OAAO,2CAA2CzD,EAAIO,MAClEqC,EAAAA,GAAUc,QAAQ,QAClB1B,GACF,CAAE,MAAOU,GACPC,QAAQD,MAAM,QAASA,GACvBE,EAAAA,GAAUF,MAAM,OAClB,IAEDiB,MAAM,KACLf,EAAAA,GAAUgB,KAAK,YAKf1D,EAAc2D,IAClB,IAAKA,EAAY,MAAO,IACxB,MAAMC,EAAO,IAAIC,KAAKF,GACtB,MAAO,GAAGC,EAAKE,kBAAkBF,EAAKG,WAAa,GAAGC,WAAWC,SAAS,EAAG,QAAQL,EAAKM,UAAUF,WAAWC,SAAS,EAAG,QAG7H,MAAO,CACLxD,UACApB,iBACAb,aACAoC,cACAC,WACAE,QACAT,UACAtB,eACAC,cACAgC,mBACAE,sBACAf,cACAjC,oBACAoC,iBACAC,eACAR,aAEJ,G,UCtOF,MAAMmE,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://ms/./src/views/evaluations/EvaluationList.vue", "webpack://ms/./src/views/evaluations/EvaluationList.vue?0bbb"], "sourcesContent": ["<template>\r\n  <div class=\"evaluation-list-container\">\r\n    <el-card class=\"box-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span class=\"title\">教学活动督导评价</span>\r\n          <el-button type=\"primary\" @click=\"goToAddEvaluation\">添加评价</el-button>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- 搜索区域 -->\r\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\r\n        <el-form-item label=\"教师姓名\">\r\n          <el-input v-model=\"searchForm.teacherName\" placeholder=\"教师姓名\" clearable />\r\n        </el-form-item>\r\n       \r\n       \r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n          <el-button @click=\"resetSearch\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <!-- 表格区域 -->\r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"evaluationList\"\r\n        border\r\n        style=\"width: 100%\"\r\n      >\r\n        <el-table-column type=\"index\" width=\"50\" label=\"#\" />\r\n        <el-table-column prop=\"teacher_name\" label=\"教师姓名\" width=\"100\" />\r\n        <el-table-column prop=\"supervising_department\" label=\"督导教研室\" width=\"120\" />\r\n        <el-table-column prop=\"case_topic\" label=\"病例/主题\" show-overflow-tooltip />\r\n        <el-table-column prop=\"teaching_form\" label=\"教学活动形式\" width=\"120\" />\r\n        <el-table-column prop=\"student_name\" label=\"学员姓名\" width=\"100\" />\r\n        <el-table-column prop=\"student_type\" label=\"学员类别\" width=\"120\" />\r\n        <el-table-column prop=\"average_score\" label=\"平均分\" width=\"80\" />\r\n        <el-table-column label=\"能力认定\" width=\"100\">\r\n          <template #default=\"scope\">\r\n            <el-tag :type=\"scope.row.competency_approved ? 'success' : 'danger'\">\r\n              {{ scope.row.competency_approved ? '同意' : '不同意' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"evaluation_date\" label=\"评价时间\" width=\"180\">\r\n          <template #default=\"scope\">\r\n            {{ formatDate(scope.row.evaluation_date) }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"evaluator_name\" label=\"评估人\" width=\"100\" />\r\n        <el-table-column label=\"操作\" width=\"180\" fixed=\"right\">\r\n          <template #default=\"scope\">\r\n            <el-button size=\"small\" @click=\"viewDetails(scope.row.id)\">详情</el-button>\r\n            <el-button size=\"small\" type=\"primary\" @click=\"editEvaluation(scope.row.id)\" v-if=\"isAdmin\">编辑</el-button>\r\n            <el-button size=\"small\" type=\"danger\" @click=\"handleDelete(scope.row)\" v-if=\"isAdmin\">删除</el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          v-model:current-page=\"currentPage\"\r\n          v-model:page-size=\"pageSize\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n        />\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, computed, onMounted } from 'vue'\r\nimport { useRouter } from 'vue-router'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'EvaluationList',\r\n  setup() {\r\n    const router = useRouter()\r\n    \r\n    // 基础数据\r\n    const loading = ref(false)\r\n    const evaluationList = ref([])\r\n    const total = ref(0)\r\n    const currentPage = ref(1)\r\n    const pageSize = ref(10)\r\n    \r\n    // 是否为管理员或督导\r\n    const isAdmin = computed(() => {\r\n      // 这里可以根据实际的用户角色判断\r\n      // 简单起见，这里暂时返回 true\r\n      return true\r\n    })\r\n    \r\n    // 搜索表单\r\n    const searchForm = reactive({\r\n      teacherName: '',\r\n      department: '',\r\n      competencyApproved: ''\r\n    })\r\n    \r\n    // 生命周期钩子\r\n    onMounted(() => {\r\n      fetchEvaluations()\r\n    })\r\n    \r\n    // 获取评价列表\r\n    const fetchEvaluations = async () => {\r\n      loading.value = true\r\n      try {\r\n        // 构建查询参数\r\n        const params = {\r\n          page: currentPage.value,\r\n          limit: pageSize.value\r\n        }\r\n        \r\n        // 添加搜索条件\r\n        if (searchForm.teacherName) {\r\n          params.teacherName = searchForm.teacherName\r\n        }\r\n        if (searchForm.department) {\r\n          params.department = searchForm.department\r\n        }\r\n        if (searchForm.competencyApproved !== '') {\r\n          params.competencyApproved = searchForm.competencyApproved\r\n        }\r\n        \r\n        const response = await axios.get('http://localhost:3000/api/evaluations', { params })\r\n        evaluationList.value = response.data.data\r\n        total.value = response.data.count\r\n      } catch (error) {\r\n        console.error('获取督导评价列表失败:', error)\r\n        ElMessage.error('获取督导评价列表失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 搜索操作\r\n    const handleSearch = () => {\r\n      currentPage.value = 1\r\n      fetchEvaluations()\r\n    }\r\n    \r\n    // 重置搜索\r\n    const resetSearch = () => {\r\n      Object.keys(searchForm).forEach(key => {\r\n        searchForm[key] = ''\r\n      })\r\n      currentPage.value = 1\r\n      fetchEvaluations()\r\n    }\r\n    \r\n    // 分页操作\r\n    const handleSizeChange = (val) => {\r\n      pageSize.value = val\r\n      fetchEvaluations()\r\n    }\r\n    \r\n    const handleCurrentChange = (val) => {\r\n      currentPage.value = val\r\n      fetchEvaluations()\r\n    }\r\n    \r\n    // 查看详情\r\n    const viewDetails = (id) => {\r\n      router.push(`/evaluations/detail/${id}`)\r\n    }\r\n    \r\n    // 添加评价\r\n    const goToAddEvaluation = () => {\r\n      router.push('/evaluations/add')\r\n    }\r\n    \r\n    // 编辑评价\r\n    const editEvaluation = (id) => {\r\n      router.push(`/evaluations/add?id=${id}`)\r\n    }\r\n    \r\n    // 删除评价\r\n    const handleDelete = (row) => {\r\n      ElMessageBox.confirm(\r\n        `确定要删除\"${row.teacher_name}\"的督导评价记录吗?`,\r\n        '警告',\r\n        {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n        }\r\n      )\r\n        .then(async () => {\r\n          try {\r\n            await axios.delete(`http://localhost:3000/api/evaluations/${row.id}`)\r\n            ElMessage.success('删除成功')\r\n            fetchEvaluations()\r\n          } catch (error) {\r\n            console.error('删除失败:', error)\r\n            ElMessage.error('删除失败')\r\n          }\r\n        })\r\n        .catch(() => {\r\n          ElMessage.info('已取消删除')\r\n        })\r\n    }\r\n    \r\n    // 格式化日期\r\n    const formatDate = (dateString) => {\r\n      if (!dateString) return '-'\r\n      const date = new Date(dateString)\r\n      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`\r\n    }\r\n    \r\n    return {\r\n      loading,\r\n      evaluationList,\r\n      searchForm,\r\n      currentPage,\r\n      pageSize,\r\n      total,\r\n      isAdmin,\r\n      handleSearch,\r\n      resetSearch,\r\n      handleSizeChange,\r\n      handleCurrentChange,\r\n      viewDetails,\r\n      goToAddEvaluation,\r\n      editEvaluation,\r\n      handleDelete,\r\n      formatDate\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.evaluation-list-container {\r\n  padding: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.search-form {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n</style> ", "import { render } from \"./EvaluationList.vue?vue&type=template&id=b657b8ce&scoped=true\"\nimport script from \"./EvaluationList.vue?vue&type=script&lang=js\"\nexport * from \"./EvaluationList.vue?vue&type=script&lang=js\"\n\nimport \"./EvaluationList.vue?vue&type=style&index=0&id=b657b8ce&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-b657b8ce\"]])\n\nexport default __exports__"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_createElementVNode", "_hoisted_2", "_component_el_button", "type", "onClick", "$setup", "goToAddEvaluation", "_cache", "_component_el_form", "inline", "model", "searchForm", "_component_el_form_item", "label", "_component_el_input", "<PERSON><PERSON><PERSON>", "$event", "placeholder", "clearable", "handleSearch", "resetSearch", "_createBlock", "_component_el_table", "data", "evaluationList", "border", "style", "_component_el_table_column", "width", "prop", "default", "scope", "_component_el_tag", "row", "competency_approved", "formatDate", "evaluation_date", "fixed", "size", "viewDetails", "id", "isAdmin", "editEvaluation", "handleDelete", "loading", "_hoisted_3", "_component_el_pagination", "currentPage", "pageSize", "layout", "total", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "name", "setup", "router", "useRouter", "ref", "computed", "reactive", "department", "competencyApproved", "onMounted", "fetchEvaluations", "async", "value", "params", "page", "limit", "response", "axios", "get", "count", "error", "console", "ElMessage", "Object", "keys", "for<PERSON>ach", "key", "val", "push", "ElMessageBox", "confirm", "teacher_name", "confirmButtonText", "cancelButtonText", "then", "delete", "success", "catch", "info", "dateString", "date", "Date", "getFullYear", "getMonth", "toString", "padStart", "getDate", "__exports__", "render"], "sourceRoot": ""}