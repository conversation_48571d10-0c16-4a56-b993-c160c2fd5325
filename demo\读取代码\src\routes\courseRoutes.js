const express = require('express');
const router = express.Router();
const courseController = require('../controllers/courseController');
const { protect, authorize } = require('../controllers/authController');
const multer = require('multer');
const path = require('path');
const fs = require('fs');

// 确保上传目录存在
const uploadDir = 'uploads/courses';
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

// 配置文件上传
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, 'uploads/courses');
  },
  filename: (req, file, cb) => {
    // 获取文件原始名称
    let originalName = file.originalname;
    
    // 从请求体中获取原始文件名（如果前端提供）
    if (req.body && req.body.originalFileName) {
      originalName = req.body.originalFileName;
    }
    
    // 使用时间戳加原始文件名，避免文件名冲突
    const timestamp = Date.now();
    const ext = path.extname(originalName);
    const nameWithoutExt = path.basename(originalName, ext);
    
    // 保存带有时间戳的文件名，但在数据库中存储原始文件名
    const newFilename = `${timestamp}-${nameWithoutExt}${ext}`;
    
    // 将原始文件名存储在请求对象中，供后续使用
    req.originalFileName = originalName;
    req.fileTimestamp = timestamp;
    
    cb(null, newFilename);
  }
});

const fileFilter = (req, file, cb) => {
  const allowedFileTypes = ['.pdf', '.doc', '.docx', '.ppt', '.pptx', '.mp4', '.zip'];
  const extname = path.extname(file.originalname).toLowerCase();
  
  if (allowedFileTypes.includes(extname)) {
    cb(null, true);
  } else {
    cb(new Error('不支持的文件类型。允许的类型: PDF, Word, PowerPoint, MP4, ZIP'), false);
  }
};

const upload = multer({ 
  storage,
  fileFilter,
  limits: { fileSize: 100 * 1024 * 1024 } // 限制文件大小为100MB
});

// 所有路由都需要授权
router.use(protect);

// 获取所有课程
router.get('/', courseController.getAllCourses);

// 搜索课程
router.get('/search', courseController.searchCourses);

// 获取单个课程信息
router.get('/:id', courseController.getCourseById);

// 创建课程 - 仅管理员和教师可访问
router.post('/', authorize('admin', 'teacher'), upload.single('material'), courseController.createCourse);

// 更新课程 - 仅管理员和教师可访问
router.put('/:id', authorize('admin', 'teacher'), upload.single('material'), courseController.updateCourse);

// 删除课程 - 仅管理员可访问
router.delete('/:id', authorize('admin'), courseController.deleteCourse);

// 下载课件
router.get('/:id/download', courseController.downloadCourseMaterial);

module.exports = router; 