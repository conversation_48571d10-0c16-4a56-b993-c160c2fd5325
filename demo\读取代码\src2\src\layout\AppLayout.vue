<template>
  <div class="app-container">
    <el-container class="layout-container">
      <!-- 左侧菜单 -->
      <el-aside :width="isCollapse ? '64px' : '220px'" class="aside">
        <div class="logo">
          <img src="../assets/logo.png" alt="logo" />
          <h1 v-show="!isCollapse">管理系统</h1>
        </div>
        <el-scrollbar>
          <el-menu
            :default-active="activeMenu"
            class="el-menu-vertical"
            :collapse="isCollapse"
            background-color="#304156"
            text-color="#bfcbd9"
            active-text-color="#409EFF"
            router
            :collapse-transition="false"
          >            
            <!-- 原有菜单 -->
            <el-sub-menu index="/teachers" v-if="hasRole(['admin', 'supervisor'])">
              <template #title>
                <el-icon><User /></el-icon>
                <span>教师管理</span>
              </template>
              <el-menu-item index="/teachers/list">教师列表</el-menu-item>
            </el-sub-menu>
            
            <el-sub-menu index="/trainings">
              <template #title>
                <el-icon><Reading /></el-icon>
                <span>专项培训</span>
              </template>
              <el-menu-item index="/trainings/list">培训课程管理</el-menu-item>
            </el-sub-menu>
            
            <el-sub-menu index="/exams">
              <template #title>
                <el-icon><DocumentChecked /></el-icon>
                <span>考试管理</span>
              </template>
              <el-menu-item index="/exams/list">考试列表</el-menu-item>
              <el-menu-item index="/exams/my-results" v-if="hasRole(['teacher'])">我的考试成绩</el-menu-item>
            </el-sub-menu>
            
            <el-sub-menu index="/evaluations" v-if="hasRole(['admin'])">
              <template #title>
                <el-icon><Comment /></el-icon>
                <span>督导评价</span>
              </template>
              <el-menu-item index="/evaluations/list">督导评价记录</el-menu-item>
              <el-menu-item index="/evaluations/add" v-if="hasRole(['admin'])">添加督导评价</el-menu-item>
            </el-sub-menu>

            <el-sub-menu index="/competency" v-if="hasRole(['admin'])">
              <template #title>
                <el-icon><Medal /></el-icon>
                <span>能力认定</span>
              </template>
              <el-menu-item index="/competency/list">能力认定列表</el-menu-item>
            </el-sub-menu>
            
            <el-sub-menu index="/supervision" v-if="hasRole(['admin'])">
              <template #title>
                <el-icon><UserFilled /></el-icon>
                <span>督导小组</span>
              </template>
              <el-menu-item index="/supervision/team" v-if="hasRole(['admin'])">督导小组成员</el-menu-item>
            </el-sub-menu>
            
            <el-sub-menu index="/users" v-if="hasRole(['admin'])">
              <template #title>
                <el-icon><Setting /></el-icon>
                <span>用户管理</span>
              </template>
              <el-menu-item index="/users/list" v-if="hasRole(['admin'])">用户列表</el-menu-item>
            </el-sub-menu>
          </el-menu>
        </el-scrollbar>
      </el-aside>
      
      <!-- 右侧内容 -->
      <el-container class="main-container">
        <!-- 顶部导航 -->
        <el-header class="header">
          <div class="header-left">
            <el-icon class="fold-icon" @click="toggleSidebar">
              <component :is="isCollapse ? 'Expand' : 'Fold'"></component>
            </el-icon>
            <el-breadcrumb separator="/">
              <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
              <el-breadcrumb-item>{{ currentRoute }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
          <div class="header-right">
         
            <el-dropdown trigger="click">
              <div class="user-info">
                <el-avatar :size="30" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"></el-avatar>
                <span>{{ userName }}</span>
                <el-icon><CaretBottom /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
             
                  <el-dropdown-item divided @click="handleLogout">退出登录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>
        
        <!-- 内容区域 -->
        <el-main class="main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  UserFilled, 
  User,
  Reading,
  DocumentChecked,
  FullScreen, 
  CaretBottom, 
  Fold, 
  Expand,
  Comment,
  Medal,
  Setting
} from '@element-plus/icons-vue'

const router = useRouter()
const route = useRoute()
const isCollapse = ref(false)

const activeMenu = computed(() => {
  return route.path
})

const currentRoute = computed(() => {
  return route.meta.title || '教师列表'
})

const userRole = computed(() => {
  return localStorage.getItem('userRole') || 'admin'
})

const userName = computed(() => {
  return JSON.parse(localStorage.getItem('userInfo')).name || 'Admin'
})

const hasRole = (roles) => {
  return roles.includes(userRole.value)
}

const toggleSidebar = () => {
  isCollapse.value = !isCollapse.value
}

const handleLogout = () => {
  ElMessageBox.confirm('确定要退出登录吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 退出登录逻辑
    router.push('/login')
    ElMessage.success('已退出登录')
  }).catch(() => {})
}
</script>

<style scoped>
.app-container {
  height: 100vh;
  width: 100%;
}

.layout-container {
  height: 100%;
}

.aside {
  background-color: #304156;
  transition: width 0.3s;
  overflow: hidden;
}

.logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b3649;
  color: #fff;
}

.logo img {
  width: 30px;
  height: 30px;
  margin-right: 10px;
}

.logo h1 {
  display: inline-block;
  margin: 0;
  color: #fff;
  font-weight: 600;
  font-size: 16px;
  white-space: nowrap;
}

.el-menu-vertical {
  border-right: none;
}

.el-menu-vertical:not(.el-menu--collapse) {
  width: 220px;
}

.header {
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 10px;
  height: 60px;
}

.header-left {
  display: flex;
  align-items: center;
}

.fold-icon {
  font-size: 20px;
  cursor: pointer;
  margin-right: 10px;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 20px;
  padding: 0 10px;
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 10px;
}

.user-info span {
  margin: 0 5px;
}

.main {
  padding: 0 !important;
  background-color: #f0f2f5;
}
</style> 