const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');

// 用户注册
router.post('/register', authController.register);

// 用户登录
router.post('/login', authController.login);

// 忘记密码
router.post('/forgot-password', authController.forgotPassword);

// 验证身份（用于忘记密码流程）
router.post('/verify-identity', authController.verifyIdentity);

// 直接重置密码（通过用户名和邮箱验证）
router.post('/reset-password', authController.directResetPassword);

// 重置密码
router.post('/reset-password-with-token', authController.resetPassword);

// 获取当前用户信息 - 需要授权
router.get('/me', authController.protect, authController.getCurrentUser);

// 修改密码 - 需要授权
router.post('/change-password', authController.protect, authController.changePassword);

module.exports = router; 