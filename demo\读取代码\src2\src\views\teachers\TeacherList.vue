<template>
  <div class="teacher-list-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="title">教师管理</span>
          <el-button type="primary" @click="openDialog()">添加教师</el-button>
        </div>
      </template>

      <!-- 搜索区域 -->
      <el-form :inline="true" :model="searchForm" class="search-form">
        <el-form-item label="姓名">
          <el-input v-model="searchForm.name" placeholder="教师姓名" clearable />
        </el-form-item>
        <el-form-item label="科室">
          <el-input v-model="searchForm.department" placeholder="所属科室" clearable />
        </el-form-item>
        <el-form-item label="在聘状态">
          <el-select v-model="searchForm.is_employed" placeholder="是否在聘" clearable style="width: 120px">
            <el-option label="在聘" :value="1" />
            <el-option label="不在聘" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="teacherList"
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID"  />
        <el-table-column prop="name" label="姓名"  />
        <el-table-column prop="gender" label="性别" />
        <el-table-column prop="department" label="科室" />
        <el-table-column prop="school" label="学校" />
        <el-table-column prop="major" label="专业" />
        <el-table-column prop="education" label="学历" />
        <el-table-column label="在聘状态" >
          <template #default="scope">
            <el-tag :type="scope.row.is_employed ? 'success' : 'danger'">
              {{ scope.row.is_employed ? '在聘' : '不在聘' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="employment_period" label="聘期" min-width="120" />
        <el-table-column label="照片" width="80">
          <template #default="scope">
            <el-image
              v-if="scope.row.photo"
              :src="`${baseUrl}${scope.row.photo}`"
              :preview-src-list="[`${baseUrl}${scope.row.photo}`]"
              fit="cover"
              style="width: 50px; height: 50px"
            />
            <el-avatar v-else :size="50" icon="UserFilled" />
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="200">
          <template #default="scope">
            <el-button size="small" @click="viewDetails(scope.row.id)">详情</el-button>
            <el-button size="small" type="primary" @click="openDialog(scope.row)">编辑</el-button>
            <el-button size="small" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 教师表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="formData.id ? '编辑教师' : '添加教师'"
      width="50%"
      destroy-on-close
    >
      <el-form
        ref="teacherFormRef"
        :model="formData"
        :rules="formRules"
        label-width="80px"
        label-position="right"
      >
        <el-form-item label="姓名" prop="name">
          <el-input v-model="formData.name" placeholder="请输入姓名" />
        </el-form-item>
        
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="formData.gender">
            <el-radio label="男">男</el-radio>
            <el-radio label="女">女</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item label="科室" prop="department">
          <el-input v-model="formData.department" placeholder="请输入科室" />
        </el-form-item>
        
        <el-form-item label="学校" prop="school">
          <el-input v-model="formData.school" placeholder="请输入学校" />
        </el-form-item>
        
        <el-form-item label="专业" prop="major">
          <el-input v-model="formData.major" placeholder="请输入专业" />
        </el-form-item>
        
        <el-form-item label="学历" prop="education">
          <el-select v-model="formData.education" placeholder="请选择学历" style="width: 100%">
            <el-option label="博士" value="博士" />
            <el-option label="硕士" value="硕士" />
            <el-option label="本科" value="本科" />
            <el-option label="专科" value="专科" />
            <el-option label="其他" value="其他" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="在聘状态" prop="is_employed">
          <el-switch
            v-model="formData.is_employed"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        
        <el-form-item label="聘期" prop="employment_period" v-if="formData.is_employed === 1">
          <el-input v-model="formData.employment_period" placeholder="例如：2023年6月-2026年5月" />
        </el-form-item>
        
        <el-form-item label="联系方式" prop="phone">
          <el-input v-model="formData.phone" placeholder="请输入联系方式" />
        </el-form-item>
        
        <el-form-item label="照片" prop="photo">
          <el-upload
            class="avatar-uploader"
            :action="`${baseUrl}/api/teachers/upload/photo`"
            :headers="uploadHeaders"
            name="photo"
            :show-file-list="false"
            :before-upload="beforePhotoUpload"
            :on-success="handlePhotoSuccess"
            :on-error="handlePhotoError"
          >
            <img v-if="photoPreview" :src="photoPreview" class="avatar" />
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">点击上传照片，JPG/PNG格式，小于2MB</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'
import { Plus } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'

export default {
  name: 'TeacherList',
  components: { Plus },
  setup() {
    // 路由器
    const router = useRouter()
    
    // API基础URL
    const baseUrl = 'http://127.0.0.1:3000'
    
    // 上传头像的headers
    const uploadHeaders = {
      // 如果需要认证可以在这里添加
    }
    
    // 基础数据
    const loading = ref(false)
    const dialogVisible = ref(false)
    const teacherFormRef = ref(null)
    const teacherList = ref([])
    const total = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(10)
    const photoPreview = ref('')
    
    // 搜索表单
    const searchForm = reactive({
      name: '',
      department: '',
      is_employed: ''
    })
    
    // 表单数据
    const formData = reactive({
      id: '',
      name: '',
      gender: '男',
      department: '',
      school: '',
      major: '',
      education: '',
      is_employed: 1,
      employment_period: '',
      phone: '',
      photo: ''
    })
    
    // 表单校验规则
    const formRules = reactive({
      name: [
        { required: true, message: '请输入姓名', trigger: 'blur' },
        { min: 2, max: 10, message: '长度在 2 到 10 个字符', trigger: 'blur' }
      ],
      gender: [
        { required: true, message: '请选择性别', trigger: 'change' }
      ],
      department: [
        { required: true, message: '请输入科室', trigger: 'blur' }
      ],
      school: [
        { required: true, message: '请输入学校', trigger: 'blur' }
      ],
      major: [
        { required: true, message: '请输入专业', trigger: 'blur' }
      ],
      education: [
        { required: true, message: '请选择学历', trigger: 'change' }
      ]
    })
    
    // 监听表单数据变化，更新照片预览
    watch(() => formData.photo, (newVal) => {
      if (newVal) {
        if (newVal.startsWith('http')) {
          photoPreview.value = newVal
        } else {
          photoPreview.value = `${baseUrl}${newVal}`
        }
      } else {
        photoPreview.value = ''
      }
    }, { immediate: true })
    
    // 生命周期钩子
    onMounted(() => {
      fetchTeachers()
    })
    
    // 获取教师列表
    const fetchTeachers = async () => {
      loading.value = true
      try {
        // 构建查询参数
        const params = new URLSearchParams()
        if (searchForm.name) params.append('name', searchForm.name)
        if (searchForm.department) params.append('department', searchForm.department)
        if (searchForm.is_employed !== '') params.append('is_employed', searchForm.is_employed)
        params.append('page', currentPage.value)
        params.append('limit', pageSize.value)
        
        const response = await axios.get(`${baseUrl}/api/teachers`, { params })
        teacherList.value = response.data.data
        total.value = response.data.count
      } catch (error) {
        console.error('获取教师列表失败:', error)
        ElMessage.error('获取教师列表失败')
      } finally {
        loading.value = false
      }
    }
    
    // 搜索操作
    const handleSearch = () => {
      currentPage.value = 1
      fetchTeachers()
    }
    
    // 重置搜索
    const resetSearch = () => {
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
      })
      currentPage.value = 1
      fetchTeachers()
    }
    
    // 分页操作
    const handleSizeChange = (val) => {
      pageSize.value = val
      fetchTeachers()
    }
    
    const handleCurrentChange = (val) => {
      currentPage.value = val
      fetchTeachers()
    }
    
    // 打开对话框
    const openDialog = (row) => {
      if (row) {
        // 编辑模式
        Object.keys(formData).forEach(key => {
          formData[key] = row[key]
        })
        // 更新照片预览
        if (row.photo) {
          photoPreview.value = `${baseUrl}${row.photo}`
        }
      } else {
        // 新增模式
        Object.keys(formData).forEach(key => {
          formData[key] = key === 'gender' ? '男' : 
                          key === 'is_employed' ? 1 : ''
        })
        photoPreview.value = ''
      }
      dialogVisible.value = true
    }
    
    // 照片上传前验证
    const beforePhotoUpload = (file) => {
      const isJPGOrPNG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isJPGOrPNG) {
        ElMessage.error('上传头像图片只能是 JPG 或 PNG 格式!')
        return false
      }
      
      if (!isLt2M) {
        ElMessage.error('上传头像图片大小不能超过 2MB!')
        return false
      }
      
      // 创建临时预览
      photoPreview.value = URL.createObjectURL(file)
      return true
    }
    
    // 照片上传成功回调
    const handlePhotoSuccess = (response) => {
      console.log('照片上传成功响应:', response)
      if (response.success) {
        formData.photo = response.path
        console.log('设置照片路径:', formData.photo)
        ElMessage.success('照片上传成功')
      } else {
        ElMessage.error(response.message || '照片上传失败')
      }
    }
 
    // 照片上传失败回调
    const handlePhotoError = (err) => {
      console.error('照片上传失败:', err)
      ElMessage.error('照片上传失败，请检查网络连接')
    }
    
    // 提交表单
    const submitForm = async () => {
      if (!teacherFormRef.value) return
      
      await teacherFormRef.value.validate(async (valid) => {
        if (valid) {
          try {
            console.log('提交表单数据:', formData)
            
            // 创建表单数据对象
            const formDataToSubmit = new FormData()
            
            // 添加所有字段到FormData
            Object.keys(formData).forEach(key => {
              if (formData[key] !== null && formData[key] !== undefined && formData[key] !== '') {
                formDataToSubmit.append(key, formData[key])
              }
            })
            
            const config = {
              headers: {
                'Content-Type': 'multipart/form-data'
              }
            }
            
            if (formData.id) {
              // 编辑
              await axios.put(`${baseUrl}/api/teachers/${formData.id}`, formDataToSubmit, config)
              ElMessage.success('教师信息更新成功')
            } else {
              // 新增
              await axios.post(`${baseUrl}/api/teachers`, formDataToSubmit, config)
              ElMessage.success('教师添加成功')
            }
            
            dialogVisible.value = false
            fetchTeachers()
          } catch (error) {
            console.error('操作失败:', error)
            ElMessage.error(error.response?.data?.message || '操作失败')
          }
        } else {
          return false
        }
      })
    }
    
    // 删除教师
    const handleDelete = (row) => {
      ElMessageBox.confirm(
        `确定要删除教师 ${row.name} 吗?`,
        '警告',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(async () => {
          try {
            await axios.delete(`${baseUrl}/api/teachers/${row.id}`)
            ElMessage.success('删除成功')
            // 如果当前页只有一条数据且不是第一页，删除后跳转到上一页
            if (teacherList.value.length === 1 && currentPage.value > 1) {
              currentPage.value--
            }
            fetchTeachers()
          } catch (error) {
            console.error('删除失败:', error)
            ElMessage.error(error.response?.data?.message || '删除失败')
          }
        })
        .catch(() => {
          ElMessage.info('已取消删除')
        })
    }
    
    // 查看详情
    const viewDetails = (id) => {
      // 跳转到详情页面
      router.push(`/teachers/detail/${id}`)
    }
    
    return {
      baseUrl,
      uploadHeaders,
      loading,
      teacherList,
      searchForm,
      formData,
      formRules,
      dialogVisible,
      teacherFormRef,
      currentPage,
      pageSize,
      total,
      photoPreview,
      handleSearch,
      resetSearch,
      handleSizeChange,
      handleCurrentChange,
      openDialog,
      submitForm,
      beforePhotoUpload,
      handlePhotoSuccess,
      handlePhotoError,
      handleDelete,
      viewDetails
    }
  }
}
</script>

<style scoped>
.teacher-list-container {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.search-form {
  margin-bottom: 20px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.avatar-uploader {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.avatar-uploader:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar {
  width: 100%;
  height: 100%;
  display: block;
  object-fit: cover;
}

.upload-tip {
  font-size: 12px;
  color: #606266;
  margin-top: 5px;
}
</style> 