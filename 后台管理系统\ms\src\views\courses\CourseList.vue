<template>
  <div class="course-list-container">
    <el-card class="filter-card">
      <div class="filter-container">
        <el-form :model="filterForm" inline>
          <el-form-item label="课程标题">
            <el-input v-model="filterForm.title" placeholder="请输入课程标题" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
    
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>岗前培训课程</span>
          <div>
            <el-button type="primary" @click="handleAddCourse">新增课程</el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="courseList" stripe border style="width: 100%" v-loading="loading">
        <el-table-column type="index" width="50" />
        <el-table-column prop="title" label="课程标题" min-width="200" />
        <el-table-column prop="description" label="课程描述" min-width="300" show-overflow-tooltip />
        <el-table-column label="课件" width="120" align="center">
          <template #default="scope">
            <el-button v-if="scope.row.material_path" size="small" type="success" link @click="handleDownload(scope.row)">
              <el-icon><Download /></el-icon>
              下载课件
            </el-button>
            <span v-else class="no-material">无</span>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="160">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-container">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pageSize"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑课程对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      width="600px"
    >
      <el-form :model="courseForm" :rules="courseRules" ref="courseFormRef" label-width="80px">
        <el-form-item label="课程标题" prop="title">
          <el-input v-model="courseForm.title" placeholder="请输入课程标题"></el-input>
        </el-form-item>
        
        <el-form-item label="课程描述" prop="description">
          <el-input type="textarea" v-model="courseForm.description" placeholder="请输入课程描述" rows="4"></el-input>
        </el-form-item>
        
        <el-form-item label="课件文件">
          <el-upload
            class="material-upload"
            :action="uploadAction"
            :headers="uploadHeaders"
            :http-request="customUploadRequest"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :on-success="handleUploadSuccess"
            :before-upload="beforeUpload"
            :limit="1"
            :file-list="fileList"
          >
            <el-button type="primary">点击上传</el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持上传PDF、Word、PPT等格式文件，大小不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download } from '@element-plus/icons-vue'
import axios from 'axios'

// 直接定义API基础URL，而不是使用环境变量
const API_URL = 'http://localhost:3000'

// 创建axios实例，配置默认headers
const http = axios.create({
  baseURL: API_URL
})

// 添加请求拦截器，为每个请求添加token
http.interceptors.request.use(config => {
  // 从localStorage获取token
  const token = localStorage.getItem('token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
}, error => {
  return Promise.reject(error)
})

// 日期格式化函数
const formatDate = (dateString) => {
  if (!dateString) return '-'
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return dateString
  
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

const loading = ref(true)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const dialogVisible = ref(false)
const dialogTitle = ref('新增课程')
const courseFormRef = ref(null)
const fileList = ref([])
const uploadAction = ref(`${API_URL}/api/courses`) // 实际的上传API地址
const submitting = ref(false)
const tempFile = ref(null) // 存储临时文件

// 上传请求头，包含token
const uploadHeaders = computed(() => {
  return {
    Authorization: `Bearer ${localStorage.getItem('token') || ''}`
  }
})

// 课程列表
const courseList = ref([])

// 过滤条件
const filterForm = reactive({
  title: ''
})

// 课程表单
const courseForm = reactive({
  id: null,
  title: '',
  description: '',
  material_path: ''
})

// 验证规则
const courseRules = {
  title: [
    { required: true, message: '请输入课程标题', trigger: 'blur' }
  ]
}

// 获取课程列表数据
const fetchCourses = async () => {
  loading.value = true
  try {
    // 处理分页
    const response = await http.get('/api/courses')
    if (response.data.success) {
      courseList.value = response.data.data
      total.value = response.data.count
    } else {
      ElMessage.error(response.data.message || '获取课程列表失败')
    }
  } catch (error) {
    console.error('获取课程列表失败:', error)
    if (error.response && error.response.status === 401) {
      ElMessage.error('登录已过期，请重新登录')
      // 可以选择重定向到登录页面
      // router.push('/login')
    } else {
      ElMessage.error('获取课程列表失败，请检查网络连接')
    }
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  fetchCourses()
})

// 重置过滤条件
const resetFilter = () => {
  filterForm.title = ''
  fetchCourses()
}

// 搜索
const handleSearch = async () => {
  loading.value = true
  try {
    const response = await http.get('/api/courses/search', {
      params: { keyword: filterForm.title }
    })
    if (response.data.success) {
      courseList.value = response.data.data
      total.value = response.data.count
      ElMessage.success('搜索完成')
    } else {
      ElMessage.error(response.data.message || '搜索失败')
    }
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请检查网络连接')
  } finally {
    loading.value = false
  }
}

// 新增课程
const handleAddCourse = () => {
  dialogTitle.value = '新增课程'
  dialogVisible.value = true
  // 重置表单
  courseForm.id = null
  courseForm.title = ''
  courseForm.description = ''
  courseForm.material_path = ''
  tempFile.value = null
  fileList.value = []
}

// 编辑课程
const handleEdit = (row) => {
  dialogTitle.value = '编辑课程'
  dialogVisible.value = true
  
  // 填充表单数据
  courseForm.id = row.id
  courseForm.title = row.title
  courseForm.description = row.description
  courseForm.material_path = row.material_path
  tempFile.value = null
  
  // 设置文件列表
  fileList.value = []
  if (row.material_path) {
    fileList.value.push({
      name: row.material_path.split('/').pop(),
      url: `${API_URL}/${row.material_path}`
    })
  }
}

// 删除课程
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除课程 ${row.title} 吗?`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      const response = await http.delete(`/api/courses/${row.id}`)
      if (response.data.success) {
        ElMessage.success(`课程 ${row.title} 已删除`)
        fetchCourses()
      } else {
        ElMessage.error(response.data.message || '删除课程失败')
      }
    } catch (error) {
      console.error('删除课程失败:', error)
      ElMessage.error('删除课程失败，请检查网络连接')
    }
  }).catch(() => {})
}

// 下载课件
const handleDownload = async (row) => {
  if (!row.material_path) {
    ElMessage.warning('该课程没有上传课件')
    return
  }
  
  try {
    ElMessage.success(`正在下载 ${row.title} 的课件`)
    
    // 使用axios发送请求并获取blob数据
    const response = await http.get(`/api/courses/${row.id}/download`, {
      responseType: 'blob' // 指定响应类型为blob
    })
    
    // 获取文件名，从响应头获取
    let filename = ''
    const contentDisposition = response.headers['content-disposition']
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/)
      if (filenameMatch && filenameMatch[1]) {
        filename = filenameMatch[1].replace(/['"]/g, '')
        try {
          // 尝试解码文件名
          filename = decodeURIComponent(filename)
        } catch (e) {
          console.error('解码文件名失败', e)
          // 如果解码失败，使用原始文件名
        }
      }
    }
    
    // 如果没有从响应头获取到文件名，则使用路径中的文件名
    if (!filename) {
      filename = row.material_path.split('/').pop()
    }
    
    // 创建blob链接
    const blob = new Blob([response.data])
    const url = window.URL.createObjectURL(blob)
    
    // 创建临时链接并模拟点击下载
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', filename)
    document.body.appendChild(link)
    link.click()
    
    // 清理
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error('下载课件失败，请检查权限或网络连接')
  }
}

// 自定义上传请求处理
const customUploadRequest = (options) => {
  // 存储文件对象
  tempFile.value = options.file
  
  // 修改文件名以解决中文编码问题
  const originalFileName = options.file.name
  // 显示成功消息，包含原始文件名
  ElMessage.success(`文件 ${originalFileName} 已选择`)
  options.onSuccess()
}

// 提交表单
const submitForm = async () => {
  if (!courseFormRef.value) return
  
  await courseFormRef.value.validate(async (valid) => {
    if (valid) {
      submitting.value = true
      try {
        const formData = new FormData()
        formData.append('title', courseForm.title)
        formData.append('description', courseForm.description)
        
        if (tempFile.value) {
          // 添加原始文件名到formData以供后端使用
          formData.append('originalFileName', tempFile.value.name)
          formData.append('material', tempFile.value)
        }
        
        let response
        if (courseForm.id) {
          // 编辑模式
          response = await http.put(`/api/courses/${courseForm.id}`, formData)
          if (response.data.success) {
            ElMessage.success(`课程 ${courseForm.title} 信息已更新`)
          } else {
            ElMessage.error(response.data.message || '更新课程失败')
          }
        } else {
          // 新增模式
          response = await http.post('/api/courses', formData)
          if (response.data.success) {
            ElMessage.success(`课程 ${courseForm.title} 添加成功`)
          } else {
            ElMessage.error(response.data.message || '添加课程失败')
          }
        }
        
        dialogVisible.value = false
        fetchCourses()
      } catch (error) {
        console.error('提交表单失败:', error)
        ElMessage.error('提交失败，请检查网络连接')
      } finally {
        submitting.value = false
      }
    } else {
      return false
    }
  })
}

// 文件上传相关方法
const handlePreview = (file) => {
  if (file.url) {
    window.open(file.url, '_blank')
  }
}

const handleRemove = () => {
  fileList.value = []
  tempFile.value = null
}

const handleUploadSuccess = () => {
  // 这个方法不再需要显示成功消息，因为在customUploadRequest中已处理
}

const beforeUpload = (file) => {
  // 文件大小限制：10MB
  const maxSize = 10 * 1024 * 1024
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过10MB')
    return false
  }
  return true
}

// 分页处理
const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  fetchCourses()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  fetchCourses()
}
</script>

<style scoped>
.course-list-container {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.filter-container {
  padding: 10px 0;
}

.table-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header span {
  font-size: 16px;
  font-weight: 600;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.no-material {
  color: #909399;
}

.material-upload {
  width: 100%;
}

/* Style the Element Plus components to match LoginView style */
:deep(.el-button--primary) {
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
  border: none;
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #66b1ff 0%, #5098fa 100%);
  border: none;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #409EFF inset;
}

:deep(.el-pagination.is-background .el-pager li:not(.is-disabled).is-active) {
  background: linear-gradient(135deg, #409EFF 0%, #3a8ee6 100%);
}
</style> 