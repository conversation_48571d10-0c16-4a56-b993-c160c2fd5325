import { createStore } from 'vuex'
import axios from 'axios'

// 配置axios默认值
axios.defaults.baseURL = 'http://127.0.0.1:3000'

export default createStore({
  state: {
    user: localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')) : null,
    token: localStorage.getItem('token') || '',
    role: localStorage.getItem('userInfo') ? JSON.parse(localStorage.getItem('userInfo')).role : ''
  },
  getters: {
    isAuthenticated: state => !!state.token,
    isAdmin: state => state.role === 'admin',
    isTeacher: state => state.role === 'teacher',
    isStudent: state => state.role === 'student',
    userRole: state => state.role,
    currentUser: state => state.user
  },
  mutations: {
    SET_TOKEN(state, token) {
      state.token = token
    },
    SET_USER(state, user) {
      state.user = user
    },
    SET_ROLE(state, role) {
      state.role = role
    },
    LOGOUT(state) {
      state.token = ''
      state.user = null
      state.role = ''
    }
  },
  actions: {
    async login({ commit }, credentials) {
      try {
        const response = await axios.post('/api/auth/login', credentials)
        const { token, user } = response.data
        
        localStorage.setItem('token', token)
        localStorage.setItem('userId', user.id)
        localStorage.setItem('userRole', user.role)
        
        commit('SET_TOKEN', token)
        commit('SET_USER', user)
        commit('SET_ROLE', user.role)
        
        // 设置axios请求头
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
        
        return response
      } catch (error) {
        throw error
      }
    },
    
    logout({ commit }) {
      localStorage.removeItem('token')
      localStorage.removeItem('userId')
      localStorage.removeItem('userRole')
      
      commit('LOGOUT')
      
      // 清除axios请求头
      delete axios.defaults.headers.common['Authorization']
    },
    
    async fetchUserProfile({ commit }) {
      try {
        const token = localStorage.getItem('token')
        if (!token) return
        
        // 设置请求头
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
        
        const response = await axios.get('/api/auth/me')
        const { user } = response.data
        
        commit('SET_USER', user)
        commit('SET_ROLE', user.role)
        localStorage.setItem('userRole', user.role)
        
        return response
      } catch (error) {
        commit('LOGOUT')
        throw error
      }
    }
  },
  modules: {
  }
})
