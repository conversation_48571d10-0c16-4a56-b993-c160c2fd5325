{"ast": null, "code": "import api from '@/utils/api';\nconst userService = {\n  /**\r\n   * 获取用户列表\r\n   * @param {Object} params - 查询参数\r\n   * @returns {Promise}\r\n   */\n  getUsers(params) {\n    return api.get('/api/users', {\n      params\n    });\n  },\n  /**\r\n   * 获取单个用户\r\n   * @param {number} id - 用户ID\r\n   * @returns {Promise}\r\n   */\n  getUser(id) {\n    return api.get(`/api/users/${id}`);\n  },\n  /**\r\n   * 创建用户\r\n   * @param {Object} userData - 用户数据\r\n   * @returns {Promise}\r\n   */\n  createUser(userData) {\n    return api.post('/api/users', userData);\n  },\n  /**\r\n   * 更新用户\r\n   * @param {number} id - 用户ID\r\n   * @param {Object} userData - 用户数据\r\n   * @returns {Promise}\r\n   */\n  updateUser(id, userData) {\n    return api.put(`/api/users/${id}`, userData);\n  },\n  /**\r\n   * 删除用户\r\n   * @param {number} id - 用户ID\r\n   * @returns {Promise}\r\n   */\n  deleteUser(id) {\n    return api.delete(`/api/users/${id}`);\n  },\n  /**\r\n   * 批量删除用户\r\n   * @param {Array} ids - 用户ID数组\r\n   * @returns {Promise}\r\n   */\n  batchDeleteUsers(ids) {\n    return api.delete('/api/users/batch', {\n      data: {\n        ids\n      }\n    });\n  },\n  /**\r\n   * 修改用户状态\r\n   * @param {number} id - 用户ID\r\n   * @param {number} status - 状态值（0禁用，1启用）\r\n   * @returns {Promise}\r\n   */\n  updateUserStatus(id, status) {\n    return api.put(`/api/users/${id}/status`, {\n      status\n    });\n  },\n  /**\r\n   * 下载用户导入模板\r\n   * @returns {Promise}\r\n   */\n  downloadImportTemplate() {\n    return api.get('/api/users/import/template', {\n      responseType: 'blob'\n    });\n  },\n  /**\r\n   * 批量导入用户\r\n   * @param {File} file - Excel文件\r\n   * @returns {Promise}\r\n   */\n  importUsers(file) {\n    const formData = new FormData();\n    formData.append('excel', file);\n    return api.post('/api/users/import/excel', formData, {\n      headers: {\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n  }\n};\nexport default userService;", "map": {"version": 3, "names": ["api", "userService", "getUsers", "params", "get", "getUser", "id", "createUser", "userData", "post", "updateUser", "put", "deleteUser", "delete", "batchDeleteUsers", "ids", "data", "updateUserStatus", "status", "downloadImportTemplate", "responseType", "importUsers", "file", "formData", "FormData", "append", "headers"], "sources": ["D:/admin/202506/督导系统/后台管理系统/ms/src/services/userService.js"], "sourcesContent": ["import api from '@/utils/api';\r\n\r\nconst userService = {\r\n  /**\r\n   * 获取用户列表\r\n   * @param {Object} params - 查询参数\r\n   * @returns {Promise}\r\n   */\r\n  getUsers(params) {\r\n    return api.get('/api/users', { params });\r\n  },\r\n\r\n  /**\r\n   * 获取单个用户\r\n   * @param {number} id - 用户ID\r\n   * @returns {Promise}\r\n   */\r\n  getUser(id) {\r\n    return api.get(`/api/users/${id}`);\r\n  },\r\n\r\n  /**\r\n   * 创建用户\r\n   * @param {Object} userData - 用户数据\r\n   * @returns {Promise}\r\n   */\r\n  createUser(userData) {\r\n    return api.post('/api/users', userData);\r\n  },\r\n\r\n  /**\r\n   * 更新用户\r\n   * @param {number} id - 用户ID\r\n   * @param {Object} userData - 用户数据\r\n   * @returns {Promise}\r\n   */\r\n  updateUser(id, userData) {\r\n    return api.put(`/api/users/${id}`, userData); \r\n  },\r\n\r\n  /**\r\n   * 删除用户\r\n   * @param {number} id - 用户ID\r\n   * @returns {Promise}\r\n   */\r\n  deleteUser(id) {\r\n    return api.delete(`/api/users/${id}`);\r\n  },\r\n\r\n  /**\r\n   * 批量删除用户\r\n   * @param {Array} ids - 用户ID数组\r\n   * @returns {Promise}\r\n   */\r\n  batchDeleteUsers(ids) {\r\n    return api.delete('/api/users/batch', { data: { ids } });\r\n  },\r\n\r\n  /**\r\n   * 修改用户状态\r\n   * @param {number} id - 用户ID\r\n   * @param {number} status - 状态值（0禁用，1启用）\r\n   * @returns {Promise}\r\n   */\r\n  updateUserStatus(id, status) {\r\n    return api.put(`/api/users/${id}/status`, { status });\r\n  },\r\n\r\n  /**\r\n   * 下载用户导入模板\r\n   * @returns {Promise}\r\n   */\r\n  downloadImportTemplate() {\r\n    return api.get('/api/users/import/template', {\r\n      responseType: 'blob'\r\n    });\r\n  },\r\n\r\n  /**\r\n   * 批量导入用户\r\n   * @param {File} file - Excel文件\r\n   * @returns {Promise}\r\n   */\r\n  importUsers(file) {\r\n    const formData = new FormData();\r\n    formData.append('excel', file);\r\n\r\n    return api.post('/api/users/import/excel', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data'\r\n      }\r\n    });\r\n  }\r\n};\r\n\r\nexport default userService; "], "mappings": "AAAA,OAAOA,GAAG,MAAM,aAAa;AAE7B,MAAMC,WAAW,GAAG;EAClB;AACF;AACA;AACA;AACA;EACEC,QAAQA,CAACC,MAAM,EAAE;IACf,OAAOH,GAAG,CAACI,GAAG,CAAC,YAAY,EAAE;MAAED;IAAO,CAAC,CAAC;EAC1C,CAAC;EAED;AACF;AACA;AACA;AACA;EACEE,OAAOA,CAACC,EAAE,EAAE;IACV,OAAON,GAAG,CAACI,GAAG,CAAC,cAAcE,EAAE,EAAE,CAAC;EACpC,CAAC;EAED;AACF;AACA;AACA;AACA;EACEC,UAAUA,CAACC,QAAQ,EAAE;IACnB,OAAOR,GAAG,CAACS,IAAI,CAAC,YAAY,EAAED,QAAQ,CAAC;EACzC,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEE,UAAUA,CAACJ,EAAE,EAAEE,QAAQ,EAAE;IACvB,OAAOR,GAAG,CAACW,GAAG,CAAC,cAAcL,EAAE,EAAE,EAAEE,QAAQ,CAAC;EAC9C,CAAC;EAED;AACF;AACA;AACA;AACA;EACEI,UAAUA,CAACN,EAAE,EAAE;IACb,OAAON,GAAG,CAACa,MAAM,CAAC,cAAcP,EAAE,EAAE,CAAC;EACvC,CAAC;EAED;AACF;AACA;AACA;AACA;EACEQ,gBAAgBA,CAACC,GAAG,EAAE;IACpB,OAAOf,GAAG,CAACa,MAAM,CAAC,kBAAkB,EAAE;MAAEG,IAAI,EAAE;QAAED;MAAI;IAAE,CAAC,CAAC;EAC1D,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEE,gBAAgBA,CAACX,EAAE,EAAEY,MAAM,EAAE;IAC3B,OAAOlB,GAAG,CAACW,GAAG,CAAC,cAAcL,EAAE,SAAS,EAAE;MAAEY;IAAO,CAAC,CAAC;EACvD,CAAC;EAED;AACF;AACA;AACA;EACEC,sBAAsBA,CAAA,EAAG;IACvB,OAAOnB,GAAG,CAACI,GAAG,CAAC,4BAA4B,EAAE;MAC3CgB,YAAY,EAAE;IAChB,CAAC,CAAC;EACJ,CAAC;EAED;AACF;AACA;AACA;AACA;EACEC,WAAWA,CAACC,IAAI,EAAE;IAChB,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,IAAI,CAAC;IAE9B,OAAOtB,GAAG,CAACS,IAAI,CAAC,yBAAyB,EAAEc,QAAQ,EAAE;MACnDG,OAAO,EAAE;QACP,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;EACJ;AACF,CAAC;AAED,eAAezB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}