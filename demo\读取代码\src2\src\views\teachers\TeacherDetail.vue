<template>
  <div class="teacher-detail-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="title">教师详情</span>
          <div>
            <el-button @click="goBack">返回列表</el-button>
       
          </div>
        </div>
      </template>

      <!-- Use a single loading state for the whole component -->
      <el-row :gutter="20">
        <!-- 左侧个人信息 -->
        <el-col :span="8" v-loading="loading">
          <div class="teacher-avatar">
            <el-image
              v-if="teacherData.photo"
              :src="`${baseUrl}${teacherData.photo}`"
              fit="cover"
              class="avatar-image"
              :preview-src-list="[`${baseUrl}${teacherData.photo}`]"
              :preview-teleported="true"
            />
            <el-avatar v-else :size="150" icon="UserFilled" />
          </div>
          
          <el-descriptions title="基本信息" direction="vertical" :column="1" border>
            <el-descriptions-item label="姓名">{{ teacherData.name || '--' }}</el-descriptions-item>
            <el-descriptions-item label="性别">{{ teacherData.gender || '--' }}</el-descriptions-item>
            <el-descriptions-item label="科室">{{ teacherData.department || '--' }}</el-descriptions-item>
            <el-descriptions-item label="学校">{{ teacherData.school || '--' }}</el-descriptions-item>
            <el-descriptions-item label="专业">{{ teacherData.major || '--' }}</el-descriptions-item>
            <el-descriptions-item label="学历">{{ teacherData.education || '--' }}</el-descriptions-item>
            <el-descriptions-item label="在聘状态">
              <el-tag :type="teacherData.is_employed ? 'success' : 'danger'" v-if="teacherData.is_employed !== undefined">
                {{ teacherData.is_employed ? '在聘' : '不在聘' }}
              </el-tag>
              <span v-else>--</span>
            </el-descriptions-item>
            <el-descriptions-item label="聘期" v-if="teacherData.is_employed && teacherData.employment_period">
              {{ teacherData.employment_period }}
            </el-descriptions-item>
            <el-descriptions-item label="联系方式" v-if="teacherData.phone">
              {{ teacherData.phone }}
            </el-descriptions-item>
          </el-descriptions>

          <!-- 能力认证状态 -->
          <el-card class="certification-card" v-if="competencyData">
            <template #header>
              <div class="card-header">
                <span>能力认证状态</span>
              </div>
            </template>
            <div class="certification-status">
              <div class="certification-tag">
                <el-tag :type="competencyData.is_certified ? 'success' : 'info'" size="large">
                  {{ competencyData.is_certified ? '已认证' : '未认证' }}
                </el-tag>
              </div>
              <el-progress 
                :percentage="competencyData.approval_rate || 0" 
                :status="competencyData.is_certified ? 'success' : ''" 
                :stroke-width="18"
                :format="percentFormat"
              />
              <div class="certification-stats">
                <div>总评价数: {{ competencyData.total_evaluations || 0 }}</div>
                <div>认可数: {{ competencyData.approved_count || 0 }}</div>
              </div>
              <div class="certification-note">
                <i class="el-icon-info"></i>
                注：需要80%以上的督导评价认可才能获得认证
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧选项卡 -->
        <el-col :span="16">
          <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <!-- 考试成绩 -->
            <el-tab-pane label="考试成绩" name="exams">
              <div class="tab-content">
                <div v-loading="examLoading">
                  <el-table
                    v-if="examResults.length > 0"
                    :data="examResults"
                    border
                    style="width: 100%"
                    :key="'exam-table'"
                  >
                    <el-table-column prop="exam_title" label="考试名称" />
                    <el-table-column prop="exam_type" label="考试类型" width="100">
                      <template #default="scope">
                        <el-tag :type="scope.row.exam_type === '资格认定考试' ? 'danger' : 'primary'">
                          {{ scope.row.exam_type }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="score" label="分数" width="80" />
                    <el-table-column label="是否及格" width="100">
                      <template #default="scope">
                        <el-tag :type="scope.row.score >= scope.row.pass_score ? 'success' : 'danger'">
                          {{ scope.row.score >= scope.row.pass_score ? '及格' : '不及格' }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="exam_date" label="考试时间" width="180">
                      <template #default="scope">
                        {{ formatDate(scope.row.exam_date) }}
                      </template>
                    </el-table-column>
                  </el-table>

                  <div v-else-if="!examLoading" class="empty-data">
                    暂无考试记录
                  </div>
                </div>
              </div>
            </el-tab-pane>

            <!-- 教学评价 -->
            <el-tab-pane label="教学评价" name="evaluations">
              <div class="tab-content">
                <div v-loading="evaluationLoading">
                  <el-table
                    v-if="evaluations.length > 0"
                    :data="evaluations"
                    border
                    style="width: 100%"
                    :key="'evaluation-table'"
                  >
                    <el-table-column prop="supervising_department" label="督导教研室" width="120" />
                    <el-table-column prop="case_topic" label="病例/主题" />
                    <el-table-column prop="teaching_form" label="教学活动形式" width="120" />
                    <el-table-column prop="student_name" label="学员姓名" width="100" />
                    <el-table-column prop="student_type" label="学员类别" width="120" />
                    <el-table-column prop="average_score" label="平均分" width="80" />
                    <el-table-column label="能力认定" width="100">
                      <template #default="scope">
                        <el-tag :type="scope.row.competency_approved ? 'success' : 'danger'">
                          {{ scope.row.competency_approved ? '同意' : '不同意' }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="evaluation_date" label="评价时间" width="180">
                      <template #default="scope">
                        {{ formatDate(scope.row.evaluation_date) }}
                      </template>
                    </el-table-column>
                    <el-table-column label="操作" width="80" fixed="right">
                      <template #default="scope">
                        <el-button size="small" @click="viewEvaluation(scope.row)">详情</el-button>
                      </template>
                    </el-table-column>
                  </el-table>

                  <div v-else-if="!evaluationLoading" class="empty-data">
                    暂无教学评价记录
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>
    </el-card>

    <!-- 评价详情对话框 -->
    <el-dialog 
      v-model="evaluationDialogVisible" 
      title="评价详情" 
      width="60%" 
      :append-to-body="true"
      :destroy-on-close="true"
    >
      <el-descriptions title="基本信息" :column="2" border>
        <el-descriptions-item label="督导教研室">{{ currentEvaluation.supervising_department }}</el-descriptions-item>
        <el-descriptions-item label="病例/主题">{{ currentEvaluation.case_topic }}</el-descriptions-item>
        <el-descriptions-item label="教学活动形式">{{ currentEvaluation.teaching_form }}</el-descriptions-item>
        <el-descriptions-item label="带教老师职称">{{ currentEvaluation.teacher_title }}</el-descriptions-item>
        <el-descriptions-item label="学员姓名">{{ currentEvaluation.student_name }}</el-descriptions-item>
        <el-descriptions-item label="学员类别">{{ currentEvaluation.student_type }}</el-descriptions-item>
        <el-descriptions-item label="评估人">{{ currentEvaluation.evaluator_name }}</el-descriptions-item>
        <el-descriptions-item label="评价时间">{{ formatDate(currentEvaluation.evaluation_date) }}</el-descriptions-item>
      </el-descriptions>

      <el-divider />

      <el-descriptions title="评价内容" :column="1" border>
        <el-descriptions-item label="平均分">{{ currentEvaluation.average_score }}</el-descriptions-item>
        <el-descriptions-item label="亮点">{{ currentEvaluation.highlights || '无' }}</el-descriptions-item>
        <el-descriptions-item label="不足">{{ currentEvaluation.shortcomings || '无' }}</el-descriptions-item>
        <el-descriptions-item label="改进建议">{{ currentEvaluation.improvement_suggestions || '无' }}</el-descriptions-item>
        <el-descriptions-item label="能力认定">
          <el-tag :type="currentEvaluation.competency_approved ? 'success' : 'danger'">
            {{ currentEvaluation.competency_approved ? '同意' : '不同意' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import axios from 'axios'

export default {
  name: 'TeacherDetail',
  setup() {
    const route = useRoute()
    const router = useRouter()
    const teacherId = route.params.id
    
    // API基础URL
    const baseUrl = 'http://127.0.0.1:3000'
    
    // 基础数据
    const loading = ref(false)
    const examLoading = ref(false)
    const evaluationLoading = ref(false)
    const teacherData = ref({})
    const examResults = ref([])
    const evaluations = ref([])
    const competencyData = ref(null)
    const activeTab = ref('exams')
    const dataLoaded = ref({
      teacher: false,
      exams: false,
      evaluations: false,
      competency: false
    })

    // 评价详情相关
    const evaluationDialogVisible = ref(false)
    const currentEvaluation = ref({})
    
    // 生命周期钩子 - 使用分阶段加载来减少布局循环
    onMounted(() => {
      // 首先加载基本教师信息
      fetchTeacherData().then(() => {
        // 等教师数据加载完成后，再加载其他数据
        setTimeout(() => {
          // 优先加载当前选中的标签页数据
          if (activeTab.value === 'exams') {
            fetchExamResults()
            setTimeout(() => {
              fetchCompetencyStatus()
            }, 100)
            setTimeout(() => {
              fetchEvaluations()
            }, 200)
          } else {
            fetchEvaluations()
            setTimeout(() => {
              fetchCompetencyStatus()
            }, 100)
            setTimeout(() => {
              fetchExamResults()
            }, 200)
          }
        }, 100)
      })
    })
    
    // 获取教师信息
    const fetchTeacherData = async () => {
      loading.value = true
      try {
        const response = await axios.get(`${baseUrl}/api/teachers/${teacherId}`)
        teacherData.value = response.data.data || {}
        dataLoaded.value.teacher = true
      } catch (error) {
        console.error('获取教师信息失败:', error)
        ElMessage.error('获取教师信息失败')
      } finally {
        loading.value = false
      }
    }
    
    // 获取考试成绩
    const fetchExamResults = async () => {
      if (activeTab.value !== 'exams' && dataLoaded.value.exams) return
      
      examLoading.value = true
      try {
        const response = await axios.get(`${baseUrl}/api/exams/teacher/${teacherId}/results`)
        await nextTick()
        examResults.value = Array.isArray(response.data.data) ? response.data.data : []
        dataLoaded.value.exams = true
      } catch (error) {
        console.error('获取考试成绩失败:', error)
        ElMessage.error('获取考试成绩失败')
        examResults.value = []
      } finally {
        examLoading.value = false
      }
    }
    
    // 获取教学评价
    const fetchEvaluations = async () => {
      if (activeTab.value !== 'evaluations' && dataLoaded.value.evaluations) return
      
      evaluationLoading.value = true
      try {
        const response = await axios.get(`${baseUrl}/api/evaluations/teacher/${teacherId}`)
        await nextTick()
        evaluations.value = Array.isArray(response.data.data) ? response.data.data : []
        dataLoaded.value.evaluations = true
      } catch (error) {
        console.error('获取教学评价失败:', error)
        ElMessage.error('获取教学评价失败')
        evaluations.value = []
      } finally {
        evaluationLoading.value = false
      }
    }
    
    // 获取能力认证状态
    const fetchCompetencyStatus = async () => {
      if (dataLoaded.value.competency) return
      
      try {
        const response = await axios.get(`${baseUrl}/api/competency/teacher/${teacherId}`)
        await nextTick()
        competencyData.value = response.data.data || null
        dataLoaded.value.competency = true
      } catch (error) {
        console.error('获取能力认证状态失败:', error)
        // 这里不显示错误信息，因为有些教师可能没有能力认证记录
      }
    }
    
    // 标签页切换处理
    const handleTabClick = (tab) => {
      if (tab.props.name === 'exams' && !dataLoaded.value.exams) {
        fetchExamResults()
      } else if (tab.props.name === 'evaluations' && !dataLoaded.value.evaluations) {
        fetchEvaluations()
      }
    }
    
    // 返回列表页
    const goBack = () => {
      router.push('/teachers/list')
    }
    
    // 编辑教师
    const editTeacher = () => {
      router.push(`/teachers/edit/${teacherId}`)
    }
    
    // 查看评价详情
    const viewEvaluation = (evaluation) => {
      currentEvaluation.value = evaluation
      // 使用nextTick确保DOM更新后再显示对话框
      nextTick(() => {
        evaluationDialogVisible.value = true
      })
    }
    
    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '--'
      const date = new Date(dateString)
      return date.toLocaleString()
    }
    
    // 格式化百分比
    const percentFormat = (percentage) => {
      return `${percentage}%`
    }
    
    return {
      baseUrl,
      teacherData,
      examResults,
      evaluations,
      competencyData,
      loading,
      examLoading,
      evaluationLoading,
      activeTab,
      evaluationDialogVisible,
      currentEvaluation,
      goBack,
      editTeacher,
      viewEvaluation,
      formatDate,
      percentFormat,
      handleTabClick
    }
  }
}
</script>

<style scoped>
.teacher-detail-container {
  padding: 20px;
  /* 防止容器尺寸变化导致的重绘问题 */
  min-height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
}

.teacher-avatar {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  /* 固定尺寸以减少重布局 */
  height: 170px;
}

.avatar-image {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #eee;
}

.tab-content {
  margin-top: 10px;
  /* 设置最小高度避免内容变化时的跳动 */
  min-height: 200px;
}

.empty-data {
  padding: 30px;
  text-align: center;
  color: #909399;
  font-size: 14px;
}

.certification-card {
  margin-top: 20px;
}

.certification-status {
  text-align: center;
  padding: 10px;
}

.certification-tag {
  margin-bottom: 15px;
}

.certification-stats {
  margin-top: 10px;
  display: flex;
  justify-content: space-around;
  color: #606266;
  font-size: 14px;
}

.certification-note {
  margin-top: 15px;
  color: #909399;
  font-size: 12px;
}

.el-descriptions {
  margin-bottom: 20px;
}

/* 解决表格重绘问题 */
.el-table {
  width: 100% !important;
}
</style> 