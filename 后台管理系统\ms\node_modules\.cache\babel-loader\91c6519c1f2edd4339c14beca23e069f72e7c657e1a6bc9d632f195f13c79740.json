{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport { useRoute, useRouter } from 'vue-router';\nimport { ElMessage } from 'element-plus';\nimport { Check, Close } from '@element-plus/icons-vue';\nimport axios from 'axios';\nexport default {\n  name: 'ExamResults',\n  components: {\n    Check,\n    Close\n  },\n  setup() {\n    let token = localStorage.getItem('token');\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    }\n    const route = useRoute();\n    const router = useRouter();\n    const examId = route.params.id;\n\n    // 基础数据\n    const loading = ref(false);\n    const examData = ref(null);\n    const results = ref([]);\n    const total = ref(0);\n    const currentPage = ref(1);\n    const pageSize = ref(10);\n\n    // 详情对话框\n    const detailDialogVisible = ref(false);\n    const currentResult = ref(null);\n\n    // 搜索表单\n    const searchForm = reactive({\n      name: '',\n      department: '',\n      status: ''\n    });\n\n    // 计算属性\n    const totalParticipants = computed(() => {\n      return results.value.length;\n    });\n    const passCount = computed(() => {\n      return results.value.filter(result => isPass(result)).length;\n    });\n    const passRate = computed(() => {\n      if (totalParticipants.value === 0) return 0;\n      return Math.round(passCount.value / totalParticipants.value * 100);\n    });\n    const averageScore = computed(() => {\n      if (totalParticipants.value === 0) return 0;\n      const totalScore = results.value.reduce((sum, result) => sum + result.score, 0);\n      return (totalScore / totalParticipants.value).toFixed(1);\n    });\n\n    // 生命周期钩子\n    onMounted(() => {\n      fetchExamData();\n      fetchResults();\n    });\n\n    // 获取考试信息\n    const fetchExamData = async () => {\n      try {\n        const response = await axios.get(`http://localhost:3000/api/exams/${examId}`);\n        examData.value = response.data.data;\n      } catch (error) {\n        console.error('获取考试信息失败:', error);\n        ElMessage.error('获取考试信息失败');\n      }\n    };\n\n    // 获取考试成绩\n    const fetchResults = async () => {\n      loading.value = true;\n      try {\n        const response = await axios.get(`http://localhost:3000/api/exams/${examId}/results`);\n        results.value = response.data.data;\n        total.value = response.data.count;\n      } catch (error) {\n        console.error('获取考试成绩失败:', error);\n        ElMessage.error('获取考试成绩失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 获取考试状态文本\n    const getExamStatusText = status => {\n      const statusMap = {\n        'draft': '草稿',\n        'published': '已发布',\n        'in_progress': '进行中',\n        'completed': '已结束'\n      };\n      return statusMap[status] || '未知状态';\n    };\n\n    // 获取考试状态类型\n    const getExamStatusType = status => {\n      const typeMap = {\n        'draft': 'info',\n        'published': 'success',\n        'in_progress': 'warning',\n        'completed': 'danger'\n      };\n      return typeMap[status] || 'info';\n    };\n\n    // 判断是否通过\n    const isPass = result => {\n      if (!examData.value || !result) return false;\n      return result.score >= examData.value.pass_score;\n    };\n\n    // 格式化日期\n    const formatDate = dateString => {\n      if (!dateString) return '-';\n      const date = new Date(dateString);\n      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;\n    };\n\n    // 格式化日期时间\n    const formatDateTime = dateString => {\n      if (!dateString) return '-';\n      const date = new Date(dateString);\n      return `${formatDate(dateString)} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;\n    };\n\n    // 格式化时长\n    const formatDuration = (startTime, endTime) => {\n      if (!startTime || !endTime) return '-';\n      const start = new Date(startTime);\n      const end = new Date(endTime);\n      const diffMs = end - start;\n      const minutes = Math.floor(diffMs / 60000);\n      const seconds = Math.floor(diffMs % 60000 / 1000);\n      return `${minutes}分${seconds}秒`;\n    };\n\n    // 返回考试列表\n    const goBack = () => {\n      router.push('/exams/list');\n    };\n\n    // 搜索操作\n    const handleSearch = () => {\n      currentPage.value = 1;\n      fetchResults();\n    };\n\n    // 重置搜索\n    const resetSearch = () => {\n      Object.keys(searchForm).forEach(key => {\n        searchForm[key] = '';\n      });\n      currentPage.value = 1;\n      fetchResults();\n    };\n\n    // 分页操作\n    const handleSizeChange = val => {\n      pageSize.value = val;\n      fetchResults();\n    };\n    const handleCurrentChange = val => {\n      currentPage.value = val;\n      fetchResults();\n    };\n\n    // 查看详情\n    const viewDetail = async row => {\n      try {\n        const response = await axios.get(`http://localhost:3000/api/exams/results/${row.id}`);\n        currentResult.value = response.data.data;\n        detailDialogVisible.value = true;\n      } catch (error) {\n        console.error('获取成绩详情失败:', error);\n        ElMessage.error('获取成绩详情失败');\n      }\n    };\n\n    // 导出成绩\n    const exportResults = () => {\n      window.open(`http://localhost:3000/api/exams/${examId}/results/export`, '_blank');\n    };\n\n    // 判断选项是否被选中\n    const isOptionSelected = (answer, option) => {\n      if (!answer.user_answers) return false;\n      return answer.user_answers.includes(option.id);\n    };\n\n    // 判断是否为错误选择\n    const isWrongOption = (answer, option) => {\n      return isOptionSelected(answer, option) && !option.is_correct;\n    };\n    return {\n      loading,\n      examData,\n      results,\n      total,\n      currentPage,\n      pageSize,\n      searchForm,\n      detailDialogVisible,\n      currentResult,\n      totalParticipants,\n      passCount,\n      passRate,\n      averageScore,\n      getExamStatusText,\n      getExamStatusType,\n      isPass,\n      formatDate,\n      formatDateTime,\n      formatDuration,\n      goBack,\n      handleSearch,\n      resetSearch,\n      handleSizeChange,\n      handleCurrentChange,\n      viewDetail,\n      exportResults,\n      isOptionSelected,\n      isWrongOption\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "useRoute", "useRouter", "ElMessage", "Check", "Close", "axios", "name", "components", "setup", "token", "localStorage", "getItem", "defaults", "headers", "common", "route", "router", "examId", "params", "id", "loading", "examData", "results", "total", "currentPage", "pageSize", "detailDialogVisible", "currentResult", "searchForm", "department", "status", "totalParticipants", "value", "length", "passCount", "filter", "result", "isPass", "passRate", "Math", "round", "averageScore", "totalScore", "reduce", "sum", "score", "toFixed", "fetchExamData", "fetchResults", "response", "get", "data", "error", "console", "count", "getExamStatusText", "statusMap", "getExamStatusType", "typeMap", "pass_score", "formatDate", "dateString", "date", "Date", "getFullYear", "getMonth", "toString", "padStart", "getDate", "formatDateTime", "getHours", "getMinutes", "formatDuration", "startTime", "endTime", "start", "end", "diffMs", "minutes", "floor", "seconds", "goBack", "push", "handleSearch", "resetSearch", "Object", "keys", "for<PERSON>ach", "key", "handleSizeChange", "val", "handleCurrentChange", "viewDetail", "row", "exportResults", "window", "open", "isOptionSelected", "answer", "option", "user_answers", "includes", "isWrongOption", "is_correct"], "sources": ["D:\\admin\\202506\\督导系统\\后台管理系统\\ms\\src\\views\\exams\\ExamResults.vue"], "sourcesContent": ["<template>\r\n  <div class=\"exam-results-container\">\r\n    <el-card class=\"box-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span class=\"title\">考试成绩</span>\r\n          <div>\r\n            <el-button @click=\"goBack\">返回考试列表</el-button>\r\n            <el-button type=\"primary\" @click=\"exportResults\">导出成绩</el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <div class=\"exam-info\" v-if=\"examData\">\r\n        <el-descriptions title=\"考试信息\" :column=\"3\" border>\r\n          <el-descriptions-item label=\"考试名称\">{{ examData.title }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"考试时长\">{{ examData.duration }}分钟</el-descriptions-item>\r\n          <el-descriptions-item label=\"总分\">{{ examData.total_score }}分</el-descriptions-item>\r\n          <el-descriptions-item label=\"及格分数\">{{ examData.pass_score }}分</el-descriptions-item>\r\n          <el-descriptions-item label=\"考试状态\">\r\n            <el-tag :type=\"getExamStatusType(examData.status)\">\r\n              {{ getExamStatusText(examData.status) }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"创建时间\">{{ formatDate(examData.created_at) }}</el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n\r\n      <!-- 成绩统计 -->\r\n      <div class=\"results-stats\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"6\">\r\n            <div class=\"stat-card\">\r\n              <div class=\"stat-value\">{{ totalParticipants }}</div>\r\n              <div class=\"stat-label\">参考人数</div>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <div class=\"stat-card\">\r\n              <div class=\"stat-value\">{{ passCount }}</div>\r\n              <div class=\"stat-label\">通过人数</div>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <div class=\"stat-card\">\r\n              <div class=\"stat-value\">{{ passRate }}%</div>\r\n              <div class=\"stat-label\">通过率</div>\r\n            </div>\r\n          </el-col>\r\n          <el-col :span=\"6\">\r\n            <div class=\"stat-card\">\r\n              <div class=\"stat-value\">{{ averageScore }}</div>\r\n              <div class=\"stat-label\">平均分</div>\r\n            </div>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 搜索区域 -->\r\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\r\n        <el-form-item label=\"姓名\">\r\n          <el-input v-model=\"searchForm.name\" placeholder=\"教师姓名\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"科室\">\r\n          <el-input v-model=\"searchForm.department\" placeholder=\"所属科室\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"成绩状态\">\r\n          <el-select v-model=\"searchForm.status\" placeholder=\"成绩状态\" clearable>\r\n            <el-option label=\"通过\" :value=\"'pass'\" />\r\n            <el-option label=\"不通过\" :value=\"'fail'\" />\r\n          </el-select>\r\n        </el-form-item>\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n          <el-button @click=\"resetSearch\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <!-- 成绩列表 -->\r\n      <div v-loading=\"loading\">\r\n        <el-empty v-if=\"results.length === 0\" description=\"暂无考试成绩\" />\r\n        \r\n        <el-table\r\n          v-else\r\n          :data=\"results\"\r\n          border\r\n          style=\"width: 100%\"\r\n        >\r\n          <el-table-column type=\"index\" width=\"50\" label=\"#\" />\r\n          <el-table-column prop=\"teacher_name\" label=\"姓名\" width=\"100\" />\r\n          <el-table-column prop=\"department\" label=\"科室\" width=\"120\" />\r\n          <el-table-column prop=\"score\" label=\"分数\" width=\"80\">\r\n            <template #default=\"scope\">\r\n              <span :class=\"{ 'pass-score': isPass(scope.row), 'fail-score': !isPass(scope.row) }\">\r\n                {{ scope.row.score }}\r\n              </span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"状态\" width=\"100\">\r\n            <template #default=\"scope\">\r\n              <el-tag :type=\"isPass(scope.row) ? 'success' : 'danger'\">\r\n                {{ isPass(scope.row) ? '通过' : '不通过' }}\r\n              </el-tag>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"start_time\" label=\"开始时间\" width=\"180\">\r\n            <template #default=\"scope\">\r\n              {{ formatDateTime(scope.row.start_time) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"end_time\" label=\"结束时间\" width=\"180\">\r\n            <template #default=\"scope\">\r\n              {{ formatDateTime(scope.row.end_time) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column prop=\"duration\" label=\"用时\" width=\"120\">\r\n            <template #default=\"scope\">\r\n              {{ formatDuration(scope.row.start_time, scope.row.end_time) }}\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"操作\" width=\"120\" fixed=\"right\">\r\n            <template #default=\"scope\">\r\n              <el-button size=\"small\" @click=\"viewDetail(scope.row)\">查看详情</el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n\r\n        <!-- 分页 -->\r\n        <div class=\"pagination-container\">\r\n          <el-pagination\r\n            v-model:current-page=\"currentPage\"\r\n            v-model:page-size=\"pageSize\"\r\n            :page-sizes=\"[10, 20, 50, 100]\"\r\n            layout=\"total, sizes, prev, pager, next, jumper\"\r\n            :total=\"total\"\r\n            @size-change=\"handleSizeChange\"\r\n            @current-change=\"handleCurrentChange\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 成绩详情对话框 -->\r\n    <el-dialog\r\n      v-model=\"detailDialogVisible\"\r\n      title=\"成绩详情\"\r\n      width=\"800px\"\r\n    >\r\n      <div v-if=\"currentResult\" class=\"result-detail\">\r\n        <el-descriptions title=\"考生信息\" :column=\"3\" border>\r\n          <el-descriptions-item label=\"姓名\">{{ currentResult.teacher_name }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"科室\">{{ currentResult.department }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"分数\">\r\n            <span :class=\"{ 'pass-score': isPass(currentResult), 'fail-score': !isPass(currentResult) }\">\r\n              {{ currentResult.score }}\r\n            </span>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"状态\">\r\n            <el-tag :type=\"isPass(currentResult) ? 'success' : 'danger'\">\r\n              {{ isPass(currentResult) ? '通过' : '不通过' }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"开始时间\">{{ formatDateTime(currentResult.start_time) }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"结束时间\">{{ formatDateTime(currentResult.end_time) }}</el-descriptions-item>\r\n        </el-descriptions>\r\n\r\n        <el-divider content-position=\"center\">答题详情</el-divider>\r\n\r\n        <el-collapse v-if=\"currentResult.answers && currentResult.answers.length > 0\">\r\n          <el-collapse-item \r\n            v-for=\"(answer, index) in currentResult.answers\" \r\n            :key=\"index\"\r\n            :name=\"index\"\r\n          >\r\n            <template #title>\r\n              <div class=\"answer-item-title\">\r\n                <span>第 {{ index + 1 }} 题</span>\r\n                <el-tag :type=\"answer.is_correct ? 'success' : 'danger'\" size=\"small\">\r\n                  {{ answer.is_correct ? '正确' : '错误' }}\r\n                </el-tag>\r\n                <span class=\"question-score\">{{ answer.score }} 分</span>\r\n              </div>\r\n            </template>\r\n            \r\n            <div class=\"question-content\">{{ answer.question_content }}</div>\r\n            \r\n            <!-- 选择题选项 -->\r\n            <div v-if=\"answer.question_type !== 'true_false'\" class=\"options-list\">\r\n              <div\r\n                v-for=\"(option, optIndex) in answer.options\"\r\n                :key=\"optIndex\"\r\n                class=\"option-item\"\r\n                :class=\"{\r\n                  'selected-option': isOptionSelected(answer, option),\r\n                  'correct-option': option.is_correct,\r\n                  'wrong-option': isWrongOption(answer, option)\r\n                }\"\r\n              >\r\n                <div class=\"option-label\">{{ String.fromCharCode(65 + optIndex) }}</div>\r\n                <div class=\"option-content\">{{ option.content }}</div>\r\n                <div v-if=\"option.is_correct\" class=\"option-mark correct-mark\">\r\n                  <el-icon><Check /></el-icon>\r\n                </div>\r\n                <div v-else-if=\"isOptionSelected(answer, option)\" class=\"option-mark wrong-mark\">\r\n                  <el-icon><Close /></el-icon>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            <!-- 判断题答案 -->\r\n            <div v-else class=\"true-false-answer\">\r\n              <div class=\"answer-row\">\r\n                <div class=\"answer-label\">正确答案：</div>\r\n                <div class=\"answer-value\">\r\n                  <el-tag :type=\"answer.correct_answer ? 'success' : 'danger'\">\r\n                    {{ answer.correct_answer ? '正确' : '错误' }}\r\n                  </el-tag>\r\n                </div>\r\n              </div>\r\n              <div class=\"answer-row\">\r\n                <div class=\"answer-label\">考生答案：</div>\r\n                <div class=\"answer-value\">\r\n                  <el-tag :type=\"answer.user_answer === answer.correct_answer ? 'success' : 'danger'\">\r\n                    {{ answer.user_answer ? '正确' : '错误' }}\r\n                  </el-tag>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n            <!-- 解析 -->\r\n            <div v-if=\"answer.explanation\" class=\"question-explanation\">\r\n              <div class=\"explanation-label\">解析：</div>\r\n              <div class=\"explanation-content\">{{ answer.explanation }}</div>\r\n            </div>\r\n          </el-collapse-item>\r\n        </el-collapse>\r\n        \r\n        <el-empty v-else description=\"暂无答题详情\" />\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, computed, onMounted } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nimport { Check, Close } from '@element-plus/icons-vue'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'ExamResults',\r\n  components: {\r\n    Check,\r\n    Close\r\n  },\r\n  setup() {\r\n      let token = localStorage.getItem('token')\r\n    if (token) {\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`\r\n    }\r\n    const route = useRoute()\r\n    const router = useRouter()\r\n    const examId = route.params.id\r\n    \r\n    // 基础数据\r\n    const loading = ref(false)\r\n    const examData = ref(null)\r\n    const results = ref([])\r\n    const total = ref(0)\r\n    const currentPage = ref(1)\r\n    const pageSize = ref(10)\r\n    \r\n    // 详情对话框\r\n    const detailDialogVisible = ref(false)\r\n    const currentResult = ref(null)\r\n    \r\n    // 搜索表单\r\n    const searchForm = reactive({\r\n      name: '',\r\n      department: '',\r\n      status: ''\r\n    })\r\n    \r\n    // 计算属性\r\n    const totalParticipants = computed(() => {\r\n      return results.value.length\r\n    })\r\n    \r\n    const passCount = computed(() => {\r\n      return results.value.filter(result => isPass(result)).length\r\n    })\r\n    \r\n    const passRate = computed(() => {\r\n      if (totalParticipants.value === 0) return 0\r\n      return Math.round((passCount.value / totalParticipants.value) * 100)\r\n    })\r\n    \r\n    const averageScore = computed(() => {\r\n      if (totalParticipants.value === 0) return 0\r\n      const totalScore = results.value.reduce((sum, result) => sum + result.score, 0)\r\n      return (totalScore / totalParticipants.value).toFixed(1)\r\n    })\r\n    \r\n    // 生命周期钩子\r\n    onMounted(() => {\r\n      fetchExamData()\r\n      fetchResults()\r\n    })\r\n    \r\n    // 获取考试信息\r\n    const fetchExamData = async () => {\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/exams/${examId}`)\r\n        examData.value = response.data.data\r\n      } catch (error) {\r\n        console.error('获取考试信息失败:', error)\r\n        ElMessage.error('获取考试信息失败')\r\n      }\r\n    }\r\n    \r\n    // 获取考试成绩\r\n    const fetchResults = async () => {\r\n      loading.value = true\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/exams/${examId}/results`)\r\n        results.value = response.data.data\r\n        total.value = response.data.count\r\n      } catch (error) {\r\n        console.error('获取考试成绩失败:', error)\r\n        ElMessage.error('获取考试成绩失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 获取考试状态文本\r\n    const getExamStatusText = (status) => {\r\n      const statusMap = {\r\n        'draft': '草稿',\r\n        'published': '已发布',\r\n        'in_progress': '进行中',\r\n        'completed': '已结束'\r\n      }\r\n      return statusMap[status] || '未知状态'\r\n    }\r\n    \r\n    // 获取考试状态类型\r\n    const getExamStatusType = (status) => {\r\n      const typeMap = {\r\n        'draft': 'info',\r\n        'published': 'success',\r\n        'in_progress': 'warning',\r\n        'completed': 'danger'\r\n      }\r\n      return typeMap[status] || 'info'\r\n    }\r\n    \r\n    // 判断是否通过\r\n    const isPass = (result) => {\r\n      if (!examData.value || !result) return false\r\n      return result.score >= examData.value.pass_score\r\n    }\r\n    \r\n    // 格式化日期\r\n    const formatDate = (dateString) => {\r\n      if (!dateString) return '-'\r\n      const date = new Date(dateString)\r\n      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`\r\n    }\r\n    \r\n    // 格式化日期时间\r\n    const formatDateTime = (dateString) => {\r\n      if (!dateString) return '-'\r\n      const date = new Date(dateString)\r\n      return `${formatDate(dateString)} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`\r\n    }\r\n    \r\n    // 格式化时长\r\n    const formatDuration = (startTime, endTime) => {\r\n      if (!startTime || !endTime) return '-'\r\n      \r\n      const start = new Date(startTime)\r\n      const end = new Date(endTime)\r\n      const diffMs = end - start\r\n      \r\n      const minutes = Math.floor(diffMs / 60000)\r\n      const seconds = Math.floor((diffMs % 60000) / 1000)\r\n      \r\n      return `${minutes}分${seconds}秒`\r\n    }\r\n    \r\n    // 返回考试列表\r\n    const goBack = () => {\r\n      router.push('/exams/list')\r\n    }\r\n    \r\n    // 搜索操作\r\n    const handleSearch = () => {\r\n      currentPage.value = 1\r\n      fetchResults()\r\n    }\r\n    \r\n    // 重置搜索\r\n    const resetSearch = () => {\r\n      Object.keys(searchForm).forEach(key => {\r\n        searchForm[key] = ''\r\n      })\r\n      currentPage.value = 1\r\n      fetchResults()\r\n    }\r\n    \r\n    // 分页操作\r\n    const handleSizeChange = (val) => {\r\n      pageSize.value = val\r\n      fetchResults()\r\n    }\r\n    \r\n    const handleCurrentChange = (val) => {\r\n      currentPage.value = val\r\n      fetchResults()\r\n    }\r\n    \r\n    // 查看详情\r\n    const viewDetail = async (row) => {\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/exams/results/${row.id}`)\r\n        currentResult.value = response.data.data\r\n        detailDialogVisible.value = true\r\n      } catch (error) {\r\n        console.error('获取成绩详情失败:', error)\r\n        ElMessage.error('获取成绩详情失败')\r\n      }\r\n    }\r\n    \r\n    // 导出成绩\r\n    const exportResults = () => {\r\n      window.open(`http://localhost:3000/api/exams/${examId}/results/export`, '_blank')\r\n    }\r\n    \r\n    // 判断选项是否被选中\r\n    const isOptionSelected = (answer, option) => {\r\n      if (!answer.user_answers) return false\r\n      return answer.user_answers.includes(option.id)\r\n    }\r\n    \r\n    // 判断是否为错误选择\r\n    const isWrongOption = (answer, option) => {\r\n      return isOptionSelected(answer, option) && !option.is_correct\r\n    }\r\n    \r\n    return {\r\n      loading,\r\n      examData,\r\n      results,\r\n      total,\r\n      currentPage,\r\n      pageSize,\r\n      searchForm,\r\n      detailDialogVisible,\r\n      currentResult,\r\n      totalParticipants,\r\n      passCount,\r\n      passRate,\r\n      averageScore,\r\n      getExamStatusText,\r\n      getExamStatusType,\r\n      isPass,\r\n      formatDate,\r\n      formatDateTime,\r\n      formatDuration,\r\n      goBack,\r\n      handleSearch,\r\n      resetSearch,\r\n      handleSizeChange,\r\n      handleCurrentChange,\r\n      viewDetail,\r\n      exportResults,\r\n      isOptionSelected,\r\n      isWrongOption\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.exam-results-container {\r\n  padding: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.exam-info {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.results-stats {\r\n  margin: 20px 0;\r\n}\r\n\r\n.stat-card {\r\n  background-color: #f7f7f7;\r\n  padding: 20px;\r\n  border-radius: 4px;\r\n  text-align: center;\r\n  height: 100px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  justify-content: center;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 28px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-top: 5px;\r\n}\r\n\r\n.search-form {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n\r\n.pass-score {\r\n  color: #67c23a;\r\n  font-weight: bold;\r\n}\r\n\r\n.fail-score {\r\n  color: #f56c6c;\r\n  font-weight: bold;\r\n}\r\n\r\n.result-detail {\r\n  padding: 10px;\r\n}\r\n\r\n.answer-item-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.question-score {\r\n  margin-left: auto;\r\n  color: #f56c6c;\r\n  font-weight: bold;\r\n}\r\n\r\n.question-content {\r\n  font-size: 16px;\r\n  margin: 15px 0;\r\n  padding: 10px;\r\n  background-color: #f7f7f7;\r\n  border-radius: 4px;\r\n}\r\n\r\n.options-list {\r\n  margin-top: 15px;\r\n}\r\n\r\n.options-list .option-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px;\r\n  border-bottom: 1px solid #eee;\r\n  border-radius: 4px;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.options-list .option-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.options-list .option-item.correct-option {\r\n  background-color: #f0f9eb;\r\n}\r\n\r\n.options-list .option-item.wrong-option {\r\n  background-color: #fef0f0;\r\n}\r\n\r\n.options-list .option-item.selected-option {\r\n  border: 1px solid #dcdfe6;\r\n}\r\n\r\n.options-list .option-label {\r\n  width: 30px;\r\n  height: 30px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-radius: 50%;\r\n  background-color: #f2f6fc;\r\n  margin-right: 10px;\r\n  font-weight: bold;\r\n}\r\n\r\n.options-list .option-content {\r\n  flex: 1;\r\n}\r\n\r\n.options-list .option-mark {\r\n  margin-left: 10px;\r\n}\r\n\r\n.options-list .correct-mark {\r\n  color: #67c23a;\r\n}\r\n\r\n.options-list .wrong-mark {\r\n  color: #f56c6c;\r\n}\r\n\r\n.true-false-answer {\r\n  margin: 15px 0;\r\n  padding: 10px;\r\n  background-color: #f7f7f7;\r\n  border-radius: 4px;\r\n}\r\n\r\n.answer-row {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.answer-row:last-child {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.answer-label {\r\n  font-weight: bold;\r\n  margin-right: 10px;\r\n  width: 80px;\r\n}\r\n\r\n.question-explanation {\r\n  margin-top: 20px;\r\n  padding: 10px;\r\n  background-color: #f7f7f7;\r\n  border-radius: 4px;\r\n}\r\n\r\n.explanation-label {\r\n  font-weight: bold;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.explanation-content {\r\n  color: #606266;\r\n  white-space: pre-line;\r\n}\r\n</style> "], "mappings": ";;;;;AAoPA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAI;AACvD,SAASC,QAAQ,EAAEC,SAAQ,QAAS,YAAW;AAC/C,SAASC,SAAQ,QAAS,cAAa;AACvC,SAASC,KAAK,EAAEC,KAAI,QAAS,yBAAwB;AACrD,OAAOC,KAAI,MAAO,OAAM;AAExB,eAAe;EACbC,IAAI,EAAE,aAAa;EACnBC,UAAU,EAAE;IACVJ,KAAK;IACLC;EACF,CAAC;EACDI,KAAKA,CAAA,EAAG;IACJ,IAAIC,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,OAAO;IAC1C,IAAIF,KAAK,EAAE;MACTJ,KAAK,CAACO,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,IAAI,UAAUL,KAAK,EAAC;IACnE;IACA,MAAMM,KAAI,GAAIf,QAAQ,CAAC;IACvB,MAAMgB,MAAK,GAAIf,SAAS,CAAC;IACzB,MAAMgB,MAAK,GAAIF,KAAK,CAACG,MAAM,CAACC,EAAC;;IAE7B;IACA,MAAMC,OAAM,GAAIxB,GAAG,CAAC,KAAK;IACzB,MAAMyB,QAAO,GAAIzB,GAAG,CAAC,IAAI;IACzB,MAAM0B,OAAM,GAAI1B,GAAG,CAAC,EAAE;IACtB,MAAM2B,KAAI,GAAI3B,GAAG,CAAC,CAAC;IACnB,MAAM4B,WAAU,GAAI5B,GAAG,CAAC,CAAC;IACzB,MAAM6B,QAAO,GAAI7B,GAAG,CAAC,EAAE;;IAEvB;IACA,MAAM8B,mBAAkB,GAAI9B,GAAG,CAAC,KAAK;IACrC,MAAM+B,aAAY,GAAI/B,GAAG,CAAC,IAAI;;IAE9B;IACA,MAAMgC,UAAS,GAAI/B,QAAQ,CAAC;MAC1BS,IAAI,EAAE,EAAE;MACRuB,UAAU,EAAE,EAAE;MACdC,MAAM,EAAE;IACV,CAAC;;IAED;IACA,MAAMC,iBAAgB,GAAIjC,QAAQ,CAAC,MAAM;MACvC,OAAOwB,OAAO,CAACU,KAAK,CAACC,MAAK;IAC5B,CAAC;IAED,MAAMC,SAAQ,GAAIpC,QAAQ,CAAC,MAAM;MAC/B,OAAOwB,OAAO,CAACU,KAAK,CAACG,MAAM,CAACC,MAAK,IAAKC,MAAM,CAACD,MAAM,CAAC,CAAC,CAACH,MAAK;IAC7D,CAAC;IAED,MAAMK,QAAO,GAAIxC,QAAQ,CAAC,MAAM;MAC9B,IAAIiC,iBAAiB,CAACC,KAAI,KAAM,CAAC,EAAE,OAAO;MAC1C,OAAOO,IAAI,CAACC,KAAK,CAAEN,SAAS,CAACF,KAAI,GAAID,iBAAiB,CAACC,KAAK,GAAI,GAAG;IACrE,CAAC;IAED,MAAMS,YAAW,GAAI3C,QAAQ,CAAC,MAAM;MAClC,IAAIiC,iBAAiB,CAACC,KAAI,KAAM,CAAC,EAAE,OAAO;MAC1C,MAAMU,UAAS,GAAIpB,OAAO,CAACU,KAAK,CAACW,MAAM,CAAC,CAACC,GAAG,EAAER,MAAM,KAAKQ,GAAE,GAAIR,MAAM,CAACS,KAAK,EAAE,CAAC;MAC9E,OAAO,CAACH,UAAS,GAAIX,iBAAiB,CAACC,KAAK,EAAEc,OAAO,CAAC,CAAC;IACzD,CAAC;;IAED;IACA/C,SAAS,CAAC,MAAM;MACdgD,aAAa,CAAC;MACdC,YAAY,CAAC;IACf,CAAC;;IAED;IACA,MAAMD,aAAY,GAAI,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,MAAME,QAAO,GAAI,MAAM5C,KAAK,CAAC6C,GAAG,CAAC,mCAAmCjC,MAAM,EAAE;QAC5EI,QAAQ,CAACW,KAAI,GAAIiB,QAAQ,CAACE,IAAI,CAACA,IAAG;MACpC,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChClD,SAAS,CAACkD,KAAK,CAAC,UAAU;MAC5B;IACF;;IAEA;IACA,MAAMJ,YAAW,GAAI,MAAAA,CAAA,KAAY;MAC/B5B,OAAO,CAACY,KAAI,GAAI,IAAG;MACnB,IAAI;QACF,MAAMiB,QAAO,GAAI,MAAM5C,KAAK,CAAC6C,GAAG,CAAC,mCAAmCjC,MAAM,UAAU;QACpFK,OAAO,CAACU,KAAI,GAAIiB,QAAQ,CAACE,IAAI,CAACA,IAAG;QACjC5B,KAAK,CAACS,KAAI,GAAIiB,QAAQ,CAACE,IAAI,CAACG,KAAI;MAClC,EAAE,OAAOF,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChClD,SAAS,CAACkD,KAAK,CAAC,UAAU;MAC5B,UAAU;QACRhC,OAAO,CAACY,KAAI,GAAI,KAAI;MACtB;IACF;;IAEA;IACA,MAAMuB,iBAAgB,GAAKzB,MAAM,IAAK;MACpC,MAAM0B,SAAQ,GAAI;QAChB,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,KAAK;QAClB,aAAa,EAAE,KAAK;QACpB,WAAW,EAAE;MACf;MACA,OAAOA,SAAS,CAAC1B,MAAM,KAAK,MAAK;IACnC;;IAEA;IACA,MAAM2B,iBAAgB,GAAK3B,MAAM,IAAK;MACpC,MAAM4B,OAAM,GAAI;QACd,OAAO,EAAE,MAAM;QACf,WAAW,EAAE,SAAS;QACtB,aAAa,EAAE,SAAS;QACxB,WAAW,EAAE;MACf;MACA,OAAOA,OAAO,CAAC5B,MAAM,KAAK,MAAK;IACjC;;IAEA;IACA,MAAMO,MAAK,GAAKD,MAAM,IAAK;MACzB,IAAI,CAACf,QAAQ,CAACW,KAAI,IAAK,CAACI,MAAM,EAAE,OAAO,KAAI;MAC3C,OAAOA,MAAM,CAACS,KAAI,IAAKxB,QAAQ,CAACW,KAAK,CAAC2B,UAAS;IACjD;;IAEA;IACA,MAAMC,UAAS,GAAKC,UAAU,IAAK;MACjC,IAAI,CAACA,UAAU,EAAE,OAAO,GAAE;MAC1B,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU;MAChC,OAAO,GAAGC,IAAI,CAACE,WAAW,CAAC,CAAC,IAAI,CAACF,IAAI,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIL,IAAI,CAACM,OAAO,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC;IAClI;;IAEA;IACA,MAAME,cAAa,GAAKR,UAAU,IAAK;MACrC,IAAI,CAACA,UAAU,EAAE,OAAO,GAAE;MAC1B,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU;MAChC,OAAO,GAAGD,UAAU,CAACC,UAAU,CAAC,IAAIC,IAAI,CAACQ,QAAQ,CAAC,CAAC,CAACJ,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIL,IAAI,CAACS,UAAU,CAAC,CAAC,CAACL,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC;IACnI;;IAEA;IACA,MAAMK,cAAa,GAAIA,CAACC,SAAS,EAAEC,OAAO,KAAK;MAC7C,IAAI,CAACD,SAAQ,IAAK,CAACC,OAAO,EAAE,OAAO,GAAE;MAErC,MAAMC,KAAI,GAAI,IAAIZ,IAAI,CAACU,SAAS;MAChC,MAAMG,GAAE,GAAI,IAAIb,IAAI,CAACW,OAAO;MAC5B,MAAMG,MAAK,GAAID,GAAE,GAAID,KAAI;MAEzB,MAAMG,OAAM,GAAIvC,IAAI,CAACwC,KAAK,CAACF,MAAK,GAAI,KAAK;MACzC,MAAMG,OAAM,GAAIzC,IAAI,CAACwC,KAAK,CAAEF,MAAK,GAAI,KAAK,GAAI,IAAI;MAElD,OAAO,GAAGC,OAAO,IAAIE,OAAO,GAAE;IAChC;;IAEA;IACA,MAAMC,MAAK,GAAIA,CAAA,KAAM;MACnBjE,MAAM,CAACkE,IAAI,CAAC,aAAa;IAC3B;;IAEA;IACA,MAAMC,YAAW,GAAIA,CAAA,KAAM;MACzB3D,WAAW,CAACQ,KAAI,GAAI;MACpBgB,YAAY,CAAC;IACf;;IAEA;IACA,MAAMoC,WAAU,GAAIA,CAAA,KAAM;MACxBC,MAAM,CAACC,IAAI,CAAC1D,UAAU,CAAC,CAAC2D,OAAO,CAACC,GAAE,IAAK;QACrC5D,UAAU,CAAC4D,GAAG,IAAI,EAAC;MACrB,CAAC;MACDhE,WAAW,CAACQ,KAAI,GAAI;MACpBgB,YAAY,CAAC;IACf;;IAEA;IACA,MAAMyC,gBAAe,GAAKC,GAAG,IAAK;MAChCjE,QAAQ,CAACO,KAAI,GAAI0D,GAAE;MACnB1C,YAAY,CAAC;IACf;IAEA,MAAM2C,mBAAkB,GAAKD,GAAG,IAAK;MACnClE,WAAW,CAACQ,KAAI,GAAI0D,GAAE;MACtB1C,YAAY,CAAC;IACf;;IAEA;IACA,MAAM4C,UAAS,GAAI,MAAOC,GAAG,IAAK;MAChC,IAAI;QACF,MAAM5C,QAAO,GAAI,MAAM5C,KAAK,CAAC6C,GAAG,CAAC,2CAA2C2C,GAAG,CAAC1E,EAAE,EAAE;QACpFQ,aAAa,CAACK,KAAI,GAAIiB,QAAQ,CAACE,IAAI,CAACA,IAAG;QACvCzB,mBAAmB,CAACM,KAAI,GAAI,IAAG;MACjC,EAAE,OAAOoB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChClD,SAAS,CAACkD,KAAK,CAAC,UAAU;MAC5B;IACF;;IAEA;IACA,MAAM0C,aAAY,GAAIA,CAAA,KAAM;MAC1BC,MAAM,CAACC,IAAI,CAAC,mCAAmC/E,MAAM,iBAAiB,EAAE,QAAQ;IAClF;;IAEA;IACA,MAAMgF,gBAAe,GAAIA,CAACC,MAAM,EAAEC,MAAM,KAAK;MAC3C,IAAI,CAACD,MAAM,CAACE,YAAY,EAAE,OAAO,KAAI;MACrC,OAAOF,MAAM,CAACE,YAAY,CAACC,QAAQ,CAACF,MAAM,CAAChF,EAAE;IAC/C;;IAEA;IACA,MAAMmF,aAAY,GAAIA,CAACJ,MAAM,EAAEC,MAAM,KAAK;MACxC,OAAOF,gBAAgB,CAACC,MAAM,EAAEC,MAAM,KAAK,CAACA,MAAM,CAACI,UAAS;IAC9D;IAEA,OAAO;MACLnF,OAAO;MACPC,QAAQ;MACRC,OAAO;MACPC,KAAK;MACLC,WAAW;MACXC,QAAQ;MACRG,UAAU;MACVF,mBAAmB;MACnBC,aAAa;MACbI,iBAAiB;MACjBG,SAAS;MACTI,QAAQ;MACRG,YAAY;MACZc,iBAAiB;MACjBE,iBAAiB;MACjBpB,MAAM;MACNuB,UAAU;MACVS,cAAc;MACdG,cAAc;MACdS,MAAM;MACNE,YAAY;MACZC,WAAW;MACXK,gBAAgB;MAChBE,mBAAmB;MACnBC,UAAU;MACVE,aAAa;MACbG,gBAAgB;MAChBK;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}