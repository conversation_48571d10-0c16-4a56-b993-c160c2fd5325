import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import api from './utils/api'
import axios from 'axios'

// Import Element Plus
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
// Import icons
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 配置全局axios默认值
axios.defaults.baseURL = 'http://localhost:3000'

const app = createApp(App)

// 全局API实例
app.config.globalProperties.$api = api
app.config.globalProperties.$axios = axios

// Register all icons globally
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
const debounce = (fn, delay) => {
  let timer
   return (...args) => {
     if (timer) {
       clearTimeout(timer)
     }
     timer = setTimeout(() => {
       fn(...args)
     }, delay)
   }
}
const _ResizeObserver = window.ResizeObserver;
window.ResizeObserver = class ResizeObserver extends _ResizeObserver{
   constructor(callback) {
     callback = debounce(callback, 200);
     super(callback);
   }
}
app.use(store).use(router).use(ElementPlus).mount('#app')
