{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport { useRoute, useRouter } from 'vue-router';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { Check, UploadFilled } from '@element-plus/icons-vue';\nimport axios from 'axios';\nexport default {\n  name: 'ExamQuestions',\n  components: {\n    Check,\n    UploadFilled\n  },\n  setup() {\n    let token = localStorage.getItem('token');\n    if (token) {\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;\n    }\n    const route = useRoute();\n    const router = useRouter();\n    const examId = route.params.id;\n\n    // 基础数据\n    const loading = ref(false);\n    const examData = ref(null);\n    const questions = ref([]);\n    const questionFormRef = ref(null);\n\n    // 对话框\n    const dialogVisible = ref(false);\n    const viewDialogVisible = ref(false);\n    const importDialogVisible = ref(false);\n    const isEdit = ref(false);\n    const currentQuestion = ref(null);\n\n    // 表单数据\n    const formData = reactive({\n      id: '',\n      type: 'single',\n      content: '',\n      score: 5,\n      options: [{\n        content: '',\n        is_correct: false\n      }, {\n        content: '',\n        is_correct: false\n      }, {\n        content: '',\n        is_correct: false\n      }, {\n        content: '',\n        is_correct: false\n      }],\n      correct_option: 0,\n      true_false_answer: true,\n      explanation: '',\n      exam_id: examId\n    });\n\n    // 表单验证规则\n    const formRules = {\n      type: [{\n        required: true,\n        message: '请选择题目类型',\n        trigger: 'change'\n      }],\n      content: [{\n        required: true,\n        message: '请输入题目内容',\n        trigger: 'blur'\n      }],\n      score: [{\n        required: true,\n        message: '请输入分值',\n        trigger: 'change'\n      }]\n    };\n\n    // 计算属性\n    const singleChoiceCount = computed(() => {\n      return questions.value.filter(q => q.type === 'single').length;\n    });\n    const multipleChoiceCount = computed(() => {\n      return questions.value.filter(q => q.type === 'multiple').length;\n    });\n    const trueFalseCount = computed(() => {\n      return questions.value.filter(q => q.type === 'true_false').length;\n    });\n    const totalScore = computed(() => {\n      return questions.value.reduce((sum, q) => sum + q.score, 0);\n    });\n\n    // 生命周期钩子\n    onMounted(() => {\n      fetchExamData();\n      fetchQuestions();\n    });\n\n    // 获取考试信息\n    const fetchExamData = async () => {\n      try {\n        const response = await axios.get(`http://localhost:3000/api/exams/${examId}`);\n        examData.value = response.data.data;\n      } catch (error) {\n        console.error('获取考试信息失败:', error);\n        ElMessage.error('获取考试信息失败');\n      }\n    };\n\n    // 获取题目列表\n    const fetchQuestions = async () => {\n      loading.value = true;\n      try {\n        const response = await axios.get(`http://localhost:3000/api/exams/${examId}/questions`);\n        questions.value = response.data.data;\n      } catch (error) {\n        console.error('获取题目列表失败:', error);\n        ElMessage.error('获取题目列表失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 获取考试状态文本\n    const getExamStatusText = status => {\n      const statusMap = {\n        'draft': '草稿',\n        'published': '已发布',\n        'in_progress': '进行中',\n        'completed': '已结束'\n      };\n      return statusMap[status] || '未知状态';\n    };\n\n    // 获取考试状态类型\n    const getExamStatusType = status => {\n      const typeMap = {\n        'draft': 'info',\n        'published': 'success',\n        'in_progress': 'warning',\n        'completed': 'danger'\n      };\n      return typeMap[status] || 'info';\n    };\n\n    // 获取题目类型文本\n    const getQuestionTypeText = type => {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'true_false': '判断题'\n      };\n      return typeMap[type] || '未知类型';\n    };\n\n    // 获取题目类型标签\n    const getQuestionTypeTag = type => {\n      const tagMap = {\n        'single': 'primary',\n        'multiple': 'success',\n        'true_false': 'warning'\n      };\n      return tagMap[type] || 'info';\n    };\n\n    // 格式化日期\n    const formatDate = dateString => {\n      if (!dateString) return '-';\n      const date = new Date(dateString);\n      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`;\n    };\n\n    // 返回考试列表\n    const goBack = () => {\n      router.push('/exams/list');\n    };\n\n    // 打开添加对话框\n    const openAddDialog = () => {\n      isEdit.value = false;\n      resetFormData();\n      dialogVisible.value = true;\n    };\n\n    // 编辑题目\n    const editQuestion = row => {\n      isEdit.value = true;\n      currentQuestion.value = row;\n\n      // 填充表单数据\n      Object.assign(formData, {\n        id: row.id,\n        type: row.type,\n        content: row.content,\n        score: row.score,\n        explanation: row.explanation || '',\n        exam_id: examId\n      });\n\n      // 处理选项和答案\n      if (row.type === 'true_false') {\n        formData.true_false_answer = row.true_false_answer;\n      } else {\n        formData.options = [...row.options];\n        if (row.type === 'single') {\n          formData.correct_option = row.options.findIndex(opt => opt.is_correct);\n        }\n      }\n      dialogVisible.value = true;\n    };\n\n    // 查看题目\n    const viewQuestion = row => {\n      currentQuestion.value = row;\n      viewDialogVisible.value = true;\n    };\n\n    // 删除题目\n    const deleteQuestion = row => {\n      ElMessageBox.confirm('确定要删除该题目吗？', '警告', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(async () => {\n        try {\n          await axios.delete(`http://localhost:3000/api/exams/questions/${row.id}`);\n          ElMessage.success('删除成功');\n          fetchQuestions();\n        } catch (error) {\n          console.error('删除失败:', error);\n          ElMessage.error('删除失败');\n        }\n      }).catch(() => {\n        ElMessage.info('已取消删除');\n      });\n    };\n\n    // 添加选项\n    const addOption = () => {\n      if (formData.options.length < 6) {\n        formData.options.push({\n          content: '',\n          is_correct: false\n        });\n      }\n    };\n\n    // 移除选项\n    const removeOption = index => {\n      if (formData.options.length > 2) {\n        formData.options.splice(index, 1);\n\n        // 如果删除的是正确答案，重置正确答案\n        if (formData.type === 'single' && formData.correct_option === index) {\n          formData.correct_option = 0;\n        } else if (formData.type === 'single' && formData.correct_option > index) {\n          formData.correct_option--;\n        }\n      }\n    };\n\n    // 重置表单数据\n    const resetFormData = () => {\n      Object.assign(formData, {\n        id: '',\n        type: 'single',\n        content: '',\n        score: 5,\n        options: [{\n          content: '',\n          is_correct: false\n        }, {\n          content: '',\n          is_correct: false\n        }, {\n          content: '',\n          is_correct: false\n        }, {\n          content: '',\n          is_correct: false\n        }],\n        correct_option: 0,\n        true_false_answer: true,\n        explanation: '',\n        exam_id: examId\n      });\n      if (questionFormRef.value) {\n        questionFormRef.value.resetFields();\n      }\n    };\n\n    // 提交表单\n    const submitForm = async () => {\n      if (!questionFormRef.value) return;\n      await questionFormRef.value.validate(async valid => {\n        if (valid) {\n          // 处理单选题答案\n          if (formData.type === 'single') {\n            formData.options.forEach((opt, index) => {\n              opt.is_correct = index === formData.correct_option;\n            });\n          }\n          try {\n            if (isEdit.value) {\n              // 编辑模式\n              await axios.put(`http://localhost:3000/api/exams/questions/${formData.id}`, formData);\n              ElMessage.success('题目更新成功');\n            } else {\n              // 添加模式\n              await axios.post('http://localhost:3000/api/exams/questions', formData);\n              ElMessage.success('题目添加成功');\n            }\n            dialogVisible.value = false;\n            fetchQuestions();\n          } catch (error) {\n            console.error('操作失败:', error);\n            ElMessage.error('操作失败');\n          }\n        } else {\n          return false;\n        }\n      });\n    };\n\n    // 导入题库\n    const importQuestions = () => {\n      importDialogVisible.value = true;\n    };\n\n    // 下载模板\n    const downloadTemplate = () => {\n      window.open('http://localhost:3000/api/exams/questions/template', '_blank');\n    };\n\n    // 上传前验证\n    const beforeImportUpload = file => {\n      const isExcel = file.type === 'application/vnd.ms-excel' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';\n      const isLt2M = file.size / 1024 / 1024 < 2;\n      if (!isExcel) {\n        ElMessage.error('只能上传Excel文件!');\n      }\n      if (!isLt2M) {\n        ElMessage.error('文件大小不能超过2MB!');\n      }\n      return isExcel && isLt2M;\n    };\n\n    // 导入成功\n    const handleImportSuccess = response => {\n      ElMessage.success(`成功导入${response.data.count}道题目`);\n      importDialogVisible.value = false;\n      fetchQuestions();\n    };\n\n    // 导入失败\n    const handleImportError = error => {\n      console.error('导入失败:', error);\n      ElMessage.error('导入失败，请检查文件格式是否正确');\n    };\n    return {\n      loading,\n      examData,\n      questions,\n      dialogVisible,\n      viewDialogVisible,\n      importDialogVisible,\n      isEdit,\n      formData,\n      formRules,\n      questionFormRef,\n      currentQuestion,\n      examId,\n      singleChoiceCount,\n      multipleChoiceCount,\n      trueFalseCount,\n      totalScore,\n      getExamStatusText,\n      getExamStatusType,\n      getQuestionTypeText,\n      getQuestionTypeTag,\n      formatDate,\n      goBack,\n      openAddDialog,\n      editQuestion,\n      viewQuestion,\n      deleteQuestion,\n      addOption,\n      removeOption,\n      submitForm,\n      importQuestions,\n      downloadTemplate,\n      beforeImportUpload,\n      handleImportSuccess,\n      handleImportError\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "useRoute", "useRouter", "ElMessage", "ElMessageBox", "Check", "UploadFilled", "axios", "name", "components", "setup", "token", "localStorage", "getItem", "defaults", "headers", "common", "route", "router", "examId", "params", "id", "loading", "examData", "questions", "questionFormRef", "dialogVisible", "viewDialogVisible", "importDialogVisible", "isEdit", "currentQuestion", "formData", "type", "content", "score", "options", "is_correct", "correct_option", "true_false_answer", "explanation", "exam_id", "formRules", "required", "message", "trigger", "singleChoiceCount", "value", "filter", "q", "length", "multipleChoiceCount", "trueFalseCount", "totalScore", "reduce", "sum", "fetchExamData", "fetchQuestions", "response", "get", "data", "error", "console", "getExamStatusText", "status", "statusMap", "getExamStatusType", "typeMap", "getQuestionTypeText", "getQuestionTypeTag", "tagMap", "formatDate", "dateString", "date", "Date", "getFullYear", "getMonth", "toString", "padStart", "getDate", "goBack", "push", "openAddDialog", "resetFormData", "editQuestion", "row", "Object", "assign", "findIndex", "opt", "viewQuestion", "deleteQuestion", "confirm", "confirmButtonText", "cancelButtonText", "then", "delete", "success", "catch", "info", "addOption", "removeOption", "index", "splice", "resetFields", "submitForm", "validate", "valid", "for<PERSON>ach", "put", "post", "importQuestions", "downloadTemplate", "window", "open", "beforeImportUpload", "file", "isExcel", "isLt2M", "size", "handleImportSuccess", "count", "handleImportError"], "sources": ["D:\\admin\\202506\\督导系统\\后台管理系统\\ms\\src\\views\\exams\\ExamQuestions.vue"], "sourcesContent": ["<template>\r\n  <div class=\"exam-questions-container\">\r\n    <el-card class=\"box-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span class=\"title\">考试题目管理</span>\r\n          <div>\r\n            <el-button @click=\"goBack\">返回考试列表</el-button>\r\n            <el-button type=\"primary\" @click=\"openAddDialog\">添加题目</el-button>\r\n            <el-button type=\"success\" @click=\"importQuestions\">导入题库</el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <div class=\"exam-info\" v-if=\"examData\">\r\n        <el-descriptions title=\"考试信息\" :column=\"3\" border>\r\n          <el-descriptions-item label=\"考试名称\">{{ examData.title }}</el-descriptions-item>\r\n          <el-descriptions-item label=\"考试时长\">{{ examData.duration }}分钟</el-descriptions-item>\r\n          <el-descriptions-item label=\"总分\">{{ examData.total_score }}分</el-descriptions-item>\r\n          <el-descriptions-item label=\"及格分数\">{{ examData.pass_score }}分</el-descriptions-item>\r\n          <el-descriptions-item label=\"考试状态\">\r\n            <el-tag :type=\"getExamStatusType(examData.status)\">\r\n              {{ getExamStatusText(examData.status) }}\r\n            </el-tag>\r\n          </el-descriptions-item>\r\n          <el-descriptions-item label=\"创建时间\">{{ formatDate(examData.created_at) }}</el-descriptions-item>\r\n        </el-descriptions>\r\n      </div>\r\n\r\n      <!-- 题目列表 -->\r\n      <div v-loading=\"loading\" class=\"question-list\">\r\n        <el-empty v-if=\"questions.length === 0\" description=\"暂无考试题目，请添加\" />\r\n        \r\n        <div v-else>\r\n          <div class=\"question-stats\">\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-value\">{{ questions.length }}</div>\r\n              <div class=\"stat-label\">总题数</div>\r\n            </div>\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-value\">{{ singleChoiceCount }}</div>\r\n              <div class=\"stat-label\">单选题</div>\r\n            </div>\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-value\">{{ multipleChoiceCount }}</div>\r\n              <div class=\"stat-label\">多选题</div>\r\n            </div>\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-value\">{{ trueFalseCount }}</div>\r\n              <div class=\"stat-label\">判断题</div>\r\n            </div>\r\n            <div class=\"stat-item\">\r\n              <div class=\"stat-value\">{{ totalScore }}</div>\r\n              <div class=\"stat-label\">总分</div>\r\n            </div>\r\n          </div>\r\n\r\n          <el-table :data=\"questions\" border style=\"width: 100%\">\r\n            <el-table-column type=\"index\" width=\"50\" label=\"#\" />\r\n            <el-table-column label=\"题型\" width=\"100\">\r\n              <template #default=\"scope\">\r\n                <el-tag :type=\"getQuestionTypeTag(scope.row.type)\">\r\n                  {{ getQuestionTypeText(scope.row.type) }}\r\n                </el-tag>\r\n              </template>\r\n            </el-table-column>\r\n            <el-table-column prop=\"content\" label=\"题目内容\" show-overflow-tooltip />\r\n            <el-table-column prop=\"score\" label=\"分值\" width=\"80\" />\r\n            <el-table-column label=\"操作\" width=\"180\" fixed=\"right\">\r\n              <template #default=\"scope\">\r\n                <el-button size=\"small\" @click=\"viewQuestion(scope.row)\">查看</el-button>\r\n                <el-button size=\"small\" type=\"primary\" @click=\"editQuestion(scope.row)\">编辑</el-button>\r\n                <el-button size=\"small\" type=\"danger\" @click=\"deleteQuestion(scope.row)\">删除</el-button>\r\n              </template>\r\n            </el-table-column>\r\n          </el-table>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n\r\n    <!-- 添加/编辑题目对话框 -->\r\n    <el-dialog\r\n      v-model=\"dialogVisible\"\r\n      :title=\"isEdit ? '编辑题目' : '添加题目'\"\r\n      width=\"700px\"\r\n    >\r\n      <el-form\r\n        ref=\"questionFormRef\"\r\n        :model=\"formData\"\r\n        :rules=\"formRules\"\r\n        label-width=\"100px\"\r\n        label-position=\"right\"\r\n      >\r\n        <el-form-item label=\"题目类型\" prop=\"type\">\r\n          <el-select v-model=\"formData.type\" placeholder=\"请选择题目类型\">\r\n            <el-option label=\"单选题\" value=\"single\" />\r\n            <el-option label=\"多选题\" value=\"multiple\" />\r\n            <el-option label=\"判断题\" value=\"true_false\" />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"题目内容\" prop=\"content\">\r\n          <el-input\r\n            v-model=\"formData.content\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"请输入题目内容\"\r\n          />\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"分值\" prop=\"score\">\r\n          <el-input-number v-model=\"formData.score\" :min=\"1\" :max=\"100\" />\r\n        </el-form-item>\r\n\r\n        <!-- 选项 (单选题和多选题) -->\r\n        <template v-if=\"formData.type === 'single' || formData.type === 'multiple'\">\r\n          <el-divider content-position=\"left\">选项</el-divider>\r\n          \r\n          <div\r\n            v-for=\"(option, index) in formData.options\"\r\n            :key=\"index\"\r\n            class=\"option-item\"\r\n          >\r\n            <el-form-item\r\n              :label=\"`选项 ${String.fromCharCode(65 + index)}`\"\r\n              :prop=\"`options.${index}.content`\"\r\n              :rules=\"{ required: true, message: '请输入选项内容', trigger: 'blur' }\"\r\n            >\r\n              <div class=\"option-content\">\r\n                <el-input v-model=\"option.content\" placeholder=\"请输入选项内容\" />\r\n                <el-checkbox\r\n                  v-if=\"formData.type === 'multiple'\"\r\n                  v-model=\"option.is_correct\"\r\n                  label=\"正确答案\"\r\n                />\r\n                <el-radio\r\n                  v-else\r\n                  v-model=\"formData.correct_option\"\r\n                  :label=\"index\"\r\n                  class=\"option-radio\"\r\n                >正确答案</el-radio>\r\n                <el-button\r\n                  type=\"danger\"\r\n                  icon=\"Delete\"\r\n                  circle\r\n                  @click=\"removeOption(index)\"\r\n                  v-if=\"formData.options.length > 2\"\r\n                />\r\n              </div>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <div class=\"add-option\">\r\n            <el-button type=\"primary\" plain @click=\"addOption\" :disabled=\"formData.options.length >= 6\">\r\n              添加选项\r\n            </el-button>\r\n          </div>\r\n        </template>\r\n\r\n        <!-- 判断题答案 -->\r\n        <template v-if=\"formData.type === 'true_false'\">\r\n          <el-form-item label=\"正确答案\" prop=\"true_false_answer\">\r\n            <el-radio-group v-model=\"formData.true_false_answer\">\r\n              <el-radio :label=\"true\">正确</el-radio>\r\n              <el-radio :label=\"false\">错误</el-radio>\r\n            </el-radio-group>\r\n          </el-form-item>\r\n        </template>\r\n\r\n        <el-form-item label=\"解析\" prop=\"explanation\">\r\n          <el-input\r\n            v-model=\"formData.explanation\"\r\n            type=\"textarea\"\r\n            :rows=\"2\"\r\n            placeholder=\"请输入题目解析（可选）\"\r\n          />\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <template #footer>\r\n        <span class=\"dialog-footer\">\r\n          <el-button @click=\"dialogVisible = false\">取消</el-button>\r\n          <el-button type=\"primary\" @click=\"submitForm\">确认</el-button>\r\n        </span>\r\n      </template>\r\n    </el-dialog>\r\n\r\n    <!-- 查看题目对话框 -->\r\n    <el-dialog\r\n      v-model=\"viewDialogVisible\"\r\n      title=\"题目详情\"\r\n      width=\"700px\"\r\n    >\r\n      <div v-if=\"currentQuestion\" class=\"question-detail\">\r\n        <div class=\"question-header\">\r\n          <el-tag :type=\"getQuestionTypeTag(currentQuestion.type)\" size=\"large\">\r\n            {{ getQuestionTypeText(currentQuestion.type) }}\r\n          </el-tag>\r\n          <span class=\"question-score\">{{ currentQuestion.score }}分</span>\r\n        </div>\r\n\r\n        <div class=\"question-content\">{{ currentQuestion.content }}</div>\r\n\r\n        <!-- 选项 -->\r\n        <div v-if=\"currentQuestion.type !== 'true_false'\" class=\"options-list\">\r\n          <div\r\n            v-for=\"(option, index) in currentQuestion.options\"\r\n            :key=\"index\"\r\n            class=\"option-item\"\r\n            :class=\"{ 'correct-option': option.is_correct }\"\r\n          >\r\n            <div class=\"option-label\">{{ String.fromCharCode(65 + index) }}</div>\r\n            <div class=\"option-content\">{{ option.content }}</div>\r\n            <div v-if=\"option.is_correct\" class=\"correct-mark\">\r\n              <el-icon><Check /></el-icon>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 判断题答案 -->\r\n        <div v-else class=\"true-false-answer\">\r\n          <div class=\"answer-label\">正确答案：</div>\r\n          <div class=\"answer-value\">\r\n            <el-tag :type=\"currentQuestion.true_false_answer ? 'success' : 'danger'\">\r\n              {{ currentQuestion.true_false_answer ? '正确' : '错误' }}\r\n            </el-tag>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 解析 -->\r\n        <div v-if=\"currentQuestion.explanation\" class=\"question-explanation\">\r\n          <div class=\"explanation-label\">解析：</div>\r\n          <div class=\"explanation-content\">{{ currentQuestion.explanation }}</div>\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 导入题库对话框 -->\r\n    <el-dialog\r\n      v-model=\"importDialogVisible\"\r\n      title=\"导入题库\"\r\n      width=\"500px\"\r\n    >\r\n      <el-upload\r\n        class=\"upload-demo\"\r\n        drag\r\n        action=\"http://localhost:3000/api/exams/questions/import\"\r\n        :headers=\"{ 'Content-Type': 'multipart/form-data' }\"\r\n        :data=\"{ exam_id: examId }\"\r\n        :on-success=\"handleImportSuccess\"\r\n        :on-error=\"handleImportError\"\r\n        :before-upload=\"beforeImportUpload\"\r\n        accept=\".xlsx,.xls\"\r\n      >\r\n        <el-icon class=\"el-icon--upload\"><upload-filled /></el-icon>\r\n        <div class=\"el-upload__text\">\r\n          将Excel文件拖到此处，或<em>点击上传</em>\r\n        </div>\r\n        <template #tip>\r\n          <div class=\"el-upload__tip\">\r\n            请上传Excel格式的题库文件，<el-button type=\"primary\" link @click=\"downloadTemplate\">下载模板</el-button>\r\n          </div>\r\n        </template>\r\n      </el-upload>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, computed, onMounted } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { Check, UploadFilled } from '@element-plus/icons-vue'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'ExamQuestions',\r\n  components: {\r\n    Check,\r\n    UploadFilled\r\n  },\r\n  setup() {\r\n      let token = localStorage.getItem('token')\r\n    if (token) {\r\n      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`\r\n    }\r\n    const route = useRoute()\r\n    const router = useRouter()\r\n    const examId = route.params.id\r\n    \r\n    // 基础数据\r\n    const loading = ref(false)\r\n    const examData = ref(null)\r\n    const questions = ref([])\r\n    const questionFormRef = ref(null)\r\n    \r\n    // 对话框\r\n    const dialogVisible = ref(false)\r\n    const viewDialogVisible = ref(false)\r\n    const importDialogVisible = ref(false)\r\n    const isEdit = ref(false)\r\n    const currentQuestion = ref(null)\r\n    \r\n    // 表单数据\r\n    const formData = reactive({\r\n      id: '',\r\n      type: 'single',\r\n      content: '',\r\n      score: 5,\r\n      options: [\r\n        { content: '', is_correct: false },\r\n        { content: '', is_correct: false },\r\n        { content: '', is_correct: false },\r\n        { content: '', is_correct: false }\r\n      ],\r\n      correct_option: 0,\r\n      true_false_answer: true,\r\n      explanation: '',\r\n      exam_id: examId\r\n    })\r\n    \r\n    // 表单验证规则\r\n    const formRules = {\r\n      type: [\r\n        { required: true, message: '请选择题目类型', trigger: 'change' }\r\n      ],\r\n      content: [\r\n        { required: true, message: '请输入题目内容', trigger: 'blur' }\r\n      ],\r\n      score: [\r\n        { required: true, message: '请输入分值', trigger: 'change' }\r\n      ]\r\n    }\r\n    \r\n    // 计算属性\r\n    const singleChoiceCount = computed(() => {\r\n      return questions.value.filter(q => q.type === 'single').length\r\n    })\r\n    \r\n    const multipleChoiceCount = computed(() => {\r\n      return questions.value.filter(q => q.type === 'multiple').length\r\n    })\r\n    \r\n    const trueFalseCount = computed(() => {\r\n      return questions.value.filter(q => q.type === 'true_false').length\r\n    })\r\n    \r\n    const totalScore = computed(() => {\r\n      return questions.value.reduce((sum, q) => sum + q.score, 0)\r\n    })\r\n    \r\n    // 生命周期钩子\r\n    onMounted(() => {\r\n      fetchExamData()\r\n      fetchQuestions()\r\n    })\r\n    \r\n    // 获取考试信息\r\n    const fetchExamData = async () => {\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/exams/${examId}`)\r\n        examData.value = response.data.data\r\n      } catch (error) {\r\n        console.error('获取考试信息失败:', error)\r\n        ElMessage.error('获取考试信息失败')\r\n      }\r\n    }\r\n    \r\n    // 获取题目列表\r\n    const fetchQuestions = async () => {\r\n      loading.value = true\r\n      try {\r\n        const response = await axios.get(`http://localhost:3000/api/exams/${examId}/questions`)\r\n        questions.value = response.data.data\r\n      } catch (error) {\r\n        console.error('获取题目列表失败:', error)\r\n        ElMessage.error('获取题目列表失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 获取考试状态文本\r\n    const getExamStatusText = (status) => {\r\n      const statusMap = {\r\n        'draft': '草稿',\r\n        'published': '已发布',\r\n        'in_progress': '进行中',\r\n        'completed': '已结束'\r\n      }\r\n      return statusMap[status] || '未知状态'\r\n    }\r\n    \r\n    // 获取考试状态类型\r\n    const getExamStatusType = (status) => {\r\n      const typeMap = {\r\n        'draft': 'info',\r\n        'published': 'success',\r\n        'in_progress': 'warning',\r\n        'completed': 'danger'\r\n      }\r\n      return typeMap[status] || 'info'\r\n    }\r\n    \r\n    // 获取题目类型文本\r\n    const getQuestionTypeText = (type) => {\r\n      const typeMap = {\r\n        'single': '单选题',\r\n        'multiple': '多选题',\r\n        'true_false': '判断题'\r\n      }\r\n      return typeMap[type] || '未知类型'\r\n    }\r\n    \r\n    // 获取题目类型标签\r\n    const getQuestionTypeTag = (type) => {\r\n      const tagMap = {\r\n        'single': 'primary',\r\n        'multiple': 'success',\r\n        'true_false': 'warning'\r\n      }\r\n      return tagMap[type] || 'info'\r\n    }\r\n    \r\n    // 格式化日期\r\n    const formatDate = (dateString) => {\r\n      if (!dateString) return '-'\r\n      const date = new Date(dateString)\r\n      return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')}`\r\n    }\r\n    \r\n    // 返回考试列表\r\n    const goBack = () => {\r\n      router.push('/exams/list')\r\n    }\r\n    \r\n    // 打开添加对话框\r\n    const openAddDialog = () => {\r\n      isEdit.value = false\r\n      resetFormData()\r\n      dialogVisible.value = true\r\n    }\r\n    \r\n    // 编辑题目\r\n    const editQuestion = (row) => {\r\n      isEdit.value = true\r\n      currentQuestion.value = row\r\n      \r\n      // 填充表单数据\r\n      Object.assign(formData, {\r\n        id: row.id,\r\n        type: row.type,\r\n        content: row.content,\r\n        score: row.score,\r\n        explanation: row.explanation || '',\r\n        exam_id: examId\r\n      })\r\n      \r\n      // 处理选项和答案\r\n      if (row.type === 'true_false') {\r\n        formData.true_false_answer = row.true_false_answer\r\n      } else {\r\n        formData.options = [...row.options]\r\n        if (row.type === 'single') {\r\n          formData.correct_option = row.options.findIndex(opt => opt.is_correct)\r\n        }\r\n      }\r\n      \r\n      dialogVisible.value = true\r\n    }\r\n    \r\n    // 查看题目\r\n    const viewQuestion = (row) => {\r\n      currentQuestion.value = row\r\n      viewDialogVisible.value = true\r\n    }\r\n    \r\n    // 删除题目\r\n    const deleteQuestion = (row) => {\r\n      ElMessageBox.confirm(\r\n        '确定要删除该题目吗？',\r\n        '警告',\r\n        {\r\n          confirmButtonText: '确定',\r\n          cancelButtonText: '取消',\r\n          type: 'warning',\r\n        }\r\n      )\r\n        .then(async () => {\r\n          try {\r\n            await axios.delete(`http://localhost:3000/api/exams/questions/${row.id}`)\r\n            ElMessage.success('删除成功')\r\n            fetchQuestions()\r\n          } catch (error) {\r\n            console.error('删除失败:', error)\r\n            ElMessage.error('删除失败')\r\n          }\r\n        })\r\n        .catch(() => {\r\n          ElMessage.info('已取消删除')\r\n        })\r\n    }\r\n    \r\n    // 添加选项\r\n    const addOption = () => {\r\n      if (formData.options.length < 6) {\r\n        formData.options.push({ content: '', is_correct: false })\r\n      }\r\n    }\r\n    \r\n    // 移除选项\r\n    const removeOption = (index) => {\r\n      if (formData.options.length > 2) {\r\n        formData.options.splice(index, 1)\r\n        \r\n        // 如果删除的是正确答案，重置正确答案\r\n        if (formData.type === 'single' && formData.correct_option === index) {\r\n          formData.correct_option = 0\r\n        } else if (formData.type === 'single' && formData.correct_option > index) {\r\n          formData.correct_option--\r\n        }\r\n      }\r\n    }\r\n    \r\n    // 重置表单数据\r\n    const resetFormData = () => {\r\n      Object.assign(formData, {\r\n        id: '',\r\n        type: 'single',\r\n        content: '',\r\n        score: 5,\r\n        options: [\r\n          { content: '', is_correct: false },\r\n          { content: '', is_correct: false },\r\n          { content: '', is_correct: false },\r\n          { content: '', is_correct: false }\r\n        ],\r\n        correct_option: 0,\r\n        true_false_answer: true,\r\n        explanation: '',\r\n        exam_id: examId\r\n      })\r\n      \r\n      if (questionFormRef.value) {\r\n        questionFormRef.value.resetFields()\r\n      }\r\n    }\r\n    \r\n    // 提交表单\r\n    const submitForm = async () => {\r\n      if (!questionFormRef.value) return\r\n      \r\n      await questionFormRef.value.validate(async (valid) => {\r\n        if (valid) {\r\n          // 处理单选题答案\r\n          if (formData.type === 'single') {\r\n            formData.options.forEach((opt, index) => {\r\n              opt.is_correct = index === formData.correct_option\r\n            })\r\n          }\r\n          \r\n          try {\r\n            if (isEdit.value) {\r\n              // 编辑模式\r\n              await axios.put(`http://localhost:3000/api/exams/questions/${formData.id}`, formData)\r\n              ElMessage.success('题目更新成功')\r\n            } else {\r\n              // 添加模式\r\n              await axios.post('http://localhost:3000/api/exams/questions', formData)\r\n              ElMessage.success('题目添加成功')\r\n            }\r\n            \r\n            dialogVisible.value = false\r\n            fetchQuestions()\r\n          } catch (error) {\r\n            console.error('操作失败:', error)\r\n            ElMessage.error('操作失败')\r\n          }\r\n        } else {\r\n          return false\r\n        }\r\n      })\r\n    }\r\n    \r\n    // 导入题库\r\n    const importQuestions = () => {\r\n      importDialogVisible.value = true\r\n    }\r\n    \r\n    // 下载模板\r\n    const downloadTemplate = () => {\r\n      window.open('http://localhost:3000/api/exams/questions/template', '_blank')\r\n    }\r\n    \r\n    // 上传前验证\r\n    const beforeImportUpload = (file) => {\r\n      const isExcel = file.type === 'application/vnd.ms-excel' || \r\n                      file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'\r\n      const isLt2M = file.size / 1024 / 1024 < 2\r\n      \r\n      if (!isExcel) {\r\n        ElMessage.error('只能上传Excel文件!')\r\n      }\r\n      if (!isLt2M) {\r\n        ElMessage.error('文件大小不能超过2MB!')\r\n      }\r\n      \r\n      return isExcel && isLt2M\r\n    }\r\n    \r\n    // 导入成功\r\n    const handleImportSuccess = (response) => {\r\n      ElMessage.success(`成功导入${response.data.count}道题目`)\r\n      importDialogVisible.value = false\r\n      fetchQuestions()\r\n    }\r\n    \r\n    // 导入失败\r\n    const handleImportError = (error) => {\r\n      console.error('导入失败:', error)\r\n      ElMessage.error('导入失败，请检查文件格式是否正确')\r\n    }\r\n    \r\n    return {\r\n      loading,\r\n      examData,\r\n      questions,\r\n      dialogVisible,\r\n      viewDialogVisible,\r\n      importDialogVisible,\r\n      isEdit,\r\n      formData,\r\n      formRules,\r\n      questionFormRef,\r\n      currentQuestion,\r\n      examId,\r\n      singleChoiceCount,\r\n      multipleChoiceCount,\r\n      trueFalseCount,\r\n      totalScore,\r\n      getExamStatusText,\r\n      getExamStatusType,\r\n      getQuestionTypeText,\r\n      getQuestionTypeTag,\r\n      formatDate,\r\n      goBack,\r\n      openAddDialog,\r\n      editQuestion,\r\n      viewQuestion,\r\n      deleteQuestion,\r\n      addOption,\r\n      removeOption,\r\n      submitForm,\r\n      importQuestions,\r\n      downloadTemplate,\r\n      beforeImportUpload,\r\n      handleImportSuccess,\r\n      handleImportError\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.exam-questions-container {\r\n  padding: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.exam-info {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.question-list {\r\n  margin-top: 20px;\r\n}\r\n\r\n.question-stats {\r\n  display: flex;\r\n  margin-bottom: 20px;\r\n  background-color: #f7f7f7;\r\n  padding: 15px;\r\n  border-radius: 4px;\r\n}\r\n\r\n.stat-item {\r\n  flex: 1;\r\n  text-align: center;\r\n  border-right: 1px solid #eee;\r\n}\r\n\r\n.stat-item:last-child {\r\n  border-right: none;\r\n}\r\n\r\n.stat-value {\r\n  font-size: 24px;\r\n  font-weight: bold;\r\n  color: #303133;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-top: 5px;\r\n}\r\n\r\n.option-item {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.option-content {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.option-radio {\r\n  margin-left: 10px;\r\n}\r\n\r\n.add-option {\r\n  display: flex;\r\n  justify-content: center;\r\n  margin-top: 10px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.question-detail {\r\n  padding: 10px;\r\n}\r\n\r\n.question-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.question-score {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  color: #f56c6c;\r\n}\r\n\r\n.question-content {\r\n  font-size: 16px;\r\n  margin-bottom: 20px;\r\n  padding: 10px;\r\n  background-color: #f7f7f7;\r\n  border-radius: 4px;\r\n}\r\n\r\n.options-list {\r\n  margin-top: 15px;\r\n}\r\n\r\n.options-list .option-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 10px;\r\n  border-bottom: 1px solid #eee;\r\n}\r\n\r\n.options-list .option-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.options-list .option-item.correct-option {\r\n  background-color: #f0f9eb;\r\n}\r\n\r\n.options-list .option-label {\r\n  width: 30px;\r\n  height: 30px;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  border-radius: 50%;\r\n  background-color: #f2f6fc;\r\n  margin-right: 10px;\r\n  font-weight: bold;\r\n}\r\n\r\n.options-list .option-content {\r\n  flex: 1;\r\n}\r\n\r\n.options-list .correct-mark {\r\n  color: #67c23a;\r\n  margin-left: 10px;\r\n}\r\n\r\n.true-false-answer {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-top: 15px;\r\n}\r\n\r\n.answer-label {\r\n  font-weight: bold;\r\n  margin-right: 10px;\r\n}\r\n\r\n.question-explanation {\r\n  margin-top: 20px;\r\n  padding: 10px;\r\n  background-color: #f7f7f7;\r\n  border-radius: 4px;\r\n}\r\n\r\n.explanation-label {\r\n  font-weight: bold;\r\n  margin-bottom: 5px;\r\n}\r\n\r\n.explanation-content {\r\n  color: #606266;\r\n  white-space: pre-line;\r\n}\r\n</style> "], "mappings": ";;;;;AA6QA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAI;AACvD,SAASC,QAAQ,EAAEC,SAAQ,QAAS,YAAW;AAC/C,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,SAASC,KAAK,EAAEC,YAAW,QAAS,yBAAwB;AAC5D,OAAOC,KAAI,MAAO,OAAM;AAExB,eAAe;EACbC,IAAI,EAAE,eAAe;EACrBC,UAAU,EAAE;IACVJ,KAAK;IACLC;EACF,CAAC;EACDI,KAAKA,CAAA,EAAG;IACJ,IAAIC,KAAI,GAAIC,YAAY,CAACC,OAAO,CAAC,OAAO;IAC1C,IAAIF,KAAK,EAAE;MACTJ,KAAK,CAACO,QAAQ,CAACC,OAAO,CAACC,MAAM,CAAC,eAAe,IAAI,UAAUL,KAAK,EAAC;IACnE;IACA,MAAMM,KAAI,GAAIhB,QAAQ,CAAC;IACvB,MAAMiB,MAAK,GAAIhB,SAAS,CAAC;IACzB,MAAMiB,MAAK,GAAIF,KAAK,CAACG,MAAM,CAACC,EAAC;;IAE7B;IACA,MAAMC,OAAM,GAAIzB,GAAG,CAAC,KAAK;IACzB,MAAM0B,QAAO,GAAI1B,GAAG,CAAC,IAAI;IACzB,MAAM2B,SAAQ,GAAI3B,GAAG,CAAC,EAAE;IACxB,MAAM4B,eAAc,GAAI5B,GAAG,CAAC,IAAI;;IAEhC;IACA,MAAM6B,aAAY,GAAI7B,GAAG,CAAC,KAAK;IAC/B,MAAM8B,iBAAgB,GAAI9B,GAAG,CAAC,KAAK;IACnC,MAAM+B,mBAAkB,GAAI/B,GAAG,CAAC,KAAK;IACrC,MAAMgC,MAAK,GAAIhC,GAAG,CAAC,KAAK;IACxB,MAAMiC,eAAc,GAAIjC,GAAG,CAAC,IAAI;;IAEhC;IACA,MAAMkC,QAAO,GAAIjC,QAAQ,CAAC;MACxBuB,EAAE,EAAE,EAAE;MACNW,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,EAAE;MACXC,KAAK,EAAE,CAAC;MACRC,OAAO,EAAE,CACP;QAAEF,OAAO,EAAE,EAAE;QAAEG,UAAU,EAAE;MAAM,CAAC,EAClC;QAAEH,OAAO,EAAE,EAAE;QAAEG,UAAU,EAAE;MAAM,CAAC,EAClC;QAAEH,OAAO,EAAE,EAAE;QAAEG,UAAU,EAAE;MAAM,CAAC,EAClC;QAAEH,OAAO,EAAE,EAAE;QAAEG,UAAU,EAAE;MAAM,EAClC;MACDC,cAAc,EAAE,CAAC;MACjBC,iBAAiB,EAAE,IAAI;MACvBC,WAAW,EAAE,EAAE;MACfC,OAAO,EAAErB;IACX,CAAC;;IAED;IACA,MAAMsB,SAAQ,GAAI;MAChBT,IAAI,EAAE,CACJ;QAAEU,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAS,EACzD;MACDX,OAAO,EAAE,CACP;QAAES,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,EACvD;MACDV,KAAK,EAAE,CACL;QAAEQ,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAS;IAE1D;;IAEA;IACA,MAAMC,iBAAgB,GAAI9C,QAAQ,CAAC,MAAM;MACvC,OAAOyB,SAAS,CAACsB,KAAK,CAACC,MAAM,CAACC,CAAA,IAAKA,CAAC,CAAChB,IAAG,KAAM,QAAQ,CAAC,CAACiB,MAAK;IAC/D,CAAC;IAED,MAAMC,mBAAkB,GAAInD,QAAQ,CAAC,MAAM;MACzC,OAAOyB,SAAS,CAACsB,KAAK,CAACC,MAAM,CAACC,CAAA,IAAKA,CAAC,CAAChB,IAAG,KAAM,UAAU,CAAC,CAACiB,MAAK;IACjE,CAAC;IAED,MAAME,cAAa,GAAIpD,QAAQ,CAAC,MAAM;MACpC,OAAOyB,SAAS,CAACsB,KAAK,CAACC,MAAM,CAACC,CAAA,IAAKA,CAAC,CAAChB,IAAG,KAAM,YAAY,CAAC,CAACiB,MAAK;IACnE,CAAC;IAED,MAAMG,UAAS,GAAIrD,QAAQ,CAAC,MAAM;MAChC,OAAOyB,SAAS,CAACsB,KAAK,CAACO,MAAM,CAAC,CAACC,GAAG,EAAEN,CAAC,KAAKM,GAAE,GAAIN,CAAC,CAACd,KAAK,EAAE,CAAC;IAC5D,CAAC;;IAED;IACAlC,SAAS,CAAC,MAAM;MACduD,aAAa,CAAC;MACdC,cAAc,CAAC;IACjB,CAAC;;IAED;IACA,MAAMD,aAAY,GAAI,MAAAA,CAAA,KAAY;MAChC,IAAI;QACF,MAAME,QAAO,GAAI,MAAMlD,KAAK,CAACmD,GAAG,CAAC,mCAAmCvC,MAAM,EAAE;QAC5EI,QAAQ,CAACuB,KAAI,GAAIW,QAAQ,CAACE,IAAI,CAACA,IAAG;MACpC,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChCzD,SAAS,CAACyD,KAAK,CAAC,UAAU;MAC5B;IACF;;IAEA;IACA,MAAMJ,cAAa,GAAI,MAAAA,CAAA,KAAY;MACjClC,OAAO,CAACwB,KAAI,GAAI,IAAG;MACnB,IAAI;QACF,MAAMW,QAAO,GAAI,MAAMlD,KAAK,CAACmD,GAAG,CAAC,mCAAmCvC,MAAM,YAAY;QACtFK,SAAS,CAACsB,KAAI,GAAIW,QAAQ,CAACE,IAAI,CAACA,IAAG;MACrC,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChCzD,SAAS,CAACyD,KAAK,CAAC,UAAU;MAC5B,UAAU;QACRtC,OAAO,CAACwB,KAAI,GAAI,KAAI;MACtB;IACF;;IAEA;IACA,MAAMgB,iBAAgB,GAAKC,MAAM,IAAK;MACpC,MAAMC,SAAQ,GAAI;QAChB,OAAO,EAAE,IAAI;QACb,WAAW,EAAE,KAAK;QAClB,aAAa,EAAE,KAAK;QACpB,WAAW,EAAE;MACf;MACA,OAAOA,SAAS,CAACD,MAAM,KAAK,MAAK;IACnC;;IAEA;IACA,MAAME,iBAAgB,GAAKF,MAAM,IAAK;MACpC,MAAMG,OAAM,GAAI;QACd,OAAO,EAAE,MAAM;QACf,WAAW,EAAE,SAAS;QACtB,aAAa,EAAE,SAAS;QACxB,WAAW,EAAE;MACf;MACA,OAAOA,OAAO,CAACH,MAAM,KAAK,MAAK;IACjC;;IAEA;IACA,MAAMI,mBAAkB,GAAKnC,IAAI,IAAK;MACpC,MAAMkC,OAAM,GAAI;QACd,QAAQ,EAAE,KAAK;QACf,UAAU,EAAE,KAAK;QACjB,YAAY,EAAE;MAChB;MACA,OAAOA,OAAO,CAAClC,IAAI,KAAK,MAAK;IAC/B;;IAEA;IACA,MAAMoC,kBAAiB,GAAKpC,IAAI,IAAK;MACnC,MAAMqC,MAAK,GAAI;QACb,QAAQ,EAAE,SAAS;QACnB,UAAU,EAAE,SAAS;QACrB,YAAY,EAAE;MAChB;MACA,OAAOA,MAAM,CAACrC,IAAI,KAAK,MAAK;IAC9B;;IAEA;IACA,MAAMsC,UAAS,GAAKC,UAAU,IAAK;MACjC,IAAI,CAACA,UAAU,EAAE,OAAO,GAAE;MAC1B,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU;MAChC,OAAO,GAAGC,IAAI,CAACE,WAAW,CAAC,CAAC,IAAI,CAACF,IAAI,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAEC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIL,IAAI,CAACM,OAAO,CAAC,CAAC,CAACF,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAC;IAClI;;IAEA;IACA,MAAME,MAAK,GAAIA,CAAA,KAAM;MACnB7D,MAAM,CAAC8D,IAAI,CAAC,aAAa;IAC3B;;IAEA;IACA,MAAMC,aAAY,GAAIA,CAAA,KAAM;MAC1BpD,MAAM,CAACiB,KAAI,GAAI,KAAI;MACnBoC,aAAa,CAAC;MACdxD,aAAa,CAACoB,KAAI,GAAI,IAAG;IAC3B;;IAEA;IACA,MAAMqC,YAAW,GAAKC,GAAG,IAAK;MAC5BvD,MAAM,CAACiB,KAAI,GAAI,IAAG;MAClBhB,eAAe,CAACgB,KAAI,GAAIsC,GAAE;;MAE1B;MACAC,MAAM,CAACC,MAAM,CAACvD,QAAQ,EAAE;QACtBV,EAAE,EAAE+D,GAAG,CAAC/D,EAAE;QACVW,IAAI,EAAEoD,GAAG,CAACpD,IAAI;QACdC,OAAO,EAAEmD,GAAG,CAACnD,OAAO;QACpBC,KAAK,EAAEkD,GAAG,CAAClD,KAAK;QAChBK,WAAW,EAAE6C,GAAG,CAAC7C,WAAU,IAAK,EAAE;QAClCC,OAAO,EAAErB;MACX,CAAC;;MAED;MACA,IAAIiE,GAAG,CAACpD,IAAG,KAAM,YAAY,EAAE;QAC7BD,QAAQ,CAACO,iBAAgB,GAAI8C,GAAG,CAAC9C,iBAAgB;MACnD,OAAO;QACLP,QAAQ,CAACI,OAAM,GAAI,CAAC,GAAGiD,GAAG,CAACjD,OAAO;QAClC,IAAIiD,GAAG,CAACpD,IAAG,KAAM,QAAQ,EAAE;UACzBD,QAAQ,CAACM,cAAa,GAAI+C,GAAG,CAACjD,OAAO,CAACoD,SAAS,CAACC,GAAE,IAAKA,GAAG,CAACpD,UAAU;QACvE;MACF;MAEAV,aAAa,CAACoB,KAAI,GAAI,IAAG;IAC3B;;IAEA;IACA,MAAM2C,YAAW,GAAKL,GAAG,IAAK;MAC5BtD,eAAe,CAACgB,KAAI,GAAIsC,GAAE;MAC1BzD,iBAAiB,CAACmB,KAAI,GAAI,IAAG;IAC/B;;IAEA;IACA,MAAM4C,cAAa,GAAKN,GAAG,IAAK;MAC9BhF,YAAY,CAACuF,OAAO,CAClB,YAAY,EACZ,IAAI,EACJ;QACEC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtB7D,IAAI,EAAE;MACR,CACF,EACG8D,IAAI,CAAC,YAAY;QAChB,IAAI;UACF,MAAMvF,KAAK,CAACwF,MAAM,CAAC,6CAA6CX,GAAG,CAAC/D,EAAE,EAAE;UACxElB,SAAS,CAAC6F,OAAO,CAAC,MAAM;UACxBxC,cAAc,CAAC;QACjB,EAAE,OAAOI,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;UAC5BzD,SAAS,CAACyD,KAAK,CAAC,MAAM;QACxB;MACF,CAAC,EACAqC,KAAK,CAAC,MAAM;QACX9F,SAAS,CAAC+F,IAAI,CAAC,OAAO;MACxB,CAAC;IACL;;IAEA;IACA,MAAMC,SAAQ,GAAIA,CAAA,KAAM;MACtB,IAAIpE,QAAQ,CAACI,OAAO,CAACc,MAAK,GAAI,CAAC,EAAE;QAC/BlB,QAAQ,CAACI,OAAO,CAAC6C,IAAI,CAAC;UAAE/C,OAAO,EAAE,EAAE;UAAEG,UAAU,EAAE;QAAM,CAAC;MAC1D;IACF;;IAEA;IACA,MAAMgE,YAAW,GAAKC,KAAK,IAAK;MAC9B,IAAItE,QAAQ,CAACI,OAAO,CAACc,MAAK,GAAI,CAAC,EAAE;QAC/BlB,QAAQ,CAACI,OAAO,CAACmE,MAAM,CAACD,KAAK,EAAE,CAAC;;QAEhC;QACA,IAAItE,QAAQ,CAACC,IAAG,KAAM,QAAO,IAAKD,QAAQ,CAACM,cAAa,KAAMgE,KAAK,EAAE;UACnEtE,QAAQ,CAACM,cAAa,GAAI;QAC5B,OAAO,IAAIN,QAAQ,CAACC,IAAG,KAAM,QAAO,IAAKD,QAAQ,CAACM,cAAa,GAAIgE,KAAK,EAAE;UACxEtE,QAAQ,CAACM,cAAc,EAAC;QAC1B;MACF;IACF;;IAEA;IACA,MAAM6C,aAAY,GAAIA,CAAA,KAAM;MAC1BG,MAAM,CAACC,MAAM,CAACvD,QAAQ,EAAE;QACtBV,EAAE,EAAE,EAAE;QACNW,IAAI,EAAE,QAAQ;QACdC,OAAO,EAAE,EAAE;QACXC,KAAK,EAAE,CAAC;QACRC,OAAO,EAAE,CACP;UAAEF,OAAO,EAAE,EAAE;UAAEG,UAAU,EAAE;QAAM,CAAC,EAClC;UAAEH,OAAO,EAAE,EAAE;UAAEG,UAAU,EAAE;QAAM,CAAC,EAClC;UAAEH,OAAO,EAAE,EAAE;UAAEG,UAAU,EAAE;QAAM,CAAC,EAClC;UAAEH,OAAO,EAAE,EAAE;UAAEG,UAAU,EAAE;QAAM,EAClC;QACDC,cAAc,EAAE,CAAC;QACjBC,iBAAiB,EAAE,IAAI;QACvBC,WAAW,EAAE,EAAE;QACfC,OAAO,EAAErB;MACX,CAAC;MAED,IAAIM,eAAe,CAACqB,KAAK,EAAE;QACzBrB,eAAe,CAACqB,KAAK,CAACyD,WAAW,CAAC;MACpC;IACF;;IAEA;IACA,MAAMC,UAAS,GAAI,MAAAA,CAAA,KAAY;MAC7B,IAAI,CAAC/E,eAAe,CAACqB,KAAK,EAAE;MAE5B,MAAMrB,eAAe,CAACqB,KAAK,CAAC2D,QAAQ,CAAC,MAAOC,KAAK,IAAK;QACpD,IAAIA,KAAK,EAAE;UACT;UACA,IAAI3E,QAAQ,CAACC,IAAG,KAAM,QAAQ,EAAE;YAC9BD,QAAQ,CAACI,OAAO,CAACwE,OAAO,CAAC,CAACnB,GAAG,EAAEa,KAAK,KAAK;cACvCb,GAAG,CAACpD,UAAS,GAAIiE,KAAI,KAAMtE,QAAQ,CAACM,cAAa;YACnD,CAAC;UACH;UAEA,IAAI;YACF,IAAIR,MAAM,CAACiB,KAAK,EAAE;cAChB;cACA,MAAMvC,KAAK,CAACqG,GAAG,CAAC,6CAA6C7E,QAAQ,CAACV,EAAE,EAAE,EAAEU,QAAQ;cACpF5B,SAAS,CAAC6F,OAAO,CAAC,QAAQ;YAC5B,OAAO;cACL;cACA,MAAMzF,KAAK,CAACsG,IAAI,CAAC,2CAA2C,EAAE9E,QAAQ;cACtE5B,SAAS,CAAC6F,OAAO,CAAC,QAAQ;YAC5B;YAEAtE,aAAa,CAACoB,KAAI,GAAI,KAAI;YAC1BU,cAAc,CAAC;UACjB,EAAE,OAAOI,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;YAC5BzD,SAAS,CAACyD,KAAK,CAAC,MAAM;UACxB;QACF,OAAO;UACL,OAAO,KAAI;QACb;MACF,CAAC;IACH;;IAEA;IACA,MAAMkD,eAAc,GAAIA,CAAA,KAAM;MAC5BlF,mBAAmB,CAACkB,KAAI,GAAI,IAAG;IACjC;;IAEA;IACA,MAAMiE,gBAAe,GAAIA,CAAA,KAAM;MAC7BC,MAAM,CAACC,IAAI,CAAC,oDAAoD,EAAE,QAAQ;IAC5E;;IAEA;IACA,MAAMC,kBAAiB,GAAKC,IAAI,IAAK;MACnC,MAAMC,OAAM,GAAID,IAAI,CAACnF,IAAG,KAAM,0BAAyB,IACvCmF,IAAI,CAACnF,IAAG,KAAM,mEAAkE;MAChG,MAAMqF,MAAK,GAAIF,IAAI,CAACG,IAAG,GAAI,IAAG,GAAI,IAAG,GAAI;MAEzC,IAAI,CAACF,OAAO,EAAE;QACZjH,SAAS,CAACyD,KAAK,CAAC,cAAc;MAChC;MACA,IAAI,CAACyD,MAAM,EAAE;QACXlH,SAAS,CAACyD,KAAK,CAAC,cAAc;MAChC;MAEA,OAAOwD,OAAM,IAAKC,MAAK;IACzB;;IAEA;IACA,MAAME,mBAAkB,GAAK9D,QAAQ,IAAK;MACxCtD,SAAS,CAAC6F,OAAO,CAAC,OAAOvC,QAAQ,CAACE,IAAI,CAAC6D,KAAK,KAAK;MACjD5F,mBAAmB,CAACkB,KAAI,GAAI,KAAI;MAChCU,cAAc,CAAC;IACjB;;IAEA;IACA,MAAMiE,iBAAgB,GAAK7D,KAAK,IAAK;MACnCC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;MAC5BzD,SAAS,CAACyD,KAAK,CAAC,kBAAkB;IACpC;IAEA,OAAO;MACLtC,OAAO;MACPC,QAAQ;MACRC,SAAS;MACTE,aAAa;MACbC,iBAAiB;MACjBC,mBAAmB;MACnBC,MAAM;MACNE,QAAQ;MACRU,SAAS;MACThB,eAAe;MACfK,eAAe;MACfX,MAAM;MACN0B,iBAAiB;MACjBK,mBAAmB;MACnBC,cAAc;MACdC,UAAU;MACVU,iBAAiB;MACjBG,iBAAiB;MACjBE,mBAAmB;MACnBC,kBAAkB;MAClBE,UAAU;MACVS,MAAM;MACNE,aAAa;MACbE,YAAY;MACZM,YAAY;MACZC,cAAc;MACdS,SAAS;MACTC,YAAY;MACZI,UAAU;MACVM,eAAe;MACfC,gBAAgB;MAChBG,kBAAkB;MAClBK,mBAAmB;MACnBE;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}