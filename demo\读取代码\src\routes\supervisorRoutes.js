const express = require('express');
const router = express.Router();
const supervisorController = require('../controllers/supervisorController');

// 获取所有督导成员
router.get('/', supervisorController.getAllSupervisors);

// 根据ID获取督导成员
router.get('/:id', supervisorController.getSupervisorById);

// 创建新督导成员
router.post('/', supervisorController.createSupervisor);

// 更新督导成员
router.put('/:id', supervisorController.updateSupervisor);

// 删除督导成员
router.delete('/:id', supervisorController.deleteSupervisor);

module.exports = router; 