import api from '../utils/api'

export default {
  // 获取考试列表
  getExams(params) {
    return api.get('/api/exams', { params })
  },
  
  // 获取单个考试详情
  getExam(id) {
    return api.get(`/api/exams/${id}`)
  },
  
  // 创建考试
  createExam(examData) {
    return api.post('/api/exams', examData)
  },
  
  // 更新考试
  updateExam(id, examData) {
    return api.put(`/api/exams/${id}`, examData)
  },
  
  // 删除考试
  deleteExam(id) {
    return api.delete(`/api/exams/${id}`)
  },
  
  // 获取考试题目
  getExamQuestions(examId) {
    return api.get(`/api/exams/${examId}/questions`)
  },
  
  // 创建考试题目
  createQuestion(examId, questionData) {
    return api.post(`/api/exams/${examId}/questions`, questionData)
  },
  
  // 更新考试题目
  updateQuestion(questionId, questionData) {
    return api.put(`/api/exams/questions/${questionId}`, questionData)
  },
  
  // 删除考试题目
  deleteQuestion(questionId) {
    return api.delete(`/api/exams/questions/${questionId}`)
  },
  
  // 批量导入考试题目
  importQuestions(examId, questions) {
    return api.post(`/api/exams/${examId}/questions/import`, questions)
  },
  
  // 从Word文档导入考试题目
  importQuestionsFromWord(examId, formData) {
    return api.post(`/api/exams/${examId}/questions/import-word`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  // 提交考试答案
  submitExam(submitData) {
    return api.post(`/api/exams/${submitData.exam_id}/submit`, submitData)
  },
  
  // 获取考试成绩汇总
  getExamResults(examId, params) {
    return api.get(`/api/exams/${examId}/results`)
  },
  
  // 获取学生的所有考试成绩
  getStudentResults(studentId) {
    return api.get(`/api/exams/student/${studentId}/results`)
  },
  
  // 获取教师的所有考试成绩
  getTeacherResults(teacherId) {
    return api.get(`/api/exams/teacher/${teacherId}/results`)
  }
} 