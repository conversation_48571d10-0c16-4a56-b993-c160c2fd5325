const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const authController = require('../controllers/authController');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const XLSX = require('xlsx');
const bcrypt = require('bcryptjs');
const { pool } = require('../config/db');

// 配置Excel文件上传
const excelStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = path.join(__dirname, '../../uploads/excel');
    // 确保目录存在
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    cb(null, 'users-import-' + uniqueSuffix + ext);
  }
});

const uploadExcel = multer({
  storage: excelStorage,
  fileFilter: function (req, file, cb) {
    // 只允许Excel文件
    const allowedTypes = ['.xlsx', '.xls'];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
      cb(null, true);
    } else {
      cb(new Error('只支持Excel文件格式(.xlsx, .xls)'));
    }
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 限制文件大小为10MB
  }
});

// 保护所有用户管理路由 - 需要管理员权限
router.use(authController.protect);
router.use(authController.authorize('admin'));

// 获取所有用户
router.get('/', userController.getAllUsers);

// 创建用户
router.post('/', userController.createUser);

// 批量删除用户
router.delete('/batch', userController.batchDeleteUsers);

// 获取单个用户
router.get('/:id', userController.getUser);

// 更新用户
router.put('/:id', userController.updateUser);

// 更新用户状态
router.put('/:id/status', userController.updateUserStatus);

// 删除用户
router.delete('/:id', userController.deleteUser);

// 批量导入用户 - Excel文件上传
router.post('/import/excel', uploadExcel.single('excel'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: '请上传Excel文件'
      });
    }

    const filePath = req.file.path;

    try {
      // 读取Excel文件
      const workbook = XLSX.readFile(filePath);
      const sheetName = workbook.SheetNames[0]; // 读取第一个工作表
      const worksheet = workbook.Sheets[sheetName];

      // 将工作表转换为JSON数组
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

      if (jsonData.length < 2) {
        return res.status(400).json({
          success: false,
          message: 'Excel文件内容为空或格式不正确'
        });
      }

      // 获取表头和数据
      const headers = jsonData[0];
      const dataRows = jsonData.slice(1);

      // 验证必要的列是否存在
      const requiredColumns = ['用户名', '密码', '角色', '姓名'];
      const missingColumns = requiredColumns.filter(col => !headers.includes(col));

      if (missingColumns.length > 0) {
        return res.status(400).json({
          success: false,
          message: `Excel文件缺少必要的列: ${missingColumns.join(', ')}`
        });
      }

      // 获取列索引
      const columnIndexes = {
        username: headers.indexOf('用户名'),
        password: headers.indexOf('密码'),
        role: headers.indexOf('角色'),
        name: headers.indexOf('姓名'),
        email: headers.indexOf('邮箱'),
        phone: headers.indexOf('电话'),
        status: headers.indexOf('状态')
      };

      const successRecords = [];
      const failedRecords = [];

      // 开始事务
      const connection = await pool.getConnection();
      await connection.beginTransaction();

      try {
        for (let i = 0; i < dataRows.length; i++) {
          const row = dataRows[i];
          const rowNumber = i + 2; // Excel行号（从第2行开始）

          try {
            // 提取数据
            const username = row[columnIndexes.username];
            const password = row[columnIndexes.password];
            const role = row[columnIndexes.role];
            const name = row[columnIndexes.name];
            const email = row[columnIndexes.email];
            const phone = row[columnIndexes.phone];
            const status = row[columnIndexes.status];

            // 验证必填字段
            if (!username || !password || !role || !name) {
              failedRecords.push({
                row: rowNumber,
                data: row,
                error: '缺少必填字段（用户名、密码、角色、姓名）'
              });
              continue;
            }

            // 验证角色
            if (!['admin', 'teacher'].includes(role)) {
              failedRecords.push({
                row: rowNumber,
                data: row,
                error: '角色必须是"admin"或"teacher"'
              });
              continue;
            }

            // 检查用户名是否已存在
            const [existingUser] = await connection.query(
              'SELECT id FROM users WHERE username = ?',
              [username]
            );

            if (existingUser.length > 0) {
              failedRecords.push({
                row: rowNumber,
                data: row,
                error: '用户名已存在'
              });
              continue;
            }

            // 处理状态字段
            let statusValue = 1; // 默认启用
            if (status !== undefined && status !== null && status !== '') {
              const statusStr = String(status).toLowerCase();
              if (['禁用', 'false', '0', 'no', '否'].includes(statusStr)) {
                statusValue = 0;
              }
            }

            // 加密密码
            const salt = await bcrypt.genSalt(10);
            const hashedPassword = await bcrypt.hash(password, salt);

            // 插入数据库
            await connection.query(`
              INSERT INTO users (
                username, password, role, name, email, phone, status
              ) VALUES (?, ?, ?, ?, ?, ?, ?)
            `, [
              username,
              hashedPassword,
              role,
              name,
              email || null,
              phone || null,
              statusValue
            ]);

            successRecords.push({
              row: rowNumber,
              username: username,
              name: name
            });

          } catch (error) {
            failedRecords.push({
              row: rowNumber,
              data: row,
              error: error.message
            });
          }
        }

        // 提交事务
        await connection.commit();
        connection.release();

        // 删除临时文件
        fs.unlinkSync(filePath);

        res.status(200).json({
          success: true,
          message: `导入完成！成功导入 ${successRecords.length} 条记录，失败 ${failedRecords.length} 条记录`,
          data: {
            total: dataRows.length,
            success: successRecords.length,
            failed: failedRecords.length,
            successRecords: successRecords,
            failedRecords: failedRecords
          }
        });

      } catch (error) {
        // 回滚事务
        await connection.rollback();
        connection.release();
        throw error;
      }

    } catch (error) {
      // 删除临时文件
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }
      throw error;
    }

  } catch (error) {
    console.error('批量导入用户失败:', error);

    // 删除临时文件
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    // 返回具体的错误信息
    if (error.code === 'ECONNRESET' || error.code === 'PROTOCOL_CONNECTION_LOST') {
      return res.status(500).json({
        success: false,
        message: '数据库连接失败，请稍后重试'
      });
    }

    res.status(500).json({
      success: false,
      message: error.message || '导入失败，请检查文件格式'
    });
  }
});

// 下载用户导入模板
router.get('/import/template', (req, res) => {
  try {
    // 创建模板数据
    const templateData = [
      ['用户名', '密码', '角色', '姓名', '邮箱', '电话', '状态'],
      ['zhangsan', '123456', 'teacher', '张三', '<EMAIL>', '13800138000', '启用'],
      ['lisi', '123456', 'admin', '李四', '<EMAIL>', '13900139000', '启用']
    ];

    // 创建工作簿
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.aoa_to_sheet(templateData);

    // 设置列宽
    worksheet['!cols'] = [
      { wch: 15 }, // 用户名
      { wch: 10 }, // 密码
      { wch: 10 }, // 角色
      { wch: 10 }, // 姓名
      { wch: 25 }, // 邮箱
      { wch: 15 }, // 电话
      { wch: 10 }  // 状态
    ];

    XLSX.utils.book_append_sheet(workbook, worksheet, '用户信息');

    // 生成Excel文件
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename="user_import_template.xlsx"');
    res.send(buffer);

  } catch (error) {
    console.error('下载模板失败:', error);
    res.status(500).json({
      success: false,
      message: '下载模板失败'
    });
  }
});

module.exports = router;