{"version": 3, "file": "js/959.f816cb49.js", "mappings": "iLACOA,MAAM,6B,GAwFFA,MAAM,wB,2WAxFfC,EAAAA,EAAAA,IAoGM,MApGNC,EAoGM,EAnGJC,EAAAA,EAAAA,IAkGUC,EAAA,CAlGDJ,MAAM,YAAU,CACZK,QAAMC,EAAAA,EAAAA,IACf,IAEMC,EAAA,KAAAA,EAAA,KAFNC,EAAAA,EAAAA,IAEM,OAFDR,MAAM,eAAa,EACtBQ,EAAAA,EAAAA,IAAiC,QAA3BR,MAAM,SAAQ,Y,uBAKxB,IAaU,EAbVG,EAAAA,EAAAA,IAaUM,EAAA,CAbAC,QAAQ,EAAOC,MAAOC,EAAAC,WAAYb,MAAM,e,kBAChD,IAEe,EAFfG,EAAAA,EAAAA,IAEeW,EAAA,CAFDC,MAAM,QAAM,C,iBACxB,IAAmE,EAAnEZ,EAAAA,EAAAA,IAAmEa,EAAA,C,WAAhDJ,EAAAC,WAAWI,K,qCAAXL,EAAAC,WAAWI,KAAIC,GAAEC,YAAY,OAAOC,UAAA,I,gCAEzDjB,EAAAA,EAAAA,IAEeW,EAAA,CAFDC,MAAM,MAAI,C,iBACtB,IAAyE,EAAzEZ,EAAAA,EAAAA,IAAyEa,EAAA,C,WAAtDJ,EAAAC,WAAWQ,W,qCAAXT,EAAAC,WAAWQ,WAAUH,GAAEC,YAAY,OAAOC,UAAA,I,gCAI/DjB,EAAAA,EAAAA,IAGeW,EAAA,M,iBAFb,IAA8D,EAA9DX,EAAAA,EAAAA,IAA8DmB,EAAA,CAAnDC,KAAK,UAAWC,QAAOZ,EAAAa,c,kBAAc,IAAElB,EAAA,KAAAA,EAAA,K,QAAF,S,4BAChDJ,EAAAA,EAAAA,IAA8CmB,EAAA,CAAlCE,QAAOZ,EAAAc,aAAW,C,iBAAE,IAAEnB,EAAA,KAAAA,EAAA,K,QAAF,S,6EAKpCoB,EAAAA,EAAAA,IA4DWC,EAAA,CA1DRC,KAAMjB,EAAAkB,YACPC,OAAA,GACAC,MAAA,gB,kBAEA,IAAyC,EAAzC7B,EAAAA,EAAAA,IAAyC8B,EAAA,CAAxBC,KAAK,KAAKnB,MAAM,QACjCZ,EAAAA,EAAAA,IAA6C8B,EAAA,CAA5BC,KAAK,OAAOnB,MAAM,UACnCZ,EAAAA,EAAAA,IAA6C8B,EAAA,CAA5BC,KAAK,SAASnB,MAAM,QACrCZ,EAAAA,EAAAA,IAAiD8B,EAAA,CAAhCC,KAAK,aAAanB,MAAM,QACzCZ,EAAAA,EAAAA,IAWkB8B,EAAA,CAXDlB,MAAM,KAAKoB,MAAM,O,CACrBC,SAAO9B,EAAAA,EAAAA,IAOd+B,GAPqB,CAEfA,EAAMC,IAAIC,Q,WADlBZ,EAAAA,EAAAA,IAMEa,EAAA,C,MAJCC,IAAG,0BAA4BJ,EAAMC,IAAIC,QACzC,mBAAgB,2BAA6BF,EAAMC,IAAIC,SACxDG,IAAI,QACJV,MAAA,8B,iDAEFL,EAAAA,EAAAA,IAAiDgB,EAAA,C,MAA9BC,KAAM,GAAIC,KAAK,kB,OAGtC1C,EAAAA,EAAAA,IAIkB8B,EAAA,CAJDlB,MAAM,OAAOoB,MAAM,O,CACvBC,SAAO9B,EAAAA,EAAAA,IAC+C+B,GADxC,E,iBACpBzB,EAAAkC,kBAAkBT,EAAMC,IAAIS,GAAI,sBAAwB,GAA1B,K,OAGrC5C,EAAAA,EAAAA,IAIkB8B,EAAA,CAJDlB,MAAM,OAAOoB,MAAM,O,CACvBC,SAAO9B,EAAAA,EAAAA,IAC4C+B,GADrC,E,iBACpBzB,EAAAkC,kBAAkBT,EAAMC,IAAIS,GAAI,mBAAqB,GAAvB,K,OAGrC5C,EAAAA,EAAAA,IAQkB8B,EAAA,CARDlB,MAAM,MAAMoB,MAAM,O,CACtBC,SAAO9B,EAAAA,EAAAA,IAKd+B,GALqB,EACvBlC,EAAAA,EAAAA,IAIE6C,EAAA,CAHCC,WAAYrC,EAAAkC,kBAAkBT,EAAMC,IAAIS,GAAI,kBAAoB,EAChEG,OAAQtC,EAAAuC,cACRC,OAAQxC,EAAAyC,oBAAoBhB,EAAMC,IAAIS,K,kDAI7C5C,EAAAA,EAAAA,IAMkB8B,EAAA,CANDlB,MAAM,OAAOoB,MAAM,O,CACvBC,SAAO9B,EAAAA,EAAAA,IAGP+B,GAHc,EACvBlC,EAAAA,EAAAA,IAESmD,EAAA,CAFA/B,KAAMX,EAAA2C,YAAYlB,EAAMC,IAAIS,IAAM,UAAY,Q,kBACrD,IAA+C,E,iBAA5CnC,EAAA2C,YAAYlB,EAAMC,IAAIS,IAAM,MAAQ,OAAZ,K,6BAIjC5C,EAAAA,EAAAA,IAWkB8B,EAAA,CAXDlB,MAAM,KAAKoB,MAAM,MAAMqB,MAAM,S,CACjCpB,SAAO9B,EAAAA,EAAAA,IACyD+B,GADlD,EACvBlC,EAAAA,EAAAA,IAAyEmB,EAAA,CAA9DsB,KAAK,QAASpB,QAAKN,GAAEN,EAAA6C,YAAYpB,EAAMC,IAAIS,K,kBAAK,IAAExC,EAAA,KAAAA,EAAA,K,QAAF,S,+BAC3DJ,EAAAA,EAAAA,IAMYmB,EAAA,CALVsB,KAAK,QACLrB,KAAK,UACJC,QAAKN,GAAEN,EAAA8C,gBAAgBrB,EAAMC,IAAIS,K,kBACnC,IAEDxC,EAAA,KAAAA,EAAA,K,QAFC,a,8DAtDMK,EAAA+C,YA8DbnD,EAAAA,EAAAA,IAUM,MAVNoD,EAUM,EATJzD,EAAAA,EAAAA,IAQE0D,EAAA,CAPQ,eAAcjD,EAAAkD,Y,sCAAAlD,EAAAkD,YAAW5C,GACzB,YAAWN,EAAAmD,S,mCAAAnD,EAAAmD,SAAQ7C,GAC1B,aAAY,CAAC,GAAI,GAAI,GAAI,KAC1B8C,OAAO,0CACNC,MAAOrD,EAAAqD,MACPC,aAAatD,EAAAuD,iBACbC,gBAAgBxD,EAAAyD,qB,qKAa3B,GACEpD,KAAM,iBACNqD,KAAAA,GACE,MAAMC,GAASC,EAAAA,EAAAA,MAGTb,GAAUc,EAAAA,EAAAA,KAAI,GACd3C,GAAc2C,EAAAA,EAAAA,IAAI,IAClBC,GAAoBD,EAAAA,EAAAA,IAAI,CAAC,GACzBR,GAAQQ,EAAAA,EAAAA,IAAI,GACZX,GAAcW,EAAAA,EAAAA,IAAI,GAClBV,GAAWU,EAAAA,EAAAA,IAAI,IAGf5D,GAAa8D,EAAAA,EAAAA,IAAS,CAC1B1D,KAAM,GACNI,WAAY,GACZkC,YAAa,MAIfqB,EAAAA,EAAAA,IAAU,KACRC,MAIF,MAAMA,EAAgBC,UACpBnB,EAAQoB,OAAQ,EAChB,IAEE,MAAMC,EAAS,CACbC,KAAMnB,EAAYiB,MAClBG,MAAOnB,EAASgB,OAGdlE,EAAWI,OAAM+D,EAAO/D,KAAOJ,EAAWI,MAC1CJ,EAAWQ,aAAY2D,EAAO3D,WAAaR,EAAWQ,YAE1D,MAAM8D,QAAiBC,EAAAA,EAAMC,IAAI,uCAAwC,CAAEL,WAC3ElD,EAAYiD,MAAQI,EAAStD,KAAKA,KAClCoC,EAAMc,MAAQI,EAAStD,KAAKyD,YAGtBC,GACR,CAAE,MAAOC,GACPC,QAAQD,MAAM,YAAaA,GAC3BE,EAAAA,GAAUF,MAAM,WAClB,CAAE,QACA7B,EAAQoB,OAAQ,CAClB,GAIIQ,EAA6BT,UACjC,IACE,MAAMa,EAAW7D,EAAYiD,MAAMa,IAAIC,GACrCT,EAAAA,EAAMC,IAAI,8DAA8DQ,EAAQ9C,MAC7E+C,KAAKX,IACJT,EAAkBK,MAAMc,EAAQ9C,IAAMoC,EAAStD,KAAKA,OAErDkE,MAAMP,IACLC,QAAQD,MAAM,OAAOK,EAAQ9C,cAAeyC,YAI5CQ,QAAQC,IAAIN,EACpB,CAAE,MAAOH,GACPC,QAAQD,MAAM,gBAAiBA,EACjC,GAII1C,EAAoBA,CAACoD,EAAWC,IAChCzB,EAAkBK,MAAMmB,IAAcC,KAASzB,EAAkBK,MAAMmB,GAClExB,EAAkBK,MAAMmB,GAAWC,GAErC,KAIH9C,EAAuB6C,IAC3B,MAAME,EAAS7C,EAAY2C,GAC3B,OAAOE,EAAS,UAAY,IAIxB7C,EAAe2C,IACqC,IAAjDpD,EAAkBoD,EAAW,gBAIhC/C,EAAiBF,GACd,GAAGA,KAINxB,EAAeA,KACnBqC,EAAYiB,MAAQ,EACpBF,KAIInD,EAAcA,KAClB2E,OAAOC,KAAKzF,GAAY0F,QAAQC,IAC9B3F,EAAW2F,GAAO,KAEpB1C,EAAYiB,MAAQ,EACpBF,KAIIV,EAAoBsC,IACxB1C,EAASgB,MAAQ0B,EACjB5B,KAGIR,EAAuBoC,IAC3B3C,EAAYiB,MAAQ0B,EACpB5B,KAIIpB,EAAeV,IACnBwB,EAAOmC,KAAK,sBAAsB3D,MAI9BW,EAAmBX,IACvBwB,EAAOmC,KAAK,oBAAoB3D,MAGlC,MAAO,CACLY,UACA7B,cACA4C,oBACA7D,aACAiD,cACAC,WACAE,QACAxC,eACAC,cACAyC,mBACAE,sBACAZ,cACAC,kBACAZ,oBACAO,sBACAE,cACAJ,gBAEJ,G,UC7PF,MAAMwD,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAASC,GAAQ,CAAC,YAAY,qBAEzF,O", "sources": ["webpack://ms/./src/views/competency/CompetencyList.vue", "webpack://ms/./src/views/competency/CompetencyList.vue?aa37"], "sourcesContent": ["<template>\r\n  <div class=\"competency-list-container\">\r\n    <el-card class=\"box-card\">\r\n      <template #header>\r\n        <div class=\"card-header\">\r\n          <span class=\"title\">教师能力认定</span>\r\n        </div>\r\n      </template>\r\n\r\n      <!-- 搜索区域 -->\r\n      <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\r\n        <el-form-item label=\"教师姓名\">\r\n          <el-input v-model=\"searchForm.name\" placeholder=\"教师姓名\" clearable />\r\n        </el-form-item>\r\n        <el-form-item label=\"科室\">\r\n          <el-input v-model=\"searchForm.department\" placeholder=\"所属科室\" clearable />\r\n        </el-form-item>\r\n        \r\n        \r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSearch\">查询</el-button>\r\n          <el-button @click=\"resetSearch\">重置</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n\r\n      <!-- 表格区域 -->\r\n      <el-table\r\n        v-loading=\"loading\"\r\n        :data=\"teacherList\"\r\n        border\r\n        style=\"width: 100%\"\r\n      >\r\n        <el-table-column prop=\"id\" label=\"ID\"  />\r\n        <el-table-column prop=\"name\" label=\"教师姓名\"  />\r\n        <el-table-column prop=\"gender\" label=\"性别\"  />\r\n        <el-table-column prop=\"department\" label=\"科室\"  />\r\n        <el-table-column label=\"照片\" width=\"100\">\r\n          <template #default=\"scope\">\r\n            <el-image\r\n              v-if=\"scope.row.photo\"\r\n              :src=\"`http://localhost:3000${scope.row.photo}`\"\r\n              :preview-src-list=\"[`http://localhost:3000${scope.row.photo}`]\"\r\n              fit=\"cover\"\r\n              style=\"width: 50px; height: 50px\"\r\n            />\r\n            <el-avatar v-else :size=\"50\" icon=\"UserFilled\" />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"评价数量\" width=\"100\">\r\n          <template #default=\"scope\">\r\n            {{ getCompetencyData(scope.row.id, 'total_evaluations') || 0 }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"认可数量\" width=\"100\">\r\n          <template #default=\"scope\">\r\n            {{ getCompetencyData(scope.row.id, 'approved_count') || 0 }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"认可率\" width=\"180\">\r\n          <template #default=\"scope\">\r\n            <el-progress \r\n              :percentage=\"getCompetencyData(scope.row.id, 'approval_rate') || 0\" \r\n              :format=\"percentFormat\"\r\n              :status=\"getCompetencyStatus(scope.row.id)\"\r\n            />\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"认证状态\" width=\"100\">\r\n          <template #default=\"scope\">\r\n            <el-tag :type=\"isCertified(scope.row.id) ? 'success' : 'info'\">\r\n              {{ isCertified(scope.row.id) ? '已认证' : '未认证' }}\r\n            </el-tag>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作\" width=\"180\" fixed=\"right\">\r\n          <template #default=\"scope\">\r\n            <el-button size=\"small\" @click=\"viewDetails(scope.row.id)\">详情</el-button>\r\n            <el-button \r\n              size=\"small\" \r\n              type=\"primary\"\r\n              @click=\"viewEvaluations(scope.row.id)\"\r\n            >\r\n              查看评价\r\n            </el-button>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n\r\n      <!-- 分页 -->\r\n      <div class=\"pagination-container\">\r\n        <el-pagination\r\n          v-model:current-page=\"currentPage\"\r\n          v-model:page-size=\"pageSize\"\r\n          :page-sizes=\"[10, 20, 50, 100]\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\"\r\n          :total=\"total\"\r\n          @size-change=\"handleSizeChange\"\r\n          @current-change=\"handleCurrentChange\"\r\n        />\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { ref, reactive, computed, onMounted } from 'vue'\r\nimport { useRouter } from 'vue-router'\r\nimport { ElMessage } from 'element-plus'\r\nimport axios from 'axios'\r\n\r\nexport default {\r\n  name: 'CompetencyList',\r\n  setup() {\r\n    const router = useRouter()\r\n    \r\n    // 基础数据\r\n    const loading = ref(false)\r\n    const teacherList = ref([])\r\n    const competencyDataMap = ref({})\r\n    const total = ref(0)\r\n    const currentPage = ref(1)\r\n    const pageSize = ref(10)\r\n    \r\n    // 搜索表单\r\n    const searchForm = reactive({\r\n      name: '',\r\n      department: '',\r\n      isCertified: ''\r\n    })\r\n    \r\n    // 生命周期钩子\r\n    onMounted(() => {\r\n      fetchTeachers()\r\n    })\r\n    \r\n    // 获取教师列表\r\n    const fetchTeachers = async () => {\r\n      loading.value = true\r\n      try {\r\n        // 添加搜索参数\r\n        const params = {\r\n          page: currentPage.value,\r\n          limit: pageSize.value\r\n        }\r\n        \r\n        if (searchForm.name) params.name = searchForm.name\r\n        if (searchForm.department) params.department = searchForm.department\r\n        \r\n        const response = await axios.get('http://localhost:3000/api/teachers', { params })\r\n        teacherList.value = response.data.data\r\n        total.value = response.data.count\r\n        \r\n        // 获取每个教师的能力认定数据\r\n        await fetchAllTeachersCompetency()\r\n      } catch (error) {\r\n        console.error('获取教师列表失败:', error)\r\n        ElMessage.error('获取教师列表失败')\r\n      } finally {\r\n        loading.value = false\r\n      }\r\n    }\r\n    \r\n    // 获取所有教师的能力认定数据\r\n    const fetchAllTeachersCompetency = async () => {\r\n      try {\r\n        const promises = teacherList.value.map(teacher => \r\n          axios.get(`http://localhost:3000/api/evaluations/competency/teacher/${teacher.id}`)\r\n            .then(response => {\r\n              competencyDataMap.value[teacher.id] = response.data.data\r\n            })\r\n            .catch(error => {\r\n              console.error(`获取教师${teacher.id}能力认定数据失败:`, error)\r\n            })\r\n        )\r\n        \r\n        await Promise.all(promises)\r\n      } catch (error) {\r\n        console.error('获取教师能力认定数据失败:', error)\r\n      }\r\n    }\r\n    \r\n    // 获取教师能力认定数据\r\n    const getCompetencyData = (teacherId, field) => {\r\n      if (competencyDataMap.value[teacherId] && field in competencyDataMap.value[teacherId]) {\r\n        return competencyDataMap.value[teacherId][field]\r\n      }\r\n      return null\r\n    }\r\n    \r\n    // 获取认证状态样式\r\n    const getCompetencyStatus = (teacherId) => {\r\n      const isCert = isCertified(teacherId)\r\n      return isCert ? 'success' : ''\r\n    }\r\n    \r\n    // 是否已认证\r\n    const isCertified = (teacherId) => {\r\n      return getCompetencyData(teacherId, 'is_certified') === true\r\n    }\r\n    \r\n    // 格式化百分比\r\n    const percentFormat = (percentage) => {\r\n      return `${percentage}%`\r\n    }\r\n    \r\n    // 搜索操作\r\n    const handleSearch = () => {\r\n      currentPage.value = 1\r\n      fetchTeachers()\r\n    }\r\n    \r\n    // 重置搜索\r\n    const resetSearch = () => {\r\n      Object.keys(searchForm).forEach(key => {\r\n        searchForm[key] = ''\r\n      })\r\n      currentPage.value = 1\r\n      fetchTeachers()\r\n    }\r\n    \r\n    // 分页操作\r\n    const handleSizeChange = (val) => {\r\n      pageSize.value = val\r\n      fetchTeachers()\r\n    }\r\n    \r\n    const handleCurrentChange = (val) => {\r\n      currentPage.value = val\r\n      fetchTeachers()\r\n    }\r\n    \r\n    // 查看详情\r\n    const viewDetails = (id) => {\r\n      router.push(`/competency/detail/${id}`)\r\n    }\r\n    \r\n    // 查看教师评价\r\n    const viewEvaluations = (id) => {\r\n      router.push(`/teachers/detail/${id}`)\r\n    }\r\n    \r\n    return {\r\n      loading,\r\n      teacherList,\r\n      competencyDataMap,\r\n      searchForm,\r\n      currentPage,\r\n      pageSize,\r\n      total,\r\n      handleSearch,\r\n      resetSearch,\r\n      handleSizeChange,\r\n      handleCurrentChange,\r\n      viewDetails,\r\n      viewEvaluations,\r\n      getCompetencyData,\r\n      getCompetencyStatus,\r\n      isCertified,\r\n      percentFormat\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.competency-list-container {\r\n  padding: 20px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.title {\r\n  font-size: 18px;\r\n  font-weight: bold;\r\n}\r\n\r\n.search-form {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.pagination-container {\r\n  margin-top: 20px;\r\n  display: flex;\r\n  justify-content: center;\r\n}\r\n</style> ", "import { render } from \"./CompetencyList.vue?vue&type=template&id=61fbc65c&scoped=true\"\nimport script from \"./CompetencyList.vue?vue&type=script&lang=js\"\nexport * from \"./CompetencyList.vue?vue&type=script&lang=js\"\n\nimport \"./CompetencyList.vue?vue&type=style&index=0&id=61fbc65c&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-61fbc65c\"]])\n\nexport default __exports__"], "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_cache", "_createElementVNode", "_component_el_form", "inline", "model", "$setup", "searchForm", "_component_el_form_item", "label", "_component_el_input", "name", "$event", "placeholder", "clearable", "department", "_component_el_button", "type", "onClick", "handleSearch", "resetSearch", "_createBlock", "_component_el_table", "data", "teacherList", "border", "style", "_component_el_table_column", "prop", "width", "default", "scope", "row", "photo", "_component_el_image", "src", "fit", "_component_el_avatar", "size", "icon", "getCompetencyData", "id", "_component_el_progress", "percentage", "format", "percentFormat", "status", "getCompetencyStatus", "_component_el_tag", "isCertified", "fixed", "viewDetails", "viewEvaluations", "loading", "_hoisted_2", "_component_el_pagination", "currentPage", "pageSize", "layout", "total", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "setup", "router", "useRouter", "ref", "competencyDataMap", "reactive", "onMounted", "fetchTeachers", "async", "value", "params", "page", "limit", "response", "axios", "get", "count", "fetchAllTeachersCompetency", "error", "console", "ElMessage", "promises", "map", "teacher", "then", "catch", "Promise", "all", "teacherId", "field", "is<PERSON>ert", "Object", "keys", "for<PERSON>ach", "key", "val", "push", "__exports__", "render"], "sourceRoot": ""}