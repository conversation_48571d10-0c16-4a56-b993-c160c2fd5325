const Course = require('../models/courseModel');
const path = require('path');
const fs = require('fs');

// 获取所有课程
exports.getAllCourses = async (req, res) => {
  try {
    const courses = await Course.findAll();
    res.status(200).json({
      success: true,
      count: courses.length,
      data: courses
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取课程列表失败',
      error: error.message
    });
  }
};

// 获取单个课程
exports.getCourseById = async (req, res) => {
  try {
    const course = await Course.findById(req.params.id);
    
    if (!course) {
      return res.status(404).json({
        success: false,
        message: '未找到该课程'
      });
    }
    
    res.status(200).json({
      success: true,
      data: course
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '获取课程信息失败',
      error: error.message
    });
  }
};

// 创建课程
exports.createCourse = async (req, res) => {
  try {
    const { title, description } = req.body;
    
    if (!title) {
      return res.status(400).json({
        success: false,
        message: '请提供课程标题'
      });
    }
    
    // 处理上传的课件文件
    let material_path = null;
    let original_filename = null;
    
    if (req.file) {
      material_path = req.file.path;
      original_filename = req.originalFileName || req.file.originalname;
    }
    
    const courseData = {
      title,
      description,
      material_path,
      original_filename
    };
    
    const courseId = await Course.create(courseData);
    const course = await Course.findById(courseId);
    
    res.status(201).json({
      success: true,
      message: '课程创建成功',
      data: course
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '创建课程失败',
      error: error.message
    });
  }
};

// 更新课程
exports.updateCourse = async (req, res) => {
  try {
    const course = await Course.findById(req.params.id);
    
    if (!course) {
      return res.status(404).json({
        success: false,
        message: '未找到该课程'
      });
    }
    
    // 处理上传的课件文件
    let material_path = course.material_path;
    let original_filename = course.original_filename;
    
    if (req.file) {
      // 如果上传了新文件，删除旧文件
      if (course.material_path && fs.existsSync(course.material_path)) {
        fs.unlinkSync(course.material_path);
      }
      material_path = req.file.path;
      original_filename = req.originalFileName || req.file.originalname;
    }
    
    const courseData = {
      title: req.body.title || course.title,
      description: req.body.description || course.description,
      material_path,
      original_filename
    };
    
    const updated = await Course.update(req.params.id, courseData);
    
    if (updated) {
      const updatedCourse = await Course.findById(req.params.id);
      res.status(200).json({
        success: true,
        message: '课程更新成功',
        data: updatedCourse
      });
    } else {
      res.status(500).json({
        success: false,
        message: '课程更新失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '更新课程失败',
      error: error.message
    });
  }
};

// 删除课程
exports.deleteCourse = async (req, res) => {
  try {
    const course = await Course.findById(req.params.id);
    
    if (!course) {
      return res.status(404).json({
        success: false,
        message: '未找到该课程'
      });
    }
    
    // 删除关联的课件文件
    if (course.material_path && fs.existsSync(course.material_path)) {
      fs.unlinkSync(course.material_path);
    }
    
    const deleted = await Course.delete(req.params.id);
    
    if (deleted) {
      res.status(200).json({
        success: true,
        message: '课程删除成功'
      });
    } else {
      res.status(500).json({
        success: false,
        message: '课程删除失败'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '删除课程失败',
      error: error.message
    });
  }
};

// 搜索课程
exports.searchCourses = async (req, res) => {
  try {
    const { keyword } = req.query;
    
    let courses;
    
    // 如果没有提供关键词，返回所有课程
    if (!keyword || keyword.trim() === '') {
      courses = await Course.findAll();
    } else {
      courses = await Course.search(keyword);
    }
    
    res.status(200).json({
      success: true,
      count: courses.length,
      data: courses
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '搜索课程失败',
      error: error.message
    });
  }
};

// 下载课件
exports.downloadCourseMaterial = async (req, res) => {
  try {
    const course = await Course.findById(req.params.id);
    
    if (!course) {
      return res.status(404).json({
        success: false,
        message: '未找到该课程'
      });
    }
    
    if (!course.material_path) {
      return res.status(404).json({
        success: false,
        message: '该课程没有上传课件'
      });
    }
    
    if (!fs.existsSync(course.material_path)) {
      return res.status(404).json({
        success: false,
        message: '课件文件不存在'
      });
    }
    
    // 使用存储的原始文件名，如果没有则使用路径中的文件名
    let filename = course.original_filename || path.basename(course.material_path);
    
    // 设置Content-Disposition头，确保文件名正确编码
    res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodeURIComponent(filename)}`);
    res.setHeader('Content-Type', 'application/octet-stream');
    
    // 发送文件
    const filePath = path.resolve(course.material_path);
    res.sendFile(filePath, { headers: { 'Content-Type': 'application/octet-stream' } });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: '下载课件失败',
      error: error.message
    });
  }
}; 