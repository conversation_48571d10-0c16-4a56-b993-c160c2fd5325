const { pool } = require('../config/db');

class Exam {
  // 创建新考试
  static async create(examData) {
    try {
      const [result] = await pool.query(
        `INSERT INTO exams (title, description, duration, pass_score, total_score, exam_type) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [
          examData.title,
          examData.description,
          examData.duration,
          examData.pass_score,
          examData.total_score,
          examData.exam_type || '线上考试'
        ]
      );
      return result.insertId;
    } catch (error) {
      console.error('创建考试失败:', error);
      throw error;
    }
  }

  // 获取所有考试
  static async findAll(filter = {}, page, limit) {
    try {
      let query = 'SELECT * FROM exams';
      const params = [];
      
      // Add WHERE clause if filters exist
      if (Object.keys(filter).length > 0) {
        query += ' WHERE ';
        const conditions = [];
        
        if (filter.title) {
          conditions.push('title LIKE ?');
          params.push(`%${filter.title}%`);
        }
        
        query += conditions.join(' AND ');
      }
      
      // Add ORDER BY
      query += ' ORDER BY created_at DESC';
      
      // Add pagination if provided
      if (page && limit) {
        const offset = (parseInt(page) - 1) * parseInt(limit);
        query += ' LIMIT ? OFFSET ?';
        params.push(parseInt(limit), offset);
      }
      
      const [rows] = await pool.query(query, params);
      return rows;
    } catch (error) {
      console.error('获取考试列表失败:', error);
      throw error;
    }
  }
  
  // 获取考试总数（用于分页）
  static async count(filter = {}) {
    try {
      let query = 'SELECT COUNT(*) as total FROM exams';
      const params = [];
      
      // Add WHERE clause if filters exist
      if (Object.keys(filter).length > 0) {
        query += ' WHERE ';
        const conditions = [];
        
        if (filter.title) {
          conditions.push('title LIKE ?');
          params.push(`%${filter.title}%`);
        }
        
        query += conditions.join(' AND ');
      }
      
      const [result] = await pool.query(query, params);
      return result[0].total;
    } catch (error) {
      console.error('获取考试总数失败:', error);
      throw error;
    }
  }

  // 根据ID获取考试
  static async findById(id) {
    try {
      const [rows] = await pool.query('SELECT * FROM exams WHERE id = ?', [id]);
      return rows[0];
    } catch (error) {
      console.error('获取考试信息失败:', error);
      throw error;
    }
  }

  // 更新考试信息
  static async update(id, examData) {
    try {
      const [result] = await pool.query(
        `UPDATE exams SET 
         title = ?, description = ?, duration = ?, pass_score = ?, 
         total_score = ?, exam_type = ?, updated_at = NOW() 
         WHERE id = ?`,
        [
          examData.title,
          examData.description,
          examData.duration,
          examData.pass_score,
          examData.total_score,
          examData.exam_type || '线上考试',
          id
        ]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新考试信息失败:', error);
      throw error;
    }
  }

  // 删除考试
  static async delete(id) {
    try {
      // 事务操作，先删除考试题目，再删除考试
      const connection = await pool.getConnection();
      try {
        await connection.beginTransaction();
        
        // 删除相关的考试题目
        await connection.query('DELETE FROM exam_questions WHERE exam_id = ?', [id]);
        
        // 删除考试
        const [result] = await connection.query('DELETE FROM exams WHERE id = ?', [id]);
        
        await connection.commit();
        connection.release();
        return result.affectedRows > 0;
      } catch (error) {
        await connection.rollback();
        connection.release();
        throw error;
      }
    } catch (error) {
      console.error('删除考试失败:', error);
      throw error;
    }
  }
  
  // 根据考试类型获取考试列表
  static async findByType(examType) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM exams WHERE exam_type = ? ORDER BY created_at DESC',
        [examType]
      );
      return rows;
    } catch (error) {
      console.error('获取考试类型列表失败:', error);
      throw error;
    }
  }
}

class ExamQuestion {
  // 创建考试题目
  static async create(questionData) {
    try {
      const [result] = await pool.query(
        `INSERT INTO exam_questions 
         (question, options, correct_answer, question_type, score, exam_id) 
         VALUES (?, ?, ?, ?, ?, ?)`,
        [
          questionData.question,
          JSON.stringify(questionData.options),
          questionData.correct_answer,
          questionData.question_type,
          questionData.score,
          questionData.exam_id
        ]
      );
      return result.insertId;
    } catch (error) {
      console.error('创建考试题目失败:', error);
      throw error;
    }
  }

  // 批量创建试题（导入模板）
  static async batchCreate(questions, exam_id) {
    try {
      const connection = await pool.getConnection();
      try {
        await connection.beginTransaction();
        
        for (const questionData of questions) {
          await connection.query(
            `INSERT INTO exam_questions 
             (question, options, correct_answer, question_type, score, exam_id) 
             VALUES (?, ?, ?, ?, ?, ?)`,
            [
              questionData.question,
              JSON.stringify(questionData.options),
              questionData.correct_answer,
              questionData.question_type,
              questionData.score,
              exam_id
            ]
          );
        }
        
        await connection.commit();
        connection.release();
        return true;
      } catch (error) {
        await connection.rollback();
        connection.release();
        throw error;
      }
    } catch (error) {
      console.error('批量创建试题失败:', error);
      throw error;
    }
  }

  // 获取考试的所有题目
  static async findByExamId(exam_id) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM exam_questions WHERE exam_id = ?',
        [exam_id]
      );
      return rows;
    } catch (error) {
      console.error('获取考试题目失败:', error);
      throw error;
    }
  }

  // 根据ID获取题目
  static async findById(id) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM exam_questions WHERE id = ?',
        [id]
      );
      return rows[0];
    } catch (error) {
      console.error('获取题目信息失败:', error);
      throw error;
    }
  }

  // 更新题目
  static async update(id, questionData) {
    try {
      const [result] = await pool.query(
        `UPDATE exam_questions SET 
         question = ?, options = ?, correct_answer = ?, 
         question_type = ?, score = ? WHERE id = ?`,
        [
          questionData.question,
          JSON.stringify(questionData.options),
          questionData.correct_answer,
          questionData.question_type,
          questionData.score,
          id
        ]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error('更新题目失败:', error);
      throw error;
    }
  }

  // 删除题目
  static async delete(id) {
    try {
      const [result] = await pool.query(
        'DELETE FROM exam_questions WHERE id = ?',
        [id]
      );
      return result.affectedRows > 0;
    } catch (error) {
      console.error('删除题目失败:', error);
      throw error;
    }
  }
}

class ExamResult {
  // 创建考试结果
  static async create(resultData) {
    try {
      const [result] = await pool.query(
        `INSERT INTO exam_results (teacher_id, exam_id, score, answers) 
         VALUES (?, ?, ?, ?)`,
        [
          resultData.teacher_id,
          resultData.exam_id,
          resultData.score,
          JSON.stringify(resultData.answers)
        ]
      );
      return result.insertId;
    } catch (error) {
      console.error('保存考试结果失败:', error);
      throw error;
    }
  }

  // 获取教师的考试结果
  static async findByTeacherId(teacher_id) {
    try {
      const [rows] = await pool.query(
        `SELECT r.*, e.title as exam_title, t.name as teacher_name
         FROM exam_results r
         JOIN exams e ON r.exam_id = e.id
         JOIN teachers t ON r.teacher_id = t.id
         WHERE r.teacher_id = ?`,
        [teacher_id]
      );
      return rows;
    } catch (error) {
      console.error('获取教师考试结果失败:', error);
      throw error;
    }
  }
  
  // 获取教师特定考试的所有尝试记录
  static async findByTeacherAndExam(teacher_id, exam_id) {
    try {
      const [rows] = await pool.query(
        `SELECT * FROM exam_results 
         WHERE teacher_id = ? AND exam_id = ?
         ORDER BY id DESC`,
        [teacher_id, exam_id]
      );
      return rows;
    } catch (error) {
      console.error('获取教师考试尝试记录失败:', error);
      throw error;
    }
  }

  // 获取考试的所有结果（成绩汇总）
  static async findByExamId(exam_id) {
    try {
      const [rows] = await pool.query(
        `SELECT r.*, t.name as teacher_name
         FROM exam_results r
         JOIN teachers t ON r.teacher_id = t.id
         WHERE r.exam_id = ?
         ORDER BY r.score DESC`,
        [exam_id]
      );
      return rows;
    } catch (error) {
      console.error('获取考试成绩汇总失败:', error);
      throw error;
    }
  }

  // 获取特定考试结果
  static async findById(id) {
    try {
      const [rows] = await pool.query(
        `SELECT r.*, e.title as exam_title, e.pass_score, t.name as teacher_name
         FROM exam_results r
         JOIN exams e ON r.exam_id = e.id
         JOIN teachers t ON r.teacher_id = t.id
         WHERE r.id = ?`,
        [id]
      );
      return rows[0];
    } catch (error) {
      console.error('获取考试结果失败:', error);
      throw error;
    }
  }
}

module.exports = {
  Exam,
  ExamQuestion,
  ExamResult
}; 