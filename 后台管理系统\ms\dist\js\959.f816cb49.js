"use strict";(self["webpackChunkms"]=self["webpackChunkms"]||[]).push([[959],{3959:function(e,a,t){t.r(a),t.d(a,{default:function(){return g}});var l=t(6768),r=t(4232);const n={class:"competency-list-container"},o={class:"pagination-container"};function i(e,a,t,i,c,s){const d=(0,l.g2)("el-input"),u=(0,l.g2)("el-form-item"),p=(0,l.g2)("el-button"),h=(0,l.g2)("el-form"),m=(0,l.g2)("el-table-column"),g=(0,l.g2)("el-image"),b=(0,l.g2)("el-avatar"),v=(0,l.g2)("el-progress"),f=(0,l.g2)("el-tag"),k=(0,l.g2)("el-table"),C=(0,l.g2)("el-pagination"),_=(0,l.g2)("el-card"),F=(0,l.gN)("loading");return(0,l.uX)(),(0,l.CE)("div",n,[(0,l.bF)(_,{class:"box-card"},{header:(0,l.k6)(()=>a[4]||(a[4]=[(0,l.Lk)("div",{class:"card-header"},[(0,l.Lk)("span",{class:"title"},"教师能力认定")],-1)])),default:(0,l.k6)(()=>[(0,l.bF)(h,{inline:!0,model:i.searchForm,class:"search-form"},{default:(0,l.k6)(()=>[(0,l.bF)(u,{label:"教师姓名"},{default:(0,l.k6)(()=>[(0,l.bF)(d,{modelValue:i.searchForm.name,"onUpdate:modelValue":a[0]||(a[0]=e=>i.searchForm.name=e),placeholder:"教师姓名",clearable:""},null,8,["modelValue"])]),_:1}),(0,l.bF)(u,{label:"科室"},{default:(0,l.k6)(()=>[(0,l.bF)(d,{modelValue:i.searchForm.department,"onUpdate:modelValue":a[1]||(a[1]=e=>i.searchForm.department=e),placeholder:"所属科室",clearable:""},null,8,["modelValue"])]),_:1}),(0,l.bF)(u,null,{default:(0,l.k6)(()=>[(0,l.bF)(p,{type:"primary",onClick:i.handleSearch},{default:(0,l.k6)(()=>a[5]||(a[5]=[(0,l.eW)("查询")])),_:1,__:[5]},8,["onClick"]),(0,l.bF)(p,{onClick:i.resetSearch},{default:(0,l.k6)(()=>a[6]||(a[6]=[(0,l.eW)("重置")])),_:1,__:[6]},8,["onClick"])]),_:1})]),_:1},8,["model"]),(0,l.bo)(((0,l.uX)(),(0,l.Wv)(k,{data:i.teacherList,border:"",style:{width:"100%"}},{default:(0,l.k6)(()=>[(0,l.bF)(m,{prop:"id",label:"ID"}),(0,l.bF)(m,{prop:"name",label:"教师姓名"}),(0,l.bF)(m,{prop:"gender",label:"性别"}),(0,l.bF)(m,{prop:"department",label:"科室"}),(0,l.bF)(m,{label:"照片",width:"100"},{default:(0,l.k6)(e=>[e.row.photo?((0,l.uX)(),(0,l.Wv)(g,{key:0,src:`http://localhost:3000${e.row.photo}`,"preview-src-list":[`http://localhost:3000${e.row.photo}`],fit:"cover",style:{width:"50px",height:"50px"}},null,8,["src","preview-src-list"])):((0,l.uX)(),(0,l.Wv)(b,{key:1,size:50,icon:"UserFilled"}))]),_:1}),(0,l.bF)(m,{label:"评价数量",width:"100"},{default:(0,l.k6)(e=>[(0,l.eW)((0,r.v_)(i.getCompetencyData(e.row.id,"total_evaluations")||0),1)]),_:1}),(0,l.bF)(m,{label:"认可数量",width:"100"},{default:(0,l.k6)(e=>[(0,l.eW)((0,r.v_)(i.getCompetencyData(e.row.id,"approved_count")||0),1)]),_:1}),(0,l.bF)(m,{label:"认可率",width:"180"},{default:(0,l.k6)(e=>[(0,l.bF)(v,{percentage:i.getCompetencyData(e.row.id,"approval_rate")||0,format:i.percentFormat,status:i.getCompetencyStatus(e.row.id)},null,8,["percentage","format","status"])]),_:1}),(0,l.bF)(m,{label:"认证状态",width:"100"},{default:(0,l.k6)(e=>[(0,l.bF)(f,{type:i.isCertified(e.row.id)?"success":"info"},{default:(0,l.k6)(()=>[(0,l.eW)((0,r.v_)(i.isCertified(e.row.id)?"已认证":"未认证"),1)]),_:2},1032,["type"])]),_:1}),(0,l.bF)(m,{label:"操作",width:"180",fixed:"right"},{default:(0,l.k6)(e=>[(0,l.bF)(p,{size:"small",onClick:a=>i.viewDetails(e.row.id)},{default:(0,l.k6)(()=>a[7]||(a[7]=[(0,l.eW)("详情")])),_:2,__:[7]},1032,["onClick"]),(0,l.bF)(p,{size:"small",type:"primary",onClick:a=>i.viewEvaluations(e.row.id)},{default:(0,l.k6)(()=>a[8]||(a[8]=[(0,l.eW)(" 查看评价 ")])),_:2,__:[8]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[F,i.loading]]),(0,l.Lk)("div",o,[(0,l.bF)(C,{"current-page":i.currentPage,"onUpdate:currentPage":a[2]||(a[2]=e=>i.currentPage=e),"page-size":i.pageSize,"onUpdate:pageSize":a[3]||(a[3]=e=>i.pageSize=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:i.total,onSizeChange:i.handleSizeChange,onCurrentChange:i.handleCurrentChange},null,8,["current-page","page-size","total","onSizeChange","onCurrentChange"])])]),_:1})])}t(4114),t(8111),t(5207),t(1701);var c=t(144),s=t(1387),d=t(1219),u=t(4373),p={name:"CompetencyList",setup(){const e=(0,s.rd)(),a=(0,c.KR)(!1),t=(0,c.KR)([]),r=(0,c.KR)({}),n=(0,c.KR)(0),o=(0,c.KR)(1),i=(0,c.KR)(10),p=(0,c.Kh)({name:"",department:"",isCertified:""});(0,l.sV)(()=>{h()});const h=async()=>{a.value=!0;try{const e={page:o.value,limit:i.value};p.name&&(e.name=p.name),p.department&&(e.department=p.department);const a=await u.A.get("http://localhost:3000/api/teachers",{params:e});t.value=a.data.data,n.value=a.data.count,await m()}catch(e){console.error("获取教师列表失败:",e),d.nk.error("获取教师列表失败")}finally{a.value=!1}},m=async()=>{try{const e=t.value.map(e=>u.A.get(`http://localhost:3000/api/evaluations/competency/teacher/${e.id}`).then(a=>{r.value[e.id]=a.data.data}).catch(a=>{console.error(`获取教师${e.id}能力认定数据失败:`,a)}));await Promise.all(e)}catch(e){console.error("获取教师能力认定数据失败:",e)}},g=(e,a)=>r.value[e]&&a in r.value[e]?r.value[e][a]:null,b=e=>{const a=v(e);return a?"success":""},v=e=>!0===g(e,"is_certified"),f=e=>`${e}%`,k=()=>{o.value=1,h()},C=()=>{Object.keys(p).forEach(e=>{p[e]=""}),o.value=1,h()},_=e=>{i.value=e,h()},F=e=>{o.value=e,h()},w=a=>{e.push(`/competency/detail/${a}`)},y=a=>{e.push(`/teachers/detail/${a}`)};return{loading:a,teacherList:t,competencyDataMap:r,searchForm:p,currentPage:o,pageSize:i,total:n,handleSearch:k,resetSearch:C,handleSizeChange:_,handleCurrentChange:F,viewDetails:w,viewEvaluations:y,getCompetencyData:g,getCompetencyStatus:b,isCertified:v,percentFormat:f}}},h=t(1241);const m=(0,h.A)(p,[["render",i],["__scopeId","data-v-61fbc65c"]]);var g=m}}]);
//# sourceMappingURL=959.f816cb49.js.map