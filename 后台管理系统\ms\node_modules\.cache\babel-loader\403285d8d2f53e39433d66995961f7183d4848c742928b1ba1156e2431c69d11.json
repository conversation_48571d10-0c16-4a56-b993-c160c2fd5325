{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { reactive, ref, onMounted } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { ElMessage } from 'element-plus';\nimport { User, Lock, Check } from '@element-plus/icons-vue';\nimport axios from 'axios';\n\n// 配置全局API请求拦截器，自动添加token\n\nexport default {\n  __name: 'LoginView',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    axios.interceptors.request.use(config => {\n      const token = localStorage.getItem('token');\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n      }\n      return config;\n    }, error => {\n      return Promise.reject(error);\n    });\n\n    // 响应拦截器，处理401错误\n    axios.interceptors.response.use(response => response, error => {\n      if (error.response && error.response.status === 401) {\n        // 清除本地存储的token\n        localStorage.removeItem('token');\n        localStorage.removeItem('userInfo');\n        // 如果用户不在登录页，重定向到登录页\n        if (window.location.pathname !== '/login') {\n          ElMessage.error('登录已过期，请重新登录');\n          window.location.href = '/#/login';\n        }\n      }\n      return Promise.reject(error);\n    });\n    const router = useRouter();\n    const loginFormRef = ref(null);\n    const loading = ref(false);\n    const API_URL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api';\n\n    // 登录相关\n    const loginForm = reactive({\n      username: '',\n      password: '',\n      remember: false\n    });\n    const loginRules = {\n      username: [{\n        required: true,\n        message: '请输入用户名',\n        trigger: 'blur'\n      }, {\n        min: 3,\n        max: 20,\n        message: '长度在 3 到 20 个字符',\n        trigger: 'blur'\n      }],\n      password: [{\n        required: true,\n        message: '请输入密码',\n        trigger: 'blur'\n      }, {\n        min: 6,\n        max: 20,\n        message: '长度在 6 到 20 个字符',\n        trigger: 'blur'\n      }]\n    };\n    const handleLogin = async () => {\n      if (!loginFormRef.value) return;\n      try {\n        await loginFormRef.value.validate(async valid => {\n          if (valid) {\n            loading.value = true;\n            try {\n              const response = await axios.post(`${API_URL}/auth/login`, {\n                username: loginForm.username,\n                password: loginForm.password\n              });\n\n              // 登录成功，保存token和用户信息\n              const {\n                token,\n                data\n              } = response.data;\n              localStorage.setItem('token', token);\n\n              // 如果选择记住我，保存用户名\n              if (loginForm.remember) {\n                localStorage.setItem('rememberedUsername', loginForm.username);\n              } else {\n                localStorage.removeItem('rememberedUsername');\n              }\n\n              // 存储用户信息\n              localStorage.setItem('userInfo', JSON.stringify(data));\n\n              // 单独保存关键信息，方便使用\n              localStorage.setItem('userId', data.id);\n              localStorage.setItem('userRole', data.role);\n              if (data.teacher_id) {\n                localStorage.setItem('teacherId', data.teacher_id);\n                console.log('保存教师ID:', data.teacher_id);\n              } else if (data.role === 'student') {\n                // 如果是学生但没有student_id，尝试从其他地方获取\n                try {\n                  const studentResponse = await axios.get(`${API_URL}/students/by-user/${data.id}`);\n                  if (studentResponse.data.success && studentResponse.data.data) {\n                    localStorage.setItem('studentId', studentResponse.data.data.id);\n                    console.log('通过用户ID获取并保存学生ID:', studentResponse.data.data.id);\n                  } else {\n                    console.error('无法获取学生ID，但用户角色为学生');\n                    ElMessage.warning('无法获取您的学生信息，部分功能可能无法使用');\n                  }\n                } catch (err) {\n                  console.error('获取学生信息失败:', err);\n                }\n              }\n              console.log('登录成功，用户信息:', data);\n              ElMessage.success('登录成功');\n\n              // 根据角色跳转到不同页面\n              if (data.role === 'admin' || data.role === 'administrator') {\n                router.push('/teachers/list');\n              } else if (data.role === 'teacher') {\n                router.push('/exams/list');\n              } else {\n                router.push('/dashboard'); // 默认页面\n              }\n            } catch (error) {\n              console.error('登录失败:', error);\n              ElMessage.error(error.response?.data?.message || '登录失败，请检查用户名和密码');\n            } finally {\n              loading.value = false;\n            }\n          }\n        });\n      } catch (error) {\n        loading.value = false;\n        ElMessage.error('表单验证失败');\n      }\n    };\n\n    // 注册相关\n    const registerDialogVisible = ref(false);\n    const registerFormRef = ref(null);\n    const registerLoading = ref(false);\n    const registerForm = reactive({\n      username: '',\n      password: '',\n      confirmPassword: '',\n      name: '',\n      email: '',\n      phone: '',\n      student_id: '',\n      role: 'user' // 默认注册为普通用户\n    });\n    const validatePass = (rule, value, callback) => {\n      if (value === '') {\n        callback(new Error('请再次输入密码'));\n      } else if (value !== registerForm.password) {\n        callback(new Error('两次输入密码不一致!'));\n      } else {\n        callback();\n      }\n    };\n    const registerRules = {\n      username: [{\n        required: true,\n        message: '请输入用户名',\n        trigger: 'blur'\n      }, {\n        min: 3,\n        max: 20,\n        message: '长度在 3 到 20 个字符',\n        trigger: 'blur'\n      }],\n      password: [{\n        required: true,\n        message: '请输入密码',\n        trigger: 'blur'\n      }, {\n        min: 6,\n        max: 20,\n        message: '长度在 6 到 20 个字符',\n        trigger: 'blur'\n      }],\n      confirmPassword: [{\n        required: true,\n        message: '请再次输入密码',\n        trigger: 'blur'\n      }, {\n        validator: validatePass,\n        trigger: 'blur'\n      }],\n      name: [{\n        required: true,\n        message: '请输入姓名',\n        trigger: 'blur'\n      }],\n      email: [{\n        type: 'email',\n        message: '请输入正确的邮箱地址',\n        trigger: 'blur'\n      }],\n      phone: [{\n        pattern: /^1[3456789]\\d{9}$/,\n        message: '请输入正确的手机号码',\n        trigger: 'blur'\n      }]\n    };\n    const showRegister = () => {\n      registerDialogVisible.value = true;\n    };\n    const handleRegister = async () => {\n      if (!registerFormRef.value) return;\n      try {\n        await registerFormRef.value.validate(async valid => {\n          if (valid) {\n            registerLoading.value = true;\n            try {\n              const {\n                confirmPassword,\n                ...registerData\n              } = registerForm;\n              const response = await axios.post(`${API_URL}/auth/register`, registerData);\n              ElMessage.success('注册成功，请登录');\n              registerDialogVisible.value = false;\n\n              // 可选：自动填充登录表单\n              loginForm.username = registerForm.username;\n              loginForm.password = '';\n            } catch (error) {\n              console.error('注册失败:', error);\n              ElMessage.error(error.response?.data?.message || '注册失败，请稍后重试');\n            } finally {\n              registerLoading.value = false;\n            }\n          }\n        });\n      } catch (error) {\n        registerLoading.value = false;\n        ElMessage.error('表单验证失败');\n      }\n    };\n\n    // 忘记密码相关\n    const forgotPasswordDialogVisible = ref(false);\n    const forgotPasswordFormRef = ref(null);\n    const forgotPasswordLoading = ref(false);\n    const verifiedIdentity = ref(false);\n    const forgotPasswordForm = reactive({\n      username: '',\n      email: '',\n      newPassword: '',\n      confirmNewPassword: ''\n    });\n    const validateNewPass = (rule, value, callback) => {\n      if (value === '') {\n        callback(new Error('请输入新密码'));\n      } else if (value.length < 6 || value.length > 20) {\n        callback(new Error('密码长度应在6到20个字符之间'));\n      } else {\n        // 如果确认密码已输入，重新验证\n        if (forgotPasswordForm.confirmNewPassword !== '') {\n          forgotPasswordFormRef.value?.validateField('confirmNewPassword');\n        }\n        callback();\n      }\n    };\n    const validateConfirmNewPass = (rule, value, callback) => {\n      if (value === '') {\n        callback(new Error('请再次输入新密码'));\n      } else if (value !== forgotPasswordForm.newPassword) {\n        callback(new Error('两次输入密码不一致!'));\n      } else {\n        callback();\n      }\n    };\n    const forgotPasswordRules = {\n      username: [{\n        required: true,\n        message: '请输入用户名',\n        trigger: 'blur'\n      }],\n      email: [{\n        required: true,\n        message: '请输入邮箱',\n        trigger: 'blur'\n      }, {\n        type: 'email',\n        message: '请输入正确的邮箱地址',\n        trigger: 'blur'\n      }],\n      newPassword: [{\n        required: true,\n        message: '请输入新密码',\n        trigger: 'blur'\n      }, {\n        validator: validateNewPass,\n        trigger: 'blur'\n      }],\n      confirmNewPassword: [{\n        required: true,\n        message: '请再次输入新密码',\n        trigger: 'blur'\n      }, {\n        validator: validateConfirmNewPass,\n        trigger: 'blur'\n      }]\n    };\n    const showForgotPassword = () => {\n      forgotPasswordDialogVisible.value = true;\n      verifiedIdentity.value = false;\n      forgotPasswordForm.newPassword = '';\n      forgotPasswordForm.confirmNewPassword = '';\n    };\n    const handleForgotPassword = async () => {\n      if (!forgotPasswordFormRef.value) return;\n      try {\n        // 根据当前步骤验证不同的字段\n        if (verifiedIdentity.value) {\n          // 只验证密码字段\n          await forgotPasswordFormRef.value.validateField(['newPassword', 'confirmNewPassword']);\n        } else {\n          // 只验证用户名和邮箱\n          await forgotPasswordFormRef.value.validateField(['username', 'email']);\n        }\n        forgotPasswordLoading.value = true;\n        try {\n          if (!verifiedIdentity.value) {\n            // 第一步：验证用户名和邮箱是否匹配\n            const response = await axios.post(`${API_URL}/auth/verify-identity`, {\n              username: forgotPasswordForm.username,\n              email: forgotPasswordForm.email\n            });\n            if (response.data.success) {\n              ElMessage.success('身份验证成功，请设置新密码');\n              verifiedIdentity.value = true;\n            } else {\n              ElMessage.error(response.data.message || '用户名和邮箱不匹配');\n            }\n          } else {\n            // 第二步：重置密码\n            const response = await axios.post(`${API_URL}/auth/reset-password`, {\n              username: forgotPasswordForm.username,\n              email: forgotPasswordForm.email,\n              newPassword: forgotPasswordForm.newPassword\n            });\n            ElMessage.success('密码重置成功，请使用新密码登录');\n            forgotPasswordDialogVisible.value = false;\n\n            // 可以选择自动填充用户名到登录表单\n            loginForm.username = forgotPasswordForm.username;\n            loginForm.password = '';\n          }\n        } catch (error) {\n          console.error('操作失败:', error);\n          if (verifiedIdentity.value) {\n            ElMessage.error(error.response?.data?.message || '密码重置失败，请稍后重试');\n          } else {\n            ElMessage.error(error.response?.data?.message || '身份验证失败，请确认用户名和邮箱是否正确');\n          }\n        } finally {\n          forgotPasswordLoading.value = false;\n        }\n      } catch (error) {\n        forgotPasswordLoading.value = false;\n        ElMessage.error('表单验证失败');\n        console.error(error);\n      }\n    };\n\n    // 检查是否有记住的用户名\n    const checkRememberedUsername = () => {\n      const rememberedUsername = localStorage.getItem('rememberedUsername');\n      if (rememberedUsername) {\n        loginForm.username = rememberedUsername;\n        loginForm.remember = true;\n      }\n    };\n\n    // 组件挂载时检查记住的用户名\n    checkRememberedUsername();\n    const __returned__ = {\n      router,\n      loginFormRef,\n      loading,\n      API_URL,\n      loginForm,\n      loginRules,\n      handleLogin,\n      registerDialogVisible,\n      registerFormRef,\n      registerLoading,\n      registerForm,\n      validatePass,\n      registerRules,\n      showRegister,\n      handleRegister,\n      forgotPasswordDialogVisible,\n      forgotPasswordFormRef,\n      forgotPasswordLoading,\n      verifiedIdentity,\n      forgotPasswordForm,\n      validateNewPass,\n      validateConfirmNewPass,\n      forgotPasswordRules,\n      showForgotPassword,\n      handleForgotPassword,\n      checkRememberedUsername,\n      reactive,\n      ref,\n      onMounted,\n      get useRouter() {\n        return useRouter;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get User() {\n        return User;\n      },\n      get Lock() {\n        return Lock;\n      },\n      get Check() {\n        return Check;\n      },\n      get axios() {\n        return axios;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["reactive", "ref", "onMounted", "useRouter", "ElMessage", "User", "Lock", "Check", "axios", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "error", "Promise", "reject", "response", "status", "removeItem", "window", "location", "pathname", "href", "router", "loginFormRef", "loading", "API_URL", "process", "env", "VUE_APP_API_URL", "loginForm", "username", "password", "remember", "loginRules", "required", "message", "trigger", "min", "max", "handleLogin", "value", "validate", "valid", "post", "data", "setItem", "JSON", "stringify", "id", "role", "teacher_id", "console", "log", "studentResponse", "get", "success", "warning", "err", "push", "registerDialogVisible", "registerFormRef", "registerLoading", "registerForm", "confirmPassword", "name", "email", "phone", "student_id", "validatePass", "rule", "callback", "Error", "registerRules", "validator", "type", "pattern", "showRegister", "handleRegister", "registerData", "forgotPasswordDialogVisible", "forgotPasswordFormRef", "forgotPasswordLoading", "verifiedIdentity", "forgotPasswordForm", "newPassword", "confirmNewPassword", "validateNewPass", "length", "validateField", "validateConfirmNewPass", "forgotPasswordRules", "showForgotPassword", "handleForgotPassword", "checkRememberedUsername", "rememberedUsername"], "sources": ["D:/admin/202506/督导系统/后台管理系统/ms/src/views/LoginView.vue"], "sourcesContent": ["<template>\n  <div class=\"login-container\">\n    <div class=\"login-card\">\n      <!-- Left side -->\n      <div class=\"login-info\">\n        <div class=\"logo-wrapper\">\n          <div class=\"logo-icon\">\n            <img src=\"@/assets/logo.png\" alt=\"Logo\" class=\"logo-img\" />\n            <i class=\"el-icon-monitor\"></i>\n          </div>\n          <div class=\"logo-text\">\n            教学师资评价与能力认定系统\n          </div>\n        </div>\n        \n        <div class=\"welcome-text\">\n          <h2>欢迎回来</h2>\n          <p>登录您的账户以继续访问系统</p>\n        </div>\n        \n        <div class=\"feature-list\">\n          <div class=\"feature-item\">\n            <div class=\"feature-icon\">\n              <el-icon><Check /></el-icon>\n            </div>\n            <div class=\"feature-text\">现代化的管理界面</div>\n          </div>\n          <div class=\"feature-item\">\n            <div class=\"feature-icon\">\n              <el-icon><Check /></el-icon>\n            </div>\n            <div class=\"feature-text\">强大的功能模块</div>\n          </div>\n          <div class=\"feature-item\">\n            <div class=\"feature-icon\">\n              <el-icon><Check /></el-icon>\n            </div>\n            <div class=\"feature-text\">安全可靠的数据保护</div>\n          </div>\n        </div>\n      </div>\n      \n      <!-- Right side -->\n      <div class=\"login-form-wrapper\">\n        <div class=\"login-form-container\">\n          <h2 class=\"form-title\">用户登录</h2>\n          <p class=\"form-subtitle\">请输入您的账户信息</p>\n          \n          <el-form :model=\"loginForm\" :rules=\"loginRules\" ref=\"loginFormRef\" class=\"login-form\">\n            <el-form-item prop=\"username\">\n              <el-input \n                v-model=\"loginForm.username\" \n                placeholder=\"\" \n                :prefix-icon=\"User\">\n              </el-input>\n            </el-form-item>\n            \n            <el-form-item prop=\"password\">\n              <el-input \n                v-model=\"loginForm.password\" \n                type=\"password\" \n                placeholder=\"\" \n                :prefix-icon=\"Lock\"\n                show-password>\n              </el-input>\n            </el-form-item>\n            \n            <div class=\"form-options\">\n              <el-checkbox v-model=\"loginForm.remember\">记住我</el-checkbox>\n              <a href=\"#\" class=\"forgot-link\" @click.prevent=\"showForgotPassword\">忘记密码?</a>\n            </div>\n            \n            <el-form-item>\n              <el-button type=\"primary\" :loading=\"loading\" @click=\"handleLogin\" class=\"login-button\">\n                登录\n              </el-button>\n            </el-form-item>\n            \n           \n          </el-form>\n        </div>\n      </div>\n    </div>\n    \n    <!-- 注册对话框 -->\n    <el-dialog\n      title=\"用户注册\"\n      v-model=\"registerDialogVisible\"\n      width=\"400px\"\n      center\n      destroy-on-close\n    >\n      <el-form :model=\"registerForm\" :rules=\"registerRules\" ref=\"registerFormRef\" label-width=\"80px\">\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input v-model=\"registerForm.username\" placeholder=\"请输入用户名\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"密码\" prop=\"password\">\n          <el-input v-model=\"registerForm.password\" type=\"password\" placeholder=\"请输入密码\" show-password></el-input>\n        </el-form-item>\n        <el-form-item label=\"确认密码\" prop=\"confirmPassword\">\n          <el-input v-model=\"registerForm.confirmPassword\" type=\"password\" placeholder=\"请再次输入密码\" show-password></el-input>\n        </el-form-item>\n        <el-form-item label=\"姓名\" prop=\"name\">\n          <el-input v-model=\"registerForm.name\" placeholder=\"请输入姓名\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"邮箱\" prop=\"email\">\n          <el-input v-model=\"registerForm.email\" placeholder=\"请输入邮箱\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"手机号\" prop=\"phone\">\n          <el-input v-model=\"registerForm.phone\" placeholder=\"请输入手机号\"></el-input>\n        </el-form-item>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"registerDialogVisible = false\">取消</el-button>\n          <el-button type=\"primary\" :loading=\"registerLoading\" @click=\"handleRegister\">注册</el-button>\n        </span>\n      </template>\n    </el-dialog>\n    \n    <!-- 忘记密码对话框 -->\n    <el-dialog\n      title=\"忘记密码\"\n      v-model=\"forgotPasswordDialogVisible\"\n      width=\"400px\"\n      center\n      destroy-on-close\n    >\n      <el-form :model=\"forgotPasswordForm\" :rules=\"forgotPasswordRules\" ref=\"forgotPasswordFormRef\" label-width=\"100px\">\n        <el-form-item label=\"用户名\" prop=\"username\">\n          <el-input v-model=\"forgotPasswordForm.username\" placeholder=\"请输入用户名\"></el-input>\n        </el-form-item>\n        <el-form-item label=\"邮箱\" prop=\"email\">\n          <el-input v-model=\"forgotPasswordForm.email\" placeholder=\"请输入注册时的邮箱\"></el-input>\n        </el-form-item>\n        \n        <template v-if=\"verifiedIdentity\">\n          <el-form-item label=\"新密码\" prop=\"newPassword\">\n            <el-input v-model=\"forgotPasswordForm.newPassword\" type=\"password\" placeholder=\"请输入新密码\" show-password></el-input>\n          </el-form-item>\n          <el-form-item label=\"确认新密码\" prop=\"confirmNewPassword\">\n            <el-input v-model=\"forgotPasswordForm.confirmNewPassword\" type=\"password\" placeholder=\"请再次输入新密码\" show-password></el-input>\n          </el-form-item>\n        </template>\n      </el-form>\n      <template #footer>\n        <span class=\"dialog-footer\">\n          <el-button @click=\"forgotPasswordDialogVisible = false\">取消</el-button>\n          <el-button type=\"primary\" :loading=\"forgotPasswordLoading\" @click=\"handleForgotPassword\">\n            {{ verifiedIdentity ? '重置密码' : '验证身份' }}\n          </el-button>\n        </span>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script setup>\nimport { reactive, ref, onMounted } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { ElMessage } from 'element-plus'\nimport { User, Lock, Check } from '@element-plus/icons-vue'\nimport axios from 'axios'\n\n// 配置全局API请求拦截器，自动添加token\naxios.interceptors.request.use(\n  config => {\n    const token = localStorage.getItem('token')\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`\n    }\n    return config\n  },\n  error => {\n    return Promise.reject(error)\n  }\n)\n\n// 响应拦截器，处理401错误\naxios.interceptors.response.use(\n  response => response,\n  error => {\n    if (error.response && error.response.status === 401) {\n      // 清除本地存储的token\n      localStorage.removeItem('token')\n      localStorage.removeItem('userInfo')\n      // 如果用户不在登录页，重定向到登录页\n      if (window.location.pathname !== '/login') {\n        ElMessage.error('登录已过期，请重新登录')\n        window.location.href = '/#/login'\n      }\n    }\n    return Promise.reject(error)\n  }\n)\n\nconst router = useRouter()\nconst loginFormRef = ref(null)\nconst loading = ref(false)\nconst API_URL = process.env.VUE_APP_API_URL || 'http://localhost:3000/api'\n\n// 登录相关\nconst loginForm = reactive({\n  username: '',\n  password: '',\n  remember: false\n})\n\nconst loginRules = {\n  username: [\n    { required: true, message: '请输入用户名', trigger: 'blur' },\n    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }\n  ],\n  password: [\n    { required: true, message: '请输入密码', trigger: 'blur' },\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\n  ]\n}\n\nconst handleLogin = async () => {\n  if (!loginFormRef.value) return\n  \n  try {\n    await loginFormRef.value.validate(async (valid) => {\n      if (valid) {\n        loading.value = true\n        \n        try {\n          const response = await axios.post(`${API_URL}/auth/login`, {\n            username: loginForm.username,\n            password: loginForm.password\n          })\n          \n          // 登录成功，保存token和用户信息\n          const { token, data } = response.data\n          localStorage.setItem('token', token)\n\n          // 如果选择记住我，保存用户名\n          if (loginForm.remember) {\n            localStorage.setItem('rememberedUsername', loginForm.username)\n          } else {\n            localStorage.removeItem('rememberedUsername')\n          }\n\n          // 存储用户信息\n          localStorage.setItem('userInfo', JSON.stringify(data))\n\n          // 单独保存关键信息，方便使用\n          localStorage.setItem('userId', data.id)\n          localStorage.setItem('userRole', data.role)\n          if (data.teacher_id) {\n            localStorage.setItem('teacherId', data.teacher_id)\n            console.log('保存教师ID:', data.teacher_id)\n          } else if (data.role === 'student') {\n            // 如果是学生但没有student_id，尝试从其他地方获取\n            try {\n              const studentResponse = await axios.get(`${API_URL}/students/by-user/${data.id}`)\n              if (studentResponse.data.success && studentResponse.data.data) {\n                localStorage.setItem('studentId', studentResponse.data.data.id)\n                console.log('通过用户ID获取并保存学生ID:', studentResponse.data.data.id)\n              } else {\n                console.error('无法获取学生ID，但用户角色为学生')\n                ElMessage.warning('无法获取您的学生信息，部分功能可能无法使用')\n              }\n            } catch (err) {\n              console.error('获取学生信息失败:', err)\n            }\n          }\n\n          console.log('登录成功，用户信息:', data)\n          \n          ElMessage.success('登录成功')\n          \n          // 根据角色跳转到不同页面\n          if (data.role === 'admin' || data.role === 'administrator') {\n            router.push('/teachers/list')\n          } else if (data.role === 'teacher') {\n            router.push('/exams/list')\n          } else {\n            router.push('/dashboard') // 默认页面\n          }\n        } catch (error) {\n          console.error('登录失败:', error)\n          ElMessage.error(error.response?.data?.message || '登录失败，请检查用户名和密码')\n        } finally {\n          loading.value = false\n        }\n      }\n    })\n  } catch (error) {\n    loading.value = false\n    ElMessage.error('表单验证失败')\n  }\n}\n\n// 注册相关\nconst registerDialogVisible = ref(false)\nconst registerFormRef = ref(null)\nconst registerLoading = ref(false)\n\nconst registerForm = reactive({\n  username: '',\n  password: '',\n  confirmPassword: '',\n  name: '',\n  email: '',\n  phone: '',\n  student_id: '',\n  role: 'user'  // 默认注册为普通用户\n})\n\nconst validatePass = (rule, value, callback) => {\n  if (value === '') {\n    callback(new Error('请再次输入密码'))\n  } else if (value !== registerForm.password) {\n    callback(new Error('两次输入密码不一致!'))\n  } else {\n    callback()\n  }\n}\n\nconst registerRules = {\n  username: [\n    { required: true, message: '请输入用户名', trigger: 'blur' },\n    { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }\n  ],\n  password: [\n    { required: true, message: '请输入密码', trigger: 'blur' },\n    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }\n  ],\n  confirmPassword: [\n    { required: true, message: '请再次输入密码', trigger: 'blur' },\n    { validator: validatePass, trigger: 'blur' }\n  ],\n  name: [\n    { required: true, message: '请输入姓名', trigger: 'blur' }\n  ],\n  email: [\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\n  ],\n  phone: [\n    { pattern: /^1[3456789]\\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }\n  ]\n}\n\nconst showRegister = () => {\n  registerDialogVisible.value = true\n}\n\nconst handleRegister = async () => {\n  if (!registerFormRef.value) return\n  \n  try {\n    await registerFormRef.value.validate(async (valid) => {\n      if (valid) {\n        registerLoading.value = true\n        \n        try {\n          const { confirmPassword, ...registerData } = registerForm\n          \n          const response = await axios.post(`${API_URL}/auth/register`, registerData)\n          \n          ElMessage.success('注册成功，请登录')\n          registerDialogVisible.value = false\n          \n          // 可选：自动填充登录表单\n          loginForm.username = registerForm.username\n          loginForm.password = ''\n        } catch (error) {\n          console.error('注册失败:', error)\n          ElMessage.error(error.response?.data?.message || '注册失败，请稍后重试')\n        } finally {\n          registerLoading.value = false\n        }\n      }\n    })\n  } catch (error) {\n    registerLoading.value = false\n    ElMessage.error('表单验证失败')\n  }\n}\n\n// 忘记密码相关\nconst forgotPasswordDialogVisible = ref(false)\nconst forgotPasswordFormRef = ref(null)\nconst forgotPasswordLoading = ref(false)\nconst verifiedIdentity = ref(false)\n\nconst forgotPasswordForm = reactive({\n  username: '',\n  email: '',\n  newPassword: '',\n  confirmNewPassword: ''\n})\n\nconst validateNewPass = (rule, value, callback) => {\n  if (value === '') {\n    callback(new Error('请输入新密码'))\n  } else if (value.length < 6 || value.length > 20) {\n    callback(new Error('密码长度应在6到20个字符之间'))\n  } else {\n    // 如果确认密码已输入，重新验证\n    if (forgotPasswordForm.confirmNewPassword !== '') {\n      forgotPasswordFormRef.value?.validateField('confirmNewPassword')\n    }\n    callback()\n  }\n}\n\nconst validateConfirmNewPass = (rule, value, callback) => {\n  if (value === '') {\n    callback(new Error('请再次输入新密码'))\n  } else if (value !== forgotPasswordForm.newPassword) {\n    callback(new Error('两次输入密码不一致!'))\n  } else {\n    callback()\n  }\n}\n\nconst forgotPasswordRules = {\n  username: [\n    { required: true, message: '请输入用户名', trigger: 'blur' }\n  ],\n  email: [\n    { required: true, message: '请输入邮箱', trigger: 'blur' },\n    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }\n  ],\n  newPassword: [\n    { required: true, message: '请输入新密码', trigger: 'blur' },\n    { validator: validateNewPass, trigger: 'blur' }\n  ],\n  confirmNewPassword: [\n    { required: true, message: '请再次输入新密码', trigger: 'blur' },\n    { validator: validateConfirmNewPass, trigger: 'blur' }\n  ]\n}\n\nconst showForgotPassword = () => {\n  forgotPasswordDialogVisible.value = true\n  verifiedIdentity.value = false\n  forgotPasswordForm.newPassword = ''\n  forgotPasswordForm.confirmNewPassword = ''\n}\n\nconst handleForgotPassword = async () => {\n  if (!forgotPasswordFormRef.value) return\n  \n  try {\n    // 根据当前步骤验证不同的字段\n    if (verifiedIdentity.value) {\n      // 只验证密码字段\n      await forgotPasswordFormRef.value.validateField(['newPassword', 'confirmNewPassword'])\n    } else {\n      // 只验证用户名和邮箱\n      await forgotPasswordFormRef.value.validateField(['username', 'email'])\n    }\n    \n    forgotPasswordLoading.value = true\n    \n    try {\n      if (!verifiedIdentity.value) {\n        // 第一步：验证用户名和邮箱是否匹配\n        const response = await axios.post(`${API_URL}/auth/verify-identity`, {\n          username: forgotPasswordForm.username,\n          email: forgotPasswordForm.email\n        })\n        \n        if (response.data.success) {\n          ElMessage.success('身份验证成功，请设置新密码')\n          verifiedIdentity.value = true\n        } else {\n          ElMessage.error(response.data.message || '用户名和邮箱不匹配')\n        }\n      } else {\n        // 第二步：重置密码\n        const response = await axios.post(`${API_URL}/auth/reset-password`, {\n          username: forgotPasswordForm.username,\n          email: forgotPasswordForm.email,\n          newPassword: forgotPasswordForm.newPassword\n        })\n        \n        ElMessage.success('密码重置成功，请使用新密码登录')\n        forgotPasswordDialogVisible.value = false\n        \n        // 可以选择自动填充用户名到登录表单\n        loginForm.username = forgotPasswordForm.username\n        loginForm.password = ''\n      }\n    } catch (error) {\n      console.error('操作失败:', error)\n      if (verifiedIdentity.value) {\n        ElMessage.error(error.response?.data?.message || '密码重置失败，请稍后重试')\n      } else {\n        ElMessage.error(error.response?.data?.message || '身份验证失败，请确认用户名和邮箱是否正确')\n      }\n    } finally {\n      forgotPasswordLoading.value = false\n    }\n  } catch (error) {\n    forgotPasswordLoading.value = false\n    ElMessage.error('表单验证失败')\n    console.error(error)\n  }\n}\n\n// 检查是否有记住的用户名\nconst checkRememberedUsername = () => {\n  const rememberedUsername = localStorage.getItem('rememberedUsername')\n  if (rememberedUsername) {\n    loginForm.username = rememberedUsername\n    loginForm.remember = true\n  }\n}\n\n// 组件挂载时检查记住的用户名\ncheckRememberedUsername()\n</script>\n\n<style scoped>\n.login-container {\n  height: 100vh;\n  width: 100%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  background-color: rgb(149, 117, 205);\n}\n\n.login-card {\n  width: 1000px;\n  height: 600px;\n  display: flex;\n  border-radius: 12px;\n  overflow: hidden;\n  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.1);\n}\n\n/* Left side */\n.login-info {\n  width: 50%;\n  background: linear-gradient(135deg, #8E44AD 0%, #7D3C98 100%);\n  padding: 40px;\n  display: flex;\n  flex-direction: column;\n  color: white;\n}\n\n.logo-wrapper {\n  display: flex;\n  align-items: center;\n  margin-bottom: 60px;\n}\n\n.logo-icon {\n  width: 120px;\n  height: 120px;\n  background-color: white;\n  border-radius: 8px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 12px;\n  font-size: 24px;\n  color: #8E44AD;\n}\n\n.logo-text {\n  font-size: 20px;\n  font-weight: bold;\n}\n\n.welcome-text {\n  margin-bottom: 60px;\n}\n\n.welcome-text h2 {\n  font-size: 32px;\n  margin-bottom: 12px;\n  font-weight: 600;\n}\n\n.welcome-text p {\n  font-size: 16px;\n  opacity: 0.8;\n}\n\n.feature-list {\n  margin-top: auto;\n}\n\n.feature-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.feature-icon {\n  width: 24px;\n  height: 24px;\n  border-radius: 50%;\n  background-color: rgba(255, 255, 255, 0.2);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 12px;\n}\n\n.feature-text {\n  font-size: 16px;\n}\n\n/* Right side */\n.login-form-wrapper {\n  width: 50%;\n  background-color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40px;\n}\n\n.login-form-container {\n  width: 100%;\n  max-width: 320px;\n}\n\n.form-title {\n  font-size: 24px;\n  color: #333;\n  margin-bottom: 8px;\n  text-align: center;\n}\n\n.form-subtitle {\n  font-size: 14px;\n  color: #999;\n  margin-bottom: 30px;\n  text-align: center;\n}\n\n.login-form :deep(.el-input__wrapper) {\n  padding: 0 15px;\n  height: 50px;\n  box-shadow: 0 0 0 1px #e4e7ed inset;\n}\n\n.login-form :deep(.el-input__wrapper.is-focus) {\n  box-shadow: 0 0 0 1px #8E44AD inset;\n}\n\n.form-options {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.forgot-link {\n  color: #9B59B6;\n  text-decoration: none;\n  font-size: 14px;\n}\n\n.login-button {\n  width: 100%;\n  height: 50px;\n  border-radius: 6px;\n  font-size: 16px;\n  background: linear-gradient(135deg, #8E44AD 0%, #7D3C98 100%);\n  border: none;\n  margin-bottom: 20px;\n}\n\n.register-link {\n  text-align: center;\n  font-size: 14px;\n  color: #666;\n}\n\n.register-link a {\n  color: #9B59B6;\n  text-decoration: none;\n  margin-left: 5px;\n}\n\n/* Responsive */\n@media (max-width: 992px) {\n  .login-card {\n    width: 90%;\n    height: auto;\n    flex-direction: column;\n  }\n  \n  .login-info,\n  .login-form-wrapper {\n    width: 100%;\n    padding: 30px;\n  }\n  \n  .login-info {\n    padding-bottom: 40px;\n  }\n  \n  .welcome-text {\n    margin-bottom: 30px;\n  }\n  \n  .feature-list {\n    margin-top: 0;\n  }\n}\n.logo-img {\n  width: 80px;\n  height: 80px;\n  /* margin-right: 4px; */\n  object-fit: contain;\n}\n\n\n</style> "], "mappings": ";AA8JA,SAASA,QAAQ,EAAEC,GAAG,EAAEC,SAAS,QAAQ,KAAI;AAC7C,SAASC,SAAS,QAAQ,YAAW;AACrC,SAASC,SAAS,QAAQ,cAAa;AACvC,SAASC,IAAI,EAAEC,IAAI,EAAEC,KAAK,QAAQ,yBAAwB;AAC1D,OAAOC,KAAK,MAAM,OAAM;;AAExB;;;;;;;;IACAA,KAAK,CAACC,YAAY,CAACC,OAAO,CAACC,GAAG,CAC5BC,MAAM,IAAI;MACR,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO;MAC1C,IAAIF,KAAK,EAAE;QACTD,MAAM,CAACI,OAAO,CAACC,aAAa,GAAG,UAAUJ,KAAK,EAAC;MACjD;MACA,OAAOD,MAAK;IACd,CAAC,EACDM,KAAK,IAAI;MACP,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK;IAC7B,CACF;;IAEA;IACAV,KAAK,CAACC,YAAY,CAACY,QAAQ,CAACV,GAAG,CAC7BU,QAAQ,IAAIA,QAAQ,EACpBH,KAAK,IAAI;MACP,IAAIA,KAAK,CAACG,QAAQ,IAAIH,KAAK,CAACG,QAAQ,CAACC,MAAM,KAAK,GAAG,EAAE;QACnD;QACAR,YAAY,CAACS,UAAU,CAAC,OAAO;QAC/BT,YAAY,CAACS,UAAU,CAAC,UAAU;QAClC;QACA,IAAIC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,QAAQ,EAAE;UACzCtB,SAAS,CAACc,KAAK,CAAC,aAAa;UAC7BM,MAAM,CAACC,QAAQ,CAACE,IAAI,GAAG,UAAS;QAClC;MACF;MACA,OAAOR,OAAO,CAACC,MAAM,CAACF,KAAK;IAC7B,CACF;IAEA,MAAMU,MAAM,GAAGzB,SAAS,CAAC;IACzB,MAAM0B,YAAY,GAAG5B,GAAG,CAAC,IAAI;IAC7B,MAAM6B,OAAO,GAAG7B,GAAG,CAAC,KAAK;IACzB,MAAM8B,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,eAAe,IAAI,2BAA0B;;IAEzE;IACA,MAAMC,SAAS,GAAGnC,QAAQ,CAAC;MACzBoC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE;IACZ,CAAC;IAED,MAAMC,UAAU,GAAG;MACjBH,QAAQ,EAAE,CACR;QAAEI,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,EACtD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,gBAAgB;QAAEC,OAAO,EAAE;MAAO,EAC/D;MACDL,QAAQ,EAAE,CACR;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,gBAAgB;QAAEC,OAAO,EAAE;MAAO;IAElE;IAEA,MAAMG,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI,CAAChB,YAAY,CAACiB,KAAK,EAAE;MAEzB,IAAI;QACF,MAAMjB,YAAY,CAACiB,KAAK,CAACC,QAAQ,CAAC,MAAOC,KAAK,IAAK;UACjD,IAAIA,KAAK,EAAE;YACTlB,OAAO,CAACgB,KAAK,GAAG,IAAG;YAEnB,IAAI;cACF,MAAMzB,QAAQ,GAAG,MAAMb,KAAK,CAACyC,IAAI,CAAC,GAAGlB,OAAO,aAAa,EAAE;gBACzDK,QAAQ,EAAED,SAAS,CAACC,QAAQ;gBAC5BC,QAAQ,EAAEF,SAAS,CAACE;cACtB,CAAC;;cAED;cACA,MAAM;gBAAExB,KAAK;gBAAEqC;cAAK,CAAC,GAAG7B,QAAQ,CAAC6B,IAAG;cACpCpC,YAAY,CAACqC,OAAO,CAAC,OAAO,EAAEtC,KAAK;;cAEnC;cACA,IAAIsB,SAAS,CAACG,QAAQ,EAAE;gBACtBxB,YAAY,CAACqC,OAAO,CAAC,oBAAoB,EAAEhB,SAAS,CAACC,QAAQ;cAC/D,CAAC,MAAM;gBACLtB,YAAY,CAACS,UAAU,CAAC,oBAAoB;cAC9C;;cAEA;cACAT,YAAY,CAACqC,OAAO,CAAC,UAAU,EAAEC,IAAI,CAACC,SAAS,CAACH,IAAI,CAAC;;cAErD;cACApC,YAAY,CAACqC,OAAO,CAAC,QAAQ,EAAED,IAAI,CAACI,EAAE;cACtCxC,YAAY,CAACqC,OAAO,CAAC,UAAU,EAAED,IAAI,CAACK,IAAI;cAC1C,IAAIL,IAAI,CAACM,UAAU,EAAE;gBACnB1C,YAAY,CAACqC,OAAO,CAAC,WAAW,EAAED,IAAI,CAACM,UAAU;gBACjDC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAER,IAAI,CAACM,UAAU;cACxC,CAAC,MAAM,IAAIN,IAAI,CAACK,IAAI,KAAK,SAAS,EAAE;gBAClC;gBACA,IAAI;kBACF,MAAMI,eAAe,GAAG,MAAMnD,KAAK,CAACoD,GAAG,CAAC,GAAG7B,OAAO,qBAAqBmB,IAAI,CAACI,EAAE,EAAE;kBAChF,IAAIK,eAAe,CAACT,IAAI,CAACW,OAAO,IAAIF,eAAe,CAACT,IAAI,CAACA,IAAI,EAAE;oBAC7DpC,YAAY,CAACqC,OAAO,CAAC,WAAW,EAAEQ,eAAe,CAACT,IAAI,CAACA,IAAI,CAACI,EAAE;oBAC9DG,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,eAAe,CAACT,IAAI,CAACA,IAAI,CAACI,EAAE;kBAC9D,CAAC,MAAM;oBACLG,OAAO,CAACvC,KAAK,CAAC,mBAAmB;oBACjCd,SAAS,CAAC0D,OAAO,CAAC,uBAAuB;kBAC3C;gBACF,CAAC,CAAC,OAAOC,GAAG,EAAE;kBACZN,OAAO,CAACvC,KAAK,CAAC,WAAW,EAAE6C,GAAG;gBAChC;cACF;cAEAN,OAAO,CAACC,GAAG,CAAC,YAAY,EAAER,IAAI;cAE9B9C,SAAS,CAACyD,OAAO,CAAC,MAAM;;cAExB;cACA,IAAIX,IAAI,CAACK,IAAI,KAAK,OAAO,IAAIL,IAAI,CAACK,IAAI,KAAK,eAAe,EAAE;gBAC1D3B,MAAM,CAACoC,IAAI,CAAC,gBAAgB;cAC9B,CAAC,MAAM,IAAId,IAAI,CAACK,IAAI,KAAK,SAAS,EAAE;gBAClC3B,MAAM,CAACoC,IAAI,CAAC,aAAa;cAC3B,CAAC,MAAM;gBACLpC,MAAM,CAACoC,IAAI,CAAC,YAAY,CAAC,EAAC;cAC5B;YACF,CAAC,CAAC,OAAO9C,KAAK,EAAE;cACduC,OAAO,CAACvC,KAAK,CAAC,OAAO,EAAEA,KAAK;cAC5Bd,SAAS,CAACc,KAAK,CAACA,KAAK,CAACG,QAAQ,EAAE6B,IAAI,EAAET,OAAO,IAAI,gBAAgB;YACnE,CAAC,SAAS;cACRX,OAAO,CAACgB,KAAK,GAAG,KAAI;YACtB;UACF;QACF,CAAC;MACH,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdY,OAAO,CAACgB,KAAK,GAAG,KAAI;QACpB1C,SAAS,CAACc,KAAK,CAAC,QAAQ;MAC1B;IACF;;IAEA;IACA,MAAM+C,qBAAqB,GAAGhE,GAAG,CAAC,KAAK;IACvC,MAAMiE,eAAe,GAAGjE,GAAG,CAAC,IAAI;IAChC,MAAMkE,eAAe,GAAGlE,GAAG,CAAC,KAAK;IAEjC,MAAMmE,YAAY,GAAGpE,QAAQ,CAAC;MAC5BoC,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZgC,eAAe,EAAE,EAAE;MACnBC,IAAI,EAAE,EAAE;MACRC,KAAK,EAAE,EAAE;MACTC,KAAK,EAAE,EAAE;MACTC,UAAU,EAAE,EAAE;MACdlB,IAAI,EAAE,MAAM,CAAE;IAChB,CAAC;IAED,MAAMmB,YAAY,GAAGA,CAACC,IAAI,EAAE7B,KAAK,EAAE8B,QAAQ,KAAK;MAC9C,IAAI9B,KAAK,KAAK,EAAE,EAAE;QAChB8B,QAAQ,CAAC,IAAIC,KAAK,CAAC,SAAS,CAAC;MAC/B,CAAC,MAAM,IAAI/B,KAAK,KAAKsB,YAAY,CAAC/B,QAAQ,EAAE;QAC1CuC,QAAQ,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC;MAClC,CAAC,MAAM;QACLD,QAAQ,CAAC;MACX;IACF;IAEA,MAAME,aAAa,GAAG;MACpB1C,QAAQ,EAAE,CACR;QAAEI,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,EACtD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,gBAAgB;QAAEC,OAAO,EAAE;MAAO,EAC/D;MACDL,QAAQ,EAAE,CACR;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEC,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,EAAE;QAAEH,OAAO,EAAE,gBAAgB;QAAEC,OAAO,EAAE;MAAO,EAC/D;MACD2B,eAAe,EAAE,CACf;QAAE7B,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,EACvD;QAAEqC,SAAS,EAAEL,YAAY;QAAEhC,OAAO,EAAE;MAAO,EAC5C;MACD4B,IAAI,EAAE,CACJ;QAAE9B,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,EACrD;MACD6B,KAAK,EAAE,CACL;QAAES,IAAI,EAAE,OAAO;QAAEvC,OAAO,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAO,EACzD;MACD8B,KAAK,EAAE,CACL;QAAES,OAAO,EAAE,mBAAmB;QAAExC,OAAO,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAO;IAE3E;IAEA,MAAMwC,YAAY,GAAGA,CAAA,KAAM;MACzBjB,qBAAqB,CAACnB,KAAK,GAAG,IAAG;IACnC;IAEA,MAAMqC,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAI,CAACjB,eAAe,CAACpB,KAAK,EAAE;MAE5B,IAAI;QACF,MAAMoB,eAAe,CAACpB,KAAK,CAACC,QAAQ,CAAC,MAAOC,KAAK,IAAK;UACpD,IAAIA,KAAK,EAAE;YACTmB,eAAe,CAACrB,KAAK,GAAG,IAAG;YAE3B,IAAI;cACF,MAAM;gBAAEuB,eAAe;gBAAE,GAAGe;cAAa,CAAC,GAAGhB,YAAW;cAExD,MAAM/C,QAAQ,GAAG,MAAMb,KAAK,CAACyC,IAAI,CAAC,GAAGlB,OAAO,gBAAgB,EAAEqD,YAAY;cAE1EhF,SAAS,CAACyD,OAAO,CAAC,UAAU;cAC5BI,qBAAqB,CAACnB,KAAK,GAAG,KAAI;;cAElC;cACAX,SAAS,CAACC,QAAQ,GAAGgC,YAAY,CAAChC,QAAO;cACzCD,SAAS,CAACE,QAAQ,GAAG,EAAC;YACxB,CAAC,CAAC,OAAOnB,KAAK,EAAE;cACduC,OAAO,CAACvC,KAAK,CAAC,OAAO,EAAEA,KAAK;cAC5Bd,SAAS,CAACc,KAAK,CAACA,KAAK,CAACG,QAAQ,EAAE6B,IAAI,EAAET,OAAO,IAAI,YAAY;YAC/D,CAAC,SAAS;cACR0B,eAAe,CAACrB,KAAK,GAAG,KAAI;YAC9B;UACF;QACF,CAAC;MACH,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdiD,eAAe,CAACrB,KAAK,GAAG,KAAI;QAC5B1C,SAAS,CAACc,KAAK,CAAC,QAAQ;MAC1B;IACF;;IAEA;IACA,MAAMmE,2BAA2B,GAAGpF,GAAG,CAAC,KAAK;IAC7C,MAAMqF,qBAAqB,GAAGrF,GAAG,CAAC,IAAI;IACtC,MAAMsF,qBAAqB,GAAGtF,GAAG,CAAC,KAAK;IACvC,MAAMuF,gBAAgB,GAAGvF,GAAG,CAAC,KAAK;IAElC,MAAMwF,kBAAkB,GAAGzF,QAAQ,CAAC;MAClCoC,QAAQ,EAAE,EAAE;MACZmC,KAAK,EAAE,EAAE;MACTmB,WAAW,EAAE,EAAE;MACfC,kBAAkB,EAAE;IACtB,CAAC;IAED,MAAMC,eAAe,GAAGA,CAACjB,IAAI,EAAE7B,KAAK,EAAE8B,QAAQ,KAAK;MACjD,IAAI9B,KAAK,KAAK,EAAE,EAAE;QAChB8B,QAAQ,CAAC,IAAIC,KAAK,CAAC,QAAQ,CAAC;MAC9B,CAAC,MAAM,IAAI/B,KAAK,CAAC+C,MAAM,GAAG,CAAC,IAAI/C,KAAK,CAAC+C,MAAM,GAAG,EAAE,EAAE;QAChDjB,QAAQ,CAAC,IAAIC,KAAK,CAAC,iBAAiB,CAAC;MACvC,CAAC,MAAM;QACL;QACA,IAAIY,kBAAkB,CAACE,kBAAkB,KAAK,EAAE,EAAE;UAChDL,qBAAqB,CAACxC,KAAK,EAAEgD,aAAa,CAAC,oBAAoB;QACjE;QACAlB,QAAQ,CAAC;MACX;IACF;IAEA,MAAMmB,sBAAsB,GAAGA,CAACpB,IAAI,EAAE7B,KAAK,EAAE8B,QAAQ,KAAK;MACxD,IAAI9B,KAAK,KAAK,EAAE,EAAE;QAChB8B,QAAQ,CAAC,IAAIC,KAAK,CAAC,UAAU,CAAC;MAChC,CAAC,MAAM,IAAI/B,KAAK,KAAK2C,kBAAkB,CAACC,WAAW,EAAE;QACnDd,QAAQ,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC;MAClC,CAAC,MAAM;QACLD,QAAQ,CAAC;MACX;IACF;IAEA,MAAMoB,mBAAmB,GAAG;MAC1B5D,QAAQ,EAAE,CACR;QAAEI,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,EACtD;MACD6B,KAAK,EAAE,CACL;QAAE/B,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,OAAO;QAAEC,OAAO,EAAE;MAAO,CAAC,EACrD;QAAEsC,IAAI,EAAE,OAAO;QAAEvC,OAAO,EAAE,YAAY;QAAEC,OAAO,EAAE;MAAO,EACzD;MACDgD,WAAW,EAAE,CACX;QAAElD,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAC,EACtD;QAAEqC,SAAS,EAAEa,eAAe;QAAElD,OAAO,EAAE;MAAO,EAC/C;MACDiD,kBAAkB,EAAE,CAClB;QAAEnD,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,UAAU;QAAEC,OAAO,EAAE;MAAO,CAAC,EACxD;QAAEqC,SAAS,EAAEgB,sBAAsB;QAAErD,OAAO,EAAE;MAAO;IAEzD;IAEA,MAAMuD,kBAAkB,GAAGA,CAAA,KAAM;MAC/BZ,2BAA2B,CAACvC,KAAK,GAAG,IAAG;MACvC0C,gBAAgB,CAAC1C,KAAK,GAAG,KAAI;MAC7B2C,kBAAkB,CAACC,WAAW,GAAG,EAAC;MAClCD,kBAAkB,CAACE,kBAAkB,GAAG,EAAC;IAC3C;IAEA,MAAMO,oBAAoB,GAAG,MAAAA,CAAA,KAAY;MACvC,IAAI,CAACZ,qBAAqB,CAACxC,KAAK,EAAE;MAElC,IAAI;QACF;QACA,IAAI0C,gBAAgB,CAAC1C,KAAK,EAAE;UAC1B;UACA,MAAMwC,qBAAqB,CAACxC,KAAK,CAACgD,aAAa,CAAC,CAAC,aAAa,EAAE,oBAAoB,CAAC;QACvF,CAAC,MAAM;UACL;UACA,MAAMR,qBAAqB,CAACxC,KAAK,CAACgD,aAAa,CAAC,CAAC,UAAU,EAAE,OAAO,CAAC;QACvE;QAEAP,qBAAqB,CAACzC,KAAK,GAAG,IAAG;QAEjC,IAAI;UACF,IAAI,CAAC0C,gBAAgB,CAAC1C,KAAK,EAAE;YAC3B;YACA,MAAMzB,QAAQ,GAAG,MAAMb,KAAK,CAACyC,IAAI,CAAC,GAAGlB,OAAO,uBAAuB,EAAE;cACnEK,QAAQ,EAAEqD,kBAAkB,CAACrD,QAAQ;cACrCmC,KAAK,EAAEkB,kBAAkB,CAAClB;YAC5B,CAAC;YAED,IAAIlD,QAAQ,CAAC6B,IAAI,CAACW,OAAO,EAAE;cACzBzD,SAAS,CAACyD,OAAO,CAAC,eAAe;cACjC2B,gBAAgB,CAAC1C,KAAK,GAAG,IAAG;YAC9B,CAAC,MAAM;cACL1C,SAAS,CAACc,KAAK,CAACG,QAAQ,CAAC6B,IAAI,CAACT,OAAO,IAAI,WAAW;YACtD;UACF,CAAC,MAAM;YACL;YACA,MAAMpB,QAAQ,GAAG,MAAMb,KAAK,CAACyC,IAAI,CAAC,GAAGlB,OAAO,sBAAsB,EAAE;cAClEK,QAAQ,EAAEqD,kBAAkB,CAACrD,QAAQ;cACrCmC,KAAK,EAAEkB,kBAAkB,CAAClB,KAAK;cAC/BmB,WAAW,EAAED,kBAAkB,CAACC;YAClC,CAAC;YAEDtF,SAAS,CAACyD,OAAO,CAAC,iBAAiB;YACnCwB,2BAA2B,CAACvC,KAAK,GAAG,KAAI;;YAExC;YACAX,SAAS,CAACC,QAAQ,GAAGqD,kBAAkB,CAACrD,QAAO;YAC/CD,SAAS,CAACE,QAAQ,GAAG,EAAC;UACxB;QACF,CAAC,CAAC,OAAOnB,KAAK,EAAE;UACduC,OAAO,CAACvC,KAAK,CAAC,OAAO,EAAEA,KAAK;UAC5B,IAAIsE,gBAAgB,CAAC1C,KAAK,EAAE;YAC1B1C,SAAS,CAACc,KAAK,CAACA,KAAK,CAACG,QAAQ,EAAE6B,IAAI,EAAET,OAAO,IAAI,cAAc;UACjE,CAAC,MAAM;YACLrC,SAAS,CAACc,KAAK,CAACA,KAAK,CAACG,QAAQ,EAAE6B,IAAI,EAAET,OAAO,IAAI,sBAAsB;UACzE;QACF,CAAC,SAAS;UACR8C,qBAAqB,CAACzC,KAAK,GAAG,KAAI;QACpC;MACF,CAAC,CAAC,OAAO5B,KAAK,EAAE;QACdqE,qBAAqB,CAACzC,KAAK,GAAG,KAAI;QAClC1C,SAAS,CAACc,KAAK,CAAC,QAAQ;QACxBuC,OAAO,CAACvC,KAAK,CAACA,KAAK;MACrB;IACF;;IAEA;IACA,MAAMiF,uBAAuB,GAAGA,CAAA,KAAM;MACpC,MAAMC,kBAAkB,GAAGtF,YAAY,CAACC,OAAO,CAAC,oBAAoB;MACpE,IAAIqF,kBAAkB,EAAE;QACtBjE,SAAS,CAACC,QAAQ,GAAGgE,kBAAiB;QACtCjE,SAAS,CAACG,QAAQ,GAAG,IAAG;MAC1B;IACF;;IAEA;IACA6D,uBAAuB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}