import docx
from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import os

def read_word_document(file_path):
    """Read content from a Word document."""
    doc = Document(file_path)
    content = []
    
    for para in doc.paragraphs:
        content.append(para.text)
    
    return content

def read_text_file(file_path):
    """Read content from a text file (source code)."""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.readlines()
        # Remove trailing newlines
        content = [line.rstrip() for line in content]
        return content
    except UnicodeDecodeError:
        # Try another encoding if UTF-8 fails
        try:
            with open(file_path, 'r', encoding='gbk') as file:
                content = file.readlines()
            content = [line.rstrip() for line in content]
            return content
        except:
            print(f"Error reading file {file_path}. Skipping.")
            return []

def replace_content_with_formatting(source_file, target_file, new_content):
    """
    Replace content in Word document while preserving formatting.
    
    Args:
        source_file: Path to the source Word document
        target_file: Path to save the new document
        new_content: List of strings for each paragraph to replace
    """
    # Load the source document
    doc = Document(source_file)
    
    # Clear all paragraphs while keeping only the minimum needed
    while len(doc.paragraphs) > len(new_content):
        p = doc.paragraphs[-1]._element
        p.getparent().remove(p)
    
    # Make sure we have enough paragraphs
    while len(doc.paragraphs) < len(new_content):
        doc.add_paragraph()
    
    # Replace text in each paragraph, preserving formatting
    for i, content in enumerate(new_content):
        if i < len(doc.paragraphs):
            # Store the original paragraph's formatting
            original_paragraph = doc.paragraphs[i]
            original_format = original_paragraph.paragraph_format
            original_alignment = original_format.alignment
            original_runs = list(original_paragraph.runs)
            
            # Clear the paragraph
            p = doc.paragraphs[i]
            p.clear()
            
            # Add new text with the first run's formatting if available
            if original_runs:
                run = p.add_run(content)
                # Copy font formatting from the first run
                if original_runs[0].font:
                    run.font.name = original_runs[0].font.name
                    run.font.size = original_runs[0].font.size
                    run.font.bold = original_runs[0].font.bold
                    run.font.italic = original_runs[0].font.italic
            else:
                p.add_run(content)
            
            # Restore paragraph formatting
            p.paragraph_format.alignment = original_alignment
    
    # Save the document
    doc.save(target_file)
    print(f"Document saved to {target_file}")

def collect_source_files(root_dir, extensions=None):
    """Recursively collect all source code files in a directory."""
    if extensions is None:
        # Default extensions for various source code files
        extensions = ['.js', '.py', '.java', '.c', '.cpp', '.h', '.vue', '.jsx', '.ts', '.tsx', '.html', '.css', '.scss']
    
    source_files = []
    
    for root, dirs, files in os.walk(root_dir):
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                source_files.append(os.path.join(root, file))
    
    return source_files

def read_all_source_code(directory, max_files=None):
    """Read all source code files in the directory structure."""
    source_files = collect_source_files(directory)
    result = []
    
    # Sort files to make output more organized
    source_files.sort()
    
    # Limit number of files if specified
    if max_files is not None:
        source_files = source_files[:max_files]
    
    for file_path in source_files:
        # Get relative path for cleaner output
        rel_path = os.path.relpath(file_path, directory)
        result.append("")
        result.append(f"// File: {rel_path}")
        result.append("")
        
        # Read and add file contents
        content = read_text_file(file_path)
        result.extend(content)
    
    return result

def process_source_code_to_document(template_file, target_file, src_directory, project_title="实习生管理系统"):
    """Process source code files and create a Word document."""
    # Read original document for formatting
    original_content = read_word_document(template_file)
    print(f"Using template {template_file} with {len(original_content)} paragraphs")
    
    # Define new content
    new_content = [
        f"{project_title}源代码",
        "",
        "以下是实习生管理系统的核心代码：",
        "",
    ]
    
    # Add source code
    source_code = read_all_source_code(src_directory, max_files=50)  # Limit to 50 files to prevent too large documents
    new_content.extend(source_code)
    
    # Replace content in the document
    replace_content_with_formatting(template_file, target_file, new_content)
    return target_file

def main():
    # Define paths
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Source code directories
    src_dir = os.path.join(current_dir, "读取代码", "src")
    src2_dir = os.path.join(current_dir, "读取代码", "src2", "src")
    
    # Template file - using one of the existing docx files
    template_file = os.path.join(current_dir, "SF6气体在线监测装置软件_源代码.docx")
    
    # Create output directory if it doesn't exist
    output_dir = os.path.join(current_dir, "processed_files")
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created output directory: {output_dir}")
    
    # Combined target file for all code
    target_file = os.path.join(output_dir, "清远市妇幼保健院教学师资评价与能力认定系统_源代码.docx")
    
    # Check if directories exist and collect source code
    all_source_code = []
    
    # Add backend code section if directory exists
    if os.path.exists(src_dir):
        print(f"Reading backend code from: {src_dir}")
        all_source_code.append("")
        all_source_code.append("// ===== 后端代码部分 =====")
        all_source_code.append("")
        backend_code = read_all_source_code(src_dir, max_files=100)
        all_source_code.extend(backend_code)
    else:
        print(f"Backend source directory not found: {src_dir}")
    
    # Add frontend code section if directory exists
    if os.path.exists(src2_dir):
        print(f"Reading frontend code from: {src2_dir}")
        all_source_code.append("")
        all_source_code.append("// ===== 前端代码部分 =====")
        all_source_code.append("")
        frontend_code = read_all_source_code(src2_dir, max_files=100)
        all_source_code.extend(frontend_code)
    else:
        print(f"Frontend source directory not found: {src2_dir}")
    
    # Create the document with all code
    try:
        # Read original document for formatting
        original_content = read_word_document(template_file)
        print(f"Using template {template_file} with {len(original_content)} paragraphs")
        
        # Define new content
        new_content = [
            "实习生管理系统源代码",
            "",
            "以下是实习生管理系统的核心代码（包含前端和后端）：",
            ""
        ]
        
        # Add all collected source code
        new_content.extend(all_source_code)
        
        # Replace content in the document
        replace_content_with_formatting(template_file, target_file, new_content)
        print(f"All code processed and saved to {target_file}")
    except Exception as e:
        print(f"Error processing code: {e}")

if __name__ == "__main__":
    main()
